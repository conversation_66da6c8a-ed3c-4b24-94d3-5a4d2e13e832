package com.niceloo.cmc.ex.utils;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Elasticsearch批量更新工具
 * 根据field6.txt文件中的值批量更新Elasticsearch中的文档
 */
public class ElasticsearchBatchUpdater {
    
    private RestHighLevelClient client;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public ElasticsearchBatchUpdater() {
        this.client = createClient();
    }
    
    /**
     * 创建Elasticsearch客户端
     */
    private static RestHighLevelClient createClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, 
            new UsernamePasswordCredentials(BatchUpdateConfig.ES_USERNAME, BatchUpdateConfig.ES_PASSWORD));

        return new RestHighLevelClient(
                RestClient.builder(new HttpHost(BatchUpdateConfig.ES_HOST, BatchUpdateConfig.ES_PORT, BatchUpdateConfig.ES_SCHEME))
                        .setHttpClientConfigCallback(httpClientBuilder -> 
                            httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }
    
    /**
     * 记录日志
     */
    private void log(String message) {
        System.out.println("[" + dateFormat.format(new Date()) + "] " + message);
    }
    
    /**
     * 从文件中读取field6值
     */
    private List<String> readField6FromFile() throws IOException {
        List<String> field6List = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(BatchUpdateConfig.FIELD6_FILE_PATH))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    field6List.add(line);
                }
            }
        }
        log("从文件中读取到 " + field6List.size() + " 条field6记录");
        return field6List;
    }
    
    /**
     * 将列表分批处理
     */
    private <T> List<List<T>> partition(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            batches.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return batches;
    }
    
    /**
     * 根据field6批次查询文档
     */
    private List<SearchHit> searchDocuments(List<String> field6Batch) throws IOException {
        SearchRequest searchRequest = new SearchRequest(BatchUpdateConfig.INDEX_NAME);
        
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("channelType", BatchUpdateConfig.CHANNEL_TYPE))
                .must(QueryBuilders.termsQuery("field6", field6Batch));
        
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(boolQuery)
                .size(BatchUpdateConfig.SEARCH_SIZE);
        
        searchRequest.source(searchSourceBuilder);
        
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        
        List<SearchHit> hits = new ArrayList<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            hits.add(hit);
        }
        
        log("查询到 " + hits.size() + " 条匹配的文档");
        return hits;
    }
    
    /**
     * 批量更新文档
     */
    private boolean updateDocuments(List<SearchHit> hits) throws IOException {
        if (hits.isEmpty()) {
            return true;
        }
        
        BulkRequest bulkRequest = new BulkRequest();
        
        for (SearchHit hit : hits) {
            UpdateRequest updateRequest = new UpdateRequest(BatchUpdateConfig.INDEX_NAME, hit.getId());
            
            // 构建更新内容
            String updateJson = "{"
                    + "\"dataCompleteStatus\": \"" + BatchUpdateConfig.DATA_COMPLETE_STATUS_VALUE + "\","
                    + "\"serverFolder\": \"" + BatchUpdateConfig.SERVER_FOLDER_VALUE + "\""
                    + "}";
            
            updateRequest.doc(updateJson, XContentType.JSON);
            bulkRequest.add(updateRequest);
        }
        
        BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
        
        if (bulkResponse.hasFailures()) {
            log("批量更新出现错误: " + bulkResponse.buildFailureMessage());
            return false;
        } else {
            log("成功更新 " + hits.size() + " 条文档");
            return true;
        }
    }
    
    /**
     * 带重试机制的批次处理
     */
    private void processBatchWithRetry(List<String> batch, int batchIndex, int totalBatches) {
        int retryCount = 0;
        boolean success = false;
        
        while (retryCount < BatchUpdateConfig.MAX_RETRIES && !success) {
            try {
                log("正在处理第 " + (batchIndex + 1) + "/" + totalBatches + " 批次" + 
                    (retryCount > 0 ? " (重试第" + retryCount + "次)" : "") + "...");
                
                // 查询文档
                List<SearchHit> hits = searchDocuments(batch);
                
                // 更新文档
                if (!hits.isEmpty()) {
                    success = updateDocuments(hits);
                } else {
                    success = true; // 没有匹配的文档也算成功
                }
                
            } catch (Exception e) {
                retryCount++;
                log("处理第 " + (batchIndex + 1) + " 批次时出错 (第" + retryCount + "次尝试): " + e.getMessage());
                
                if (retryCount < BatchUpdateConfig.MAX_RETRIES) {
                    try {
                        Thread.sleep(BatchUpdateConfig.INTERVAL_MS * retryCount); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        if (!success) {
            log("第 " + (batchIndex + 1) + " 批次处理失败，已达到最大重试次数");
        }
    }
    
    /**
     * 执行批量更新任务
     */
    public void executeBatchUpdate() {
        try {
            log("开始执行批量更新任务...");
            
            // 读取field6文件
            List<String> field6List = readField6FromFile();
            
            if (field6List.isEmpty()) {
                log("field6文件为空，任务结束");
                return;
            }
            
            // 分批处理
            List<List<String>> batches = partition(field6List, BatchUpdateConfig.BATCH_SIZE);
            log("总共分为 " + batches.size() + " 个批次处理，每批次间隔 " + BatchUpdateConfig.INTERVAL_MS + " 毫秒");
            
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < batches.size(); i++) {
                processBatchWithRetry(batches.get(i), i, batches.size());
                
                // 批次间隔
                if (i < batches.size() - 1) {
                    try {
                        Thread.sleep(BatchUpdateConfig.INTERVAL_MS);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log("任务被中断");
                        break;
                    }
                }
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log("批量更新任务完成！");
            log("总耗时: " + (duration / 1000.0) + " 秒");
            log("处理批次数: " + batches.size());
            
        } catch (Exception e) {
            log("执行批量更新时出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeClient();
        }
    }
    
    /**
     * 关闭客户端连接
     */
    private void closeClient() {
        try {
            if (client != null) {
                client.close();
                log("Elasticsearch客户端连接已关闭");
            }
        } catch (IOException e) {
            log("关闭客户端连接时出错: " + e.getMessage());
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        ElasticsearchBatchUpdater updater = new ElasticsearchBatchUpdater();
        updater.executeBatchUpdate();
    }
}
