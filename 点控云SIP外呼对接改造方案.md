# 点控云SIP外呼对接改造方案

## 1. 点控云API分析

### 1.1 核心功能
点控云提供了完整的呼叫中心API，主要包括：
- **座席管理**：登录/退出、状态设置、并发查询
- **呼叫控制**：外呼、挂断、转接、咨询、监听等
- **事件推送**：响铃、应答、挂机事件实时推送
- **通话记录**：详细的通话记录查询和统计
- **分机管理**：SIP分机、WebRTC分机管理

### 1.2 关键接口
1. **座席登录**：`/agentlogin` - 座席登录并绑定分机
2. **电话呼叫**：`/placecall` - 发起外呼
3. **双呼接口**：`/webcall` - 双呼模式
4. **事件推送**：实时推送通话状态变化
5. **通话记录**：`/callrecord` - 获取通话详情

## 2. 当前项目架构分析

### 2.1 现有外呼服务商
当前项目已集成多个外呼服务商：
- 京东言犀 (JDYX)
- 百应AI (BYAI)
- 中弘智享 (ZHZX)
- 巨量飞鱼 (JLFY)
- 云客 (YK)
- 亿讯 (YX)
- 天权 (TQ)
- 飞鱼 (FY)

### 2.2 现有架构优势
- 统一的外呼服务适配层
- 标准化的通话记录处理
- 完善的权限控制机制
- 异步消息处理架构

## 3. 点控云对接改造方案

### 3.1 新增点控云服务类

#### 3.1.1 创建点控云服务接口
```java
// 文件位置：src/main/java/com/niceloo/cmc/ex/service/DKYunService.java
public interface DKYunService {
    
    /**
     * 座席登录
     */
    void agentLogin(String agentId, String extension, String queue);
    
    /**
     * 座席退出
     */
    void agentLogout(String agentId);
    
    /**
     * 发起外呼
     */
    String placeCall(String agentId, String calledNum, String userField);
    
    /**
     * 双呼模式
     */
    String webCall(String salesNumber, String calledNum, String userField);
    
    /**
     * 挂断电话
     */
    void hangup(String agentId, String linkedId);
    
    /**
     * 查询通话记录
     */
    List<CallRecordVO> getCallRecords(String startTime, String endTime);
}
```

#### 3.1.2 实现点控云服务
```java
// 文件位置：src/main/java/com/niceloo/cmc/ex/service/impl/DKYunServiceImpl.java
@Service
@CustomLog
public class DKYunServiceImpl implements DKYunService {
    
    @Value("${dkyun.api.baseUrl}")
    private String baseUrl;
    
    @Value("${dkyun.api.projectId}")
    private String projectId;
    
    @Value("${dkyun.api.token}")
    private String token;
    
    @Resource
    private RestTemplate restTemplate;
    
    @Override
    public void agentLogin(String agentId, String extension, String queue) {
        String url = buildUrl("/agentlogin");
        Map<String, Object> params = new HashMap<>();
        params.put("agentId", agentId);
        params.put("extension", extension);
        params.put("queue", queue);
        params.put("initialstate", 2); // 空闲状态
        
        callDKYunAPI(url, params);
    }
    
    @Override
    public String placeCall(String agentId, String calledNum, String userField) {
        String url = buildUrl("/placecall");
        Map<String, Object> params = new HashMap<>();
        params.put("agentId", agentId);
        params.put("callednum", calledNum);
        params.put("userfield", userField);
        
        Map<String, Object> response = callDKYunAPI(url, params);
        return (String) ((Map<String, Object>) response.get("data")).get("linkedId");
    }
    
    private String buildUrl(String endpoint) {
        long timestamp = System.currentTimeMillis() / 1000;
        String sign = DigestUtils.md5Hex(projectId + token + timestamp);
        return String.format("%s%s?projectId=%s&timestamp=%d&sign=%s", 
                           baseUrl, endpoint, projectId, timestamp, sign);
    }
    
    private Map<String, Object> callDKYunAPI(String url, Map<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        headers.set("Connection", "Keep-Alive");
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params, headers);
        ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
        
        if (response.getStatusCode() == HttpStatus.OK) {
            Map<String, Object> result = response.getBody();
            if (200 == (Integer) result.get("code")) {
                return result;
            } else {
                throw new ApplicationException(ApiErrorCodes.execute_failed, 
                                             (String) result.get("msg"));
            }
        }
        throw new ApplicationException(ApiErrorCodes.execute_failed, "点控云API调用失败");
    }
}
```

### 3.2 新增点控云外呼渠道枚举

```java
// 修改文件：src/main/java/com/niceloo/cmc/ex/common/CallChannelEnum.java
public enum CallChannelEnum {
    // ... 现有枚举
    DK("DK", "点控云", DKYunService.class);
    
    // ... 其他代码
}
```

### 3.3 创建点控云事件监听器

#### 3.3.1 事件监听Controller
```java
// 文件位置：src/main/java/com/niceloo/cmc/ex/controller/callback/DKYunCallbackController.java
@RestController
@RequestMapping("/callback/dkyun")
@CustomLog
public class DKYunCallbackController {
    
    @Resource
    private CallRecordService callRecordService;
    
    /**
     * 点控云事件回调接口
     */
    @PostMapping("/events")
    public String handleEvents(@RequestBody Map<String, Object> eventData) {
        try {
            processCallEvent(eventData);
            return "success";
        } catch (Exception e) {
            log.error("处理点控云事件失败", e);
            return "error";
        }
    }
    
    private void processCallEvent(Map<String, Object> eventData) {
        // 处理座席侧事件
        if (eventData.containsKey("agent")) {
            Map<String, Object> agentEvent = (Map<String, Object>) eventData.get("agent");
            processAgentEvent(agentEvent);
        }
        
        // 处理客户侧事件
        if (eventData.containsKey("customer")) {
            Map<String, Object> customerEvent = (Map<String, Object>) eventData.get("customer");
            processCustomerEvent(customerEvent);
        }
    }
    
    private void processAgentEvent(Map<String, Object> agentEvent) {
        String eventType = (String) agentEvent.get("eventType");
        String linkedId = (String) agentEvent.get("linkedId");
        
        switch (eventType) {
            case "RINGING":
                handleRingingEvent(agentEvent);
                break;
            case "ANSWERED":
                handleAnsweredEvent(agentEvent);
                break;
            case "HANGUP":
                handleHangupEvent(agentEvent);
                break;
        }
    }
    
    private void handleHangupEvent(Map<String, Object> event) {
        // 构建通话记录并保存到ES
        CallRecordES callRecord = buildCallRecord(event);
        callRecordService.saveCallRecord(callRecord);
    }
    
    private CallRecordES buildCallRecord(Map<String, Object> event) {
        CallRecordES record = new CallRecordES();
        record.setLinkedId((String) event.get("linkedId"));
        record.setChannelType("DK");
        record.setAgentNumber((String) event.get("agentNumber"));
        record.setCustomerTelNumber((String) event.get("customerTelNumber"));
        record.setStartTime(parseTime((String) event.get("startTime")));
        record.setEndTime(parseTime((String) event.get("endTime")));
        record.setBillsec((Integer) event.get("billsec"));
        record.setDuration((Integer) event.get("duration"));
        // ... 设置其他字段
        return record;
    }
}
```

### 3.4 配置文件修改

#### 3.4.1 添加点控云配置
```yaml
# 文件位置：src/main/resources/application-local.yml
dkyun:
  api:
    baseUrl: https://your-dkyun-domain/dky-open-api/version-2.0.0
    projectId: your-project-id
    token: your-token
  callback:
    url: http://your-domain/communicationcenter/callback/dkyun/events
```

### 3.5 数据库表结构调整

#### 3.5.1 外呼账号配置表
```sql
-- 在cc_call_account_config表中添加点控云配置
INSERT INTO cc_call_account_config (
    channel_type, account_name, config_json, status, create_time
) VALUES (
    'DK', '点控云账号', 
    '{"projectId":"your-project-id","token":"your-token","baseUrl":"https://api.dk-yun.cn"}',
    1, NOW()
);
```

### 3.6 消息队列处理

#### 3.6.1 点控云通话记录消费者
```java
// 文件位置：src/main/java/com/niceloo/cmc/ex/mq/consumer/DKYunCallRecordMQConsumer.java
@Component
@CustomLog
public class DKYunCallRecordMQConsumer {
    
    @Resource
    private CallRecordService callRecordService;
    
    @RabbitListener(queues = "dkyun.call.record.queue")
    public void handleCallRecord(String message) {
        try {
            Map<String, Object> callData = JSONUtils.parseObject(message, Map.class);
            CallRecordES record = convertToCallRecord(callData);
            callRecordService.saveCallRecord(record);
        } catch (Exception e) {
            log.error("处理点控云通话记录失败", e);
        }
    }
}
```

## 4. 集成步骤

### 4.1 第一阶段：基础集成
1. 创建DKYunService接口和实现类
2. 添加配置文件和数据库配置
3. 实现基础的外呼功能
4. 测试座席登录和外呼功能

### 4.2 第二阶段：事件处理
1. 创建事件回调Controller
2. 实现通话记录处理逻辑
3. 集成消息队列处理
4. 测试完整的通话流程

### 4.3 第三阶段：功能完善
1. 添加高级功能（转接、监听等）
2. 完善错误处理和重试机制
3. 添加监控和告警
4. 性能优化和压力测试

## 5. 注意事项

### 5.1 API限制
- 点控云API使用签名验证，需要正确计算MD5签名
- 异步接口需要通过事件推送获取结果
- 需要配置回调URL接收事件推送

### 5.2 数据映射
- 点控云的linkedId对应项目中的通话唯一标识
- 需要将点控云的事件类型映射到项目的通话状态
- 通话记录字段需要适配现有的ES索引结构

### 5.3 安全考虑
- API密钥需要加密存储
- 回调接口需要验证来源
- 敏感数据需要脱敏处理

## 6. 预期效果

集成完成后，项目将支持：
1. 点控云SIP外呼功能
2. 实时通话状态监控
3. 完整的通话记录管理
4. 与现有系统的无缝集成
5. 统一的权限控制和数据统计

这样的改造方案可以在保持现有架构稳定的基础上，快速集成点控云的SIP外呼能力。
