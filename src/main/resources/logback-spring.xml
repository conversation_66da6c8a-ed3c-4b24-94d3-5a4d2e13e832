<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" debug="false">
    <!-- CONSOLE -->
    <!-- 自定义控制台输出格式时,请在springboot配置文件中使用logging.pattern.console指定,不要直接修改此处 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%cyan([%d{yyyy-MM-dd HH:mm:ss.SSS}]) %highlight([%-5level]) %cyan([%thread][%logger]%X{apilog}[%X{logId}]:) %highlight(%msg) %n}"/>

    <appender name="LOG_LOCAL" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            <charset>utf-8</charset>
        </encoder>
        <withJansi>true</withJansi>
    </appender>

    <property name="logDir" value="/data/glusterfs/logs/cmc"/>
    <!-- 日志最大的历史 30天 -->
    <property name="maxHistory" value="30"/>
    <!-- INFO级别日志 -->
    <appender name="INFO"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
        </filter>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logDir}\%d{yyyyMMdd}\info.log</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
        <append>false</append>
        <prudent>false</prudent>
    </appender>


    <root level="info">
        <appender-ref ref="LOG_LOCAL"/>
        <appender-ref ref="INFO"/>
    </root>

    <!-- sql日志打印 -->
    <logger name="mybatis" level="DEBUG"/>
    <!-- niceloo框架日志打印 -->
    <logger name="FRAMEWORK_API_MONITOR" level="WARN"/>
    <logger name="com.niceloo.framework" level="DEBUG"/>
    <logger name="com.niceloo.adapter" level="DEBUG"/>
    <logger name="com.niceloo.core" level="DEBUG"/>
    <!-- apollo日志打印 -->
    <logger name="com.ctrip.framework.foundation.internals" level="ERROR"/>
    <logger name="com.ctrip.framework.apollo" level="ERROR"/>
    <!-- kafka日志打印 -->
    <logger name="org.apache.kafka.clients.NetworkClient" level="ERROR"/>

    <!-- nacos心跳INFO屏蔽 -->
    <logger name="com.alibaba.nacos" level="OFF"/>
    <!-- swagger 加载日志 -->
    <logger name="springfox.documentation" level="ERROR"/>
    <!-- MQ CLIENT监控日志 -->
    <logger name="MQ_CLIENT_MONITOR" level="WARN"/>
    <!-- MQ CLIENT日志 -->
    <logger name="com.niceloo.mq.client" level="WARN"/>

    <!-- springboot默认配置的logger -->
    <logger name="org.apache.catalina.startup.DigesterFactory" level="ERROR"/>
    <logger name="org.apache.catalina.util.LifecycleBase" level="ERROR"/>
    <logger name="org.apache.coyote.http11.Http11NioProtocol" level="WARN"/>
    <logger name="org.apache.sshd.common.util.SecurityUtils" level="WARN"/>
    <logger name="org.apache.tomcat.util.net.NioSelectorPool" level="WARN"/>
    <logger name="org.eclipse.jetty.util.component.AbstractLifeCycle" level="ERROR"/>
    <logger name="org.hibernate.validator.internal.util.Version" level="WARN"/>
</configuration>