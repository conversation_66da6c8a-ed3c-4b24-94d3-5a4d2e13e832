app:
  # 控制台设置的AppId
  id: communicationcenter
apollo:
  # dev环境的metaServer服务器
  meta: http://192.168.10.118:8080
  bootstrap:
    # 设置在应用启动阶段就加载 Apollo 配置
    enabled: true
    # 命名空间，类似于分组的配置（比如mysql一个，redis一个，rabbitmq一个等）
    namespaces: application.yml,mysql.yml,redis.yml,mq.yml,feign.yml,es.yml,id_client.yml,business.yml,okhttp.yml
    eagerLoad:
      # 在日志系统启动前启动apollo，可以动态更新日志级别
      enabled: true