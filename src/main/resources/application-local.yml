spring:
  profiles:
    include:
      - localapplication
      - localmysql
      - localredis
      - localmq
      - locales
      - localfeign
      - localbusiness
      - localid_client
      - ok-http
      - auth

---
spring:
  profiles: localapplication
server:
  port: 8590
  servlet:
    context-path: /communicationcenter
logging:
    config: classpath:logback-spring.xml
    level:
      com.niceloo.cmc.ex: debug
# 定时任务使用启用
jobs:
  enable: false

---
spring:
  profiles: localmysql
  datasource:
    dynamic:
      hikari:
        connectionTimeout: 10000
      datasource:
        cmc:
          url: ***************************************************************************************************************************************************************************
          username: db_writer
          password: mleWf_Os5XX
          hikari:
            minimumIdle: 10
            maximumPoolSize: 10
        bd:
          url: **************************************************************************************************************************************************
          username: db_writer
          password: mleWf_Os5XX
          hikari:
            minimumIdle: 10
            maximumPoolSize: 10
mybatis-plus:
  # 扫描xml文件
  mapper-locations: classpath:/mapper/*.xml,classpath*:com/niceloo/framework/job/manager/mapper/**/*.xml
  # 打印SQL日志
  #configuration:
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

---
spring:
  profiles: localredis
  redis:
    database: 0
    host: **************
    port: 6379
    password: 123456
    # 连接超时时间
    timeout: 10000
    lettuce: #多线程安全的链接配置
      pool:
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 10
        # 连接池最大阻塞等待时间（使用负值表示没有限制）(毫秒)
        max-wait: 10000
  klock: # 使用上面配置的Redis
    #redis链接地址
    address: **************:6379
    #redis密码（如果没有密码，无需添加该选项，否则会报错）
    password: 123456
    #单机环境下采用的数据库
    database: 15
    #获取锁最长阻塞时间（默认：60，单位：秒）
    wait-time: 60
    #已获取锁后自动释放时间（默认：60，单位：秒）
    lease-time: 60
    #创建的redis的key的统一前缀。如果为空，默认为lock:
    prefix: NICELOO_CMC:LOCK

---
sys:
  clusterSeq: 01
spring:
  profiles: localmq
  mq:
    rabbit:
      client:
        # 默认客户端
        default:
          host: **************
          port: 5672
          group: CMC
          username: username
          password: password
          codecId: json
          supportDelayed: true
          ioThread:
            threadName: rabbitmq-default-io-thread
          producerThread:
            threadName: rabbitmq-default-producer-thread
          consumerThread:
            threadCount: 10
            threadName: rabbitmq-default-consumer-thread
          consumer:
            channel:
              basicQos: 6
        # 拉取通话记录特用MQ配置
        syncRecordRabbitMqClient:
          host: **************
          port: 5672
          group: CMC
          username: username
          password: password
          codecId: json
          supportDelayed: false
          ioThread:
            threadName: rabbitmq-sync-record-io-thread
          producerThread:
            threadName: rabbitmq-sync-record-producer-thread
          consumerThread:
            threadCount: 10
            threadName: rabbitmq-sync-record-consumer-thread
          consumer:
            channel:
              basicQos: 6
          # AI外呼任务追加客户特用MQ配置
        jobAddCustomerRabbitMqClient:
            host: **************
            port: 5672
            group: CMC
            username: username
            password: password
            codecId: json
            supportDelayed: true
            ioThread:
              threadName: rabbitmq-create-aiJob-io-thread
            producerThread:
              threadName: rabbitmq-create-aiJob-producer-thread
            consumerThread:
              threadCount: 10
              threadName: rabbitmq-create-aiJob-consumer-thread
            consumer:
              channel:
                basicQos: 10

---
spring:
  profiles: locales
  elasticsearch:
    rest:
      uris:
        - http://**************:9200
      username: elastic
      password: W86mXT4x1t2T
      #connectionTimeout: 15000
      cmc:
        connection-keep-alive: 3m

---
spring:
  profiles: localfeign
# feign请求配置
feign:
  encode:
    allowList:
      - uc-producer
  client:
    config:
      # 服务名
      cmc-producer:
        # 连接超时
        connectTimeout: 5000
        # 读取超时
        readTimeout: 5000

---
spring:
  profiles: localbusiness
uc:
  url: http://usercenter-dev.kube.com/usercenter
fs:
  url: http://fileservice-dev.kube.com/fileservice
  ossUrl: niceloo-test1.oss-cn-hangzhou.aliyuncs.com
ta:
  url: http://thirdpartyadapter-dev.kube.com/thirdpartyadapter/
cust:
  url: http://servicedev.zywinner.com/marketing
  size: 200
  selectCount: 1000
zhijian:
  recordUrl:
    expire: 1800000
feignTest:
  url: http://umsouat.niceloo.com/api/commu/callback/yxCallRecords?account=C112&sign=Y&accountName=zunyi
yunke:
  ossUrl: hqyl-yunke.oss-cn-hangzhou.aliyuncs.com
  url: http://ykadmin.niceloo.com/open/call/records
  manager:
    partnerId: pE1F5F622BEF74E98907BD24FDD2F4ADC
  company: 3lkbm4
  sign: jyks2f2qANSZzF7X
fy:
  url: http://apis.7moor.com/v20180426/cdr/getCCCdr
zhongke:
  host: http://*************/
  host.huibo: http://**************/
  host.sip: http://**************/
  records: /openapi/V2.0.6/getCdrList
  records.sleep: 30000
  records.busy.sleep: 600000
  record.download: /recordFile/download?file=
  record.download.ciphertext: /recordFile/download?key=
  oneRecords: /openapi/V2.0.6/getOneCdr
  balance: /openapi/V2.0.6/getBalance
tq:
  url: http://webservice.nl.tq.cn
  oldHost: mdb.tq.cn
  newHost: mdbnl.tq.cn
  verify:
    url: http://passport.nl.tq.cn:81/pulse
  token:
    time: 5400000
  sync:
    appkey: 123
youlu:
  manager:
    account: 9704669
call:
  yhhl:
    account: ***********
    password: yl123456
    type: web
    authUrl: http://**************/Api/Login/getAppToken?sign=
    recordUrl: http://**************/Api/Record/getRecordsData?sign=
    callIn: 5,6,23,24,33,34
    callOutNot: 3,4,55,56
jlfy:
  record_url: https://ad.oceanengine.com/open_api/2/tools/clue/contact_log/record_url/get/
  record_list: https://ad.oceanengine.com/open_api/2/tools/clue/contact_log/list/
jdyx:
  # url: https://yanxi-aicall-api.jd.com
  url: https://extpre-yanxi-api.jd.com
  token: 42025733eec0f6b7
  caller: <EMAIL>
  tenant_id: 10583
  bot_id: 103501
  user_pin: <EMAIL>
yanxi:
  tenants:
    - tenantId: 11208
      token: 6a48076a44a07755
      caller: youlujiaoyu
      userPin: youlutest176
      botId: 109062
    - tenantId: 11210
      token: 6a48076a44a07755
      caller: youlujiaoyu
      userPin: youlu158
      botId: 109068
    - tenantId: 5000102
      token: 6a48076a44a07755
      caller: youlujiaoyu
      userPin: youlufuwu
      botId: 5000105
zhzx:
  host: https://eeop-api.izxedu.com
  # 获取短信验证码
  sendSms: /extra/double_call/sendSms
  # 设置白名单
  setupWhiteList: /extra/double_call/setWhiteNumberByCode
  # 删除白名单
  deleteWhiteList: /extra/double_call/destroyWhiteNumber
  # 获取白名单
  getWhiteNumber: /v1/double_call/getWhiteNumber
  # 双呼请求
  doubleCall: /v1/double_call/bind
  # 获取账户余额
  getBalance: /v1/account/balance
  # 短信验证码vid有效期
  smsVidTimeout: 305
  # 需查询余额的外呼通道，多个用逗号分隔
  balanceChannelTypes: ZHZX,ZK
---
spring:
  profiles: localid_client
id-client:
  # 服务名称
  service-code: CMC
  # 业务标签
  tags:
    - COUNT
    - ACCOUNT
    - LOG
    - AiCallJob
    - CcSchoolBalance
  # ID发号服务端地址
  # k8s 内服务间调用务必使用地址 -> http://id-server/id-server
  id-sever-address: http://id-server-dev.kube.com/id-server

---
spring:
  profiles: ok-http
ok-http:
  # OKHttp客户端连接超时时间(秒)
  connectTimeout: 15
  # OKHttp客户端读取超时时间(秒)
  readTimeout: 15
  # OKHttp客户端写超时时间(秒)
  writeTimeout: 15
  # OKHttp客户端最大连接缓存池空闲数量
  maxIdleConnections: 20
---
spring:
  profiles: auth
auth:
  url: http://userauth-center-dev.kube.com/userauth-center

# 加密服务服务端配置项
encrypt:
  # k8s 内服务间调用务必使用地址 -> http://encrypt-service
  host: http://encrypt-service-dev.kube.com
#---------------------------------企业微信群告警设置---------------------------------
wechat:
  enabled: true
  bot_url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=868a3e62-4f26-4d9a-bb36-eec804ca0c37
  mentioned_mobile_list: 18903831292
  channeltypes: YP,ZK,ZHZX,TQ,FS
  warningThreshold:
    # 当月缺失通话记录占比百分比预警阈值
    record: 5
    # 当月缺失录音预警阈值
    recording: 100
    # 当天无状态通话记录占比阈值
    statelessRecordRatio: 90
record:
  # 标准有效通话时长(秒)
  effectiveTime: 30
  # 并行处理线程数
  threadCount: 10
  # 批次处理记录数
  batchSize: 1000
  # 滚动查询超时时间（单位：分钟）
  scrollTimeout: 60
#-------------------------------百应AI-------------------------------
byai:
  url: https://open.byai.com
  tiandun_list: /api/oauth/byai.openapi.tiandun.strategy.group/1.0.0/list
  oauth_token: /oauth/token
  client_id: lMj7QFQe8JxVHArf
  client_secret: MYTJX5dd93yq06cG8Ld8tcCGb8hEqe
  company_id: 96836