<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.BdCallaccountinfoMapper">

    <select id="selectSchoolIdByAccountType" resultType="String" parameterType="string">
        SELECT DISTINCT schoolId
          FROM BdCallaccountinfo
         WHERE accountDelstatus = 'N'
           AND schoolId is not null
           and schoolId != ''
           AND accountType = #{accountType}
    </select>
    <select id="selectAccount" resultType="com.niceloo.cmc.ex.pojo.dto.AccountDTO">
        SELECT apiAccount, apiSecret, accountName, schoolId
        FROM BdCallaccountinfo
        WHERE apiAccount = #{account}
          AND accountType = #{type}
        GROUP BY apiAccount
    </select>

    <select id="selectUniqueAccountsByType" resultType="com.niceloo.cmc.ex.pojo.dto.AccountDTO">
        SELECT apiAccount, apiSecret, accountName, schoolId
        FROM BdCallaccountinfo
        WHERE accountDelstatus = 'N'
          AND accountType = #{channelType}
        GROUP BY apiAccount
    </select>
    <select id="selectAccountNameByBdEeId" resultType="java.lang.String">
        SELECT DISTINCT a.accountName
        FROM BdCallaccountinfo a
        WHERE a.accountType = #{type}
          AND LOCATE(a.dptLevelcode, (SELECT d.dptLevelcode
                                      FROM BdDpt d
                                      WHERE d.dptAvlstatus = 'Y'
                                        AND d.dptDelstatus =
                                            'N'
                                        AND d.dptId IN (SELECT de.dptId
                                                        FROM BdDptee de
                                                        WHERE de.eeId = #{eeId} AND de.dpteeRelation = 'P'))) = 1
        ORDER BY LENGTH(a.dptLevelcode) DESC
        LIMIT 1
    </select>
</mapper>
