<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.CcCountdayMapper">

    <delete id="deleteByDateAndChannelType">
        DELETE
        FROM
        CcCountday
        WHERE
        countcreatedTime = #{date}
        AND countchannelType = #{channelType}
    </delete>

    <select id="selectListByDate" resultType="com.niceloo.cmc.ex.entity.CcCountday">
        SELECT * FROM CcCountday WHERE countchannelType = #{channelType}
        AND countcreatedTime &gt;= #{start} AND countcreatedTime &lt;= #{end}
    </select>

    <insert id="batchSave">
        INSERT INTO CcCountday VALUES
        <foreach collection="commuCounts" item="item" separator=",">
            (
            #{item.countId},
            #{item.countcallerName},
            #{item.countcallerUserId},
            #{item.countdptId},
            #{item.countschoolId},
            #{item.countcallAccount},
            #{item.countcallinNum},
            #{item.countcallinSuccessNum},
            #{item.countinConnectNum},
            #{item.countcalloutNum},
            #{item.countcalloutSuccessNum},
            #{item.countoutConnectNum},
            #{item.countvalidCallNum},
            #{item.countdurationTotal},
            #{item.countdurationAvg},
            #{item.countinDurationTotal},
            #{item.countoutDurationTotal},
            #{item.countcalloutSuccess},
            #{item.countconnectSuccess},
            #{item.countvalidCall},
            #{item.countinConnectSuccess},
            #{item.countoutConnectSuccess},
            #{item.countchannelType},
            #{item.countcreatedTime},
            #{item.countmodifiedTime},
            '1'
            )
        </foreach>
    </insert>

    <sql id="trafficStatisticId">
        <if test="null != callerUserId and '' != callerUserId">
            AND countcallerUserId = #{callerUserId}
        </if>
        <if test="null != deptList and deptList.size() >0">
            AND countdptId in
            <foreach item="item" collection="deptList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != dates and dates.size() >0">
            AND countcreatedTime in
            <foreach item="item" collection="dates" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != channelType and '' != channelType">
            AND countchannelType = #{channelType}
        </if>
    </sql>

    <sql id="trafficStatisticId2">
        <if test="null != callerUserId and '' != callerUserId">
            AND countcallerUserId = #{callerUserId}
        </if>
        <if test="null != deptList and deptList.size() >0">
            AND countdptId in
            <foreach item="item" collection="deptList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != minDate and '' != minDate">
            AND countcreatedTime &gt;= #{minDate}
        </if>
        <if test="null != maxDate and '' != maxDate">
            AND countcreatedTime &lt;= #{maxDate}
        </if>
        <if test="null != channelType and '' != channelType">
            AND countchannelType = #{channelType}
        </if>
    </sql>

    <sql id="callStatsId">
        <if test="null != callerUserId and '' != callerUserId">
            AND countcallerUserId = #{callerUserId}
        </if>
        <if test="null != dates and dates.size() >0">
            AND countcreatedTime in
            <foreach item="item" collection="dates" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getTrafficStatisticGroupDateSql"
            resultType="com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData">
        /*FORCE_IMCI_NODES*/ SELECT /*+ SET_VAR(cost_threshold_for_imci=1500) */
        countcreatedTime AS date,
        SUM(countcalloutNum) AS calloutNum,
        SUM(countoutConnectNum) AS outConnectNum,
        SUM(countcallinNum) AS callinNum,
        SUM(countinConnectNum) AS inConnectNum,
        SUM(countvalidCallNum) AS validCallNum,
        SUM(countoutDurationTotal) AS outDurationTotal,
        SUM(countinDurationTotal) AS inDurationTotal,
        SUM(countdurationTotal) AS durationTotal,
        SUM(countcalloutSuccessNum) AS outSuccessNum
        FROM CcCountday WHERE 1=1
        <include refid="trafficStatisticId"/>
        GROUP BY countcreatedTime;
    </select>

    <select id="getTrafficStatisticGroupDateSql2"
            resultType="com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData">
        /*FORCE_IMCI_NODES*/ SELECT /*+ SET_VAR(cost_threshold_for_imci=1500) */
        countcreatedTime AS date,
        SUM(countcalloutNum) AS calloutNum,
        SUM(countoutConnectNum) AS outConnectNum,
        SUM(countcallinNum) AS callinNum,
        SUM(countinConnectNum) AS inConnectNum,
        SUM(countvalidCallNum) AS validCallNum,
        SUM(countoutDurationTotal) AS outDurationTotal,
        SUM(countinDurationTotal) AS inDurationTotal,
        SUM(countdurationTotal) AS durationTotal,
        SUM(countcalloutSuccessNum) AS outSuccessNum
        FROM CcCountday WHERE 1=1
        <include refid="trafficStatisticId2"/>
        GROUP BY countcreatedTime;
    </select>
    
    
    <select id="getTrafficStatisticGroupEeSql" resultType="com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO">
        /*FORCE_IMCI_NODES*/ SELECT /*+ SET_VAR(cost_threshold_for_imci=1500) */
        countcallerName AS callerName,
        countcallerUserId AS callerUserId,
        countdptId AS dptId,
        countschoolId AS schoolId,
        countcallAccount AS callAccount,
        countchannelType AS channelType,
        SUM(countcalloutNum) AS calloutNum,
        SUM(countoutConnectNum) AS outConnectNum,
        SUM(countcallinNum) AS callinNum,
        SUM(countinConnectNum) AS inConnectNum,
        SUM(countvalidCallNum) AS validCallNum,
        SUM(countoutDurationTotal) AS outDurationTotal,
        SUM(countinDurationTotal) AS inDurationTotal,
        SUM(countdurationTotal) AS durationTotal,
        SUM(countcalloutSuccessNum) AS outSuccessNum
        FROM CcCountday WHERE 1=1
        <include refid="trafficStatisticId"/>
        GROUP BY countcallerUserId,countcallAccount ORDER BY countcallerUserId DESC;
    </select>


    <select id="getTrafficStatisticGroupEeSql2" resultType="com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO">
        /*FORCE_IMCI_NODES*/ SELECT /*+ SET_VAR(cost_threshold_for_imci=1500) */
        countcallerName AS callerName,
        countcallerUserId AS callerUserId,
        countdptId AS dptId,
        countschoolId AS schoolId,
        countcallAccount AS callAccount,
        countchannelType AS channelType,
        SUM(countcalloutNum) AS calloutNum,
        SUM(countoutConnectNum) AS outConnectNum,
        SUM(countcallinNum) AS callinNum,
        SUM(countinConnectNum) AS inConnectNum,
        SUM(countvalidCallNum) AS validCallNum,
        SUM(countoutDurationTotal) AS outDurationTotal,
        SUM(countinDurationTotal) AS inDurationTotal,
        SUM(countdurationTotal) AS durationTotal,
        SUM(countcalloutSuccessNum) AS outSuccessNum
        FROM CcCountday WHERE 1=1
        <include refid="trafficStatisticId2"/>
        GROUP BY countcallerUserId,countcallAccount ORDER BY countcallerUserId DESC;
    </select>

    <select id="getCallStatsByUserId" resultType="com.niceloo.cmc.ex.pojo.vo.CallStatsVO">
        SELECT
        countcallerUserId AS callerUserId,
        SUM(countvalidCallNum) AS validCallNum,
        SUM(countdurationTotal) AS durationTotal
        FROM CcCountday WHERE 1=1
        <include refid="callStatsId"/>
        GROUP BY countcallerUserId;
    </select>
</mapper>
