<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.BdEeMapper">

    <select id="findBdEeDTOByUserId" resultType="com.niceloo.cmc.ex.pojo.dto.BdEeDTO">
        SELECT
        ee.eeId,
        ee.userId,
        ee.userName,
        ee.schoolId,
        ee.eeNo,
        ee.eeInnerphone,
        ee.eeWorkstatus,
        ee.eeHiredate,
        ee.eeTermdate,
        ee.eeModifieddate,
        info.zkyAccount,
        info.infoModifieddate
        FROM BdEe ee
        LEFT JOIN BdPersonalinfo info ON ee.eeId = info.eeId
        WHERE ee.userId = #{userId}
    </select>
    <select id="selectBdEeInfoUseSQLOrder" resultType="com.niceloo.cmc.ex.pojo.dto.BdEeDTO">
        ${sql}
    </select>
    
    <select id="findSchoolNameByIds" resultType="com.niceloo.cmc.ex.pojo.dto.EeSchoolInfoDTO">
        SELECT schoolId, schoolName FROM BdSchool WHERE schoolId in
        <foreach item="item" collection="schoolIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findBdEeDTOAndSchoolNameByUserId" resultType="com.niceloo.cmc.ex.pojo.dto.BdEeDTO">
        select be.eeId, be.userId, be.schoolId, be.userName, be.eeInnerphone, bs.schoolName
        from BdEe be
        left join BdSchool bs on be.schoolId = bs.schoolId
        where be.userId = #{userId}
    </select>
    
    <select id="findBdEeDptListByUserIds" resultType="com.niceloo.cmc.ex.pojo.dto.BdEeDptDTO">
        select
        be.eeId,be.userId,be.schoolId,be.userName,bs.schoolName,bd.dptId,bd.dptName
        from BdEe be
        left join BdSchool bs on be.schoolId = bs.schoolId
        left join BdDptee bde on be.eeId = bde.eeId and bde.dpteeRelation = 'P'
        left join BdDpt bd on bd.dptId = bde.dptId
        where
        be.userId in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

</mapper>
