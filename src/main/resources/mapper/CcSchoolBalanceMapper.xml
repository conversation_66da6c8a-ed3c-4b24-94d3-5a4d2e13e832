<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.CcSchoolBalanceMapper">
    <insert id="batchSave">
        INSERT INTO CcSchoolBalance(balanceId, schoolId, channelType, accountBalance, countDay, creator, createdDate,
        modifier, modifiedDate, monthlySpending, dailySpending, remark) VALUES
        <foreach collection="ccSchoolBalanceList" item="item" separator=",">
            (
            #{item.balanceId},
            #{item.schoolId},
            #{item.channelType},
            #{item.accountBalance},
            #{item.countDay},
            #{item.creator},
            #{item.createdDate},
            #{item.modifier},
            #{item.modifiedDate},
            #{item.monthlySpending},
            #{item.dailySpending},
            #{item.remark}
            )
        </foreach>
    </insert>
</mapper>
