<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.CcCallAccountConfigMapper">

    <insert id="insertAccountConfig">
        insert into CcCallAccountConfig
        value (#{account.callAccountId},
        #{account.channelType},
        #{account.schoolId},
        #{account.dptId},
        #{account.schoolName},
        #{account.eeUserId},
        #{account.eeNo},
        #{account.eeUserName},
        #{account.account},
        #{account.password},
        #{account.bindStatus},
        #{account.disableStatus},
        #{account.vendorAccountStatus},
        #{account.vendorAccountInfo},
        #{account.callDelstatus},
        #{account.accountCreator},
        #{account.accountCreatorName},
        #{account.accountCreateddate},
        #{account.accountModifier},
        #{account.accountModifierName},
        #{account.accountModifieddate},
        #{account.tenantId});
    </insert>
    <insert id="insertOperationLog">
        insert into CcCallAccountModifyLog
        values (#{operation.callAccountLogId},
                #{operation.callAccountId},
                #{operation.operationType},
                #{operation.operationIp},
                #{operation.beforeEeUserId},
                #{operation.afterEeUserId},
                #{operation.beforeEeUserName},
                #{operation.afterEeUserName},
                #{operation.beforeEeSchoolId},
                #{operation.afterEeSchoolId},
                #{operation.beforeEeSchoolName},
                #{operation.afterEeSchoolName},
                #{operation.accountLogCreator},
                #{operation.accountLogCreatorName},
                #{operation.accountLogCreateddate});
    </insert>

    <insert id="batchInsertOperationLog">
        INSERT INTO CcCallAccountModifyLog VALUES
        <foreach collection="operationList" item="operation" separator=",">
            (#{operation.callAccountLogId},
            #{operation.callAccountId},
            #{operation.operationType},
            #{operation.operationIp},
            #{operation.beforeEeUserId},
            #{operation.afterEeUserId},
            #{operation.beforeEeUserName},
            #{operation.afterEeUserName},
            #{operation.beforeEeSchoolId},
            #{operation.afterEeSchoolId},
            #{operation.beforeEeSchoolName},
            #{operation.afterEeSchoolName},
            #{operation.accountLogCreator},
            #{operation.accountLogCreatorName},
            #{operation.accountLogCreateddate})
        </foreach>
    </insert>

    <update id="accountOperation">
        update CcCallAccountConfig
        set bindStatus = #{config.bindStatus},
        disableStatus = #{config.disableStatus},
        eeNo = #{config.eeNo},
        eeUserName = #{config.eeUserName},
        eeUserId = #{config.eeUserId},
        dptId = #{config.dptId},
        schoolId = #{config.schoolId},
        schoolName = #{config.schoolName},
        accountModifier = #{config.accountModifier},
        accountModifierName = #{config.accountModifierName},
        accountModifieddate = #{config.accountModifieddate},
        tenantId = #{config.tenantId}
        where callAccountId = #{config.callAccountId}
    </update>

    <update id="batchUnbind">
        update CcCallAccountConfig
        set bindStatus = 'N',
        accountModifier = #{config.accountModifier},
        accountModifierName = #{config.accountModifierName},
        accountModifieddate = #{config.accountModifieddate}
        where callAccountId in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="searchCallAccountInfoById" resultType="com.niceloo.cmc.ex.entity.CcCallAccountConfig">
        select callAccountId,
        channelType,
        schoolId,
        dptId,
        schoolName,
        eeUserId,
        eeNo,
        eeUserName,
        account,
        password,
        bindStatus,
        disableStatus,
        vendorAccountStatus,
        vendorAccountInfo,
        callDelstatus,
        accountCreator,
        accountCreatorName,
        accountCreateddate,
        tenantId
        from CcCallAccountConfig
        where callAccountId = #{id}
        and callDelstatus = 'N'
    </select>
    <select id="searchCallAccountInfoByAccount" resultType="com.niceloo.cmc.ex.entity.CcCallAccountConfig">
        select callAccountId,
               channelType,
               schoolId,
               dptId,
               schoolName,
               eeUserId,
               eeNo,
               eeUserName,
               account,
               password,
               bindStatus,
               vendorAccountStatus,
               vendorAccountInfo,
               callDelstatus,
               accountCreator,
               accountCreatorName,
               accountCreateddate,
               tenantId
        from CcCallAccountConfig
        where channelType = #{type}
          and account = #{account}
          and callDelstatus = 'N'
        limit 1
    </select>


    <select id="searchPage" resultType="com.niceloo.cmc.ex.entity.CcCallAccountConfig">
        select callAccountId,
        channelType,
        schoolId,
        schoolName,
        eeUserId,
        eeNo,
        eeUserName,
        account,
        password,
        bindStatus,
        disableStatus,
        vendorAccountStatus,
        vendorAccountInfo,
        callDelstatus,
        accountCreator,
        accountCreatorName,
        accountCreateddate,
        tenantId
        from CcCallAccountConfig
        <include refid="selectCondition"/>
        order by accountCreateddate desc limit #{request.pageIndex},#{request.pageSize}
    </select>


    <select id="selectTotalCount" resultType="java.lang.Integer">
        select count(*) from CcCallAccountConfig
        <include refid="selectCondition"/>
    </select>

    <select id="searchAccountOperationLogList" resultType="com.niceloo.cmc.ex.entity.CcCallAccountModifyLog">
        select callAccountLogId,
               callAccountId,
               operationType,
               operationIp,
               beforeEeUserId,
               afterEeUserId,
               beforeEeUserName,
               afterEeUserName,
               beforeEeSchoolId,
               afterEeSchoolId,
               beforeEeSchoolName,
               afterEeSchoolName,
               accountLogCreator,
               accountLogCreatorName,
               accountLogCreateddate
        from CcCallAccountModifyLog
        where callAccountId = #{callAccountId}
        order by accountLogCreateddate desc
    </select>

    <select id="hasCallAccount" resultType="java.lang.Integer">
        select count(*)
        from CcCallAccountConfig
        where account = #{account}
          and channelType = #{type}
          and callDelstatus = 'N'
    </select>


    <select id="hasCallAccountByUserId" resultType="java.lang.Integer">
        select count(*)
        from CcCallAccountConfig
        where eeUserId = #{userId}
        and channelType = #{type}
        and callDelstatus = 'N'
        and bindStatus = 'Y' and disableStatus = 'N'
    </select>

    <select id="batchQueryEeCallAccount" resultType="com.niceloo.cmc.ex.entity.CcCallAccountConfig">
        select eeUserId, channelType, account
        from CcCallAccountConfig
        where eeUserId in
        <foreach collection="eeUserIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and bindStatus = 'Y' and callDelstatus = 'N' and disableStatus = 'N'
    </select>
    <select id="searchCallAccountInfoByUserId" resultType="com.niceloo.cmc.ex.entity.CcCallAccountConfig">
        select callAccountId,
        channelType,
        schoolId,
        dptId,
        schoolName,
        eeUserId,
        eeNo,
        eeUserName,
        account,
        password,
        bindStatus,
        vendorAccountStatus,
        vendorAccountInfo,
        disableStatus,
        callDelstatus,
        accountCreator,
        accountCreatorName,
        accountCreateddate,
        tenantId
        from CcCallAccountConfig
        where channelType = #{channelType}
        and eeUserId = #{userId}
        and callDelstatus = 'N'
        and bindStatus = 'Y'
        and disableStatus = 'N'
        limit 1
    </select>

    <select id="searchBandingAccountListByIds" resultType="com.niceloo.cmc.ex.entity.CcCallAccountConfig">
        select callAccountId,
        channelType,
        schoolId,
        dptId,
        schoolName,
        eeUserId,
        eeNo,
        eeUserName,
        account,
        password,
        bindStatus
        from CcCallAccountConfig
        where bindStatus = 'Y' and callDelstatus = 'N' and
        callAccountId in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getOrgStructureOfCreator" resultType="com.niceloo.cmc.ex.pojo.request.AuthBaseRequest">
        select
        schoolId,
        dptId,
        eeUserId as userId
        from CcCallAccountConfig
        where callAccountId = #{id}
    </select>


    <sql id="selectCondition">
        <where>
            callDelstatus = 'N'
            <if test="request.channelType != null and request.channelType != ''">
                and channelType = #{request.channelType}
            </if>
            <if test="request.eeUserId != null and request.eeUserId != ''">
                and eeUserId = #{request.eeUserId} and bindStatus = 'Y'
            </if>
            <if test="request.account != null and request.account != ''">
                and account = #{request.account}
            </if>
            <if test="request.eeNo != null and request.eeNo != ''">
                and eeNo = #{request.eeNo} and bindStatus = 'Y'
            </if>
            <if test="request.bindStatus != null and request.bindStatus != ''">
                and bindStatus = #{request.bindStatus}
            </if>
            <if test="request.disableStatus != null and request.disableStatus != ''">
                and disableStatus = #{request.disableStatus}
            </if>
            <if test="request.schoolId != null and request.schoolId != ''">
                and schoolId = #{request.schoolId}
            </if>
            <if test="request.dptId != null and request.dptId != ''">
                and dptId in (#{request.dptId})
            </if>
            <if test="request.startTime != null and request.startTime != '' and request.endTime != null and request.endTime != ''">
                and accountCreateddate &gt;= #{request.startTime} and accountCreateddate &lt;= #{request.endTime}
            </if>
        </where>
    </sql>
</mapper>
