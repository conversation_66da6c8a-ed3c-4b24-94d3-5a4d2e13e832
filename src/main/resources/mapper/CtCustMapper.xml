<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.CtCustMapper">

    <select id="selectByPhone" resultType="com.niceloo.cmc.ex.entity.CtCust">
        SELECT custId,
               userId,
               custName,
               custMobile,
               custMobileareacode,
               custCreateddate
        FROM CtCust
        WHERE custMobile = #{receiverPhone}
        ORDER BY custCreateddate DESC
        LIMIT 1
    </select>
    <select id="selectByTel" resultType="com.niceloo.cmc.ex.entity.CtCust">
        SELECT custId,
               userId,
               custName,
               custTel,
               custMobile,
               custMobileareacode,
               custCreateddate
        FROM CtCust
        WHERE custTel = #{tel}
        ORDER BY custCreateddate DESC
        LIMIT 1
    </select>

    <select id="getListByMobileList" resultType="com.niceloo.cmc.ex.entity.CtCust">
        select custId, custName, userId, custMobile, custMobileareacode, custAreacode, custTel, custCreateddate,
        custModifieddate
        from CtCust
        where custMobile in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getMobileListByPage" resultType="string">
        SELECT DISTINCT custMobile
        FROM CtCust
        where custId > (select custId from CtCust limit #{pageNum}, 1)
        LIMIT #{pageSize}
    </select>

    <select id="selectMobileListUsePrimaryRange" resultType="com.niceloo.cmc.ex.entity.CtCust">
        SELECT custId, custMobile
        FROM CtCust
        <where>
            <if test="custId != null and custId != ''">
                custId &gt; #{custId}
            </if>
        </where>
        ORDER BY custId
        LIMIT #{size}
    </select>

    <insert id="replaceInsertBatch">
        replace into CtCust (custId, custName, userId, custMobile, custMobileareacode,
        custAreacode,custTel, custCreateddate, custModifieddate)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.custId},#{item.custName},#{item.userId},#{item.custMobile},#{item.custMobileareacode},
            #{item.custAreacode},#{item.custTel},#{item.custCreateddate},#{item.custModifieddate})
        </foreach>
    </insert>


    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update CtCust set
            <if test="item.custName != null and item.custName != ''">
                custName = #{item.custName},
            </if>
            <if test="item.custAreacode != null and item.custAreacode != ''">
                custAreacode = #{item.custAreacode},
            </if>
            <if test="item.custTel != null and item.custTel != ''">
                custTel = #{item.custTel},
            </if>
            <if test="item.custModifieddate != null and item.custModifieddate != ''">
                custModifieddate = #{item.custModifieddate}
            </if>
            where custId = #{item.custId}
        </foreach>
    </update>

</mapper>
