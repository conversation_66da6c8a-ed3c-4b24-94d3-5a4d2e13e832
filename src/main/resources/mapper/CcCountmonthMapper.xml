<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.CcCountmonthMapper">
    <insert id="batchSave">
        INSERT INTO CcCountmonth VALUES
        <foreach collection="ccCountMonths" item="item" separator=",">
            (
            #{item.countId},
            #{item.countcallerName},
            #{item.countcallerUserId},
            #{item.countdptId},
            #{item.countschoolId},
            #{item.countcallAccount},
            #{item.countcallinNum},
            #{item.countcallinSuccessNum},
            #{item.countinConnectNum},
            #{item.countcalloutNum},
            #{item.countcalloutSuccessNum},
            #{item.countoutConnectNum},
            #{item.countvalidCallNum},
            #{item.countdurationTotal},
            #{item.countdurationAvg},
            #{item.countinDurationTotal},
            #{item.countoutDurationTotal},
            #{item.countdurationDayAvg},
            #{item.countcalloutSuccess},
            #{item.countconnectSuccess},
            #{item.countvalidCall},
            #{item.countinConnectSuccess},
            #{item.countoutConnectSuccess},
            #{item.countchannelType},
            #{item.countcreatedTime},
            #{item.countmodifiedTime},
            '1'
            )
        </foreach>
    </insert>

    <delete id="deleteByDate">
        delete from CcCountmonth where countcreatedTime = #{date} AND countchannelType = #{channelType}
    </delete>
</mapper>
