<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.BdDptMapper">

    <select id="findDptIdAndNameByEeId" resultType="com.niceloo.cmc.ex.entity.BdDpt">
        SELECT dpt.dptId, dpt.dptName
        FROM BdDptee de
                 INNER JOIN BdDpt dpt ON de.dptId = dpt.dptId
        WHERE de.eeId = #{eeId}
          AND de.dpteeRelation = 'P'
          AND dpt.dptAvlstatus = 'Y'
          AND dpt.dptDelstatus = 'N'
    </select>
</mapper>
