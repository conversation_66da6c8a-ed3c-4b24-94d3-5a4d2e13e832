<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.niceloo.cmc.ex.mapper.CcAiCallJobMapper">


    <select id="selectParentJobPage" resultType="com.niceloo.cmc.ex.entity.CcAiCallJob">
        select jobId,
        parentJobId,
        jobLevel,
        vendorJobId,
        jobName,
        aiJobType,
        contextId,
        contextName,
        jobProgress,
        jobSchoolName,
        callProject,
        exploitProject,
        outboundProcess,
        totalTaskNum,
        connectedTaskNum,
        status,
        creator,
        creatorName,
        createDate,
        modifyName,
        modifyDate,
        errorLog
        from CcAiCallJob
        where jobId in (select acj.parentJobId
        from CcAiCallJob as acj
        where acj.jobLevel = 1
        <include refid="selectCondition"/>
        group by acj.parentJobId)
        order by createDate desc
        limit #{request.pageIndex},#{request.pageSize};
    </select>

    <select id="selectChildrenJobPageParentIds" resultType="com.niceloo.cmc.ex.entity.CcAiCallJob">
        select jobId,
        parentJobId,
        jobLevel,
        vendorJobId,
        jobName,
        aiJobType,
        contextId,
        contextName,
        jobProgress,
        jobSchoolName,
        callProject,
        exploitProject,
        outboundProcess,
        totalTaskNum,
        connectedTaskNum,
        status,
        creator,
        creatorName,
        createDate,
        modifyName,
        modifyDate,
        errorLog
        from CcAiCallJob
        where jobLevel = 1 and status != 'canceled' and parentJobId in
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <select id="selectParentJobCount" resultType="java.lang.Integer">
        select count(*)
        from CcAiCallJob
        where jobId in (select acj.parentJobId
        from CcAiCallJob as acj
        where acj.jobLevel = 1
        <include refid="selectCondition"/>
        group by acj.parentJobId)
    </select>


    <sql id="selectCondition">
        <if test="request.contextId != null and request.contextId != ''">
            and acj.contextId = #{request.contextId}
        </if>
        <choose>
            <when test="request.status != null and request.status != ''">
                and acj.status = #{request.status}
            </when>
            <otherwise>
                and acj.status != 'canceled'
            </otherwise>
        </choose>
        <if test="request.jobSchoolId != null and request.jobSchoolId != ''">
            and acj.jobSchoolId = #{request.jobSchoolId}
        </if>
        <if test="request.eeUserId != null and request.eeUserId != ''">
            and acj.creator = #{request.eeUserId}
        </if>
        <if test="request.dptId != null and request.dptId != ''">
            and acj.dptId = #{request.dptId}
        </if>
        <if test="request.schoolId != null and request.schoolId != ''">
            and acj.schoolId = #{request.schoolId}
        </if>
        <if test="request.name != null and request.name != ''">
            and acj.jobName like #{request.name}
        </if>
        <if test="request.startTime != null and request.startTime != '' and request.endTime != null and request.endTime != ''">
            and acj.createDate &gt;= #{request.startTime} and acj.createDate &lt;= #{request.endTime}
        </if>
        <if test="request.aiJobType != null and request.aiJobType != ''">
            and acj.aiJobType = #{request.aiJobType}
        </if>
    </sql>
</mapper>
