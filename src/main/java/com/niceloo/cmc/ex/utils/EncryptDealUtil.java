package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.entity.CcCallAccountModifyLog;
import com.niceloo.cmc.ex.entity.CcCountday;
import com.niceloo.cmc.ex.entity.CcCountmonth;
import com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO;
import com.niceloo.framework.utils.StringUtils;

/**
 * 字段加解密类
 * <AUTHOR>
 * @date 2023/11/1 16:55 2023-11-01
 */
public class EncryptDealUtil {


    /**
     * 对CcCallAccountConfig对象的指定字段进行加密
     * @param ccCallAccountConfig CcCallAccountConfig对象
     */
    public static void encryptFields(CcCallAccountConfig ccCallAccountConfig) {
        if (!FieldCipherUtil.checkEncrypt(ccCallAccountConfig.getEeUserName())) {
            ccCallAccountConfig.setEeUserName(FieldCipherUtil.encrypt(ccCallAccountConfig.getEeUserName()));
        }
        if (!FieldCipherUtil.checkEncrypt(ccCallAccountConfig.getAccount())) {
            ccCallAccountConfig.setAccount(FieldCipherUtil.encrypt(ccCallAccountConfig.getAccount()));
        }
        if (!FieldCipherUtil.checkEncrypt(ccCallAccountConfig.getAccountCreatorName())) {
            ccCallAccountConfig.setAccountCreatorName(FieldCipherUtil.encrypt(ccCallAccountConfig.getAccountCreatorName()));
        }
        if (!FieldCipherUtil.checkEncrypt(ccCallAccountConfig.getAccountModifierName())) {
            ccCallAccountConfig.setAccountModifierName(FieldCipherUtil.encrypt(ccCallAccountConfig.getAccountModifierName()));
        }
    }

    /**
     * 对CcCallAccountModifyLog对象的指定字段进行加密
     * @param ccCallAccountModifyLog 待加密对象
     */
    public static void encryptFields(CcCallAccountModifyLog ccCallAccountModifyLog) {
        if (!FieldCipherUtil.checkEncrypt(ccCallAccountModifyLog.getBeforeEeUserName())) {
            ccCallAccountModifyLog.setBeforeEeUserName(FieldCipherUtil.encrypt(ccCallAccountModifyLog.getBeforeEeUserName()));
        }
        if (!FieldCipherUtil.checkEncrypt(ccCallAccountModifyLog.getAfterEeUserName())) {
            ccCallAccountModifyLog.setAfterEeUserName(FieldCipherUtil.encrypt(ccCallAccountModifyLog.getAfterEeUserName()));
        }
        if (!FieldCipherUtil.checkEncrypt(ccCallAccountModifyLog.getAccountLogCreatorName())) {
            ccCallAccountModifyLog.setAccountLogCreatorName(FieldCipherUtil.encrypt(ccCallAccountModifyLog.getAccountLogCreatorName()));
        }
    }

    /**
     * 对CcCountday对象的指定字段进行加密
     * @param ccCountday 待加密对象
     */
    public static void encryptFields(CcCountday ccCountday) {
        if (!FieldCipherUtil.checkEncrypt(ccCountday.getCountcallerName())) {
            ccCountday.setCountcallerName(FieldCipherUtil.encrypt(ccCountday.getCountcallerName()));
        }
    }

    /**
     * 对CcCountmonth对象的指定字段进行加密
     * @param ccCountmonth 待加密对象
     */
    public static void encryptFields(CcCountmonth ccCountmonth) {
        if (!FieldCipherUtil.checkEncrypt(ccCountmonth.getCountcallerName())) {
            ccCountmonth.setCountcallerName(FieldCipherUtil.encrypt(ccCountmonth.getCountcallerName()));
        }
    }

    /**
     * 对CcCallAccountConfig对象的指定字段进行解密
     * @param ccCallAccountConfig 待处理对象
     */
    public static void decryptFields(CcCallAccountConfig ccCallAccountConfig) {
        if (FieldCipherUtil.checkEncrypt(ccCallAccountConfig.getEeUserName())) {
            ccCallAccountConfig.setEeUserName(FieldCipherUtil.decrypt(ccCallAccountConfig.getEeUserName()));
        }
        if (FieldCipherUtil.checkEncrypt(ccCallAccountConfig.getAccount())) {
            ccCallAccountConfig.setAccount(FieldCipherUtil.decrypt(ccCallAccountConfig.getAccount()));
        }
        if (FieldCipherUtil.checkEncrypt(ccCallAccountConfig.getAccountCreatorName())) {
            ccCallAccountConfig.setAccountCreatorName(FieldCipherUtil.decrypt(ccCallAccountConfig.getAccountCreatorName()));
        }
        if (FieldCipherUtil.checkEncrypt(ccCallAccountConfig.getAccountModifierName())) {
            ccCallAccountConfig.setAccountModifierName(FieldCipherUtil.decrypt(ccCallAccountConfig.getAccountModifierName()));
        }
    }

    /**
     * 对CcCallAccountModifyLog对象的指定字段进行解密
     * @param ccCallAccountModifyLog 待处理对象
     */
    public static void decryptFields(CcCallAccountModifyLog ccCallAccountModifyLog) {
        if (FieldCipherUtil.checkEncrypt(ccCallAccountModifyLog.getBeforeEeUserName())) {
            ccCallAccountModifyLog.setBeforeEeUserName(FieldCipherUtil.decrypt(ccCallAccountModifyLog.getBeforeEeUserName()));
        }
        if (FieldCipherUtil.checkEncrypt(ccCallAccountModifyLog.getAfterEeUserName())) {
            ccCallAccountModifyLog.setAfterEeUserName(FieldCipherUtil.decrypt(ccCallAccountModifyLog.getAfterEeUserName()));
        }
        if (FieldCipherUtil.checkEncrypt(ccCallAccountModifyLog.getAccountLogCreatorName())) {
            ccCallAccountModifyLog.setAccountLogCreatorName(FieldCipherUtil.decrypt(ccCallAccountModifyLog.getAccountLogCreatorName()));
        }
    }

    /**
     * 对CcCountday对象的指定字段进行解密
     * @param ccCountday 待处理对象
     */
    public static void decryptFields(CcCountday ccCountday) {
        if (FieldCipherUtil.checkEncrypt(ccCountday.getCountcallerName())) {
            ccCountday.setCountcallerName(FieldCipherUtil.decrypt(ccCountday.getCountcallerName()));
        }
    }

    /**
     * 对ccCountmonth对象的指定字段进行解密
     * @param ccCountmonth 待处理对象
     */
    public static void decryptFields(CcCountmonth ccCountmonth) {
        if (FieldCipherUtil.checkEncrypt(ccCountmonth.getCountcallerName())) {
            ccCountmonth.setCountcallerName(FieldCipherUtil.decrypt(ccCountmonth.getCountcallerName()));
        }
    }

    /**
     * 对recordEeStatisticsVO对象的指定字段进行解密
     * @param recordEeStatisticsVO 待处理对象
     */
    public static void decryptFields(RecordEeStatisticsVO recordEeStatisticsVO) {
        if (FieldCipherUtil.checkEncrypt(recordEeStatisticsVO.getCallerName())) {
            recordEeStatisticsVO.setCallerName(FieldCipherUtil.decrypt(recordEeStatisticsVO.getCallerName()));
        }
    }


}
