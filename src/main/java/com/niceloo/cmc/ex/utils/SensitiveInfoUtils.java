package com.niceloo.cmc.ex.utils;

/**
 * 敏感信息工具类，用于对手机号和身份证号进行脱敏处理。
 * <AUTHOR>
 * @since 2023/8/21 17:42
 */
public class SensitiveInfoUtils {

    /**
     * 对手机号进行脱敏处理，保留前三位、后四位，中间位数用星号代替。
     *
     * @param phoneNumber 原始手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return phoneNumber;
        }

        // 脱敏逻辑：保留前三位，后四位，中间用星号代替
        return phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    /**
     * 对身份证号进行脱敏处理，保留前两位、后两位，中间位数用星号代替。
     *
     * @param idNumber 原始身份证号
     * @return 脱敏后的身份证号
     */
    public static String maskIDNumber(String idNumber) {
        if (idNumber == null || idNumber.isEmpty()) {
            return idNumber;
        }

        // 脱敏逻辑：保留前两位，后两位，中间用星号代替
        return idNumber.replaceAll("(\\d{2})\\d+(\\d{2})", "$1**************$2");
    }
}
