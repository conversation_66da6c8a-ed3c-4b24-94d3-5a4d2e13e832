package com.niceloo.cmc.ex.utils;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.range.ParsedRange;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.range.RangeAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class CallRecordAnalyzer {

    private static final String INDEX_PREFIX = "call_record_";
    private static final String DURATION_FIELD = "duration";
    private static final String CALL_TIME_FIELD = "callTime";
    private static final String RECEIVER_USER_ID_FIELD = "reciverUserId";
    private static final String CHANNEL_TYPE_FIELD = "channelType";
    private static final String SERVER_FOLDER_FIELD = "serverFolder";
    private static final int BATCH_SIZE = 10000; // 每个批次处理1万条记录

    private static RestHighLevelClient createClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T"));

        return new RestHighLevelClient(
                RestClient.builder(new HttpHost("**************", 9200, "http"))
                        .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }

    public static void main(String[] args) throws IOException {
        RestHighLevelClient client = createClient(); // 初始化Elasticsearch客户端
        FileInputStream fis = new FileInputStream(new File("D:\\data\\commu\\23年系统课下单学员客户ID_new.xlsx"));
        Workbook workbook = new XSSFWorkbook(fis);
        Sheet sheet = workbook.getSheetAt(0);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        int totalRows = sheet.getPhysicalNumberOfRows() - 1; // 总行数，减去表头
        int currentRow = 0;
        int batchStartRow = 0;
        int batchEndRow = Math.min(BATCH_SIZE, totalRows);

        while (batchStartRow < totalRows) {
            System.out.printf("Processing batch %d to %d of %d rows...\n", batchStartRow + 1, batchEndRow, totalRows);

            for (int i = batchStartRow; i < batchEndRow; i++) {
                Row row = sheet.getRow(i + 1); // 行号从1开始

                Cell custIdCell = row.getCell(0);
                Cell finishTimeCell = row.getCell(1);

                String custId = custIdCell.getStringCellValue();
                LocalDate finishDate = LocalDate.parse(finishTimeCell.getStringCellValue(), formatter);

                // 构建查询
                SearchRequest searchRequest = new SearchRequest(buildIndicesForDate(finishDate).toArray(new String[0]));
                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

                // 时间范围
                RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery(CALL_TIME_FIELD)
                        .lte(finishDate.atStartOfDay().format(formatter));

                // 客户ID、serverFolder不为空、channelType为YP
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery(RECEIVER_USER_ID_FIELD, custId))
                        .mustNot(QueryBuilders.termQuery(SERVER_FOLDER_FIELD,""))
                        .must(QueryBuilders.termQuery(CHANNEL_TYPE_FIELD, "YP"));

                searchSourceBuilder.query(queryBuilder)
                        .postFilter(timeRangeQuery);

                // 聚合
                RangeAggregationBuilder aggregationBuilder = AggregationBuilders.range("duration_ranges")
                        .field(DURATION_FIELD)
                        .addRange(0, 30)
                        .addRange(31, 60)
                        .addRange(61, 180)
                        .addRange(181, 300)
                        .addRange(301, 600)
                        .addRange(601,86400);

                searchSourceBuilder.aggregation(aggregationBuilder);

                searchRequest.source(searchSourceBuilder);

                // 执行查询
                SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);

                // 处理结果
                ParsedRange rangeAggregation = response.getAggregations().get("duration_ranges");
                List<Range.Bucket> buckets = (List<Range.Bucket>) rangeAggregation.getBuckets();
                for (Range.Bucket bucket : buckets) {
                    Double from = Double.parseDouble(bucket.getFromAsString());
                    Double to = Double.parseDouble(bucket.getToAsString());
                    long count = bucket.getDocCount();

                    // 获取名为"total_duration"的Sum聚合
                    Aggregation agg = bucket.getAggregations().get("total_duration");
                    double totalDuration = 0.0;

                    if (agg instanceof Sum) {
                        Sum sumAgg = (Sum) agg;
                        totalDuration = sumAgg.getValue();
                    }

                    System.out.printf("Duration range [%f, %f): %d calls, total duration: %f seconds%n",
                            from, to, count, totalDuration);
                }

                currentRow++;
            }

            batchStartRow = batchEndRow;
            batchEndRow = Math.min(batchStartRow + BATCH_SIZE, totalRows);
        }

        System.out.println("Processing complete.");

        workbook.close();
        fis.close();
    }

    private static List<String> buildIndicesForDate(LocalDate date) {
        List<String> indices = new ArrayList<>();
        int startMonth = 1;
        if (date.getYear() == 2023 && date.getMonthValue() >= 1) {
            startMonth = date.getMonthValue();
        }

        for (int i = startMonth; i <= date.getMonthValue(); i++) {
            indices.add(INDEX_PREFIX + String.format("%04d%02d", date.getYear(), i));
        }

        return indices;
    }
}