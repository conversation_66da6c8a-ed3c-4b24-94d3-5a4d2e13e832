package com.niceloo.cmc.ex.utils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class IpDistrictCleaner {
    private static final String CSV_FILE = "C:\\Users\\<USER>\\Desktop\\ip地址库清理.csv";
    private static final String OUTPUT_FILE = "ip地址库清理.sql";
    private static final int BATCH_SIZE = 500;

    public static void main(String[] args) {
        List<String> ipDistrictIds = new ArrayList<>();

        // 读取CSV文件
        try (BufferedReader br = new BufferedReader(new FileReader(CSV_FILE))) {
            String line;
            // 跳过标题行
            br.readLine();
            while ((line = br.readLine()) != null) {
                String[] columns = line.split(",");
                if (columns.length > 0) {
                    ipDistrictIds.add(columns[0].trim()); // 假设ipdistrictId在第一列
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }

        // 生成DELETE语句
        try (BufferedWriter bw = new BufferedWriter(new FileWriter(OUTPUT_FILE))) {
            for (int i = 0; i < ipDistrictIds.size(); i += BATCH_SIZE) {
                StringBuilder sql = new StringBuilder("DELETE \nFROM\n\tex.ExIpdistrict\nWHERE\n\tipdistrictId IN (");
                for (int j = i; j < i + BATCH_SIZE && j < ipDistrictIds.size(); j++) {
                    sql.append("'").append(ipDistrictIds.get(j)).append("'"); // 添加单引号
                    if (j < i + BATCH_SIZE - 1 && j < ipDistrictIds.size() - 1) {
                        sql.append(", ");
                    }
                }
                sql.append(");\n");
                bw.write(sql.toString());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}