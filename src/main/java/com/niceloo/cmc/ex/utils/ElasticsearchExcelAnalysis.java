package com.niceloo.cmc.ex.utils;

import lombok.Data;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.range.RangeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class ElasticsearchExcelAnalysis {
    private static final String INDEX_PREFIX = "call_record_";
        private static final String[] INDICES = {"call_record_202301","call_record_202302","call_record_202303","call_record_202304", "call_record_202305", "call_record_202306", "call_record_202307", "call_record_202308", "call_record_202309", "call_record_202310", "call_record_202311", "call_record_202312"};

    public static void main(String[] args) {
        try {
            // 创建 ElasticsearchClient
            RestHighLevelClient client = createClient();

            // 读取Excel文件
            File excelFile = new File("D:\\data\\commu\\23年系统课下单学员客户ID_new.xlsx");
            Workbook workbook = new XSSFWorkbook(new FileInputStream(excelFile));
            Sheet sheet = workbook.getSheetAt(0);

            // 遍历Excel数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                String custId = getCellValue(row, 0);
                String finishTime = getCellValue(row, 1);


                // 通过年月确定要查询的索引名称
                String indexPattern = "call_record_" + finishTime.substring(0, 7).replace("-", "");
                // 查出上面indexPattern在INDICES中元素的位置
                int index = -1;
                for (int j = 0; j < INDICES.length; j++) {
                    if (INDICES[j].startsWith(indexPattern)) {
                        index = j;
                        break;
                    }
                }
                // 把INDICES中所有的小于等于index的元素查出来形成新的数组
                String[] indices = new String[index+1];
                for (int j = 0; j < index+1; j++) {
                    indices[j] = INDICES[j];
                }

                // 查询通话记录并统计
                CallRecordStatistics stats = getCallRecordStatistics(client, indices, custId, finishTime);

                // 将统计结果写回Excel
                setCellValue(row, 2, stats.getRange0To30Seconds());
                setCellValue(row, 3, stats.getRange30SecondsTo1Minute());
                setCellValue(row, 4, stats.getRange1MinuteTo3Minutes());
                setCellValue(row, 5, stats.getRange3MinutesTo5Minutes());
                setCellValue(row, 6, stats.getRange5MinutesTo10Minutes());
                setCellValue(row, 7, stats.getRange10MinutesUp());
                setCellValue(row, 8, stats.getRange0To30SecondsTotalDuration());
                setCellValue(row, 9, stats.getRange30SecondsTo1MinuteTotalDuration());
                setCellValue(row, 10, stats.getRange1MinuteTo3MinutesTotalDuration());
                setCellValue(row, 11, stats.getRange3MinutesTo5MinutesTotalDuration());
                setCellValue(row, 12, stats.getRange5MinutesTo10MinutesTotalDuration());
                setCellValue(row, 13, stats.getRange10MinutesUpTotalDuration());

            }

            // 保存修改后的Excel文件
            FileOutputStream outputStream = new FileOutputStream(excelFile);
            workbook.write(outputStream);
            outputStream.close();

            client.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据指定的客户ID和完成时间，查询指定索引下的通话记录统计信息
     *
     * @param client      Elasticsearch客户端
     * @param indices     待查询的索引数组
     * @param custId      客户ID
     * @param finishTime  完成时间
     * @return 通话记录统计信息
     * @throws IOException IO异常
     */
    private static CallRecordStatistics getCallRecordStatistics(RestHighLevelClient client, String[] indices, String custId, String finishTime) throws IOException {
        SearchRequest searchRequest = new SearchRequest(indices);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        // 构建查询条件
        sourceBuilder.query(QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("reciverUserId", custId))
                .must(QueryBuilders.rangeQuery("callTime").lte(finishTime))
                .mustNot(QueryBuilders.termQuery("serverFolder", ""))
                .must(QueryBuilders.termQuery("channelType", "YP")));

        // 构建聚合统计
        RangeAggregationBuilder rangeAgg = AggregationBuilders.range("duration_range")
                .field("duration")
                .addRange(0, 30)
                .addRange(30, 60)
                .addRange(60, 180)
                .addRange(180, 300)
                .addRange(300, 600)
                .addRange(600, Double.MAX_VALUE);
        // 为每个 duration_range 分组计算 total_duration
        TermsAggregationBuilder termsAgg = AggregationBuilders.terms("duration_range_terms")
                .field("duration_range");
        SumAggregationBuilder sumAgg = AggregationBuilders.sum("total_duration").field("duration");
        termsAgg.subAggregation(sumAgg);

        // 将 termsAgg 添加到 rangeAgg 中
        sourceBuilder.aggregation(rangeAgg.subAggregation(termsAgg));

        searchRequest.source(sourceBuilder);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        // 解析聚合结果
        Aggregations aggs = searchResponse.getAggregations();
        Range durationRange = aggs.get("duration_range");
        CallRecordStatistics stats = new CallRecordStatistics();
        double totalDuration = 0.0;

        for (Range.Bucket bucket : durationRange.getBuckets()) {
            double from = Double.parseDouble(bucket.getFromAsString());
            double to = Double.parseDouble(bucket.getToAsString());
            long count = bucket.getDocCount();
            ParsedSum totalDurationAgg = bucket.getAggregations().get("total_duration");
            double bucketTotalDuration = totalDurationAgg != null ? totalDurationAgg.getValue() : 0.0;
            totalDuration += bucketTotalDuration;

            if (from == 0 && to <= 30) {
                stats.setRange0To30Seconds(stats.getRange0To30Seconds() + count);
                stats.setRange0To30SecondsTotalDuration(stats.getRange0To30SecondsTotalDuration() + bucketTotalDuration);
            } else if (from > 30 && to <= 60) {
                stats.setRange30SecondsTo1Minute(stats.getRange30SecondsTo1Minute() + count);
                stats.setRange30SecondsTo1MinuteTotalDuration(stats.getRange30SecondsTo1MinuteTotalDuration() + bucketTotalDuration);
            } else if (from > 60 && to <= 180) {
                stats.setRange1MinuteTo3Minutes(stats.getRange1MinuteTo3Minutes() + count);
                stats.setRange1MinuteTo3MinutesTotalDuration(stats.getRange1MinuteTo3MinutesTotalDuration() + bucketTotalDuration);
            } else if (from > 180 && to <= 300) {
                stats.setRange3MinutesTo5Minutes(stats.getRange3MinutesTo5Minutes() + count);
                stats.setRange3MinutesTo5MinutesTotalDuration(stats.getRange3MinutesTo5MinutesTotalDuration() + bucketTotalDuration);
            } else if (from > 300 && to <= 600) {
                stats.setRange5MinutesTo10Minutes(stats.getRange5MinutesTo10Minutes() + count);
                stats.setRange5MinutesTo10MinutesTotalDuration(stats.getRange5MinutesTo10MinutesTotalDuration() + bucketTotalDuration);
            } else if (from > 600) {
                stats.setRange10MinutesUp(stats.getRange10MinutesUp() + count);
                stats.setRange10MinutesUpTotalDuration(stats.getRange10MinutesUpTotalDuration() + bucketTotalDuration);
            }
        }
        return stats;
    }

    /**
     * 从Excel行中获取指定单元格的字符串值
     *
     * @param row Excel行对象
     * @param cellIndex 单元格索引
     * @return 返回指定单元格的字符串值，如果单元格为空则返回空字符串
     */
    private static String getCellValue(Row row, int cellIndex) {
        Cell cell = row.getCell(cellIndex);
        return cell != null ? cell.getStringCellValue() : "";
    }

    /**
     * 设置Excel单元格的值
     *
     * @param row Excel行对象
     * @param cellIndex 单元格索引
     * @param value 单元格的值，支持String、Double、Long类型
     */
    private static void setCellValue(Row row, int cellIndex, Object value) {
        Cell cell = row.createCell(cellIndex);
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        }
    }

    /**
     * 创建一个RestHighLevelClient实例
     *
     * @return 返回RestHighLevelClient实例
     */
    private static RestHighLevelClient createClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T"));

        return new RestHighLevelClient(
                RestClient.builder(new HttpHost("**************", 9200, "http"))
                        .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }

    @Data
    private static class CallRecordStatistics {
        private long range0To30Seconds;
        private double range0To30SecondsTotalDuration;
        private long range30SecondsTo1Minute;
        private double range30SecondsTo1MinuteTotalDuration;
        private long range1MinuteTo3Minutes;
        private double range1MinuteTo3MinutesTotalDuration;
        private long range3MinutesTo5Minutes;
        private double range3MinutesTo5MinutesTotalDuration;
        private long range5MinutesTo10Minutes;
        private double range5MinutesTo10MinutesTotalDuration;
        private long range10MinutesUp;
        private double range10MinutesUpTotalDuration;
    }
}