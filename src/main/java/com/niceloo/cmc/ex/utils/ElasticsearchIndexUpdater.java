package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.framework.utils.DateUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class ElasticsearchIndexUpdater {
    private static final String INDEX_NAME = "call_record_202407";

    public static void main(String[] args) {
        String startTime = "2024-08-01 08:50:00";
        String endTime = "2024-08-01 21:30:00";
        // 设置Elasticsearch集群的连接信息
        RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        new HttpHost("**************", 9200, "http")
                ).setHttpClientConfigCallback(httpClientBuilder ->
                        httpClientBuilder
                                .setDefaultCredentialsProvider(new BasicCredentialsProvider() {
                                    @Override
                                    public Credentials getCredentials(AuthScope authScope) {
                                        return new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T");
                                    }
                                })
                )
        );
        // 获取电话记录
        List<PhoneRecordClient.PhoneRecord.PhoneRecordItem> phoneRecords = PhoneRecordClient.getPhoneRecords(startTime, endTime);
        // 更新索引
        updateIndexDocuments(client, phoneRecords);
    }

    /**
     * 更新索引中的电话记录。
     *
     * @param esClient Elasticsearch RestHighLevelClient对象，用于与Elasticsearch进行交互。
     * @param records 包含要更新的电话记录的列表。
     */
    private static void updateIndexDocuments(RestHighLevelClient esClient, List<PhoneRecordClient.PhoneRecord.PhoneRecordItem> records) {
        BulkRequest bulkRequest = new BulkRequest();

        for (PhoneRecordClient.PhoneRecord.PhoneRecordItem record : records) {
            TermQueryBuilder queryBuilder = QueryBuilders.termQuery("field2", record.getClient_id());
            TermQueryBuilder channelTypeQuery = QueryBuilders.termQuery("channelType", "TQ");
            TermQueryBuilder durationQuery = QueryBuilders.termQuery("duration", -1);

            SearchRequest searchRequest = new SearchRequest(INDEX_NAME)
                    .source(new SearchSourceBuilder()
                            .query(QueryBuilders.boolQuery()
                                    .must(queryBuilder)
                                    .must(channelTypeQuery)
                                    .must(durationQuery))
                            .size(1));

            try {
                SearchResponse searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);
                if (searchResponse.getHits().getTotalHits() > 0) {
                    String callId = searchResponse.getHits().getHits()[0].getId();
                    UpdateRequest updateRequest = new UpdateRequest(INDEX_NAME, "_doc", callId)
                            .doc(
                                    "callType", record.getCall_style().equals("3") ? BizConst.CALL_OUT : BizConst.CALL_IN,
                                    "callTime", convertUnixTimestampToLongDate(record.getInsert_time()),
                                    "duration", record.getDuration(),
                                    "isValidCall", record.getDuration() >= BizConst.EFFECTIVE_TIME ? BizConst.VALID_CALL_Y : BizConst.VALID_CALL_N,
                                    "voiceSourceUrl", record.getRecordfile(),
                                    "voiceStatus", record.getDuration() > 0 ? BizConst.VOICE_DEALING : BizConst.VOICE_NO_DEAL,
                                    "voiceSourceId", record.getFsunique_id(),
                                    "modifiedTime", DateUtils.getNowDString(DateUtil.YMD_HMS)
                            );

                    bulkRequest.add(updateRequest);
                }
            } catch (IOException e) {
                // 处理异常
                e.printStackTrace();
            }
        }

        try {
            BulkResponse bulkResponse = esClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            // 处理批量更新响应
            System.out.println("bulkResponse:"+bulkResponse);
        } catch (IOException e) {
            // 处理异常
            e.printStackTrace();
        }
    }

    /**
     * 将 Unix 时间戳转换为指定时区的长日期时间字符串
     *
     * @param unixTimestamp Unix 时间戳（以秒为单位）
     * @return 转换后的日期时间字符串，格式为 "yyyy-MM-dd HH:mm:ss.SSS"，时区设置为 UTC+10
     */
    public static String convertUnixTimestampToLongDate(long unixTimestamp) {
        // 创建 Instant 对象
        Instant instant = Instant.ofEpochSecond(unixTimestamp);

        // 设置时区为 UTC+8
        ZoneId zoneId = ZoneId.of("UTC+8");

        // 使用 DateTimeFormatter 格式化日期时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = instant.atZone(zoneId).format(formatter);

        return formattedDateTime;
    }
}