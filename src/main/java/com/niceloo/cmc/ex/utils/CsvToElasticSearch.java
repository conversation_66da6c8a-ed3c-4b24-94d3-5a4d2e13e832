package com.niceloo.cmc.ex.utils;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Reader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class CsvToElasticSearch {

    private static final int BATCH_SIZE = 1000; // 每批处理的custId数量

    private static RestHighLevelClient createClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T"));

        return new RestHighLevelClient(
                RestClient.builder(new HttpHost("es-cn-8ex3wgtqb000755wp.public.elasticsearch.aliyuncs.com", 9200, "http"))
                        .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }

    public static void main(String[] args) {
        String csvFilePath = "D:\\data\\产品\\吕书伟\\6月后仅二建新客户_202412051511.csv"; // 修改为你的CSV文件路径
        List<String> custIds = readCustIdsFromCsv(csvFilePath);

        try (RestHighLevelClient client = createClient()) {
            List<String[]> results = new ArrayList<>();

            for (int month = 202406; month <= 202412; month++) {
                String indexName = "call_record_" + month;

                // 分批处理custId
                for (int i = 0; i < custIds.size(); i += BATCH_SIZE) {
                    int end = Math.min(i + BATCH_SIZE, custIds.size());
                    List<String> batch = custIds.subList(i, end);
                    results.addAll(searchElasticSearch(client, indexName, batch));

                    // 打印处理进度
                    System.out.printf("Processed %d out of %d custIds for index %s%n", end, custIds.size(), indexName);
                }
            }

            // 输出结果到Excel
            writeResultsToExcel("output.xlsx", results); // 修改为你想要输出的文件名

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static List<String> readCustIdsFromCsv(String csvFilePath) {
        List<String> custIds = new ArrayList<>();
        try (Reader reader = Files.newBufferedReader(Paths.get(csvFilePath));
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {
            for (CSVRecord csvRecord : csvParser) {
                custIds.add(csvRecord.get("custId")); // 假设CSV中有标题为custId的列
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return custIds;
    }

    private static List<String[]> searchElasticSearch(RestHighLevelClient client, String indexName, List<String> custIds) throws IOException {
        List<String[]> results = new ArrayList<>();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("reciverUserId", custIds))
                .must(QueryBuilders.rangeQuery("duration").gt(300))
                .must(QueryBuilders.termQuery("dataCompleteStatus", "Y"));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.fetchSource(new String[]{"reciverUserId", "callTime", "duration", "channelType", "voiceSourceUrl", "serverFolder", "field1"}, null);

        SearchRequest searchRequest = new SearchRequest(indexName);
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        searchResponse.getHits().forEach(hit -> {
            String reciverUserId = hit.getSourceAsMap().get("reciverUserId").toString();
            String callTime = hit.getSourceAsMap().get("callTime").toString();
            String duration = hit.getSourceAsMap().get("duration").toString();
            String channelType = hit.getSourceAsMap().get("channelType").toString();
            String voiceSourceUrl = hit.getSourceAsMap().get("voiceSourceUrl").toString();
            String serverFolder = hit.getSourceAsMap().get("serverFolder").toString();
            String field1 = hit.getSourceAsMap().get("field1").toString();

            results.add(new String[]{reciverUserId, callTime, duration, channelType, voiceSourceUrl, serverFolder, field1});
        });

        return results;
    }

    private static void writeResultsToExcel(String outputFilePath, List<String[]> results) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Results");

            // 添加标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"reciverUserId", "callTime", "duration", "channelType", "voiceSourceUrl", "serverFolder", "field1"};
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
            }

            // 添加数据行
            for (int i = 0; i < results.size(); i++) {
                Row row = sheet.createRow(i + 1);
                String[] result = results.get(i);
                for (int j = 0; j < result.length; j++) {
                    row.createCell(j).setCellValue(result[j]);
                }
            }

            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                workbook.write(fos);
            }

            System.out.println("Results written to " + outputFilePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}