package com.niceloo.cmc.ex.utils;

/**
 * 批量更新配置类
 */
public class BatchUpdateConfig {
    
    // Elasticsearch连接配置
    public static final String ES_HOST = "es-cn-8ex3wgtqb000755wp.public.elasticsearch.aliyuncs.com";
    public static final int ES_PORT = 9200;
    public static final String ES_SCHEME = "http";
    public static final String ES_USERNAME = "elastic";
    public static final String ES_PASSWORD = "W86mXT4x1t2T";
    
    // 索引和查询配置
    public static final String INDEX_NAME = "call_record_202505";
    public static final String CHANNEL_TYPE = "ZK";
    // field6文件位于项目根目录下
    public static final String FIELD6_FILE_PATH = "field6.txt";
    
    // 批处理配置
    public static final int BATCH_SIZE = 100; // 每批处理的field6数量
    public static final int INTERVAL_MS = 100; // 批次间隔时间（毫秒）
    public static final int SEARCH_SIZE = 1000; // 每次搜索返回的最大文档数
    public static final int MAX_RETRIES = 3; // 最大重试次数
    
    // 更新字段配置
    public static final String DATA_COMPLETE_STATUS_VALUE = "N";
    public static final String SERVER_FOLDER_VALUE = "";
}
