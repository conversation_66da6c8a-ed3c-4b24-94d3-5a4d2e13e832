package com.niceloo.cmc.ex.utils;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

public class CallRecordProcessor {

    private static final String INDEX_PREFIX = "call_record";
    private static final String NEW_INDEX_NAME = "call_record_2023_34";
    private static final int BATCH_SIZE = 100; // 每处理100行输出一次日志

    public static void main(String[] args) throws IOException {
        // 创建Elasticsearch客户端
        try (RestHighLevelClient client = createClient()) {
            // 读取Excel文件
            FileInputStream fis = new FileInputStream(new File("D:\\data\\commu\\23年系统课下单学员客户ID_34.xlsx"));
            Workbook workbook = new XSSFWorkbook(fis);
            Sheet sheet = workbook.getSheetAt(0);

            // 计算总行数
            int totalRows = (int) sheet.getLastRowNum();

            // 初始化累积记录列表
            List<Map<String, Object>> accumulatedRecords = new ArrayList<>();

            // 遍历Excel中的每一行
            int processedRows = 0;
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过表头

                String custId = row.getCell(0).getStringCellValue();
                String finishTime = row.getCell(1).getStringCellValue();

                // 查询数据
                List<Map<String, Object>> records = queryRecords(client, custId, finishTime);
                // 将查询结果累积到列表中
                accumulatedRecords.addAll(records);

                // 更新已处理的行数
                processedRows++;

                // 当累积记录达到BATCH_SIZE时，执行存储操作
                if (accumulatedRecords.size() >= BATCH_SIZE || processedRows == totalRows) {
                    storeRecords(client, accumulatedRecords);
                    // 清空累积记录列表
                    accumulatedRecords.clear();
                    // 输出进度日志
                    System.out.println("Processed " + processedRows + " out of " + totalRows + " rows. Last custId: " + custId);
                }
            }

            workbook.close();
            fis.close();
        }
    }

    /**
     * 查询指定客户ID和截止时间的记录列表
     *
     * @param client RestHighLevelClient对象，用于执行Elasticsearch查询
     * @param custId 客户ID
     * @param finishTime 截止时间，格式为yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
     * @return List<Map<String, Object>> 符合条件的记录列表
     * @throws IOException 查询Elasticsearch发生异常时抛出
     */
    private static List<Map<String, Object>> queryRecords(RestHighLevelClient client, String custId, String finishTime) throws IOException {
        List<Map<String, Object>> records = new ArrayList<>();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("reciverUserId", custId))
                .mustNot(QueryBuilders.termQuery("serverFolder",""))
                .must(QueryBuilders.termQuery("channelType", "YP"))
                .must(QueryBuilders.rangeQuery("callTime").lte(finishTime));

        // 遍历所有相关的索引
        int finishMonth = Integer.parseInt(finishTime.substring(5, 7));
        String[] indices = new String[finishMonth];
        for (int month = 1; month <= finishMonth; month++) {
            String indexName = INDEX_PREFIX + "_" + finishTime.substring(0, 4).replace("-", "") + String.format("%02d", month);
            indices[month - 1] = indexName;
        }

        SearchRequest searchRequest = new SearchRequest(indices)
                .source(new SearchSourceBuilder().query(queryBuilder));
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        for (SearchHit hit : searchResponse.getHits()) {
            Map<String, Object> record = hit.getSourceAsMap();
            records.add(record);
        }
        return records;
    }

    /**
     * 将记录批量存储到Elasticsearch中
     *
     * @param client RestHighLevelClient实例
     * @param records 要存储的记录列表，其中每个记录为一个键值对形式的Map
     * @throws IOException 存储记录时可能出现的IOException
     */
    private static void storeRecords(RestHighLevelClient client, List<Map<String, Object>> records) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        for (Map<String, Object> record : records) {
            IndexRequest indexRequest = new IndexRequest(NEW_INDEX_NAME, "_doc")
                    .source(record, XContentType.JSON);
            bulkRequest.add(indexRequest);
        }

        BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
    }

    /**
     * 连接Elasticsearch的方法
     *
     * @return 返回一个RestHighLevelClient对象，用于与Elasticsearch进行交互
     */
    private static RestHighLevelClient createClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T"));

        return new RestHighLevelClient(
                RestClient.builder(new HttpHost("**************", 9200, "http"))
                        .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }
}
