package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.pojo.param.SyncAiVoiceParam;
import com.niceloo.framework.utils.DateUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 通话记录工具类
 * <AUTHOR>
 * @since 2022-02-24 09:42
 */
public class RecordUtil {

    /**
     * 获取两个时间内所包含的索引（一个月一个索引）
     */
    public static List<String> getIndiceList(String timeStart, String timeEnd) {
        String[] starts = timeStart.split("-");
        String[] ends = timeEnd.split("-");
        List<String> indices = new ArrayList<>();
        Date startDate = DateUtils.toDate(starts[0] + "-" + starts[1], DateUtil.YM);
        indices.add(getRecordIndexName(startDate));
        Date endDate = DateUtils.toDate(ends[0] + "-" + ends[1], DateUtil.YM);
        if (DateUtils.compare(startDate, endDate) != 0) {
            while (DateUtils.compare(startDate, endDate) < 0) {
                startDate = DateUtils.addMonth(startDate, 1);
                String indexName = getRecordIndexName(startDate);
                indices.add(indexName);
            }
        }
        return indices;
    }

    /**
     * 获取两个时间内所包含的索引（一个月一个索引）
     * 日期字符串格式应保持一致并不低于yyyy-MM
     *
     * @return java.lang.String 返回包含的索引,多个之间使用","拼接
     * @param timeStart 开始时间
     * @param timeEnd 结束时间
     * <AUTHOR>
     * @since 16:15 2022/3/15
     **/
    public static String getIndexStr(String timeStart, String timeEnd) {
        String format = "yyyy-MM";
        String[] starts = timeStart.split("-");
        String[] ends = timeEnd.split("-");
        StringBuilder indices = new StringBuilder(BizConst.CALL_RECORD_ES_PREFIX + starts[0] + starts[1]);

        Date startDate = DateUtils.toDate(starts[0] + "-" + starts[1], format);
        Date endDate = DateUtils.toDate(ends[0] + "-" + ends[1], format);
        if (DateUtils.compare(startDate, endDate) != 0) {
            while (DateUtils.compare(startDate, endDate) < 0) {
                startDate = DateUtils.addMonth(startDate, 1);
                String[] now = DateUtils.toStr(startDate, format).split("-");
                indices.append("," + BizConst.CALL_RECORD_ES_PREFIX).append(now[0]).append(now[1]);
            }
        }
        return indices.toString();
    }


    /**
     * 获取当月的索引名
     * <AUTHOR>
     * @since 2021/12/8 11:32
     */
    public static String getCurrentMonthRecordIndexName() {
        Date now = new Date();
        Integer month = DateUtils.getMonth(now);
        String monthStr = month < 10 ? "0" + month : String.valueOf(month);
        return BizConst.CALL_RECORD_ES_PREFIX + DateUtils.getYear(now) + monthStr;
    }

    /**
     * 获取该时间的通话记录索引
     *
     * @return java.lang.String
     * @param date 日期
     * <AUTHOR>
     * @since 15:29 2022/2/24
     **/
    public static String getRecordIndexName(Date date) {
        Integer month = DateUtils.getMonth(date);
        String monthStr = month < 10 ? "0" + month : String.valueOf(month);
        return BizConst.CALL_RECORD_ES_PREFIX + DateUtils.getYear(date) + monthStr;
    }

    /**
     * 获取该时间的通话记录索引
     *
     * @return java.lang.String
     * @param date 字符串日期
     * @param fmt  日期格式
     * <AUTHOR>
     * @since 15:29 2022/3/11
     **/
    public static String getRecordIndexName(String date, String fmt) {
        Date toDate = DateUtils.toDate(date, fmt);
        Integer month = DateUtils.getMonth(toDate);
        String monthStr = month < 10 ? "0" + month : String.valueOf(month);
        return BizConst.CALL_RECORD_ES_PREFIX + DateUtils.getYear(toDate) + monthStr;
    }

    /**
     * 获取所有索引, 从2017-01开始
     * <AUTHOR>
     * @since 2022/2/28
     */
    public static List<String> getAllIndexNames() {
        List<String> indexNames = new ArrayList<>();
        LocalDate startLocalDate = LocalDate.of(2017, 1, 1);
        for (LocalDate localDate = startLocalDate; !localDate.isAfter(LocalDate.now()); localDate = localDate.plusMonths(1)) {
            String monthValue = localDate.getMonthValue() < 10 ? "0" + localDate.getMonthValue() : "" + localDate.getMonthValue();
            indexNames.add(BizConst.CALL_RECORD_ES_PREFIX + localDate.getYear() + monthValue);
        }
        Collections.reverse(indexNames);
        return indexNames;
    }

    /**
     * 校验查询时间是否符合查询条件
     *
     * @return java.util.Map<java.lang.String, java.lang.String> 返回正确的开始、结束时间[createdTimeStart、createdTimeEnd]
     * @param createdTimeStart 非空 查询起始时间
     * @param createdTimeEnd   非空 查询结束时间
     * @param upToOneYear      是否允许查询时间段超过一年
     * <AUTHOR>
     * @since 16:22 2022/3/15
     **/
    public static Map<String, String> checkSearchTime(String createdTimeStart, String createdTimeEnd, boolean upToOneYear) {
        if (DateUtils.compare(createdTimeStart, createdTimeEnd) > 0) {
            throw new RuntimeException("查询起始时间不能大于查询结束时间");
        }
        if (DateUtils.compare(createdTimeStart, DateUtils.getNowDString()) > 0) {
            throw new RuntimeException(createdTimeStart + "之后的通话记录数据暂未开始同步");
        }
        if (DateUtils.compare(createdTimeEnd, BizConst.RECORD_INIT_DATE_STR) < 0) {
            throw new RuntimeException("暂无" + createdTimeEnd + "之前的通话记录数据");
        }
        if (upToOneYear) {
            LocalDate startDate = LocalDate.parse(createdTimeStart, DateTimeFormatter.ofPattern(DateUtils.DEFAULT_FORMAT));
            LocalDate endDate = LocalDate.parse(createdTimeEnd, DateTimeFormatter.ofPattern(DateUtils.DEFAULT_FORMAT));
            if (startDate.plusYears(1).compareTo(endDate) < 0) {
                throw new RuntimeException("查询起始时间和查询结束时间的跨度不能超过12个月");
            }
        }

        Map<String, String> result = new HashMap<>(2);
        result.put("createdTimeStart", createdTimeStart);
        result.put("createdTimeEnd", createdTimeEnd);
        if (DateUtils.compare(createdTimeEnd, DateUtils.getNowDString()) > 0) {
            createdTimeEnd = DateUtils.getNowDString();
            result.put("createdTimeEnd", createdTimeEnd);
        }
        if (DateUtils.compare(createdTimeStart, BizConst.RECORD_INIT_DATE_STR) < 0) {
            createdTimeStart = BizConst.RECORD_INIT_DATE_STR;
            result.put("createdTimeStart", createdTimeStart);
        }
        return result;
    }

    /**
     * 根据通话记录索引名称获取月份
     *
     * @param indexName 通话记录索引名称
     * @return java.lang.String
     * <AUTHOR>
     * @since 11:51 2022/7/4
     **/
    public static String getDateByIndexName(String indexName) {
        String[] s = indexName.split("_");
        String date = s[s.length - 1];
        String pre = date.substring(0, 4);
        String suf = date.substring(4);
        date = pre + "-" + suf;
        return date;
    }

    /**
     * 获取大写没有‘-’的32位UUID
     *
     * @return java.lang.String
     * <AUTHOR>
     * @since 13:11 2022/9/30
     **/
    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    /**
     * 获取该时间的AI机器人通话记录索引
     *
     * @return java.lang.String
     * @param date 字符串日期
     * @param fmt  日期格式
     * <AUTHOR>
     * @since 15:29 2022/3/11
     **/
    public static String getAIRecordIndexName(String date, String fmt) {
        Date toDate = DateUtils.toDate(date, fmt);
        Integer month = DateUtils.getMonth(toDate);
        String monthStr = month < 10 ? "0" + month : String.valueOf(month);
        return BizConst.AI_JOB_CUSTOMER_ES_PREFIX + DateUtils.getYear(toDate) + monthStr;
    }

    /**
     * 获取京东言犀平台customerVariables中所需的UUID。
     *
     * 该UUID由Java生成的UUID（去掉中间的连字符后的32位字符串）和记录的归属Elasticsearch索引名（indexName）组合而成，
     * 具体格式为：UUID（去连字符）+ "@" + indexName。
     *
     * @param uuid      Java生成的UUID字符串，包含连字符
     * @param indexName Elasticsearch索引的名称
     * @return 组合后的UUID字符串，用于京东言犀平台的customerVariables
     */
    public static String getCustomerVariablesUUID(String uuid, String indexName) {
        return uuid.replace("-", "") + "@" + indexName;
    }

    /**
     * 构建一个包含同步AI语音参数的SyncAiVoiceParam对象。
     *
     * @param index 指定的Elasticsearch索引名称。
     * @param hoursStart 起始时间范围，以小时为单位，表示多少小时前的时间作为查询的起始时间。
     * @param hoursEnd 结束时间范围，以小时为单位，表示多少小时前的时间作为查询的结束时间。
     * @return 构建好的SyncAiVoiceParam对象，包含了设置的索引名、时间范围、下载次数范围等参数。
     *
     * 该方法首先创建一个新的SyncAiVoiceParam实例，然后根据传入的参数设置索引名、时间范围（包括起始时间和当前时间作为结束时间）以及下载次数范围（默认为0到最大值）。
     * 最后，返回构建好的SyncAiVoiceParam对象。
     */
    public static SyncAiVoiceParam buildSyncAiVoiceParam(String index, int hoursStart, int hoursEnd) {
        SyncAiVoiceParam param = new SyncAiVoiceParam();

        // 设置单个索引名
        param.setIndex(index);

        // 设置时间范围
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = dateFormat.format(getDateHoursAgo(hoursStart));
        String endTime = dateFormat.format(getDateHoursAgo(hoursEnd));

        param.setRingTimeStart(startTime);
        param.setRingTimeEnd(endTime);

        // 设置下载次数范围
        param.setDownloadTimesStart(0); // 下限
        param.setDownloadTimesEnd(Integer.MAX_VALUE); // 上限

        return param;
    }

    /**
     * 获取当前时间向前推指定小时数的时间点
     *
     * @param hours 要向前推的小时数
     * @return 返回向前推了指定小时数的时间点
     *
     * 该方法用于计算当前时间向前推指定小时数的时间点。
     * 通过将当前时间的毫秒数减去指定小时数对应的毫秒数来得到新的时间点。
     */
    public static Date getDateHoursAgo(int hours) {
        // 获取当前时间向前推 hours 小时的时间
        return new Date(System.currentTimeMillis() - hours * 60 * 60 * 1000L);
    }

    /**
     * 获取当前月份的Elasticsearch索引名。
     *
     * @return 当前月份的Elasticsearch索引名，格式为 "ai_job_customer_yyyyMM"。
     *
     * 该方法首先创建一个SimpleDateFormat对象，用于将当前日期格式化为"yyyyMM"格式。
     * 然后，将格式化后的日期与预定义的索引前缀（BizConst.AI_JOB_CUSTOMER_ES_PREFIX）拼接，
     * 生成完整的Elasticsearch索引名。
     */
    public static String getCurrentMonthIndex() {
        // 根据当前日期生成对应的索引名
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM"); // 索引格式为 "ai_job_customer_yyyyMM"
        return BizConst.AI_JOB_CUSTOMER_ES_PREFIX + dateFormat.format(new Date());
    }

    /**
     * 根据起始月份和结束月份获取对应时间段内的索引列表。
     *
     * @param startMonths 起始月份，从当前月份开始计算的偏移量（负数表示过去月份，正数表示未来月份）
     * @param endMonths   结束月份，从当前月份开始计算的偏移量（负数表示过去月份，正数表示未来月份）
     * @return 返回一个包含指定时间段内所有索引名称的列表
     *
     * 该方法首先创建一个空的索引列表，并使用SimpleDateFormat类来格式化日期为"yyyyMM"的格式。
     * 然后，通过一个循环从起始月份迭代到结束月份，计算每个月份对应的日期，并使用BizConst中定义的索引前缀和日期格式来构造索引名称。
     * 最后，将构造好的索引名称添加到索引列表中，并返回该列表。
     *
     * 注意：此方法中计算月份时采用了近似值，即假设每月为30天，这可能会导致在某些情况下计算出的月份不准确。
     */
    public static List<String> getIndexesForRange(int startMonths, int endMonths) {
        List<String> indexes = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");

        for (int i = startMonths; i <= endMonths; i++) {
            Date pastDate = new Date(System.currentTimeMillis() - (i * 30L * 24 * 60 * 60 * 1000)); // 近似计算每月30天
            indexes.add(BizConst.AI_JOB_CUSTOMER_ES_PREFIX + dateFormat.format(pastDate));
        }

        return indexes;
    }

    /**
     * 从给定的字符串中提取 '@' 前面的部分。
     *
     * @param input 输入字符串
     * @return '@' 前面的部分，如果没有 '@' 则返回原字符串
     */
    public static String extractId(String input) {
        if (input == null) {
            return null;
        }
        int atIndex = input.indexOf('@');
        return atIndex == -1 ? input : input.substring(0, atIndex);
    }
}
