package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.pojo.vo.FileTokenVO;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.cmc.ex.feign.FileFeignClient;
import com.niceloo.framework.file.FileUploadRequest;
import com.niceloo.framework.file.FileUploadResponse;
import com.niceloo.framework.file.service.FileService;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文件工具类
 * <AUTHOR>
 * @since 2022-02-17 16:25
 */
@Component
public class FileUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 文件上传到OSS文件的服务地址
     */
    @Value("${fs.ossUrl}")
    private String ossUrl;

    private final FileFeignClient fileFeignClient;
    private final FileService fileService;

    @Autowired
    public FileUtil(FileFeignClient fileFeignClient, FileService fileService) {
        this.fileFeignClient = fileFeignClient;
        this.fileService = fileService;
    }

    /**
     * OSS复制文件到另一个BK中
     *
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @param oldBK 旧的BK服务器地址
     * <AUTHOR>
     * @since 16:30 2022/2/17
     **/
    public Map<String, String> ossCopy(String filePath, String fileName, String oldBK) {
        Map<String, String> params = new HashMap<>(5);
        params.put("fileType", "A");
        params.put("bkUrlold", oldBK);
        params.put("filePathold", filePath.substring(filePath.indexOf(".com") + 5));
        params.put("bkUrlnew", ossUrl);
        params.put("fileName", fileName);
        return fileFeignClient.ossCopyFile(JSONUtils.toJSONString(params));
    }

    /**
     * OSS复制文件到另一个BK中
     *
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @param oldBK 旧的BK服务器地址
     * <AUTHOR>
     * @since 16:30 2022/2/17
     **/
    public Map<String, Object> ossCopy2(String filePath, String fileName, String oldBK) {
        Map<String, String> params = new HashMap<>(5);
        params.put("fileType", "A");
        params.put("bkUrlold", oldBK);
        params.put("filePathold", filePath.substring(filePath.indexOf(".com") + 5));
        params.put("bkUrlnew", ossUrl);
        params.put("fileName", fileName);
        return fileFeignClient.ossCopyFile2(JSONUtils.toJSONString(params));
    }

    /**
     * 根据输入流上传文件
     * 根据URL上传应使用HttpUtils.uploadVoiceToOSS(方法)
     *
     * @return com.niceloo.framework.file.FileUploadResponse
     * @param in 数据流
     * @param fileName 文件名称
     * <AUTHOR>
     * @since 9:41 2022/2/26
     * @see HttpUtils#uploadVoiceToOSS(String, String)
     **/
    public FileUploadResponse uploadFile(InputStream in, String fileName) {
        FileUploadRequest fileRequest = new FileUploadRequest();
        fileRequest.setInputStream(in);
        fileRequest.setFileStatus("F");
        fileRequest.setFileName(fileName);
        fileRequest.setFileStoretype("A");
        fileRequest.setFileType("A");
        fileRequest.setBkUrl(ossUrl);
        return fileService.uploadInputStream(fileRequest);
    }

    public FileUploadResponse uploadExcelFile(InputStream in, String fileName) {
        FileUploadRequest fileRequest = new FileUploadRequest();
        fileRequest.setInputStream(in);
        fileRequest.setFileStatus("T");
        fileRequest.setFileName(fileName);
        fileRequest.setFileStoretype("A");
        fileRequest.setFileType("A");
        fileRequest.setBkUrl(ossUrl);
        // 一天后过期
        fileRequest.setFileExpire(DateUtil.addDay(DateUtils.getNowDString(), 1, DateUtils.DEFAULT_FORMAT));
        return fileService.uploadInputStream(fileRequest);
    }

    /**
     * 根据文件路径查询并返回临时下载URL
     *
     * @param filePath 文件路径
     * @return 与文件路径相关联的临时下载URL，如果未找到则返回null
     */
    public String getTemporaryDownloadUrlByFilePath(String filePath) {
        // 获取配置属性，添加空值检查
        String expire = SpringUtils.getProperty("zhijian.recordUrl.expire");
        if (expire == null) {
            expire = String.valueOf(1000 * 60 * 30);
        }

        Map<String, Object> request = new HashMap<>(2);
        // 使用singletonList提高语义清晰度
        request.put("filePaths", Collections.singletonList(filePath));
        request.put("expire", expire);

        try {
            Map<String, Object> result = fileFeignClient.queryTemporaryURL(JSONUtils.toJSONString(request));
            if (result != null) {
                List<Map<String, String>> mapList = (List<Map<String, String>>) result.get("data");
                if (!CollectionUtils.isEmpty(mapList)) {
                    Map<String, String> filePathUrlMap = mapList.stream()
                            .collect(Collectors.toMap(map -> map.get("filePath"), map -> map.get("url")));
                    return filePathUrlMap.get(filePath);
                }
            }
        } catch (RuntimeException e) {
            logger.error("获取文件下载链接出现异常: " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取可访问的URL地址。
     *
     * @param voiceSourceUrl 原始语音源URL地址
     * @return 可访问的URL地址，如果无法获取则返回null
     * @throws ApplicationException 如果无法找到对应的下载Token，则抛出ApplicationException异常
     */
    public String getAccessibleUrl(String voiceSourceUrl) {
        // 1. 生成文件下载 TOKEN 集合
        Map<String, Object> voiceTokenMap = fileFeignClient.getVoiceToken(JSONUtils.toJSONString(Map.of("ossUrls", List.of(voiceSourceUrl))));
        List<FileTokenVO> fileTokens = JSONUtils.toList(JSONUtils.toJSONString(voiceTokenMap.get("data")), FileTokenVO.class);

        if (fileTokens.isEmpty()) {
            throw new ApplicationException(ApiErrorCodes.execute_failed, "未找到对应的下载 Token");
        }

        // 2. 获取 OSS 私有文件访问路径
        FileTokenVO fileTokenVO = fileTokens.get(0);
        String ossUrl = fileTokenVO.getOssUrl();
        String downloadToken = fileTokenVO.getDownloadToken();

        // 设置录音有效期为半小时
        long expire = 1800 * 1000L;
        Map<String, Object> ossUrlResponse = fileFeignClient.ossUrl(
                JSONUtils.toJSONString(Map.of("ossUrl", ossUrl, "downloadToken", downloadToken, "expire", expire))
        );

        return (String) ossUrlResponse.get("url");
    }
}
