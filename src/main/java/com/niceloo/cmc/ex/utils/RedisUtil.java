package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @desc redis工具类简单封装
 * @date 2021/12/13 16:05
 **/
@Component
@SuppressWarnings("all")
public class RedisUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisUtil.class);

    /** 对象专用 */
    public static RedisTemplate redisTemplate;

    private static DefaultRedisScript<String> redisScript;

    private static RedisSerializer<String> argsSerializer;

    private static RedisSerializer resultSerializer;


    @Resource
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        RedisSerializer stringSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringSerializer);
        redisTemplate.setValueSerializer(stringSerializer);
        redisTemplate.setHashKeySerializer(stringSerializer);
        redisTemplate.setHashValueSerializer(stringSerializer);
        RedisUtil.redisTemplate = redisTemplate;
    }

    @PostConstruct
    public void init() {
        redisScript = new DefaultRedisScript<String>();
        redisScript.setResultType(String.class);
        argsSerializer = new StringRedisSerializer();
        resultSerializer = new StringRedisSerializer();
    }

    /** String专用 */
    public static StringRedisTemplate stringRedisTemplate;

    @Resource
    public void setStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
        RedisUtil.stringRedisTemplate = stringRedisTemplate;
    }

    /** 原框架中LUA脚本1 */
    public static final String SCRIPT = "" +
            "local size = redis.call('lrange', KEYS[1], 0, ARGV[1]); " +
            "if #size < #ARGV[1] " +
            "then redis.call('del', KEYS[1]); " +
            "else redis.call('ltrim', KEYS[1], ARGV[2], -1); " +
            "end; " +
            "return size;";

    /** 拆解--原框架中LUA脚本1 */
    public static List<Object> getList(String key, long start, long stop) {
        //执行lrange命令获取长度
        List<Object> list = redisTemplate.opsForList().range(key, 0, start);
        if (list.size() < stop) {
            //执行删除操作
            redisTemplate.delete(key);
        } else {
            //执行ltrim命令修剪list
            redisTemplate.opsForList().trim(key, stop, -1);
        }
        return list;

    }

    /** 原框架中LUA脚本2 */
    public static final String LOCK_SCRIPT = "" +
            "local result = nil; " +
            "local time = redis.call('hget', KEYS[1], KEYS[2]); " +
            "if time == false " +
            "then redis.call('hmset', KEYS[1], KEYS[2], ARGV[1]); " +
            "result = 'true'; " +
            "elseif time == ARGV[1] " +
            "then result = 'false'; " +
            "else " +
            "redis.call('hmset', KEYS[1], KEYS[2], ARGV[1]); " +
            "result = time; " +
            "end; " +
            "return result;";

    /** 拆解--原框架中LUA脚本2 */
    public static Object getLock(String key, String hashKey, String arg) {
        //执行hget命令获取hash值
        Object result = redisTemplate.opsForHash().get(key, hashKey);
        if (arg.equals(result)) {
            return "false";
        }
        Map<String, Object> map = new HashMap<>(1);
        map.put(key, hashKey);
        redisTemplate.opsForHash().putAll(key, map);
        if ("false".equals(result)) {
            return "true";
        }
        return result;
    }

    /**
     * 将指定值转化为指定类型并返回
     * 当前转换适用于基本类型参数
     * @param obj
     * @param cs
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T getValueByClass(Object obj, Class<T> cs) {
        String obj2 = String.valueOf(obj);
        Object obj3 = null;
        if (cs.equals(String.class)) {
            obj3 = obj2;
        } else if (cs.equals(int.class) || cs.equals(Integer.class)) {
            obj3 = Integer.valueOf(obj2);
        } else if (cs.equals(long.class) || cs.equals(Long.class)) {
            obj3 = Long.valueOf(obj2);
        } else if (cs.equals(short.class) || cs.equals(Short.class)) {
            obj3 = Short.valueOf(obj2);
        } else if (cs.equals(byte.class) || cs.equals(Byte.class)) {
            obj3 = Byte.valueOf(obj2);
        } else if (cs.equals(float.class) || cs.equals(Float.class)) {
            obj3 = Float.valueOf(obj2);
        } else if (cs.equals(double.class) || cs.equals(Double.class)) {
            obj3 = Double.valueOf(obj2);
        } else if (cs.equals(boolean.class) || cs.equals(Boolean.class)) {
            obj3 = Boolean.valueOf(obj2);
        } else {
            obj3 = (T)obj;
        }
        return (T)obj3;
    }

    //=======================KEY操作======================================
    /** 检查给定 key 是否存在 */
    public static boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /** 该命令用于在 key 存在时删除 key。 */
    public static boolean delKey(String key) {
        return redisTemplate.delete(key);
    }

    /** 该命令用于在 key 存在时删除 key。 */
    public static long delKey(Collection<?> keys) {
        return redisTemplate.delete(keys);
    }

    /** 移除 key 的过期时间，key 将持久保持。 */
    public static boolean persistKey(String key) {
        return redisTemplate.persist(key);
    }

    /** 返回 key 所储存的值的类型。 */
    public static DataType getKeyType(String key) {
        return redisTemplate.type(key);
    }

    /** 支持模糊查询 KEYS *、KEYS *key*、KEYS *key、KEYS key*等方式 */
    public static Set<String> getKeys(String keys) {
        return redisTemplate.keys(keys);
    }

    /** 设置key多长时间过期 */
    public static void setExpire(String key, long time, TimeUnit timeUnit) {
        redisTemplate.expire(key, time, timeUnit);
    }

    /** 设置key过期的截至日期 */
    public static void setExpire(String key, Date time) {
        redisTemplate.expireAt(key, time);
    }

    /** 以秒为单位，返回给定 key 的剩余生存时间 */
    public static long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }

    /** 以毫秒为单位返回 key 的剩余的过期时间 */
    public static long getExpire(String key, TimeUnit timeUnit) {
        return redisTemplate.getExpire(key, timeUnit);
    }

    //=======================STRING操作======================================

    /** 将 key 中储存的数字值增一。 */
    public static long incr(String key) {
        return redisTemplate.opsForValue().increment(key);
    }

    /** 将 key 中储存的数字值增一。 */
    public static long incr(String key, long decr) {
        return redisTemplate.opsForValue().increment(key, decr);
    }

    /** 将 key 中储存的数字值减。 */
    public static long decr(String key) {
        return redisTemplate.opsForValue().decrement(key);
    }

    /** 将 key 中储存的数字值减。 */
    public static long decr(String key, long decr) {
        return redisTemplate.opsForValue().decrement(key, decr);
    }

    /** 设置指定 key 的值 */
    public static void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /** 设置指定 key 的值并设置过期时间 */
    public static void set(String key, Object value, long time, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, time, timeUnit);
    }

    /** 只有在 key 不存在时设置 key 的值 */
    public static boolean setNx(String key, Object value) {
        return redisTemplate.opsForValue().setIfAbsent(key, value);
    }

    /** 只有在 key 不存在时设置 key 的值 */
    public static boolean setNx(String key, Object value, long time, TimeUnit timeUnit) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, time, timeUnit);
    }

    /** 判断当前的键的值是否为v，是的话不作操作，不是的话进行替换。如果没有这个键也不会做任何操作。 */
    public static boolean setEx(String key, Object value) {
        return redisTemplate.opsForValue().setIfPresent(key, value);
    }

    /** 判断当前的键的值是否为v，是的话不作操作，不是的话进行替换。如果没有这个键也不会做任何操作。 */
    public static boolean setEx(String key, Object value, long time, TimeUnit timeUnit) {
        return redisTemplate.opsForValue().setIfPresent(key, value, time, timeUnit);
    }

    /** 获取指定 key 的值。 */
    public static <T> T get(String key, Class<T> cs) {
        Object value = redisTemplate.opsForValue().get(key);
        return getValueByClass(value, cs);
    }

    /** 将给定 key 的值设为 value ，并返回 key 的旧值(old value) */
    public static <T> T getSet(String key, String newValue, Class<T> cs) {
        Object value = redisTemplate.opsForValue().getAndSet(key, newValue);
        return getValueByClass(value, cs);
    }

    //=======================HASH操作======================================

    /** 查看哈希表 key 中，指定的字段是否存在。 */
    public static boolean hkey(String key, Object... hashKeys) {
        return redisTemplate.opsForHash().hasKey(key, hashKeys);
    }

    /** 删除一个或多个哈希表字段 */
    public static long hdel(String key, Object... hashKeys) {
        return redisTemplate.opsForHash().delete(key, hashKeys);
    }

    /** 获取存储在哈希表中指定字段的值。 */
    public static Object hget(String key, Object... hashKeys) {
        return redisTemplate.opsForHash().get(key, hashKeys);
    }

    /** 获取在哈希表中指定 key 的所有字段和值 */
    public static Map<String, Object> hgetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /** 获取存储在哈希表中指定字段的值。 */
    public static void hset(String key, Object field, Object value) {
        redisTemplate.opsForHash().put(key, field, value);
    }

    /** 使用 {@code m} 中提供的数据将多个哈希字段设置为多个值。 */
    public static void hset(String key, List<String> hashKeys, List<Object> value) {
        int size = hashKeys.size();
        Map<String, Object> map = new HashMap<>(size);
        for (int i = 0; i < size; i++) {
            map.put(hashKeys.get(i), value.get(i));
        }
        redisTemplate.opsForHash().putAll(key, map);
    }

    /** 只有在字段 field 不存在时，设置哈希表字段的值。 */
    public static void hsetNx(String key, Object field, Object value) {
        redisTemplate.opsForHash().putIfAbsent(key, field, value);
    }

    /** 获取所有哈希表中的字段。 */
    public static Set<String> hkeys(String key) {
        return redisTemplate.opsForHash().keys(key);
    }

    /** 获取哈希表中所有值。 */
    public static List<String> hvalues(String key) {
        return redisTemplate.opsForHash().values(key);
    }

    /** 获取哈希表中字段的数量。 */
    public static long hlen(String key) {
        return redisTemplate.opsForHash().size(key);
    }

    //=======================LIST操作======================================

    /** 通过索引获取列表中的元素 */
    public static Object lget(String key, long index) {
        return redisTemplate.opsForList().index(key, index);
    }

    /** 获取列表指定范围内的元素 */
    public static List<Object> lget(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    /** 通过索引设置列表元素的值 */
    public static void lset(String key,long index, Object value) {
        redisTemplate.opsForList().set(key, index, value);
    }

    /** 获取列表长度 */
    public static long llen(String key) {
        return redisTemplate.opsForList().size(key);
    }

    /** 移除列表元素 */
    public static long ldel(String key, long count, Object value) {
        return redisTemplate.opsForList().remove(key, count, value);
    }

    /** 移出并获取列表的第一个元素 */
    public static Object lblpop(String key) {
        return redisTemplate.opsForList().leftPop(key);
    }

    /** 移除列表的最后一个元素，返回值为移除的元素。 */
    public static Object lbrpop(String key) {
        return redisTemplate.opsForList().rightPop(key);
    }

    /** 移出并获取列表的第一个元素， 如果列表没有元素会阻塞列表直到等待超时或发现可弹出元素为止。 */
    public static Object lblpop(String key, long time, TimeUnit timeUnit) {
        return redisTemplate.opsForList().leftPop(key, time, timeUnit);
    }

    /** 移出并获取列表的最后一个元素， 如果列表没有元素会阻塞列表直到等待超时或发现可弹出元素为止。 */
    public static Object lbrpop(String key, long time, TimeUnit timeUnit) {
        return redisTemplate.opsForList().rightPop(key, time, timeUnit);
    }

    //=======================SET操作======================================

    /** 向集合添加一个或多个成员 */
    public static long sset(String key, Object...values) {
        return redisTemplate.opsForSet().add(key, values);
    }

    /** 移除集合中一个或多个成员 */
    public static long slen(String key) {
        return redisTemplate.opsForSet().size(key);
    }

    /** 移除集合中一个或多个成员 */
    public static long sdel(String key, Object...values) {
        return redisTemplate.opsForSet().remove(key, values);
    }

    /** 集合中是否包含当前值 */
    public static boolean sFlag(String key, Object value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    /** 返回集合中的所有成员 */
    public static Set<Object> sget(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /** 返回集合中的所有成员 */
    public static Object sRandomGet(String key) {
        return redisTemplate.opsForSet().randomMember(key);
    }

    //=======================SORTED SET操作======================================

    /** 向有序集合添加一个或多个成员，或者更新已存在成员的分数 */
    public static boolean zset(String key, Object value, double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    /** 移除有序集合中的一个或多个成员 */
    public static long zdel(String key, Object...values) {
        return redisTemplate.opsForZSet().remove(key, values);
    }

    /** 获取有序集合的成员数 */
    public static long zlen(String key) {
        return redisTemplate.opsForZSet().size(key);
    }

    /** 计算在有序集合中指定区间分数的成员数 */
    public static long zcount(String key, double min, double max) {
        return redisTemplate.opsForZSet().count(key, min, max);
    }

    /** 获取排序集的大小 */
    public static long zcard(String key) {
        return redisTemplate.opsForZSet().zCard(key);
    }

    /** 返回有序集中，成员的分数值 */
    public static double zscore(String key, Object value) {
        return redisTemplate.opsForZSet().score(key, value);
    }

    /** 返回有序集合中指定成员的索引 */
    public static long zrank(String key, Object values) {
        return redisTemplate.opsForZSet().rank(key, values);
    }

    /** 返回有序集合中指定成员的索引 */
    public static Set<Object> zrang(String key, long start, long end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }

    /** 通过分数返回有序集合指定区间内的成员 */
    public static Set<Object> zrang(String key, double min, double max) {
        return redisTemplate.opsForZSet().rangeByScore(key, min, max);
    }

    /** 从从高到低排序的有序集合中获取从 { start} 到 { end} 范围内的元素。 */
    public static Set<Object> zreverRang(String key, long start, long end) {
        return redisTemplate.opsForZSet().reverseRange(key, start, end);
    }

    /** 通过分数返回有序集合指定区间内的成员 */
    public static long zdelRang(String key, long start, long end) {
        return redisTemplate.opsForZSet().removeRange(key, start, end);
    }

}
