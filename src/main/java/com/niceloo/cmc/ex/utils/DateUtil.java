package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.niceloo.cmc.ex.common.BizConst.ONE_MINUTE;

/**
 * @description: 日期工具类
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-24 10:57
 */
public class DateUtil {
    private static final Logger logger = LoggerFactory.getLogger(DateUtil.class);

    public static final String YMD = "yyyy-MM-dd";
    public static final String YM = "yyyy-MM";
    /**
     * HH:mm:ss
     */
    public static final String HMS = "HH:mm:ss";
    /**
     * yyyy-MM-dd HH:mm
     */
    public static final String YMD_HM = "yyyy-MM-dd HH:mm";
    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public static final String YMD_HMS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 两个时间之间相差距离多少天,两个时间的格式保持一致
     * <p>比如2022-11-8到2022-11-9相差只有一天,而不论是00:00:00还是23:59:59</p>
     *
     * @param str1 时间参数 1：
     * @param str2 时间参数 2：
     * @return 相差天数
     */
    public static Long getDistanceDays(String str1, String str2, String fmt) {
        DateFormat df = new SimpleDateFormat(fmt);
        Date one;
        Date two;
        long days = 0;
        try {
            one = df.parse(str1);
            two = df.parse(str2);
            long time1 = one.getTime();
            long time2 = two.getTime();
            long diff;
            if (time1 < time2) {
                diff = time2 - time1;
            } else {
                diff = time1 - time2;
            }
            days = diff / (1000 * 60 * 60 * 24);
        } catch (ParseException e) {
            logger.error(e, "日期格式转换异常");
        }
        return days;
    }

    /**
     * 格式转换
     *
     * @return java.lang.String
     * @paramter strDate 字符串类型日期
     * @paramter format 要转换的日期格式
     * <AUTHOR>
     * @Date 15:20 2022/2/24
     **/
    public static String format(String strDate, String format) {
        try {
            return new SimpleDateFormat(format).format(DateUtils.toDate(strDate, format));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取两个日期相差的月数
     *
     * @return int
     * @paramter date1
     * @paramter date2
     * <AUTHOR>
     * @Date 14:52 2021/12/8
     **/
    public static int getDistanceMonths(String date1, String date2) {
        if (StringUtils.isEmpty(date1) || StringUtils.isEmpty(date2)) {
            return 0;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(YMD);
            Calendar c1 = Calendar.getInstance();
            Calendar c2 = Calendar.getInstance();
            c1.setTime(sdf.parse(date1));
            c2.setTime(sdf.parse(date2));
            //相差的年数
            int year = c2.get(Calendar.YEAR) - c1.get(Calendar.YEAR);
            int distanceMonths = c2.get(Calendar.MONTH) - c1.get(Calendar.MONTH) + year * 12;
            return Math.abs(distanceMonths);
        } catch (ParseException e) {
            logger.error(e, "日期格式转换异常");
            return 0;
        }
    }

    /**
     * 判断两个日期是否在同一月,不在一个月内返回true
     *
     * <AUTHOR> wangchenyu
     * @Date 2021/11/4 17:43
     */
    public static boolean inSameMonth(Date date, Date otherDate) {
        return !DateUtils.getMonth(date).equals(DateUtils.getMonth(otherDate))
                || DateUtils.getYear(date) != (DateUtils.getYear(otherDate));
    }

    /**
     * @Description: 返回当月的第一天
     * <AUTHOR>
     * @Date 2021/11/4 18:09
     */
    public static Date getFirstDayOfMonth(Date date) {
        return DateUtils.getFirstDayOfMonth(DateUtils.getYear(date), DateUtils.getMonth(date));
    }


    /**
     * 获取两个时间内所包含的所有天（或所有月份）
     * 日期格式应保持一致
     * 类型为日时,格式为yyyy-MM-dd以上
     * 类型为月时,格式为yyyy-MM以上
     *
     * @return java.util.List<java.lang.String>
     * @paramter timeStart
     * @paramter timeEnd
     * @paramter type 统计类型(D:日;M:月)
     * <AUTHOR>
     * @Date 16:00 2022/3/15
     **/
    public static List<String> getDates(String timeStart, String timeEnd, String type) {
        List<String> dates = new LinkedList<>();
        Date start = DateUtils.toDate(timeStart);
        Date end = DateUtils.toDate(timeEnd);

        if (BizConst.TYPE_DAY.equals(type)) {
            dates.add(timeStart.substring(0, 10));
            Date end1 = DateUtils.addDay(start, 1);
            while (DateUtils.compare(end1, end) < 0) {
                dates.add(DateUtils.toStr(end1, YMD));
                end1 = DateUtils.addDay(end1, 1);
            }
        }
        if (BizConst.TYPE_MONTH.equals(type)) {
            dates.add(timeStart.substring(0, 7));
            Date end1 = DateUtils.addMonth(start, 1);
            while (DateUtils.compare(end1, end) < 0) {
                dates.add(DateUtils.toStr(end1, YM));
                end1 = DateUtils.addMonth(end1, 1);
            }
        }
        return dates;
    }

    /**
     * 将日期格式的字符串转换为 yyyy-MM-dd HH:mm:ss格式的字符串
     * 原格式不应小于yyyy
     *
     * @return java.lang.String
     * @paramter strDate
     * <AUTHOR>
     * @Date 16:08 2022/3/12
     **/
    public static String formatYMD_HMS(String strDate) {
        if (StringUtils.isEmpty(strDate)) {
            throw new RuntimeException("时间字符串不能为空");
        }
        strDate = strDate.trim();
        if (strDate.matches("\\d{4}")) {
            strDate += "-01-01 00:00:00";
        } else if (strDate.matches("\\d{4}-\\d{1,2}")) {
            strDate += "-01 00:00:00";
        } else if (strDate.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
            strDate += " 00:00:00";
        } else if (strDate.matches("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}")) {
            strDate += ":00";
        }

        return strDate;
    }

    /**
     * 获取传入分钟的毫秒数
     *
     * @param minute 分钟数
     * @return long
     * <AUTHOR>
     * @Date 9:49 2022/6/7
     **/
    public static long getMinuteMSeconds(int minute) {
        return minute * ONE_MINUTE;
    }

    /**
     * 对时间字符串添加相应的天数
     *
     * @param date      需要添加天数的时间字符串
     * @param day       需要添加的天数
     * @param formatter 字符串的格式
     * @return java.lang.String
     * <AUTHOR>
     * @Date 17:51 2022/6/30
     **/
    public static String addDay(String date, int day, String formatter) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(formatter);
        LocalDateTime localDateTime = LocalDateTime.parse(date, dateTimeFormatter);
        localDateTime = localDateTime.plusDays(day);
        return localDateTime.format(dateTimeFormatter);
    }

    /**
     * 将秒数转为分秒(122:32)格式
     *
     * @param seconds 秒数
     * @return java.lang.String
     * <AUTHOR>
     * @Date 16:47 2022/10/10
     **/
    public static String secondConvertToMinutesSecond(int seconds) {
        int minutes = seconds / 60;
        int second = seconds % 60;
        String minutesStr = minutes < 10 ? "0" + minutes : String.valueOf(minutes);
        String secondStr = second < 10 ? "0" + second : String.valueOf(second);
        return minutesStr + ":" + secondStr;
    }

    /**
     * 将指定格式的日期字符串转换为另一种格式的日期字符串
     *
     * @param dateStr 日期字符串，格式为 yyyy-MM-dd HH:mm:ss 或者 EEE MMM dd HH:mm:ss zzz yyyy
     * @return 转换后的日期字符串，格式为 yyyy-MM-dd HH:mm:ss
     */
    public static String getCompatibleDateTime(String dateStr) {
        // 定义两个日期格式化器，一个用于解析 yyyy-MM-dd HH:mm:ss 格式的日期字符串，另一个用于解析 EEE MMM dd HH:mm:ss zzz yyyy 格式的日期字符串
        DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat dateFormat2 = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
        String formattedDate = "";

        try {
            Date dateTime;

            // 如果日期字符串包含 "-"，则说明它是 yyyy-MM-dd HH:mm:ss 格式的日期字符串
            if (dateStr.contains("-")) {
                formattedDate = dateStr;

            // 否则，说明它是 EEE MMM dd HH:mm:ss zzz yyyy 格式的日期字符串
            } else {

                // 解析日期字符串为 Date 对象
                dateTime = dateFormat2.parse(dateStr);

                // 将 Date 对象格式化为 yyyy-MM-dd HH:mm:ss 格式的日期字符串
                formattedDate = dateFormat1.format(dateTime);
            }
        } catch (ParseException e) {
            logger.error("日期格式转换异常，原始日期字符串为：" + dateStr, e);
        }

        logger.info("格式化后的日期为：" + formattedDate);
        return formattedDate;
    }

    /**
     * 返回给定时间字符串数组中第一个非空且有效的日期对象。
     *
     * @param times 包含时间字符串的数组，可以为空或包含null值
     * @return 第一个非空且有效的日期对象，如果数组为空或所有元素均为null或无效日期字符串，则返回null
     */
    public static Date getFirstNonNullDate(String... times) {
        if (ArrayUtils.isEmpty(times)) {
            return null;
        }

        for (String time : times) {
            if (time != null && !time.isEmpty() && !"0".equals(time)) {
                return DateUtils.toDate(time);
            }
        }

        // 记录告警日志
        logger.warn("所有日期字段均为 null，无法返回有效日期。");
        return null;
    }

    /**
     * 获取昨天23:59:59的日期信息
     *
     * @return 返回昨天23:59:59的日期信息
     */
    public static Date getYesterdayEndDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 获取今天00:00:00的日期信息
     *
     * @return 返回今天00:00:00的日期信息
     */
    public static Date getTodayStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }


}
