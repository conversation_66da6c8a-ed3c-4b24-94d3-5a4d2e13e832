package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.entity.es.CallRecording;
import com.niceloo.framework.json.JSONUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.*;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.IOException;

/**
 * 通话记录补充工具类
 */
public class CallRecordSupply {
    private static final String RECORD_INDEX_PREFIX = "call_record_";
    private static final String RECORDING_INDEX = "call_recording";
    private static final String START_TIME = "2024-09-01 08:00:00";
    private static final String END_TIME = "2024-09-17 23:00:00";
    private static final String CHANNEL_TYPE = "ZHZX";

    public static void main(String[] args) {
        try (RestHighLevelClient client = createClient()) {

            // 根据START_TIME和END_TIME解析出起始年份和结束年份
            int startYear = Integer.parseInt(START_TIME.substring(0, 4));
            int endYear = Integer.parseInt(END_TIME.substring(0, 4));

            int totalRecordsProcessed = 0; // 记录处理的总数

            // 根据起始年份和结束年份循环遍历索引
            for (int year = startYear; year <= endYear; year++) {
                // 确定起始月份和结束月份
                int startMonth = (year == startYear) ? Integer.parseInt(START_TIME.substring(5, 7)) : 1;
                int endMonth = (year == endYear) ? Integer.parseInt(END_TIME.substring(5, 7)) : 12;

                // 遍历月份
                for (int month = startMonth; month <= endMonth; month++) {
                    String index = RECORD_INDEX_PREFIX + String.format("%04d%02d", year, month);
                    System.out.println("Processing index: " + index);

                    // 构建查询条件
                    BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("serverFolder", ""))
                            .mustNot(QueryBuilders.termQuery("voiceSourceUrl", ""))
                            .must(QueryBuilders.rangeQuery("duration").gte(10))
                            .must(QueryBuilders.termQuery("dataCompleteStatus", "N"))
                            .must(QueryBuilders.rangeQuery("callTime").gte(START_TIME).lte(END_TIME));
                    if (StringUtils.isNotEmpty(CHANNEL_TYPE)) {
                        queryBuilder.must(QueryBuilders.termQuery("channelType", CHANNEL_TYPE));
                    }

                    // 构建搜索请求
                    SearchRequest searchRequest = new SearchRequest(index);
                    SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().query(queryBuilder);
                    sourceBuilder.size(2000); // 设置每次搜索的记录数
                    searchRequest.source(sourceBuilder);
                    searchRequest.scroll(TimeValue.timeValueMinutes(30L)); // 设置滚动超时时间为30分钟

                    // 执行搜索请求并遍历结果
                    SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
                    String scrollId = response.getScrollId();
                    BulkRequest bulkRequest = new BulkRequest();

                    while (response.getHits().getHits().length > 0) {
                        for (SearchHit hit : response.getHits().getHits()) {
                            totalRecordsProcessed++; // 增加处理的记录数
                            String callId = hit.getId();
                            String voiceSourceUrl = hit.getSourceAsMap().get("voiceSourceUrl").toString();
                            Integer duration = (Integer) hit.getSourceAsMap().get("duration");
                            String channelType = hit.getSourceAsMap().get("channelType").toString();

                            // 在call_recording索引中查找对应的记录
                            SearchRequest recordingSearchRequest = new SearchRequest(RECORDING_INDEX);
                            SearchSourceBuilder recordingSourceBuilder = new SearchSourceBuilder()
                                    .query(QueryBuilders.termQuery("callId", callId));
                            recordingSearchRequest.source(recordingSourceBuilder);

                            SearchResponse recordingResponse = client.search(recordingSearchRequest, RequestOptions.DEFAULT);
                            if (recordingResponse.getHits().getTotalHits() == 0) {
                                // call_recording中不存在该记录，生成新的记录
                                String newRecording = generateNewRecordingRecord(callId, voiceSourceUrl, duration, channelType, index);
                                bulkRequest.add(new IndexRequest(RECORDING_INDEX, "_doc", callId).source(newRecording, XContentType.JSON));
                            }
                        }

                        // 输出处理进度
                        System.out.println("Processed " + totalRecordsProcessed + " records so far...");

                        // 获取下一页结果
                        SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                        scrollRequest.scroll(TimeValue.timeValueMinutes(30L));
                        response = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                        scrollId = response.getScrollId();
                    }

                    // 执行批量索引请求
                    if (bulkRequest.numberOfActions() > 0) {
                        BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
                        System.out.println("Bulk request completed with " + bulkResponse.getItems().length + " items.");
                    }

                    // 清除滚动上下文
                    ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
                    clearScrollRequest.addScrollId(scrollId);
                    ClearScrollResponse clearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
                    boolean succeeded = clearScrollResponse.isSucceeded();
                    System.out.println("Scroll context cleared: " + succeeded);
                }
            }
            System.out.println("Total records processed: " + totalRecordsProcessed);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建Elasticsearch客户端。
     *
     * @return RestHighLevelClient
     */
    private static RestHighLevelClient createClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T"));

        return new RestHighLevelClient(
                RestClient.builder(new HttpHost("es-cn-8ex3wgtqb000755wp.public.elasticsearch.aliyuncs.com", 9200, "http"))
                        .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }

    /**
     * 生成新的通话录音记录。
     *
     * @param callId         通话ID
     * @param voiceSourceUrl 录音源URL
     * @param duration       通话时长
     * @param channelType    通道类型
     * @param indexName          索引
     * @return 新的通话录音记录的JSON字符串
     */
    private static String generateNewRecordingRecord(String callId, String voiceSourceUrl, int duration, String channelType, String indexName) {

        // 构建新的call_recording记录的JSON对象
        CallRecording callRecording = CallRecording.initialize();
        callRecording.setCallId(callId);
        String date = RecordUtil.getDateByIndexName(indexName);
        callRecording.setDate(date);
        callRecording.setVoiceSourceUrl(voiceSourceUrl);
        callRecording.setChannelType(channelType);
        callRecording.setDuration(duration);

        // 将JSON对象转换为字符串
        return JSONUtils.toJSONString(callRecording);
    }
}