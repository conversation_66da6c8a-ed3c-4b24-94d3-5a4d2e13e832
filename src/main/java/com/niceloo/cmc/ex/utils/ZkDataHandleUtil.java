package com.niceloo.cmc.ex.utils;

import com.niceloo.framework.utils.DateUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ZkDataHandleUtil {

    public static void main(String[] args) {
        String indexPrefix = "call_record_";
        String indexYearMonth = "202405";
        String channelTypeOfZk = "ZK";
        String channelTypeOfYp = "TQ";
        String startTime = "2024-05-16 00:00:00";
        String endTime = "2024-05-17 00:00:00";
        String remarks = "zk数据处理_tq录音补充";

        // 设置Elasticsearch集群的连接信息
        RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        new HttpHost("**************", 9200, "http")
                ).setHttpClientConfigCallback(httpClientBuilder ->
                        httpClientBuilder
                                .setDefaultCredentialsProvider(new BasicCredentialsProvider() {
                                    @Override
                                    public Credentials getCredentials(AuthScope authScope) {
                                        return new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T");
                                    }
                                })
                )
        );

        try {
            SearchRequest searchRequest = new SearchRequest(indexPrefix + indexYearMonth);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 构建查询条件
            BoolQueryBuilder boolQueryBuilderOfZk = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("channelType", channelTypeOfZk))
                    .mustNot(QueryBuilders.termQuery("voiceSourceUrl", ""))
                    .must(QueryBuilders.termQuery("serverFolder", ""))
                    .must(QueryBuilders.termQuery("dataCompleteStatus", "N"))
                    .must(QueryBuilders.rangeQuery("callTime")
                            // 在 1 分钟范围内
                            .gte(startTime)
                            .lt(endTime))
                    .must(QueryBuilders.rangeQuery("duration")
                            // 通话时长大于 0
                            .gt(0));

            searchSourceBuilder.query(boolQueryBuilderOfZk);
            searchRequest.source(searchSourceBuilder);
            // 设置滚动超时时间为30秒
            searchRequest.scroll(TimeValue.timeValueMinutes(30L));

            // 设置批量大小
            int batchSize = 10000; // 每次滚动的大小
            searchSourceBuilder.size(batchSize);

            // 开始滚动搜索
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();
            List<Map<String, Object>> records = new ArrayList<>();

            // 遍历第一次滚动的结果
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                String callerUserId = (String) sourceAsMap.get("callerUserId");
                Integer zkDuration = (Integer) sourceAsMap.get("duration");

                // 获取 ZK 记录的 callTime
                String zkCallTimeString = (String) sourceAsMap.get("callTime");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime zkCallTime = LocalDateTime.parse(zkCallTimeString, formatter);

                // 对时间进行加减，转成字符串
                LocalDateTime zkCallTimePlusOneMinute = zkCallTime.plusMinutes(1);
                LocalDateTime zkCallTimeMinusOneMinute = zkCallTime.minusMinutes(1);
                String zkCallTimePlusOneMinuteString = zkCallTimePlusOneMinute.format(formatter);
                String zkCallTimeMinusOneMinuteString = zkCallTimeMinusOneMinute.format(formatter);

                // 构建 YP 查询条件
                BoolQueryBuilder boolQueryBuilderOfYp = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("channelType", channelTypeOfYp))
                        .must(QueryBuilders.termQuery("dataCompleteStatus", "Y"))
                        .mustNot(QueryBuilders.termQuery("serverFolder", ""))
                        .must(QueryBuilders.termQuery("callerUserId", callerUserId))
                        .mustNot(QueryBuilders.termQuery("field1", ""))
                        .must(QueryBuilders.rangeQuery("callTime")
                                // 在 1 分钟范围内
                                .gte(zkCallTimeMinusOneMinuteString)
                                .lte(zkCallTimePlusOneMinuteString))
                        .must(QueryBuilders.rangeQuery("duration")
                                // 在 1 分钟范围内
                                .gte(zkDuration - 60)
                                .lte(zkDuration + 60));

                // 执行 YP 查询
                SearchRequest ypSearchRequest = new SearchRequest(indexPrefix + indexYearMonth);
                SearchSourceBuilder ypSearchSourceBuilder = new SearchSourceBuilder();
                ypSearchSourceBuilder.query(boolQueryBuilderOfYp);
                ypSearchRequest.source(ypSearchSourceBuilder);

                SearchResponse ypSearchResponse = client.search(ypSearchRequest, RequestOptions.DEFAULT);
                SearchHits ypHits = ypSearchResponse.getHits();

                if (ypHits.getTotalHits() > 0) {
                    // 找到匹配的 YP 记录，替换字段并修改 dataCompleteStatus
                    Map<String, Object> ypSourceAsMap = ypHits.getAt(0).getSourceAsMap();
                    String ypServerFolder = (String) ypSourceAsMap.get("serverFolder");
                    String ypField1 = (String) ypSourceAsMap.get("field1");
                    String modifiedTime = DateUtils.getNowDString(DateUtil.YMD_HMS);

                    UpdateRequest updateRequest = new UpdateRequest(indexPrefix + indexYearMonth, "_doc", hit.getId());
                    updateRequest.doc("serverFolder", ypServerFolder, "field1", ypField1, "dataCompleteStatus", "Y", "modifiedTime", modifiedTime, "remarks", remarks);

                    client.update(updateRequest, RequestOptions.DEFAULT);
                }

                records.add(sourceAsMap);
            }

            // 滚动搜索结果的下一页数据
            while (searchResponse.getHits().getHits().length != 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);

                // 设置滚动超时时间为30秒
                scrollRequest.scroll(TimeValue.timeValueMinutes(30L));
                scrollId = searchResponse.getScrollId();

                searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);

                // 遍历滚动的结果
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                    String callerUserId = (String) sourceAsMap.get("callerUserId");

                    // 获取 ZK 记录的 callTime
                    String zkCallTimeString = (String) sourceAsMap.get("callTime");
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime zkCallTime = LocalDateTime.parse(zkCallTimeString, formatter);

                    // 对时间进行加减，转成字符串
                    LocalDateTime zkCallTimePlusOneMinute = zkCallTime.plusMinutes(1);
                    LocalDateTime zkCallTimeMinusOneMinute = zkCallTime.minusMinutes(1);
                    String zkCallTimePlusOneMinuteString = zkCallTimePlusOneMinute.format(formatter);
                    String zkCallTimeMinusOneMinuteString = zkCallTimeMinusOneMinute.format(formatter);

                    // 构建 YP 查询条件
                    BoolQueryBuilder boolQueryBuilderOfYp = QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("channelType", channelTypeOfYp))
                            .must(QueryBuilders.termQuery("dataCompleteStatus", "Y"))
                            .mustNot(QueryBuilders.termQuery("serverFolder", ""))
                            .must(QueryBuilders.termQuery("callerUserId", callerUserId))
                            .mustNot(QueryBuilders.termQuery("field1", ""))
                            .must(QueryBuilders.rangeQuery("callTime")
                                    // 在 1 分钟范围内
                                    .gte(zkCallTimeMinusOneMinuteString)
                                    .lte(zkCallTimePlusOneMinuteString));

                    // 执行 YP 查询
                    SearchRequest ypSearchRequest = new SearchRequest(indexPrefix + indexYearMonth);
                    SearchSourceBuilder ypSearchSourceBuilder = new SearchSourceBuilder();
                    ypSearchSourceBuilder.query(boolQueryBuilderOfYp);
                    ypSearchRequest.source(ypSearchSourceBuilder);

                    SearchResponse ypSearchResponse = client.search(ypSearchRequest, RequestOptions.DEFAULT);
                    SearchHits ypHits = ypSearchResponse.getHits();

                    if (ypHits.getTotalHits() > 0) {
                        // 找到匹配的 YP 记录，替换字段并修改 dataCompleteStatus
                        Map<String, Object> ypSourceAsMap = ypHits.getAt(0).getSourceAsMap();
                        String ypServerFolder = (String) ypSourceAsMap.get("serverFolder");
                        String ypField1 = (String) ypSourceAsMap.get("field1");
                        String modifiedTime = DateUtils.getNowDString(DateUtil.YMD_HMS);

                        UpdateRequest updateRequest = new UpdateRequest(indexPrefix + indexYearMonth, "_doc", hit.getId());
                        updateRequest.doc("serverFolder", ypServerFolder, "field1", ypField1, "dataCompleteStatus", "Y", "modifiedTime", modifiedTime, "remarks", remarks);

                        client.update(updateRequest, RequestOptions.DEFAULT);
                    }

                    records.add(sourceAsMap);
                }
            }

            // 导出数据到文件
            String fileName = "export_" + indexYearMonth + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".txt";
            writeToFile(records, fileName);

            System.out.println("导出成功，文件名：" + fileName);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 关闭Elasticsearch客户端连接
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static void writeToFile(List<Map<String, Object>> records, String fileName) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileName))) {
            for (Map<String, Object> record : records) {
                writer.write(record.toString());
                writer.newLine();
            }
        }
    }
}

