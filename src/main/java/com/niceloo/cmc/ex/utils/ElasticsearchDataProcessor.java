package com.niceloo.cmc.ex.utils;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.range.RangeAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

public class ElasticsearchDataProcessor {
    private static final String ELASTICSEARCH_HOST = "localhost";
    private static final int ELASTICSEARCH_PORT = 9200;
    private static final String EXCEL_DATA_FILE = "customer_data.xlsx";

    private static final String INDEX_PREFIX = "call_record_";
    private static final String DURATION_FIELD = "duration";
    private static final String CALL_TIME_FIELD = "callTime";
    private static final String RECEIVER_USER_ID_FIELD = "reciverUserId";

    // 定义聚合名称常量
    private static final String DURATION_RANGE_AGG = "duration_range";
    private static final String DURATION_SUM_AGG = "duration_sum";


    public static void main(String[] args) {
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(new HttpHost(ELASTICSEARCH_HOST, ELASTICSEARCH_PORT, "http")))) {
            // 读取 Excel 文件数据
            Map<String, LocalDateTime> customerFinishTime = readExcelData(EXCEL_DATA_FILE);

            // 遍历每个月的索引,并针对 Excel 中的客户进行统计
            for (int month = 1; month <= 12; month++) {
                String indexName = INDEX_PREFIX + String.format("%04d%02d", 2023, month);
                statisticsForIndex(client, indexName, customerFinishTime);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建一个RestHighLevelClient对象。
     *
     * @return 返回一个RestHighLevelClient对象，该对象与ElasticSearch服务进行通信。
     */
    private static RestHighLevelClient createClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T"));

        return new RestHighLevelClient(
                RestClient.builder(new HttpHost("**************", 9200, "http"))
                        .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }

    /**
     * 对指定索引进行统计，根据reciverUserId进行聚合查询，根据通话时长进行范围聚合，并计算通话时长总和
     *
     * @param client         RestHighLevelClient对象，用于与Elasticsearch进行交互
     * @param indexName      要统计的索引名称
     * @param customerFinishTime 用户完成时间Map，键为reciverUserId，值为对应的完成时间
     * @throws IOException IO异常
     */
    private static void statisticsForIndex(RestHighLevelClient client, String indexName, Map<String, LocalDateTime> customerFinishTime) throws IOException {
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        // 1. 根据 reciverUserId 进行查询聚合
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        for (Map.Entry<String, LocalDateTime> entry : customerFinishTime.entrySet()) {
            String custId = entry.getKey();
            boolQueryBuilder.should(QueryBuilders.termQuery(RECEIVER_USER_ID_FIELD, custId));
        }
        sourceBuilder.query(boolQueryBuilder);

        // 2. 根据通话时长进行范围聚合
        RangeAggregationBuilder rangeAgg = AggregationBuilders.range(DURATION_RANGE_AGG)
                .field(DURATION_FIELD)
                .addRange(0, 60)
                .addRange(60, 300)
                .addRange(300, 3600)
                .addRange(3600, Double.MAX_VALUE);
        sourceBuilder.aggregation(rangeAgg);

        // 3. 通话时长求和聚合，并命名为 DURATION_SUM_AGG 以避免命名冲突
        SumAggregationBuilder sumAgg = AggregationBuilders.sum(DURATION_SUM_AGG).field(DURATION_FIELD);
        // 将求和聚合添加到范围聚合中
        rangeAgg.subAggregation(sumAgg);

        // 执行查询并处理结果
        SearchResponse searchResponse = client.search(searchRequest.source(sourceBuilder), RequestOptions.DEFAULT);

        Aggregations aggs = searchResponse.getAggregations();
        Range durationRange = aggs.get(DURATION_RANGE_AGG);
        for (Range.Bucket bucket : durationRange.getBuckets()) {
            // 使用 Double 而不是 Integer 来避免类型转换问题
            Double from = Double.parseDouble(bucket.getFromAsString());
            Double to = Double.parseDouble(bucket.getToAsString());
            long count = bucket.getDocCount();
            Sum sum = bucket.getAggregations().get(DURATION_SUM_AGG);
            double totalDuration = sum.getValue();
            System.out.printf("Duration range [%f, %f): %d calls, total duration: %f seconds%n", from, to, count, totalDuration);
        }
    }


    /**
     * 读取Excel数据，返回客户完成时间映射表
     *
     * @param fileName Excel文件路径
     * @return 客户完成时间映射表，键为客户ID，值为完成时间
     */
    private static Map<String, LocalDateTime> readExcelData(String fileName) {
        Map<String, LocalDateTime> customerFinishTime = new HashMap<>();

        try (FileInputStream fis = new FileInputStream(fileName);
             Workbook workbook = new XSSFWorkbook(fis)) {
            Sheet sheet = workbook.getSheetAt(0);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过表头行

                Cell custIdCell = row.getCell(0);
                Cell finishTimeCell = row.getCell(1);

                if (custIdCell != null && finishTimeCell != null) {
                    String custId = custIdCell.getStringCellValue();
                    LocalDateTime finishTime = LocalDateTime.parse(finishTimeCell.getStringCellValue(), formatter);
                    customerFinishTime.put(custId, finishTime);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return customerFinishTime;
    }
}