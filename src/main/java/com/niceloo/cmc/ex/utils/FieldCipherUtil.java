package com.niceloo.cmc.ex.utils;

import com.niceloo.framework.utils.StringUtils;
import com.niceloo.plugin.sdk.lang.crypto.constant.CipherDesc;
import com.niceloo.plugin.sdk.lang.util.Aes;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES加解密工具类
 *
 * <AUTHOR>
 * @Date 16:34 2021/9/28
 **/
public final class FieldCipherUtil {

    private static final String AES_KEY = "20fIjBkxcYUeOifaGzZ5HtUYYhh9XjJBfyC2s/t671w=";
    private static final String AES_IV = "N0wgg6Tzo5vQS+jX6V7K1w==";
    private static final Aes AES = buildAes();

    /**
     * AES加密，参数为空或加密出错返回原参数,可以多次加密
     *
     * @param arg 明文
     * @return java.lang.String
     * <AUTHOR>
     * @Date 17:25 2021/9/28
     **/
    public static String encrypt(String arg) {
        if (StringUtils.isEmpty(arg)) {
            return arg;
        }
        try {
            return Base64.getEncoder().encodeToString(AES.encrypt(arg.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            return arg;
        }
    }

    /**
     * 加密:只支持一次加密,传入密文返回原参数
     * 参数为空或加密出错返回原参数
     * @paramter arg
     * @return java.lang.String
     * <AUTHOR>
     * @Date 11:33 2022/3/12
     **/
    public static String oneEncrypt(String arg) {
        if (StringUtils.isEmpty(arg)) {
            return arg;
        }
        if (checkEncrypt(arg)) {
            return arg;
        }
        try {
            return Base64.getEncoder().encodeToString(AES.encrypt(arg.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            return arg;
        }
    }

    /**
     * AES解密，参数为空或解密出错返回原参数
     *
     * @param arg 密文
     * @return java.lang.String
     * <AUTHOR>
     * @Date 17:24 2021/9/28
     **/
    public static String decrypt(String arg) {
        if (StringUtils.isEmpty(arg)) {
            return arg;
        }
        try {
            return new String(AES.decrypt(Base64.getDecoder().decode(arg)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            return arg;
        }
    }

    /**
     * 检验是否被加密,传参为空返回false
     *
     * @param arg 需要校验的字符串
     * @return boolean 已加密返回true
     * <AUTHOR>
     * @Date 16:55 2021/9/28
     **/
    public static boolean checkEncrypt(String arg) {
        if (StringUtils.isEmpty(arg)) {
            return false;
        }
        // 进行解密,解密出错会返回原参数
        String decrypt = FieldCipherUtil.decrypt(arg);
        // 如果解密能成功说明被加过密
        return !decrypt.equals(arg);
    }

    private static Aes buildAes() {
        CipherDesc desc = CipherDesc.AES_256_PKCS7Padding;
        return new Aes(Base64.getDecoder().decode(AES_KEY), Base64.getDecoder().decode(AES_IV), desc);
    }

    public static void main(String[] args) {
        System.out.println(decrypt("wgLQ9NlxFV1YpHX6K6aTyA=="));
        System.out.println(encrypt("15379625086"));
        System.out.println(encrypt("雷春兰"));
    }
}
