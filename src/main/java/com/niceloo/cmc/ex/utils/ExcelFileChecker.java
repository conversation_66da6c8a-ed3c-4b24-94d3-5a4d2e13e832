package com.niceloo.cmc.ex.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

public class ExcelFileChecker {
    private static final int THREAD_POOL_SIZE = 10; // 增加线程池大小
    private static final String VOICE_SOURCE_URL_COLUMN_NAME = "voiceSourceUrl"; // 定义变量

    public static void main(String[] args) {
        String filePath = "D:\\git2\\communicationcenter\\亿讯未下载录音_20250529_01.xlsx";
        String sheetName = "Sheet";
        int voiceSourceUrlColumnIndex = -1;
        int appendColumnIndex; // 追加列，后面动态获取

        try {
            FileInputStream input = new FileInputStream(filePath);
            Workbook workbook = new XSSFWorkbook(input);
            Sheet sheet = workbook.getSheet(sheetName);

            // 查找标题行，假设标题在第一行
            Row headerRow = sheet.getRow(0);

            // 遍历标题行，查找voiceSourceUrl列标索引位置
            for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null && VOICE_SOURCE_URL_COLUMN_NAME.equals(cell.getStringCellValue())) { // 使用变量
                    voiceSourceUrlColumnIndex = i; // 找到索引
                    break;
                }
            }

            if (voiceSourceUrlColumnIndex == -1) {
                throw new RuntimeException("Column '" + VOICE_SOURCE_URL_COLUMN_NAME + "' not found."); // 使用变量
            }

            // 动态获取最后一列的索引
            appendColumnIndex = headerRow.getPhysicalNumberOfCells(); // 获取最后一列的索引
            int rowCount = sheet.getLastRowNum() - sheet.getFirstRowNum();

            ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
            List<Future<UrlCheckResult>> futures = new ArrayList<>();

            for (int i = 1; i <= rowCount; i++) { // 从第二行开始循环，跳过表头
                Row row = sheet.getRow(i);
                String voiceSourceUrl = row.getCell(voiceSourceUrlColumnIndex).getStringCellValue();

                Callable<UrlCheckResult> urlChecker = new UrlChecker(voiceSourceUrl);
                Future<UrlCheckResult> future = executorService.submit(urlChecker);
                futures.add(future);

                // 打印进度信息
                if (i % 500 == 0) {
                    System.out.println("Submitted " + i + " rows out of " + rowCount);
                }
            }

            for (int i = 1; i <= rowCount; i++) {
                Future<UrlCheckResult> future = futures.get(i - 1);
                Row row = sheet.getRow(i);

                Cell field6Cell = row.getCell(appendColumnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                Cell field6PlusCellIndex = row.createCell(appendColumnIndex + 1); // 创建新的单元格
                UrlCheckResult urlCheckResult = future.get();

                if (urlCheckResult.isUrlAccessible()) {
                    field6Cell.setCellValue("Url Accessible");
                    field6PlusCellIndex.setCellValue(formatFileSize(urlCheckResult.getFileSize()));
                } else {
                    field6Cell.setCellValue("Url Not Accessible");
                }

                // 打印进度信息
                if (i % 500 == 0) {
                    System.out.println("Processed " + i + " rows out of " + rowCount);
                }
            }

            executorService.shutdown();
            FileOutputStream output = new FileOutputStream(filePath);
            workbook.write(output);
            output.close();
            workbook.close();
            System.out.println("Field6 updated successfully.");
        } catch (IOException | InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
    }

    private static class UrlChecker implements Callable<UrlCheckResult> {
        private String urlString;

        public UrlChecker(String urlString) {
            this.urlString = urlString;
        }

        /**
         * 调用此方法以检查URL是否有效并返回UrlCheckResult对象
         *
         * @return UrlCheckResult对象，包含URL是否有效和文件大小信息
         * @throws IOException 如果在打开连接或获取响应时发生错误，则抛出此异常
         */
        @Override
        public UrlCheckResult call() {
            try {
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("HEAD"); // 使用HEAD方法,不下载整个文件
//                System.out.println("Checking URL: " + urlString);
                connection.connect();

                int responseCode = connection.getResponseCode();
                if (responseCode >= 200 && responseCode < 300) {
                    long fileSize = connection.getContentLengthLong();
                    if (fileSize > 0) {
                        return new UrlCheckResult(true, fileSize); // 可以下载
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

            return new UrlCheckResult(false, 0);
        }
    }

    private static class UrlCheckResult {
        private boolean isUrlAccessible;
        private long fileSize;

        public UrlCheckResult(boolean isUrlAccessible, long fileSize) {
            this.isUrlAccessible = isUrlAccessible;
            this.fileSize = fileSize;
        }

        public boolean isUrlAccessible() {
            return isUrlAccessible;
        }

        public long getFileSize() {
            return fileSize;
        }
    }

    /**
     * 格式化文件大小，返回字符串形式
     *
     * @param fileSize 文件大小，单位为字节
     * @return 返回格式化后的文件大小字符串，格式为 "XX.XX 单位"
     */
    private static String formatFileSize(long fileSize) {
        DecimalFormat df = new DecimalFormat("#.##");
        double size = fileSize;
        String unit = "B";

        if (size > 1024) {
            size /= 1024;
            unit = "KB";
        }
        if (size > 1024) {
            size /= 1024;
            unit = "MB";
        }
        if (size > 1024) {
            size /= 1024;
            unit = "GB";
        }

        return df.format(size) + " " + unit;
    }
}
