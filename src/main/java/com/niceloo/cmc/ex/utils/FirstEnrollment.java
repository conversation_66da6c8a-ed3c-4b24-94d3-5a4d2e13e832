package com.niceloo.cmc.ex.utils;

import lombok.Data;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Reader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class FirstEnrollment {

    private static final int BATCH_SIZE = 1000; // 每批处理的custId数量

    private static RestHighLevelClient createClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "W86mXT4x1t2T"));

        return new RestHighLevelClient(
                RestClient.builder(new HttpHost("es-cn-8ex3wgtqb000755wp.public.elasticsearch.aliyuncs.com", 9200, "http"))
                        .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
        );
    }

    public static void main(String[] args) {
        String csvFilePath = "D:\\data\\产品\\吕书伟\\6月后仅二建新客户_202412051511.csv";
        List<CustRecord> custRecords = readCustIdsFromCsv(csvFilePath);
        List<String[]> results = new ArrayList<>();

        try (RestHighLevelClient client = createClient()) {
            for (int month = 202406; month <= 202412; month++) {
                String indexName = "call_record_" + month;

                for (int i = 0; i < custRecords.size(); i += BATCH_SIZE) {
                    int end = Math.min(i + BATCH_SIZE, custRecords.size());
                    List<CustRecord> batch = custRecords.subList(i, end);
                    results.addAll(searchElasticSearch(client, indexName, batch));

                    System.out.printf("Processed %d out of %d custIds for index %s%n", end, custRecords.size(), indexName);
                }
            }

            // 标记最早记录
            markFirstRecords(results);

            // 输出结果到Excel
            writeResultsToExcel("output.xlsx", results);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static List<CustRecord> readCustIdsFromCsv(String csvFilePath) {
        List<CustRecord> custRecords = new ArrayList<>();
        try (Reader reader = Files.newBufferedReader(Paths.get(csvFilePath));
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {
            for (CSVRecord csvRecord : csvParser) {
                String custId = csvRecord.get("custId");
                String projectId = csvRecord.get("projectId");
                String gradestatus = csvRecord.get("gradestatus");
                custRecords.add(new CustRecord(custId, projectId, gradestatus));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return custRecords;
    }

    private static List<String[]> searchElasticSearch(RestHighLevelClient client, String indexName, List<CustRecord> custRecords) throws IOException {
        List<String[]> results = new ArrayList<>();
        List<String> custIds = new ArrayList<>();

        for (CustRecord custRecord : custRecords) {
            custIds.add(custRecord.getCustId());
        }

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("reciverUserId", custIds))
                .must(QueryBuilders.rangeQuery("duration").gt(300))
                .must(QueryBuilders.termQuery("dataCompleteStatus", "Y"));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.fetchSource(new String[]{"reciverUserId", "callTime", "duration", "channelType", "serverFolder"}, null);

        SearchRequest searchRequest = new SearchRequest(indexName);
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        searchResponse.getHits().forEach(hit -> {
            String reciverUserId = hit.getSourceAsMap().get("reciverUserId").toString();
            String callTime = hit.getSourceAsMap().get("callTime").toString();
            String duration = hit.getSourceAsMap().get("duration").toString();
            String channelType = hit.getSourceAsMap().get("channelType").toString();
            String serverFolder = hit.getSourceAsMap().get("serverFolder").toString();

            results.add(new String[]{reciverUserId, callTime, duration, channelType, serverFolder});
        });

        return results;
    }

    private static void markFirstRecords(List<String[]> results) {
        Map<String, String[]> earliestRecords = new HashMap<>();

        // 找到每个custId最早的记录
        for (String[] result : results) {
            String custId = result[0];
            String callTime = result[1];
            if (!earliestRecords.containsKey(custId) || earliestRecords.get(custId)[1].compareTo(callTime) > 0) {
                earliestRecords.put(custId, result);
            }
        }

        // 更新原始结果列表
        for (String[] record : results) {
            String custId = record[0];
            if (earliestRecords.containsKey(custId) && earliestRecords.get(custId) == record) {
                record = Arrays.copyOf(record, record.length + 1);
                record[record.length - 1] = "Y"; // 标记为首次报班
                // 需要将更新后的记录放回结果中
                int index = results.indexOf(earliestRecords.get(custId));
                results.set(index, record);
            } else {
                record = Arrays.copyOf(record, record.length + 1);
                record[record.length - 1] = ""; // 非首次的记录留空
            }
        }
    }

    private static void writeResultsToExcel(String outputFilePath, List<String[]> results) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Results");

            Row headerRow = sheet.createRow(0);
            String[] headers = {"reciverUserId", "callTime", "duration", "channelType", "serverFolder", "firstReport"};
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
            }

            for (int i = 0; i < results.size(); i++) {
                Row row = sheet.createRow(i + 1);
                String[] result = results.get(i);
                for (int j = 0; j < result.length; j++) {
                    row.createCell(j).setCellValue(result[j]);
                }
            }

            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                workbook.write(fos);
            }

            System.out.println("Results written to " + outputFilePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Data
    static class CustRecord {
        private final String custId;
        private final String projectId;
        private final String gradestatus;

        public CustRecord(String custId, String projectId, String gradestatus) {
            this.custId = custId;
            this.projectId = projectId;
            this.gradestatus = gradestatus;
        }
    }
}