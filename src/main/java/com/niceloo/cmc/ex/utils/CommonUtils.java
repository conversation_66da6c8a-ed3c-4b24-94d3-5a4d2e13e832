package com.niceloo.cmc.ex.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringEscapeUtils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

/**
 * 通用的工具类
 *
 * <AUTHOR>
 * @since 2022-11-05 17:24
 */
public class CommonUtils {


    /**
     * 字符串转码
     *
     * @param str 原字符串
     * @return java.lang.String
     * <AUTHOR>
     * @since 17:09 2022/11/5
     **/
    public static String encode(String str) {
        Base64.Encoder encoder = Base64.getEncoder();
        byte[] bytes = encoder.encode(str.getBytes());
        return new String(bytes, StandardCharsets.UTF_8);
    }

    /**
     * 对EXCEL的工作表名称合法处理,即移除`:\/?*[]`七个字符
     *
     * @param sheetName 原sheet名称
     * @return java.lang.String
     * <AUTHOR>
     * @since 10:34 2022/12/1
     **/
    public static String legalExcelSheetName(String sheetName) {
        String regex = "[/\\\\:?*\\[\\]]";
        return sheetName.replaceAll(regex, "");
    }

    /**
     * 对 JSON 消息体进行解析并将字段值进行 Unicode 解码。
     *
     * @param responseStr 返回的 JSON 消息体
     * @return 解码后的消息体
     */
    public static String decodeUnicodeInJson(String responseStr) {
        JSONObject jsonObject = JSON.parseObject(responseStr);
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                String decodedValue = StringEscapeUtils.unescapeJava((String) value);
                entry.setValue(decodedValue);
            }
        }
        return jsonObject.toJSONString();
    }

    public static boolean isAddressEncrypted(String address) {
        try {
            // 使用Base64解码录音地址
            byte[] decodedBytes = Base64.getDecoder().decode(address);
            // 将解码后的字节数组转换为字符串
            String decodedAddress = new String(decodedBytes, StandardCharsets.UTF_8);
            // 检查解码后的字符串是否符合明文地址的格式
            // 这里可以根据实际的录音地址格式进行匹配判断
            // 示例中使用了一个简单的判断条件
            return !decodedAddress.equals(address);
        } catch (IllegalArgumentException e) {
            // 解码失败，说明录音地址不是Base64编码的密文
            return false;
        }
    }
}
