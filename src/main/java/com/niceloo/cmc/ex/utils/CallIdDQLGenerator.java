package com.niceloo.cmc.ex.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

public class CallIdDQLGenerator {
    public static void main(String[] args) {
        String inputFilePath = "D:\\git2\\communicationcenter\\info.json"; // 输入文件路径
        String outputFilePath = "D:\\git2\\communicationcenter\\callid_dql_script.json"; // 输出文件路径
        try {
            // 读取文件内容
            String jsonContent = readFile(inputFilePath);
            // 解析 JSON 内容并生成 DQL
            String dqlScript = generateDQL(jsonContent);
            // 将 DQL 写入输出文件
            writeFile(outputFilePath, dqlScript);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String readFile(String filePath) throws IOException {
        StringBuilder contentBuilder = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                contentBuilder.append(line);
            }
        }
        return contentBuilder.toString();
    }

    private static String generateDQL(String jsonContent) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonContent);
        JsonNode hits = rootNode.path("hits").path("hits");

        Set<String> callIds = new HashSet<>();
        for (JsonNode hit : hits) {
            String callId = hit.path("_source").path("callId").asText();
            callIds.add(callId);
        }

        StringBuilder dqlScript = new StringBuilder();
        dqlScript.append("POST /call_record_202410,call_record_202411/_search\n");
        dqlScript.append("{ \"query\": { \"terms\": { \"callId\": [");

        // 构建 callId 列表
        for (String callId : callIds) {
            dqlScript.append("\"").append(callId).append("\",");
        }

        // 移除最后一个逗号并关闭 JSON 数组
        if (callIds.size() > 0) {
            dqlScript.setLength(dqlScript.length() - 1); // 去掉最后一个逗号
        }
        dqlScript.append("] } } }\n");

//        dqlScript.append("POST /call_record_202411/_search\n");
//        dqlScript.append("{ \"query\": { \"terms\": { \"callId\": [");
//
//        // 重复构建 callId 列表
//        for (String callId : callIds) {
//            dqlScript.append("\"").append(callId).append("\",");
//        }
//
//        // 移除最后一个逗号并关闭 JSON 数组
//        if (callIds.size() > 0) {
//            dqlScript.setLength(dqlScript.length() - 1); // 去掉最后一个逗号
//        }
//        dqlScript.append("] } } }\n");

        return dqlScript.toString();
    }

    private static void writeFile(String filePath, String content) throws IOException {
        try (FileWriter fileWriter = new FileWriter(filePath)) {
            fileWriter.write(content);
        }
    }
}