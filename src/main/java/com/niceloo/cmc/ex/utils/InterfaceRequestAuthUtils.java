package com.niceloo.cmc.ex.utils;

import com.niceloo.auth.service.AuthService;
import com.niceloo.cmc.ex.common.MenuCodeEnum;
import com.niceloo.cmc.ex.feign.AuthFeignClient;
import com.niceloo.cmc.ex.pojo.request.AuthBaseRequest;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;

import java.util.*;

import static com.niceloo.cmc.ex.common.BizConst.BRAND_YOULU;

/**
 * 接口请求权限校验工具类
 *
 * <AUTHOR>
 * @Date 2022-08-12 17:32
 */
public class InterfaceRequestAuthUtils {

    /**
     * 对查询类型的接口进行权限验证
     *
     * @param request  权限接口请求的参数
     * @param ngExpand 操作人
     * @param menuCode 菜单编码
     * <AUTHOR>
     * @Date 17:44 2022/8/12
     **/
    public static void interfaceAuthOfSelectType(AuthBaseRequest request, NgExpand ngExpand, String menuCode) {
        AuthService authService = SpringUtils.getBean(AuthService.class);
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        UcUser ucUser = ngExpand.getUcUser();
        List<String> dataPolicy = authService.getDatapolicy(menuCode, ucUser.getUserId());
        if (dataPolicy.contains("all")) {
            return;
        }
        // 仅包含mine==>只能检索自己的数据
        if (dataPolicy.size() == 1 && dataPolicy.contains("mine")) {
            String eeUserId = request.getEeUserId();
            if (!StringUtils.isEmpty(eeUserId) && !eeUserId.equals(ucUser.getUserId())) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选员工权限");
            }
            request.setEeUserId(ucUser.getUserId());
            return;
        }
        // 包含myArea==>只能检索自己所属大区下的外呼账号
        if (dataPolicy.contains("myArea")) {
            // 查询登录的员工在此菜单下的有权限分校
            List<String> schoolIds = authService.getSchoolIds(BRAND_YOULU, menuCode, ucUser.getUserId());
            //如果权限是本大区,但是获取的分校id列表是空的,表示他没有大区,按照'空'处理
            if (CollectionUtils.isEmpty(schoolIds)) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选数据权限");
            }
            if (StringUtils.isEmpty(request.getSchoolId())) {
                request.setSchoolId(String.join(",", schoolIds));
            } else {
                // 校验是否选择了无权限的分校
                String[] schoolIdArray = request.getSchoolId().split(",");
                Set<String> schoolIdSet = new HashSet<>(schoolIds);
                for (String schoolId : schoolIdArray) {
                    if (!schoolIdSet.contains(schoolId)) {
                        throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选分校权限");
                    }
                }
            }
            if (StringUtils.isNotEmpty(request.getDptId())) {
                // 查询登录的员工在此菜单下的有权限部门
                List<String> dptIds = authService.getDptIds(BRAND_YOULU, menuCode, ucUser.getUserId());
                if (CollectionUtils.isEmpty(dptIds)) {
                    throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选数据权限");
                }
                // 校验是否选择了无权限的部门
                String[] dptIdArray = request.getDptId().split(",");
                Set<String> dptIdSet = new HashSet<>(dptIds);
                for (String dptId : dptIdArray) {
                    if (!dptIdSet.contains(dptId)) {
                        throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选部门权限");
                    }
                }
            }
            return;
        }
        // 包含mySchool==>只能检索自己分校下的数据
        if (dataPolicy.contains("mySchool")) {
            String schoolId = request.getSchoolId();
            if (StringUtils.isNotEmpty(schoolId) && !schoolId.equals(ucUser.getSchoolId())) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选分校权限");
            }
            request.setSchoolId(ucUser.getSchoolId());
            return;
        }
        // 包含myDept==>只能检索自己部门下的数据
        if (dataPolicy.contains("myDept")) {
            String dptId = request.getDptId();
            List<String> dptIds = authService.getDptIds(BRAND_YOULU, menuCode, ucUser.getUserId());
            // 校验是否选择了无权限的部门
            if (StringUtils.isNotEmpty(dptId)) {
                String[] dptIdArray = dptId.split(",");
                Set<String> dptIdSet = new HashSet<>(dptIds);
                for (String dptIdParam : dptIdArray) {
                    if (!dptIdSet.contains(dptIdParam)) {
                        throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选部门权限");
                    }
                }
            }
            if (StringUtils.isEmpty(dptId)) {
                request.setDptId(String.join(",", dptIds));
            }
            return;
        }
        throw new ApplicationException(ApiErrorCodes.execute_failed, "未找到您的权限");
    }

    /**
     * 对指定类型的接口进行权限校验
     * 新网关新增的权限校验方法，用于校验用户是否有权限执行特定操作。该方法会根据用户的角色和菜单配置来决定是否允许访问。
     *
     * @param request 请求对象，包含请求数据
     * @param ngExpand 用户上下文对象，包含用户信息
     * @param menuCode 菜单代码，用于权限校验
     * @throws ApplicationException 如果用户没有相应权限，抛出异常
     */
    public static void interfaceAuthOfSelectTypeV2(AuthBaseRequest request, NgExpand ngExpand, String menuCode) {
        AuthService authService = SpringUtils.getBean(AuthService.class);
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        UcUser ucUser = ngExpand.getUcUser();
        List<String> dataPolicy = authService.getDatapolicy(menuCode, ucUser.getUserId());
        if (dataPolicy.contains("all")) {
            return;
        }
        // 仅包含mine==>只能检索自己的数据
        if (dataPolicy.size() == 1 && dataPolicy.contains("mine")) {
            String eeUserId = request.getEeUserId();
            if (!StringUtils.isEmpty(eeUserId) && !eeUserId.equals(ucUser.getUserId())) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选员工权限");
            }
            request.setEeUserId(ucUser.getUserId());
            return;
        }
        // 包含myArea==>只能检索自己所属大区下的外呼账号
        if (dataPolicy.contains("myArea")) {
            // 查询登录的员工在此菜单下的有权限分校
            List<String> schoolIds = authService.getSchoolIds(BRAND_YOULU, menuCode, ucUser.getUserId());
            //如果权限是本大区,但是获取的分校id列表是空的,表示他没有大区,按照'空'处理
            if (CollectionUtils.isEmpty(schoolIds)) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选数据权限");
            }
            if (StringUtils.isEmpty(request.getSchoolId())) {
                request.setSchoolId(String.join(",", schoolIds));
            } else {
                // 校验是否选择了无权限的分校
                String[] schoolIdArray = request.getSchoolId().split(",");
                Set<String> schoolIdSet = new HashSet<>(schoolIds);
                for (String schoolId : schoolIdArray) {
                    if (!schoolIdSet.contains(schoolId)) {
                        throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选分校权限");
                    }
                }
            }
            if (StringUtils.isNotEmpty(request.getDptId())) {
                // 查询登录的员工在此菜单下的有权限部门
                List<String> dptIds = authService.getDptIds(BRAND_YOULU, menuCode, ucUser.getUserId());
                if (CollectionUtils.isEmpty(dptIds)) {
                    throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选数据权限");
                }
                // 校验是否选择了无权限的部门
                String[] dptIdArray = request.getDptId().split(",");
                Set<String> dptIdSet = new HashSet<>(dptIds);
                for (String dptId : dptIdArray) {
                    if (!dptIdSet.contains(dptId)) {
                        throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选部门权限");
                    }
                }
            }
            return;
        }
        // 包含mySchool==>只能检索自己分校下的数据
        if (dataPolicy.contains("mySchool")) {
            String schoolId = request.getSchoolId();
            if (StringUtils.isNotEmpty(schoolId) && !schoolId.equals(ucUser.getSchoolId())) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选分校权限");
            }
            request.setSchoolId(ucUser.getSchoolId());
            return;
        }
        // 包含myDept==>只能检索自己部门下的数据
        if (dataPolicy.contains("myDept")) {
            String dptId = request.getDptId();
            List<String> dptIds = authService.getDptIds(BRAND_YOULU, menuCode, ucUser.getUserId());
            // 从传参的部门中过滤掉无权限的部门
            if (StringUtils.isNotEmpty(dptId)) {
                String[] dptIdArray = dptId.split(",");
                Set<String> dptIdSet = new HashSet<>(dptIds);
                List<String> validDptIds = new ArrayList<>();
                for (String dptIdParam : dptIdArray) {
                    if (dptIdSet.contains(dptIdParam)) {
                        validDptIds.add(dptIdParam);
                    }
                }
                // 过滤后如果没有有效的部门，则抛出权限异常
                if (validDptIds.isEmpty()) {
                    throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选部门权限");
                }
                // 更新请求中的部门ID为过滤后的有效部门
                request.setDptId(String.join(",", validDptIds));
            }
            if (StringUtils.isEmpty(dptId)) {
                request.setDptId(String.join(",", dptIds));
            }
            return;
        }
        throw new ApplicationException(ApiErrorCodes.execute_failed, "未找到您的权限");
    }


    /**
     * 对操作类型的接口进行权限验证(只适用于不随着员工的组织架构变化的资源)
     *
     * @param ngExpand    操作人
     * @param menuCode    菜单编码
     * @param authRequest 被操作的资源创建人相关id
     * <AUTHOR>
     * @Date 17:58 2022/8/12
     **/
    public static void interfaceAuthOfOperationType(NgExpand ngExpand, String menuCode, AuthBaseRequest authRequest) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        AuthService authService = SpringUtils.getBean(AuthService.class);
        AuthFeignClient authFeignClient = SpringUtils.getBean(AuthFeignClient.class);
        UcUser ucUser = ngExpand.getUcUser();
        // 是否有菜单权限校验[没有权限直接抛出异常]
        Map<String, String> param = new HashMap<>(2);
        param.put("code", menuCode);
        param.put("userId", ucUser.getUserId());
        authFeignClient.checkAuth(JSONUtils.toJSONString(param));
        // 是否需要数据权限校验
        boolean flag = MenuCodeEnum.checkMenuIsDataAuth(menuCode);
        if (!flag) {
            return;
        }
        // 获取权限策略
        List<String> dataPolicy = authService.getDatapolicy(menuCode, ucUser.getUserId());
        if (dataPolicy.contains("all")) {
            return;
        }
        // 仅包含mine==>只能检索自己的数据
        if (dataPolicy.size() == 1 && dataPolicy.contains("mine")) {
            if (!authRequest.getEeUserId().equals(ucUser.getUserId())) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选员工权限");
            }
            return;
        }
        // 包含myArea==>只能创建自己所属大区下的外呼账号
        if (dataPolicy.contains("myArea")) {
            String schoolId = authRequest.getSchoolId();
            List<String> schoolIds = authService.getSchoolIds(BRAND_YOULU, menuCode, ucUser.getUserId());
            //如果权限是本大区,但是获取的分校id列表是空的,表示他没有大区,按照'空'处理
            if (CollectionUtils.isEmpty(schoolIds)) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选员工所属分校权限");
            }
            if (!schoolIds.contains(schoolId)) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选员工所属分校权限");
            }
            return;
        }
        // 包含mySchool==>只能创建自己分校下的外呼账号
        if (dataPolicy.contains("mySchool")) {
            String schoolId = authRequest.getSchoolId();
            if (!ucUser.getSchoolId().equals(schoolId)) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选分校权限");
            }
            return;
        }
        // 包含myDept==>只能创建自己部门下的外呼账号
        if (dataPolicy.contains("myDept")) {
            String dptId = authRequest.getDptId();
            List<String> dptIds = authService.getDptIds(BRAND_YOULU, menuCode, ucUser.getUserId());
            if (!dptIds.contains(dptId)) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选部门权限");
            }
            return;
        }
        throw new ApplicationException(ApiErrorCodes.execute_failed, "未找到您的权限");
    }

    /**
     * 功能权限验证,仅仅验证是否有此菜单的功能权限
     *
     * @param ngExpand 操作人
     * @param menuCode 菜单编码
     * <AUTHOR>
     * @Date 10:41 2022/10/11
     **/
    public static void interfaceAuthOfFunction(NgExpand ngExpand, String menuCode) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        AuthFeignClient authFeignClient = SpringUtils.getBean(AuthFeignClient.class);
        UcUser ucUser = ngExpand.getUcUser();
        // 是否有菜单权限校验[没有权限直接抛出异常]
        Map<String, String> param = new HashMap<>(2);
        param.put("code", menuCode);
        param.put("userId", ucUser.getUserId());
        authFeignClient.checkAuth(JSONUtils.toJSONString(param));
    }

    /**
     * 简化的权限验证方法 - 直接返回验证后的参数，避免修改入参对象
     *
     * @param eeUserId 员工用户ID
     * @param dptIds 部门ID列表
     * @param schoolIds 分校ID列表
     * @param ngExpand 操作人
     * @param menuCode 菜单编码
     * @return String[] 返回验证后的参数数组 [eeUserId, dptIds, schoolIds]
     */
    public static String[] validateAndGetParams(String eeUserId, String dptIds, String schoolIds,
                                               NgExpand ngExpand, String menuCode) {
        AuthService authService = SpringUtils.getBean(AuthService.class);
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }

        UcUser ucUser = ngExpand.getUcUser();
        List<String> dataPolicy = authService.getDatapolicy(menuCode, ucUser.getUserId());

        // 初始化验证结果
        String validatedEeUserId = eeUserId;
        String validatedDptIds = dptIds;
        String validatedSchoolIds = schoolIds;

        if (dataPolicy.contains("all")) {
            return new String[]{validatedEeUserId, validatedDptIds, validatedSchoolIds};
        }

        // 仅包含mine==>只能检索自己的数据
        if (dataPolicy.size() == 1 && dataPolicy.contains("mine")) {
            if (!StringUtils.isEmpty(validatedEeUserId) && !validatedEeUserId.equals(ucUser.getUserId())) {
                throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选员工权限");
            }
            validatedEeUserId = ucUser.getUserId();
            return new String[]{validatedEeUserId, validatedDptIds, validatedSchoolIds};
        }

        // 包含myDept或otherDept==>只能检索有权限的部门下的数据
        if (dataPolicy.contains("myDept") || dataPolicy.contains("otherDept")) {
            List<String> dptIdList = authService.getDptIds(BRAND_YOULU, menuCode, ucUser.getUserId());
            if (StringUtils.isNotEmpty(validatedDptIds)) {
                String[] dptIdArray = validatedDptIds.split(",");
                Set<String> dptIdSet = new HashSet<>(dptIdList);
                List<String> validDptIds = new ArrayList<>();
                for (String dptIdParam : dptIdArray) {
                    if (dptIdSet.contains(dptIdParam)) {
                        validDptIds.add(dptIdParam);
                    }
                }
                // 过滤后如果没有有效的部门，则抛出权限异常
                if (validDptIds.isEmpty()) {
                    throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选部门权限");
                }
                validatedDptIds = String.join(",", validDptIds);
            } else {
                validatedDptIds = String.join(",", dptIdList);
            }

            return new String[]{validatedEeUserId, validatedDptIds, validatedSchoolIds};
        }

        throw new ApplicationException(ApiErrorCodes.execute_failed, "未找到您的权限");
    }
}
