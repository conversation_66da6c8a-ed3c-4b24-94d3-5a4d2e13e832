package com.niceloo.cmc.ex.utils;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.Data;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class PhoneRecordClient {
    private static final String ACCESS_TOKEN = "1308D9CA4DAEAAE81C12F97D574E73F07F5D90EAB0726B485BDC0B0A14E06743FE24F3999BA6556D7E28F485F8D64078F118F895B5C50993C459D5CD0FB002D9";
    private static final String ADMIN_UIN = "9704669";
    private static final String API_URL = "http://webservice.nl.tq.cn/webservice/phoneRecord/list";

    public static void main(String[] args) {
        int pageNum = 1;
        int pageSize = 100;
        String startTimeStr = "2024-09-01 08:00:00";
        String endTimeStr = "2024-09-06 22:00:00";
        String fileName = "Phone Records_" + startTimeStr.substring(0,10).replace("-", "") +".xlsx";

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将字符串转换为LocalDateTime对象
        LocalDateTime startTimeDateTime = LocalDateTime.parse(startTimeStr, formatter);
        LocalDateTime endTimeDateTime = LocalDateTime.parse(endTimeStr, formatter);

        // 将LocalDateTime对象转换为时间戳（以秒为单位，从1970-01-01T00:00:00Z开始）
        long startTime = startTimeDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;
        long endTime = endTimeDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;

        // 打印结果
        System.out.println("Start Time (Epoch seconds): " + startTime);
        System.out.println("End Time (Epoch seconds): " + endTime);

        String orderName = "insert_time";
        String orderRule = "asc";

        List<PhoneRecord.PhoneRecordItem> records = new ArrayList<>();
        while (true) {
            JsonObject response = fetchPhoneRecords(pageNum, pageSize, startTime, endTime, orderName, orderRule);
            if (response.get("errorCode").getAsInt() == 0) {
                JsonObject data = response.getAsJsonObject("data");
                records.addAll(parsePhoneRecords(data.getAsJsonArray("list")));
                int totalPage = (data.get("total").getAsInt() + data.get("pageSize").getAsInt() - 1) / data.get("pageSize").getAsInt();
                if (pageNum >= totalPage) {
                    break;
                }
                pageNum++;
            } else {
                System.out.println("Error fetching phone records: " + response.get("message").getAsString());
                break;
            }
        }

        System.out.println("Total phone records: " + records.size());
        // Process the phone records as needed
        // 将records对象中的 PhoneRecordData 列表转换为PhoneRecord.PhoneRecordItem列表
        List<PhoneRecord.PhoneRecordItem> phoneRecords = new ArrayList<>();
        for (PhoneRecord.PhoneRecordItem item : records) {
            if (item != null) { // 检查返回的列表是否为null
                phoneRecords.add(item); // 使用addAll方法将列表中的所有项添加到phoneRecords中
            }
        }
        writeToExcel(phoneRecords, fileName);
    }

    /**
     * 将电话记录列表写入Excel文件
     *
     * @param records   包含电话记录数据的列表
     * @param fileName  要写入的Excel文件名
     * @throws IOException 如果写入Excel文件时发生I/O错误
     */
    public static void writeToExcel(List<PhoneRecord.PhoneRecordItem> records, String fileName) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Phone Records");

            // 创建表头行
            Row headerRow = sheet.createRow(0);
            createCell(headerRow, 0, "ID");
            createCell(headerRow, 1, "UIN");
            createCell(headerRow, 2, "Caller ID");
            createCell(headerRow, 3, "Called ID");
            createCell(headerRow, 4, "Call Style");
            createCell(headerRow, 5, "Deal State");
            createCell(headerRow, 6, "Resume");
            createCell(headerRow, 7, "Insert Time");
            createCell(headerRow, 8, "Is Called Phone");
            createCell(headerRow, 9, "Pathway");
            createCell(headerRow, 10, "Client ID");
            createCell(headerRow, 11, "Duration");
            createCell(headerRow, 12, "Area ID");
            createCell(headerRow, 13, "Caller Area");
            createCell(headerRow, 14, "DNIS");
            createCell(headerRow, 15, "Queue Name");
            createCell(headerRow, 16, "Satisfaction Degree");
            createCell(headerRow, 17, "Record File");
            createCell(headerRow, 18, "Seat ID");
            createCell(headerRow, 19, "FS Unique ID");
            createCell(headerRow, 20, "Caller Start Time");
            createCell(headerRow, 21, "Ring Time");
            createCell(headerRow, 22, "Caller Queue Time");
            createCell(headerRow, 23, "Hangup Cause");
            createCell(headerRow, 24, "Media ID");
            createCell(headerRow, 25, "Insert DB Time");
            createCell(headerRow, 26, "Hangup Side");
            createCell(headerRow, 27, "Is Create Visitor");
            createCell(headerRow, 28, "Transparent Number");

            // 填充数据行
            int rowNum = 1;
            for (PhoneRecord.PhoneRecordItem record : records) {
                Row dataRow = sheet.createRow(rowNum++);
                createCell(dataRow, 0, record.getId());
                createCell(dataRow, 1, record.getUin());
                createCell(dataRow, 2, record.getCaller_id());
                createCell(dataRow, 3, record.getCalled_id());
                createCell(dataRow, 4, record.getCall_style());
                createCell(dataRow, 5, record.getDeal_state());
                createCell(dataRow, 6, record.getResume());
                createCell(dataRow, 7, record.getInsert_time());
                createCell(dataRow, 8, record.getIs_called_phone());
                createCell(dataRow, 9, record.getPathway());
                createCell(dataRow, 10, record.getClient_id());
                createCell(dataRow, 11, record.getDuration());
                createCell(dataRow, 12, record.getArea_id());
                createCell(dataRow, 13, record.getCallerArea());
                createCell(dataRow, 14, record.getDnis());
                createCell(dataRow, 15, record.getQueuename());
                createCell(dataRow, 16, record.getSatisfaction_degree());
                createCell(dataRow, 17, record.getRecordfile());
                createCell(dataRow, 18, record.getSeatid());
                createCell(dataRow, 19, record.getFsunique_id());
                createCell(dataRow, 20, record.getCaller_stime());
                createCell(dataRow, 21, record.getRingTime());
                createCell(dataRow, 22, record.getCaller_queue_time());
                createCell(dataRow, 23, record.getHangup_cause());
                createCell(dataRow, 24, record.getMedia_id());
                createCell(dataRow, 25, record.getInsert_db_time());
                createCell(dataRow, 26, record.getHangup_side());
                createCell(dataRow, 27, record.getIsCreateVisitor());
                createCell(dataRow, 28, record.getTransparent_number());
            }

            // 自动调整列宽
            for (int i = 0; i < 29; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写出Excel文件
            try (FileOutputStream outputStream = new FileOutputStream(fileName)) {
                workbook.write(outputStream);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 在指定的行中创建一个单元格，并设置其值。
     *
     * @param row         要在其中创建单元格的行
     * @param columnIndex 单元格的列索引
     * @param value       要设置到单元格中的值，可以是String、Integer、Long或Boolean类型
     */
    private static void createCell(Row row, int columnIndex, Object value) {
        Cell cell = row.createCell(columnIndex);
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        }
    }

    /**
     * 从服务器获取电话记录数据
     *
     * @param pageNum    分页的页码
     * @param pageSize   分页的每页大小
     * @param startTime  开始时间（时间戳）
     * @param endTime    结束时间（时间戳）
     * @param orderName  排序字段名
     * @param orderRule  排序规则（升序/降序）
     * @return 返回包含电话记录数据的Json对象
     * @throws RuntimeException 如果在获取电话记录过程中发生IO异常，则抛出运行时异常
     */
    private static JsonObject fetchPhoneRecords(int pageNum, int pageSize, long startTime, long endTime, String orderName, String orderRule) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(API_URL + "?access_token=" + ACCESS_TOKEN + "&admin_uin=" + ADMIN_UIN);

            // 创建 application/x-www-form-urlencoded 类型的请求体
            List<NameValuePair> formParams = new ArrayList<>();
            formParams.add(new BasicNameValuePair("pageNum", String.valueOf(pageNum)));
            formParams.add(new BasicNameValuePair("pageSize", String.valueOf(pageSize)));
            formParams.add(new BasicNameValuePair("start_time", String.valueOf(startTime)));
            formParams.add(new BasicNameValuePair("end_time", String.valueOf(endTime)));
            formParams.add(new BasicNameValuePair("order_name", orderName));
            formParams.add(new BasicNameValuePair("order_rule", orderRule));

            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(formParams, StandardCharsets.UTF_8);
            request.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                return new Gson().fromJson(responseBody, JsonObject.class);
            }
        } catch (IOException e) {
            throw new RuntimeException("Error fetching phone records", e);
        }
    }

    /**
     * 从给定的Json数组中解析电话记录，并返回一个包含解析后的电话记录的列表。
     *
     * @param jsonArray 包含电话记录数据的Json数组
     * @return 包含解析后的电话记录的列表
     */
    private static List<PhoneRecord.PhoneRecordItem> parsePhoneRecords(com.google.gson.JsonArray jsonArray) {
        List<PhoneRecord.PhoneRecordItem> records = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JsonObject itemJson = jsonArray.get(i).getAsJsonObject();
            PhoneRecord.PhoneRecordItem item = new PhoneRecord.PhoneRecordItem();

            // Use the safe get method to handle null values
            item.setId(safeGetAsLong(itemJson, "id"));
            item.setUin(safeGetAsLong(itemJson, "uin"));
            item.setCaller_id(safeGetAsString(itemJson, "caller_id"));
            item.setCalled_id(safeGetAsString(itemJson, "called_id"));
            item.setCall_style(safeGetAsString(itemJson, "call_style"));
            item.setDeal_state(safeGetAsString(itemJson, "deal_state"));
            item.setResume(safeGetAsString(itemJson, "resume"));
            item.setInsert_time(safeGetAsLong(itemJson, "insert_time"));
            item.setIs_called_phone(safeGetAsString(itemJson, "is_called_phone"));
            item.setPathway(safeGetAsString(itemJson, "pathway"));
            item.setClient_id(safeGetAsString(itemJson, "client_id"));
            item.setDuration(safeGetAsInt(itemJson, "duration"));
            item.setArea_id(safeGetAsString(itemJson, "area_id"));
            item.setCallerArea(safeGetAsString(itemJson, "callerArea"));
            item.setDnis(safeGetAsString(itemJson, "dnis"));
            item.setQueuename(safeGetAsString(itemJson, "queuename"));
            item.setSatisfaction_degree(safeGetAsString(itemJson, "satisfaction_degree"));
            item.setRecordfile(safeGetAsString(itemJson, "recordfile"));
            item.setSeatid(safeGetAsString(itemJson, "seatid"));
            item.setFsunique_id(safeGetAsString(itemJson, "fsunique_id"));
            item.setCaller_stime(safeGetAsLong(itemJson, "caller_stime"));
            item.setRingTime(safeGetAsLong(itemJson, "ringTime"));
            item.setCaller_queue_time(safeGetAsString(itemJson, "caller_queue_time"));
            item.setHangup_cause(safeGetAsString(itemJson, "hangup_cause"));
            item.setMedia_id(safeGetAsString(itemJson, "media_id"));
            item.setInsert_db_time(safeGetAsLong(itemJson, "insert_db_time"));
            item.setHangup_side(safeGetAsString(itemJson, "hangup_side"));
            item.setIsCreateVisitor(safeGetAsString(itemJson, "isCreateVisitor"));
            item.setTransparent_number(safeGetAsString(itemJson, "transparent_number"));

            records.add(item);
        }
        return records;
    }

    /**
     * 安全地获取JsonObject中指定键的Long类型值
     *
     * @param obj JsonObject对象，包含要检索的键值对
     * @param key 要检索的键
     * @return 如果JsonObject中存在指定键且对应的值不为null，则返回该键对应的Long类型值；否则返回0L
     */
    private static long safeGetAsLong(JsonObject obj, String key) {
        JsonElement element = obj.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsLong();
        } else {
            return 0L;
        }
    }

    /**
     * 安全地从JsonObject中获取指定键对应的整数值。
     *
     * @param obj JsonObject对象，包含要获取的整数值
     * @param key 要获取的整数值对应的键
     * @return 如果JsonObject中存在该键且对应的值不为null，则返回该整数值；否则返回0
     */
    private static int safeGetAsInt(JsonObject obj, String key) {
        JsonElement element = obj.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsInt();
        } else {
            return 0;
        }
    }

    /**
     * 安全地获取JsonObject中指定key对应的字符串值。
     *
     * @param obj JsonObject类型的对象，用于从中获取值
     * @param key 需要获取的key
     * @return 如果JsonObject中不存在该key或该key对应的值为null，则返回空字符串""，否则返回key对应的字符串值
     */
    private static String safeGetAsString(JsonObject obj, String key) {
        if (!obj.has(key)) {
            return "";
        }
        JsonElement element = obj.get(key);
        if (element.isJsonNull()) {
            return "";
        }
        return element.getAsString();
    }

    /**
     * 安全地从JsonObject中获取指定key的布尔值。
     *
     * @param obj 包含目标数据的JsonObject对象
     * @param key 需要获取的布尔值的key
     * @return 如果key存在且为布尔类型，则返回对应的布尔值；否则返回false
     */
    private static boolean safeGetAsBoolean(JsonObject obj, String key) {
        JsonElement element = obj.get(key);
        if (element != null && element.isJsonPrimitive() && element.getAsJsonPrimitive().isBoolean()) {
            return element.getAsBoolean();
        } else {
            return false;
        }
    }

    /**
     * 获取电话记录列表
     *
     * @return 包含电话记录数据的列表，类型为 PhoneRecord.PhoneRecordItem
     */
    public static List<PhoneRecord.PhoneRecordItem> getPhoneRecords(String startTimeStr, String endTimeStr) {
        int pageNum = 1;
        int pageSize = 100;

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将字符串转换为LocalDateTime对象
        LocalDateTime startTimeDateTime = LocalDateTime.parse(startTimeStr, formatter);
        LocalDateTime endTimeDateTime = LocalDateTime.parse(endTimeStr, formatter);

        // 将LocalDateTime对象转换为时间戳（以秒为单位，从1970-01-01T00:00:00Z开始）
        long startTime = startTimeDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;
        long endTime = endTimeDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;

        // 打印结果
        System.out.println("Start Time (Epoch seconds): " + startTime);
        System.out.println("End Time (Epoch seconds): " + endTime);

        String orderName = "insert_time";
        String orderRule = "asc";

        List<PhoneRecord.PhoneRecordItem> records = new ArrayList<>();
        while (true) {
            JsonObject response = fetchPhoneRecords(pageNum, pageSize, startTime, endTime, orderName, orderRule);
            if (response.get("errorCode").getAsInt() == 0) {
                JsonObject data = response.getAsJsonObject("data");
                records.addAll(parsePhoneRecords(data.getAsJsonArray("list")));
                int totalPage = (data.get("total").getAsInt() + data.get("pageSize").getAsInt() - 1) / data.get("pageSize").getAsInt();
                if (pageNum >= totalPage) {
                    break;
                }
                pageNum++;
            } else {
                System.out.println("Error fetching phone records: " + response.get("message").getAsString());
                break;
            }
        }

        System.out.println("Total phone records: " + records.size());
        // Process the phone records as needed
        // 将records对象中的 PhoneRecordData 列表转换为PhoneRecord.PhoneRecordItem列表
        List<PhoneRecord.PhoneRecordItem> phoneRecords = new ArrayList<>();
        for (PhoneRecord.PhoneRecordItem item : records) {
            if (item != null) { // 检查返回的列表是否为null
                phoneRecords.add(item); // 使用addAll方法将列表中的所有项添加到phoneRecords中
            }
        }

        return phoneRecords;
    }

    public static class PhoneRecord {
        private int errorCode;
        private String message;
        private PhoneRecordData data;

        public int getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(int errorCode) {
            this.errorCode = errorCode;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public PhoneRecordData getData() {
            return data;
        }

        public void setData(PhoneRecordData data) {
            this.data = data;
        }

        public static class PhoneRecordData {
            private int listSize;
            private int total;
            private int pageSize;
            private int pageNum;
            private List<PhoneRecordItem> list;

            public int getListSize() {
                return listSize;
            }

            public void setListSize(int listSize) {
                this.listSize = listSize;
            }

            public int getTotal() {
                return total;
            }

            public void setTotal(int total) {
                this.total = total;
            }

            public int getPageSize() {
                return pageSize;
            }

            public void setPageSize(int pageSize) {
                this.pageSize = pageSize;
            }

            public int getPageNum() {
                return pageNum;
            }

            public void setPageNum(int pageNum) {
                this.pageNum = pageNum;
            }

            public List<PhoneRecordItem> getList() {
                return list;
            }

            public void setList(List<PhoneRecordItem> list) {
                this.list = list;
            }
        }

        @Data
        public static class PhoneRecordItem {

            private long id;
            private long uin;
            private String caller_id;
            private String called_id;
            private String call_style;
            private String deal_state;
            private String resume;
            private Long insert_time;
            private String is_called_phone;
            private String pathway;
            private String client_id;
            private Integer duration;
            private String area_id;
            private String callerArea;
            private String dnis;
            private String queuename;
            private String satisfaction_degree;
            private String recordfile;
            private String seatid;
            private String fsunique_id;
            private Long caller_stime;
            private Long ringTime;
            private String caller_queue_time;
            private String hangup_cause;
            private String media_id;
            private Long insert_db_time;
            private String hangup_side;
            private String isCreateVisitor;
            private String transparent_number;

            // 省略 getter 和 setter 方法

        }
    }
}