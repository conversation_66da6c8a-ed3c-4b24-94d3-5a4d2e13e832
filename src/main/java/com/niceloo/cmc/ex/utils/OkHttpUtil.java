package com.niceloo.cmc.ex.utils;

import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.utils.StringUtils;
import okhttp3.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.UUID;

/**
 * OKHttp3请求简单工具类
 */
@Component
public class OkHttpUtil {
    @Resource
    private OkHttpClient okHttpClient;

    private static final Logger logger = LoggerFactory.getLogger(OkHttpUtil.class);
    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    /**
     * 发起 HTTP GET 请求
     *
     * @param url     请求的 URL
     * @param queries 请求的参数，在 URL 中以查询参数的形式传递，可以为 null
     * @param headers 请求头，可以为 null
     * @return 响应结果字符串
     */
    public String get(String url, Map<String, String> queries, Headers headers) {
        // 拼接参数
        url = this.jointUrl(url, queries);
        Request.Builder requestBuilder = new Request.Builder().url(url);
        if (headers != null) {
            requestBuilder.headers(headers);
        }
        Request request = requestBuilder.build();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        logger.info("OKHttp 发起请求，请求 ID: {}，请求地址: {}，请求参数: {}，请求头: {}", uuid, url, queries, headers);
        return this.requestHttp(request, uuid);
    }


    /**
     * post From表单请求
     *
     * @param url        请求的url
     * @param bodyParams post form 提交的参数
     * @return 响应值的字符串
     */
    public String postFrom(String url, Map<String, Object> bodyParams) {
        return this.postFrom(url, bodyParams, null, null);
    }

    /**
     * post(application/x-www-form-urlencoded) From表单请求
     *
     * @param url        请求的url
     * @param bodyParams post form 提交的参数
     * @param headers    post headers 请求头,可以使用Headers.of()方法转换
     * @return 响应值的字符串
     */
    public String postFrom(String url, Map<String, Object> bodyParams, Headers headers) {
        return this.postFrom(url, bodyParams, null, headers);
    }

    /**
     * post(application/x-www-form-urlencoded) From表单请求
     *
     * @param url        请求的url
     * @param bodyParams post form 提交的参数
     * @param queries    post queries 请求的参数，在浏览器？后面的数据
     * @return 响应值的字符串
     */
    public String postFrom(String url, Map<String, Object> bodyParams, Map<String, String> queries) {
        return this.postFrom(url, bodyParams, queries, null);
    }

    /**
     * post(application/x-www-form-urlencoded) From表单请求
     *
     * @return java.lang.String
     * @param url 请求的url
     * @param bodyParams 请求体参数
     * @param queries 请求的参数，在浏览器？后面的数据
     * @param headers 请求头
     * <AUTHOR>
     * @since 18:41 2022/1/11
     **/
    public String postFrom(String url, Map<String, Object> bodyParams, Map<String, String> queries, Headers headers) {
        RequestBody requestBody = this.buildRequestBodyByMap(bodyParams);
        //发起HTTP请求
        String uuid = UUID.randomUUID().toString().replace("-", "");
        logger.info("OKHttp发起请求,请求id:{},请求地址:{},请求参数:{},请求体:{}", uuid, url,
                queries == null ? "" : JSONUtils.toJSONString(queries),
                bodyParams == null ? "" : JSONUtils.toJSONString(bodyParams));
        return this.post(url, queries, headers, requestBody, uuid);
    }

    /**
     * post
     *
     * @param url      请求的url
     * @param jsonBody post form 提交的参数
     * @return 响应值的字符串
     */
    public String post(String url, String jsonBody) {
        return this.post(url, jsonBody, null, null);
    }

    /**
     * post
     *
     * @param url      请求的url
     * @param jsonBody post form 提交的参数
     * @param headers  post headers 请求头
     * @return 响应值的字符串
     */
    public String post(String url, String jsonBody, Headers headers) {
        return this.post(url, jsonBody, null, headers);
    }

    /**
     * post
     *
     * @param url      请求的url
     * @param jsonBody post form 提交的参数
     * @param queries  post queries 请求的参数，在浏览器？后面的数据
     * @return 响应值的字符串
     */
    public String post(String url, String jsonBody, Map<String, String> queries) {
        return this.post(url, jsonBody, queries, null);
    }

    /**
     * post(JSON请求)
     *
     * @param url      请求的url
     * @param jsonBody post form 提交的参数
     * @param queries  post queries 请求的参数，在浏览器？后面的数据
     * @param headers  post headers 请求头
     * @return 响应值的字符串
     */
    public String post(String url, String jsonBody, Map<String, String> queries, Headers headers) {
        RequestBody requestBody;
        if (null == headers || StringUtils.isEmpty(headers.get("Content-Type"))) {
            requestBody = RequestBody.create(JSON, jsonBody);
        } else {
            requestBody = RequestBody.create(null, jsonBody);
        }
        //发起HTTP请求
        String uuid = UUID.randomUUID().toString().replace("-", "");
        logger.info("OKHttp发起请求,请求id:{},请求地址:{},请求参数:{},请求体:{}", uuid, url,
                queries == null ? "" : JSONUtils.toJSONString(queries), jsonBody);
        return this.post(url, queries, headers, requestBody, uuid);
    }

    /**
     * post
     *
     * @param url     请求的url
     * @param queries post queries 请求的参数，在浏览器？后面的数据
     * @param headers post headers 请求头
     * @param body 请求体
     * @return 响应值的字符串
     */
    public String post(String url, Map<String, String> queries, Headers headers, RequestBody body, String uuid) {
        //拼接参数
        if (!MapUtils.isEmpty(queries)) {
            url = this.jointUrl(url, queries);
        }
        Request request;
        //校验是否添加请求头
        if (null != headers) {
            request = new Request.Builder().url(url).headers(headers).post(body).build();
        } else {
            request = new Request.Builder().url(url).post(body).build();
        }
        return this.requestHttp(request, uuid);
    }

    /**
     * 将?后面的参数拼接到url上
     *
     * @param url     url路径
     * @param queries 参数对
     * @return java.lang.String
     * <AUTHOR>
     * @since 11:01 2021/9/9
     **/
    private String jointUrl(String url, Map<String, String> queries) {
        StringBuilder sb = new StringBuilder(url);
        if (queries != null && !queries.keySet().isEmpty()) {
            boolean firstFlag = true;
            for (Map.Entry<String, String> entry : queries.entrySet()) {
                if (firstFlag) {
                    sb.append("?").append(entry.getKey()).append("=").append(entry.getValue());
                    firstFlag = false;
                } else {
                    sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
                }
            }
        }
        return sb.toString();
    }


    /**
     * 发起HTTP请求
     *
     * @param request 请求
     * @param uuid    请求唯一id
     * @return String：请求失败或返回状态不是200返回结果为 ""
     * <AUTHOR>
     * @since 11:18 2021/9/9
     **/
    private String requestHttp(Request request, String uuid) {
        try (Response response = okHttpClient.newCall(request).execute()) {
            int status = response.code();
            if (status == 200) {
                ResponseBody body = response.body();
                if (null == body) {
                    logger.info("OKHttp请求成功,请求id:{},请求地址:{},响应结果:{}", uuid, request.toString(), "");
                    return "";
                }
                String responseStr = body.string();
                logger.info("OKHttp请求成功,请求id:{},请求地址:{},响应结果:{}", uuid, request.toString(), responseStr);
                return responseStr;
            } else {
                logger.error("HTTP请求返回状态为非200,请求id:{},状态码:{},请求地址:{},返回内容:{}", uuid, status, request.toString(), response.toString());
            }
        } catch (Exception e) {
            logger.error(e, "okhttp请求出现异常,请求id:" + uuid + ",请求地址:" + request.toString() + "异常信息:" + e.getMessage());
        }
        return "";
    }

    /**
     * Map转为RequestBody
     *
     * @param bodyParams 请求体
     * @return okhttp3.RequestBody
     * <AUTHOR>
     * @since 18:37 2022/1/11
     **/
    private RequestBody buildRequestBodyByMap(Map<String, Object> bodyParams) {
        RequestBody body;
        FormBody.Builder builder = new FormBody.Builder();
        if (bodyParams != null) {
            for (Map.Entry<String, Object> entry : bodyParams.entrySet()) {
                Object value = entry.getValue();
                // 如果value值本来就是String类型,则直接添加,如果不是将其转换为JSON字符串
                builder.add(entry.getKey(), value instanceof String ? value.toString() : JSONUtils.toJSONString(value));
            }
        }
        body = builder.build();
        return body;
    }
}