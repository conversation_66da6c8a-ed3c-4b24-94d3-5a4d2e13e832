package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.pojo.param.UrlCheckResult;
import lombok.CustomLog;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 音频地址校验器
 *
 * <AUTHOR>
 * @since 2023-08-31 17:07:28
 */
@CustomLog
public class AudioAddressCheckerUtil {
    // 下载文件的最小大小，单位为字节，这里设置为 2 KB
    private static final long MIN_AUDIO_SIZE = 2048;

    /**
     * 判断给定的录音地址是否为密文
     * 亿讯录音地址判断中使用
     *
     * @param address 录音地址，可以是明文或密文
     * @return 如果录音地址是密文，则返回true；否则返回false
     */
    public static boolean isAddressEncrypted(String address) {
        try {
            // 使用Base64解码录音地址
            byte[] decodedBytes = Base64.getDecoder().decode(address);
            // 将解码后的字节数组转换为字符串
            String decodedAddress = new String(decodedBytes, StandardCharsets.UTF_8);
            // 检查解码后的字符串是否符合明文地址的格式
            return !decodedAddress.equals(address);
        } catch (IllegalArgumentException e) {
            // 解码失败，说明录音地址不是Base64编码的密文
            return false;
        }
    }


    /**
     * 检查录音地址是否可下载
     *
     * @param voiceSourceUrl 录音文件的URL地址
     * @return 如果录音地址可下载，则返回true；否则返回false
     * @throws 如果在检查过程中出现异常，则返回false
     */
    public static UrlCheckResult checkUrlAccessibility(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD"); // 使用HEAD方法
            connection.connect();

            int responseCode = connection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                long fileSize = connection.getContentLengthLong();
                if (fileSize > 0) {
                    return new UrlCheckResult(true, fileSize); // 可以下载
                }
            }
        } catch (IOException e) {
            LOGGER.warn("检查录音地址时发生异常", e);
        }

        return new UrlCheckResult(false, 0); // 不可下载
    }
}
