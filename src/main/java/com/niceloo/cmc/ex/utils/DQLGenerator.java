package com.niceloo.cmc.ex.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

public class DQLGenerator {
    public static void main(String[] args) {
        String inputFilePath = "D:\\git2\\communicationcenter\\info.json"; // 输入文件路径
        String outputFilePath = "D:\\git2\\communicationcenter\\dql_script.json"; // 输出文件路径
        try {
            // 读取文件内容
            String jsonContent = readFile(inputFilePath);
            // 解析 JSON 内容并生成 DQL
            String dqlScript = generateDQL(jsonContent);
            // 将 DQL 写入输出文件
            writeFile(outputFilePath, dqlScript);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String readFile(String filePath) throws IOException {
        StringBuilder contentBuilder = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                contentBuilder.append(line);
            }
        }
        return contentBuilder.toString();
    }

    private static String generateDQL(String jsonContent) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonContent);

        // 动态获取索引名称
        String indexName = rootNode.path("hits").path("hits").get(0).path("_index").asText();

        JsonNode hits = rootNode.path("hits").path("hits");
        StringBuilder dqlScript = new StringBuilder();
        dqlScript.append("POST /" + indexName + "/_doc/_bulk\n"); // 使用动态索引名称

        for (JsonNode hit : hits) {
            String id = hit.path("_id").asText();
            JsonNode source = hit.path("_source");

            dqlScript.append("{ \"index\": { \"_id\": \"" + id + "\" } }\n");
            dqlScript.append(source.toString()).append("\n");
        }

        return dqlScript.toString();
    }

    private static void writeFile(String filePath, String content) throws IOException {
        try (FileWriter fileWriter = new FileWriter(filePath)) {
            fileWriter.write(content);
        }
    }
}