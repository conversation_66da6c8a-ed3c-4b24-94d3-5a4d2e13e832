package com.niceloo.cmc.ex.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * @desc: 简易map
 * @author: song
 * @date: 2022/2/21
 */
public class EasyMap extends LinkedHashMap<String, Object> implements Serializable {

    private static final long serialVersionUID = -3619360949155867562L;

    private EasyMap() {}

    /** 获取一个值 */
    @Override
    public Object get(Object key) {
        if ("this".equals(key)) {
            return this;
        }
        return super.get(key);
    }

    /** 如果为空，则返回默认值 */
    public Object get(Object key, Object defaultValue) {
        Object value = get(key);
        if (ObjectUtils.isEmpty(value)) {
            return defaultValue;
        }
        return value;
    }

    /** 转为String并返回 */
    public String getStr(String key) {
        Object value = get(key);
        if (ObjectUtils.isEmpty(value)) {
            return "";
        }
        return String.valueOf(value);
    }

    /** 如果为空，则返回默认值 */
    public String getStr(String key, String defaultValue) {
        String value = getStr(key);
        if (ObjectUtils.isEmpty(value)) {
            return defaultValue;
        }
        return  value;
    }

    /**
     * 返回整数
     */
    public int getInt(String key) {
        String value = getStr(key);
        if (ObjectUtils.isEmpty(value)) {
            return 0;
        }
        return Integer.parseInt(value);
    }

    /**
     * 返回整数
     */
    public int getInt(String key, int defaultValue) {
        String value = getStr(key);
        if (ObjectUtils.isEmpty(value)) {
            return defaultValue;
        }
        return Integer.parseInt(value);
    }

    /** 转为long并返回 */
    public long getLong(String key) {
        String value = getStr(key);
        if (ObjectUtils.isEmpty(value)) {
            return 0;
        }
        return Long.parseLong(value);
    }

    /** 转为boolean并返回 */
    public boolean getBoolean(String key) {
        Object value = get(key);
        if (ObjectUtils.isEmpty(value)) {
            return false;
        }
        return Boolean.parseBoolean(String.valueOf(value));
    }

    /** 获取集合(必须原先就是个集合，否则会创建个新集合并返回) */
    @SuppressWarnings("unchecked")
    public List<Object> getList(String key) {
        Object value = get(key);
        List<Object> list = null;
        if (value == null || "".equals(value)) {
            list = new ArrayList<>();
        } else if (value instanceof List) {
            list = (List<Object>)value;
        } else {
            list = new ArrayList<Object>();
            list.add(value);
        }
        return list;
    }

    /** 获取集合 (指定泛型类型) */
    public <T> List<T> getList(String key, Class<T> cs) {
        List<Object> list = getList(key);
        List<T> list2 = new ArrayList<T>();
        for (Object obj : list) {
            T objC = getValueByClass(obj, cs);
            list2.add(objC);
        }
        return list2;
    }

    /** 根据指定类型从map中取值，返回实体对象 */
    public <T> T getModel(Class<T> cs) {
        try {
            return getModelByObject(cs.getDeclaredConstructor().newInstance());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /** 从map中取值，塞到一个对象中 */
    public <T> T getModelByObject(T obj) {
        // 获取类型
        Class<?> cs = obj.getClass();
        // 循环复制
        for (Field field : cs.getDeclaredFields()) {
            try {
                // 获取对象
                Object value = this.get(field.getName());
                if (value == null) {
                    continue;
                }
                field.setAccessible(true);
                Object valueConvert = getValueByClass(value, field.getType());
                field.set(obj, valueConvert);
            } catch (IllegalArgumentException | IllegalAccessException e) {
                throw new RuntimeException("属性取值出错：" + field.getName(), e);
            }
        }
        return obj;
    }

    /**
     * 将指定值转化为指定类型并返回
     *
     * @param obj
     * @param cs
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T getValueByClass(Object obj, Class<T> cs) {
        String obj2 = String.valueOf(obj);
        Object obj3 = null;
        if (cs.equals(String.class)) {
            obj3 = obj2;
        } else if (cs.equals(int.class) || cs.equals(Integer.class)) {
            obj3 = Integer.valueOf(obj2);
        } else if (cs.equals(long.class) || cs.equals(Long.class)) {
            obj3 = Long.valueOf(obj2);
        } else if (cs.equals(short.class) || cs.equals(Short.class)) {
            obj3 = Short.valueOf(obj2);
        } else if (cs.equals(byte.class) || cs.equals(Byte.class)) {
            obj3 = Byte.valueOf(obj2);
        } else if (cs.equals(float.class) || cs.equals(Float.class)) {
            obj3 = Float.valueOf(obj2);
        } else if (cs.equals(double.class) || cs.equals(Double.class)) {
            obj3 = Double.valueOf(obj2);
        } else if (cs.equals(boolean.class) || cs.equals(Boolean.class)) {
            obj3 = Boolean.valueOf(obj2);
        } else {
            obj3 = (T)obj;
        }
        return (T)obj3;
    }

    // ============================= 写值 =============================

    /**
     * 给指定key添加一个默认值（只有在这个key原来无值的情况先才会set进去）
     */
    public void setDefaultValue(String key, Object defaultValue) {
        if (ObjectUtils.isEmpty(key)) {
            set(key, defaultValue);
        }
    }

    /** set一个值，连缀风格 */
    public EasyMap set(String key, Object value) {
        // 防止敏感key
        if ("this".equals(key.toLowerCase())) {
            return this;
        }
        put(key, value);
        return this;
    }

    /** 将一个Map塞进EasyMap */
    public EasyMap setMap(Map<String, ?> map) {
        if (map != null) {
            for (String key : map.keySet()) {
                this.set(key, map.get(key));
            }
        }
        return this;
    }

    /** 将一个对象解析塞进EasyMap */
    public EasyMap setModel(Object model) {
        if (model == null) {
            return this;
        }
        Field[] fields = model.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                boolean isStatic = Modifier.isStatic(field.getModifiers());
                if (!isStatic) {
                    this.set(field.getName(), field.get(model));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return this;
    }

    /** 将json字符串解析后塞进EasyMap */
    public EasyMap setJsonString(String jsonString) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> map = new ObjectMapper().readValue(jsonString, Map.class);
            return this.setMap(map);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    // ============================= 删值 =============================

    /** delete一个值，连缀风格 */
    public EasyMap delete(String key) {
        remove(key);
        return this;
    }

    /** 清理所有value为null的字段 */
    public EasyMap clearNull() {
        Iterator<String> iterator = this.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if (ObjectUtils.isEmpty(key)) {
                iterator.remove();
                this.remove(key);
            }

        }
        return this;
    }

    /** 清理指定key */
    public EasyMap clearIn(String... keys) {
        List<String> keys2 = Arrays.asList(keys);
        Iterator<String> iterator = this.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if (keys2.contains(key)) {
                iterator.remove();
                this.remove(key);
            }
        }
        return this;
    }

    /** 清理掉不在列表中的key */
    public EasyMap clearNotIn(String... keys) {
        List<String> keys2 = Arrays.asList(keys);
        Iterator<String> iterator = this.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if (!keys2.contains(key)) {
                iterator.remove();
                this.remove(key);
            }

        }
        return this;
    }

    /** 清理掉所有key */
    public EasyMap clearAll() {
        clear();
        return this;
    }

    // ============================= 快速构建 =============================

    /** 构建一个EasyMap并返回 */
    public static EasyMap getEasyMap() {
        return new EasyMap();
    }

    /** 构建一个EasyMap并返回 */
    public static EasyMap getEasyMap(String key, Object value) {
        return new EasyMap().set(key, value);
    }

    /** 构建一个EasyMap并返回 */
    public static EasyMap getEasyMap(Map<String, ?> map) {
        return new EasyMap().setMap(map);
    }

    /** 将一个对象集合解析成为EasyMap */
    public static EasyMap getEasyMapByModel(Object model) {
        return EasyMap.getEasyMap().setModel(model);
    }

    /** 将一个对象集合解析成为EasyMap集合 */
    public static List<EasyMap> getEasyMapByList(List<?> list) {
        List<EasyMap> listMap = new ArrayList<EasyMap>();
        for (Object model : list) {
            listMap.add(getEasyMapByModel(model));
        }
        return listMap;
    }

    /** 克隆指定key，返回一个新的EasyMap */
    public EasyMap cloneKeys(String... keys) {
        EasyMap so = new EasyMap();
        for (String key : keys) {
            so.set(key, this.get(key));
        }
        return so;
    }

    /** 克隆所有key，返回一个新的EasyMap */
    public EasyMap cloneEasyMap() {
        EasyMap so = new EasyMap();
        for (String key : this.keySet()) {
            so.set(key, this.get(key));
        }
        return so;
    }

    /** 将所有key转为大写 */
    public EasyMap toUpperCase() {
        EasyMap so = new EasyMap();
        for (String key : this.keySet()) {
            so.set(key.toUpperCase(), this.get(key));
        }
        this.clearAll().setMap(so);
        return this;
    }

    /** 将所有key转为小写 */
    public EasyMap toLowerCase() {
        EasyMap so = new EasyMap();
        for (String key : this.keySet()) {
            so.set(key.toLowerCase(), this.get(key));
        }
        this.clearAll().setMap(so);
        return this;
    }

}
