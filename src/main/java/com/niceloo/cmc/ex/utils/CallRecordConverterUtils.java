package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.pojo.dto.jdyx.JDCallRecordCallback;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDJobDetailsDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 通话记录转换工具类
 * <AUTHOR>
 * @since 2025-12-31 11:45:08
 */
public class CallRecordConverterUtils {

    /**
     * 将JDJobDetailsDTO.JobDetail对象转换为JDCallRecordCallback对象
     *
     * @param jobDetail JDJobDetailsDTO.JobDetail对象
     * @return 转换后的JDCallRecordCallback对象
     */
    public static JDCallRecordCallback convertToCallRecordCallback(JDJobDetailsDTO.JobDetail jobDetail) {
        JDCallRecordCallback callback = new JDCallRecordCallback();
        callback.setJobId(jobDetail.getJobId());
        callback.setUserName(jobDetail.getCustomerName());
        callback.setPhone(jobDetail.getCustomerPhone());
        callback.setCallDuration(Long.parseLong(jobDetail.getDuration()));
        callback.setRedialTimes(jobDetail.getRedialTimes());
        callback.setAnswerTime(jobDetail.getBeginTime());
        callback.setRingTime(jobDetail.getBeginTime());
        callback.setLastCallTime(jobDetail.getLatestCallTime());
        callback.setSid(jobDetail.getSessionId());
        callback.setCallStatus(Integer.parseInt(jobDetail.getCustomerPhoneStatus()));
        callback.setDialogCount(Long.parseLong(jobDetail.getChatTurnCount()));
        callback.setBotId(jobDetail.getBotId().intValue());
        callback.setJobType(1);
        callback.setIntentLabels(convertIntentLabels(jobDetail.getIntentLabels()));
        callback.setCustomerVariables(jobDetail.getCustomerVariables());
        callback.setAudioPath(jobDetail.getAudioPath());
        callback.setChatText(jobDetail.getChatText());
        return callback;
    }

    /**
     * 将意图标签列表转换为以逗号分隔的字符串
     *
     * @param intentLabels 意图标签列表
     * @return 以逗号分隔的意图名称字符串，如果意图标签列表为空或为null，则返回空字符串
     */
    public static String convertIntentLabels(List<JDJobDetailsDTO.JobDetail.IntentLabel> intentLabels) {
        if (intentLabels == null || intentLabels.isEmpty()) {
            return "";
        }
        return intentLabels.stream()
                .map(JDJobDetailsDTO.JobDetail.IntentLabel::getIntentName)
                .collect(Collectors.joining(","));
    }
}
