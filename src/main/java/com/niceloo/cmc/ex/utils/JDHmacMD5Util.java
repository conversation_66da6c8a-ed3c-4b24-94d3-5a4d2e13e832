package com.niceloo.cmc.ex.utils;

import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.MapUtils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * HMAC加密工具类(京东言犀提供);
 *
 * @date 2022-04-16;
 */
public class JDHmacMD5Util {

    private static final Logger logger = LoggerFactory.getLogger(JDHmacMD5Util.class);

    /**
     * MAC算法可选以下多种算法
     * <p>
     * HmacMD5
     * HmacSHA1
     * HmacSHA256
     * HmacSHA384
     * HmacSHA512
     */
    public static final String KEY_MAC = "HmacMD5";


    /**
     * HMAC加密
     *
     * @param data 需要加密的数据转的byte数组
     * @param key  加密key,即:token
     * @return byte[] 密文
     * <AUTHOR>
     * @Date 11:42 2022/8/1
     **/
    private static byte[] encryptHMAC(byte[] data, String key) throws Exception {
        SecretKey secretKey = new SecretKeySpec(key.getBytes(), KEY_MAC);
        Mac mac = Mac.getInstance(secretKey.getAlgorithm());
        mac.init(secretKey);
        return mac.doFinal(data);
    }

    /**
     * byte数组转换为HexString
     *
     * @param b b;
     * @return String;
     */
    private static String byteArrayToHexString(byte[] b) {
        StringBuilder sb = new StringBuilder(b.length * 2);
        for (byte value : b) {
            int v = value & 0xff;
            if (v < 16) {
                sb.append('0');
            }
            sb.append(Integer.toHexString(v));
        }
        return sb.toString();
    }

    /**
     * hmacMD5加密(京东言犀提供)
     *
     * @param str   需要加密的数据(格式:key=value的形式组织，如果参数存在多个层级，则只取最顶层的key，分支参数以JSON字符串的形式作为值)
     * @param token 加密使用的token
     * @return java.lang.String
     * <AUTHOR>
     * @Date 11:37 2022/8/1
     **/
    public static String hmacMD5(String str, String token) {
        try {
            byte[] inputData = str.getBytes();
            return JDHmacMD5Util.byteArrayToHexString(JDHmacMD5Util.encryptHMAC(inputData, token));
        } catch (Exception e) {
            logger.error(e, "京东言犀HmacMD5加密出现异常,加密参数:{},token:{},异常信息:{}", str, token, e.getMessage());
        }
        return null;
    }

    /**
     * 根据业务参数进行签名
     *
     * @param param 业务参数
     * @param token token
     * @return java.lang.String
     * <AUTHOR>
     * @Date 11:51 2022/8/1
     **/
    public static String genSign(Map<String, Object> param, String token) {
        if (MapUtils.isEmpty(param)) {
            return null;
        }
        Set<String> keys = param.keySet();
        StringBuilder sb = new StringBuilder();
        keys.stream().sorted().forEach((key) -> {
            sb.append(key);
            sb.append("=");
            Object value = param.get(key);
            if (value instanceof Collection) {
                sb.append(JSONUtils.toJSONString(value));
            } else {
                sb.append(param.get(key));
            }
            sb.append("&");
        });
        String paramStr = sb.length() > 0 ? sb.substring(0, sb.length() - 1) : null;
        return hmacMD5(paramStr, token);
    }
}


    