package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.exception.FileUploadException;
import com.niceloo.cmc.ex.exception.HttpResponseException;
import com.niceloo.framework.file.FileUploadResponse;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.StringUtils;
import it.sauronsoftware.jave.AudioUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.*;

import static java.nio.file.StandardCopyOption.REPLACE_EXISTING;
import static java.nio.file.StandardOpenOption.DELETE_ON_CLOSE;

/**
 * @desc: http工具
 * @author: song
 * @date: 2022/2/25
 */
@Component
public class HttpUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtils.class);

    public static final String SEPARATOR = File.separator;

    private static FileUtil fileUtil;

    @Autowired
    public void setFileUtil(FileUtil fileUtil) {
        HttpUtils.fileUtil = fileUtil;
    }

    /**
     * 上传成功时返回存储路径
     *
     * @param urlStr   支持http或https协议
     * @param fileName 文件名(包含默认的文件扩展名)
     * @return Map<String, Object> 文件服务器返回的文件存储路径信息
     * @throws HttpResponseException-响应不是预期的录音数据 FileUploadException-录音数据没问题,但是上传过程出现异常
     *                                           IOException-其他I/O异常
     * <AUTHOR>
     * @Date 2022/1/19 9:20
     */
    public static Map<String, Object> uploadVoiceToOSS(String urlStr, String fileName)
            throws HttpResponseException, FileUploadException, IOException {
        if (StringUtils.isEmpty(urlStr)) {
            return new HashMap<>();
        }
        HttpURLConnection conn = openAndSetupHttpURLConnection(urlStr);
        InputStream in = null;
        try {
            in = getVoiceInputStreamFromConnection(conn);

            fileName = adjustFileNameExtByConnection(conn, fileName);
            if ("amr".equalsIgnoreCase(fileName.substring(fileName.lastIndexOf(".") + 1))) {
                InputStream mp3In = tryToConvertAmrToMp3(in);
                if (mp3In != null) {
                    in = mp3In;
                    fileName = fileName.substring(0, fileName.lastIndexOf(".")) + ".mp3";
                }
            }

            try {
                FileUploadResponse response = fileUtil.uploadFile(in, fileName);
                return JSONUtils.toMap(JSONUtils.toJSONString(response));
            } catch (Exception e) {
                throw new FileUploadException("录音文件上传失败", e);
            }
        } finally {
            close(in);
        }
    }

    /**
     * 将amr格式转成mp3格式(方便前端使用H5 audio标签),
     * 通过jave在子进程中调用ffmpeg进行音频转码,转码过程需要借助临时文件
     *
     * @param in 转码前的数据流
     * @return InputStream 转码后的数据流,转码失败时返回null
     * @throws IOException 非转码引起的其他IO异常
     * <AUTHOR>
     * @Date 2022/1/25 10:24
     */
    private static InputStream tryToConvertAmrToMp3(InputStream in) throws IOException {
        File temp = new File(System.getProperty("java.io.tmpdir"), "audio");
        if (!temp.exists()) {
            // 创建临时目录
            temp.mkdirs();
            // JVM正常退出时删除临时目录(只有为空才能删除成功)
            temp.deleteOnExit();
        }

        File source = new File(temp, UUID.randomUUID().toString() + ".amr");
        try {
            // 将amr格式数据写入临时文件
            Files.copy(in, source.toPath(), REPLACE_EXISTING);
        } catch (IOException e) {
            Files.deleteIfExists(source.toPath());
            throw new IOException("将录音写入临时文件失败", e);
        } finally {
            // 写入成功或者失败,都需要关闭录音数据流
            close(in);
        }

        File target = new File(temp, UUID.randomUUID().toString() + ".mp3");
        try {
            AudioUtils.amrToMp3(source, target);
        } catch (Exception e) {
            LOGGER.info(e, "amr转mp3失败");
            Files.deleteIfExists(target.toPath());
            return null;
        } finally {
            Files.deleteIfExists(source.toPath());
        }

        try {
            // 转码成功,打开转码后的数据流,关闭数据流时删除关联的临时录音文件
            return Files.newInputStream(target.toPath(), DELETE_ON_CLOSE);
        } catch (IOException e) {
            Files.deleteIfExists(target.toPath());
            throw new IOException("打开临时录音文件失败", e);
        }

    }

    /**
     * 首先从Content-Disposition响应头解析录音文件扩展名,
     * 未获取到且url无查询参数时,从路径参数获取文件扩展名
     * 仍未获取到,使用客户端设置的文件扩展名
     * 上述步骤都未获取到,使用mp3兜底
     *
     * @param conn     指向录音文件
     * @param fileName 客户端设置的录音文件名
     * @return 录音文件名
     * <AUTHOR>
     * @Date 2022/1/24 9:32
     */
    private static String adjustFileNameExtByConnection(HttpURLConnection conn, String fileName) {
        // 文件扩展名
        String ext = "";

        // 尝试获取Content-Disposition响应头中的文件扩展名
        // 例: Content-Disposition: attachment; filename="20220105/22010502065712038688548.wav"
        String contentDisposition = conn.getHeaderField("Content-Disposition");
        if (contentDisposition != null) {
            String filename = parseContentDisposition(contentDisposition).get("filename");
            if (!StringUtils.isEmpty(filename)) {
                ext = filename.substring(filename.lastIndexOf(".") + 1);
            }
        }

        if (StringUtils.isEmpty(ext)) {
            URL url = conn.getURL();
            // 不处理此类url, http://mdbnl.tq.cn/mj/filelist.do?type=voiceRecords&uid=0742b8fe-33d6-4171-9e52-e7ce5812cef5&callagent_time=
            if (StringUtils.isEmpty(url.getQuery())) {
                // url无查询参数时,从路径参数获取文件扩展名
                // 例: http://47.107.129.78/data/voicerecord/39/20210519/21051911564412038206361.wav  ==> .wav
                ext = url.getPath().substring(url.getPath().lastIndexOf(".") + 1);
            }
        }

        String fileNameWithoutExt;
        if (fileName.lastIndexOf(".") < 0) {
            fileNameWithoutExt = fileName;
        } else {
            if (StringUtils.isEmpty(ext)) {
                // 取入参中的后缀名
                ext = fileName.substring(fileName.lastIndexOf(".") + 1);
            }
            fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
        }

        if (StringUtils.isEmpty(ext)) {
            // mp3兜底
            ext = "mp3";
        }

        return fileNameWithoutExt + "." + ext;
    }

    /**
     * 首先检查是否Response error,检查返回的数据类型是否正确(预期是录音文件),
     * 检查返回的数据是否小于2KB(小于2KB的数据有可能不是录音文件而是错误描述,即使是录音文件,小于2KB时也可以丢弃),
     * 通过上述检查后,返回指向录音文件的InputStream
     *
     * @param conn 指向录音文件的HttpURLConnection
     * @return 指向录音文件的InputStream
     * @throws IOException -I/O 错误
     *                     HttpResponseException-未通过上述检查
     * <AUTHOR>
     * @Date 2022/1/22 18:21
     */
    private static InputStream getVoiceInputStreamFromConnection(HttpURLConnection conn)
            throws IOException, HttpResponseException {
        URL url = conn.getURL();
        int responseCode = conn.getResponseCode();
        if (responseCode >= 400) {
            // 4XX: client error 或 5XX: server error
            throw new HttpResponseException(url.toString(), responseCode, conn.getResponseMessage());
        }

        String contentType = conn.getContentType();
        if (contentType == null) {
            LOGGER.info("无contentType响应头,未知MIME类型: " + url);
        }
        if (contentType != null && contentType.contains("text")
                && !CallProperties.ZhzxProperty.HOST.contains(url.getHost())) {
            // 返回的contentType是text类型, 且不是中弘智享的录音文件
            throw new HttpResponseException(url.toString(), "非录音文件," + contentType);
        }

        InputStream in = conn.getInputStream();

        int contentLength = conn.getContentLength();
        int _2KB = 2 * 1024;
        if (contentLength >= 0 && contentLength < _2KB) {
            throw new HttpResponseException(url.toString(), "响应数据小于2KB");
        }
        // 无contentLength响应头
        if (contentLength < 0) {
            // 尝试从响应中读出2KB
            byte[] prefetchBytes = new byte[_2KB];
            int readNumber = readNBytesFromInputStream(in, prefetchBytes, 0, prefetchBytes.length);
            if (readNumber < prefetchBytes.length) {
                // 2KB的录音文件可以丢弃(因为几乎没有有效的对话内容),
                // 而且厂商有可能返回的不是正确的录音格式文件,而是返回了文本格式的错误描述,此时往往返回的数据量较小,一般不会大于2KB
                throw new HttpResponseException(url.toString(), "响应数据小于2KB");
            }
            in = new ByteArrayPlusInputStream(prefetchBytes, in);
        }
        return in;
    }

    /**
     * @param urlStr
     * @return HttpURLConnection
     * @throws IOException
     * @Description: 设置超时时间, https协议时尝试在打开的连接实例上信任所有安全证书, 建立连接
     * <AUTHOR>
     * @Date 2022/1/22 17:59
     */
    private static HttpURLConnection openAndSetupHttpURLConnection(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        // 设置超时时间
        conn.setConnectTimeout(15_000);
        conn.setReadTimeout(15_000);

        // 如果是https协议,在当前连接实例上信任所有安全证书
        if (conn instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConn = (HttpsURLConnection) conn;
            try {
                httpsConn.setSSLSocketFactory(getTrustAllCertificateSSLSocketFactory());
                httpsConn.setHostnameVerifier(NOT_VERIFY);
            } catch (NoSuchAlgorithmException | KeyManagementException e) {
                // 忽略异常
                LOGGER.info(e, "设置SSL信任所有安全证书异常");
            }
        }

        conn.connect();
        return conn;
    }

    /**
     * @param in  输入流
     * @param b   读取数据的字节数组
     * @param off b中写入数据的起始偏移量
     * @param len 要读取的最大字节数
     * @return 读入缓冲区的实际字节数
     * @throws IOException – 如果发生 I/O 错误
     * @Description: copy自 jdk11 InputStream#readNBytes(byte[] b, int off, int len)
     * 从输入流中读取请求的字节数到给定的字节数组中。
     * 此方法会一直阻塞，直到读取了len个字节的输入数据、检测到流结束或抛出异常。
     * 返回实际读取的字节数，可能为零。 此方法不会关闭输入流。
     * <AUTHOR>
     * @Date 2022/1/22 16:59
     */
    private static int readNBytesFromInputStream(InputStream in, byte[] b, int off, int len) throws IOException {
        int n = 0;
        while (n < len) {
            int count = in.read(b, off + n, len - n);
            if (count < 0) {
                break;
            }
            n += count;
        }
        return n;
    }

    /**
     * @return paramPairs
     * @Description: 解析contentDisposition的值
     * Content-Disposition: attachment; filename=1641884377.mp3
     * Content-Disposition: attachment; filename="20220105/22010502065712038688548.wav"
     * <AUTHOR>
     * @Date 2022/1/19 16:49
     */
    private static Map<String, String> parseContentDisposition(String contentDisposition) {
        Map<String, String> paramPairs = new HashMap<>();
        for (String paramPair : contentDisposition.split(";")) {
            String[] paramPairArr = paramPair.trim().split("=");
            if (paramPairArr.length >= 2) {
                paramPairs.put(paramPairArr[0].trim(), paramPairArr[1].replace("\"", "").replace("'", "").trim());
            } else {
                paramPairs.put(paramPairArr[0].trim(), "");
            }
        }
        return paramPairs;
    }


    /**
     * 封装调用外部网关的请求参数
     *
     * @param headers
     * @param body
     * @param url
     * @return
     */
    public static Map<String, Object> createOutGatewayMap(String url, String httpType, Map<String, String> headers, String body) {
        Map<String, Object> reqGateData = new HashMap<>(4);
        reqGateData.put("address", url);
        reqGateData.put("httpType", httpType);
        reqGateData.put("header", headers);
        reqGateData.put("body", body);
        return reqGateData;
    }

    /**
     * HTTP GET 请求
     *
     * @param url
     * @param param
     * @return
     */
    public static Map getLocal(String url, Map<String, Object> param) throws Exception {

        String params = builderParams(param, "UTF-8");
        URL restServiceURL = new URL(url + (params.length() > 0 ? "?" + params : ""));
        HttpURLConnection conn = (HttpURLConnection) restServiceURL.openConnection();
        conn.setConnectTimeout(10000);
        conn.setReadTimeout(10000);
        conn.setRequestMethod("GET");

        StringBuilder sb = null;
        BufferedReader bufferedReader = null;
        InputStreamReader streamReader = null;
        InputStream inputStream = null;
        try {
            sb = new StringBuilder();
            conn.connect();
            if (conn.getResponseCode() == 200) {
                inputStream = conn.getInputStream();
                streamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                bufferedReader = new BufferedReader(streamReader);
                String s;
                while ((s = bufferedReader.readLine()) != null) {
                    sb.append(s);
                }
            }
        } catch (Exception e) {
            throw e;
        } finally {
            close(bufferedReader);
            close(streamReader);
            close(inputStream);
            conn.disconnect();
        }
        return JSONUtils.toMap(sb.toString());
    }

    /**
     * HTTP POST 请求 - 内部服务调用
     *
     * @param url
     * @param params
     * @param heads
     * @return
     * @throws IOException
     */
    public static Map postLocalSelf(String url, Map<String, Object> params, Map<String, String> heads) throws Exception {
        HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
        // 连接参数设置
        conn.setRequestMethod("POST");
        conn.setConnectTimeout(10000);
        conn.setReadTimeout(10000);
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);

        String content = "";
        // 请求头设置
        if (heads != null) {
            Iterator var13 = heads.keySet().iterator();
            while (var13.hasNext()) {
                String headName = (String) var13.next();
                conn.addRequestProperty(headName, heads.get(headName));
            }
            content = JSONUtils.toJSONString(params);
        } else {
            conn.addRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
            content = builderParams(params, "UTF-8");
        }
        conn.addRequestProperty("Content-Length", String.valueOf(content.getBytes(StandardCharsets.UTF_8).length));

        LOGGER.info("[" + url + "][REQ]" + content);

        // 输出请求
        BufferedWriter writer = null;
        BufferedReader reader = null;
        OutputStreamWriter streamWriter = null;
        OutputStream outputStream = null;
        InputStreamReader streamReader = null;
        InputStream inputStream = null;
        StringBuilder sb = new StringBuilder();
        try {
            conn.connect();
            outputStream = conn.getOutputStream();
            streamWriter = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
            writer = new BufferedWriter(streamWriter);
            writer.write(content);
            writer.flush();
            writer.close();

            // 获取响应
            sb = new StringBuilder();
            if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                inputStream = conn.getInputStream();
                streamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                reader = new BufferedReader(streamReader);
                String s;
                while ((s = reader.readLine()) != null) {
                    sb.append(s);
                }
            }
        } catch (Exception e) {
            throw e;
        } finally {
            close(writer);
            close(streamWriter);
            close(outputStream);
            close(reader);
            close(streamReader);
            close(inputStream);
            conn.disconnect();
        }

        LOGGER.info("[" + url + "][RES]" + sb.toString());
        return JSONUtils.toMap(sb.toString());
    }

    /**
     * @return java.lang.String
     * @desc 发送post请求
     * <AUTHOR>
     * @date 2022/3/2
     */
    public static String sendPost(String url, String param) {
        OutputStreamWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();

        try {
            URL realUrl = new URL(url);
            //打开和URL之间的连接
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
//            URLConnection conn = realUrl.openConnection();
            //设置通用的请求属性
            conn.setRequestMethod("POST");
            conn.setRequestProperty("accept", "*/");
            conn.setRequestProperty("connection", "keep-alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0(compatible; MSIE 6.0 NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setUseCaches(false);//设置不要缓存
//            conn.setInstanceFollowRedirects(true);
            //发送post必须设置
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.connect();
            //获取URLConnection对象对应的输出流
            out = new OutputStreamWriter(conn.getOutputStream());
            //发送请求参数
            out.write(param);
            //flush输出流的缓冲
            out.flush();
            //定义bufferedReader输入路来读取url的相应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                line = new String(line.getBytes(), StandardCharsets.UTF_8);
                result.append(line);
            }
            in.close();
            //断开连接
            conn.disconnect();
        } catch (Exception e) {
            LOGGER.error(e, e.toString());
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                LOGGER.error(ex, ex.toString());
            }
        }
        return result.toString();
    }

    public static int downloadFile(String url, String filePath) {
        InputStream reader = null;
        InputStream inputStream = null;
        FileOutputStream fs = null;
        try {
            HttpsURLConnection.setDefaultHostnameVerifier(NOT_VERIFY);
            HttpsURLConnection.setDefaultSSLSocketFactory(getTrustAllCertificateSSLSocketFactory());

            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(15_000);
            conn.setReadTimeout(25_000);

            inputStream = conn.getInputStream();
            reader = new BufferedInputStream(inputStream);
            fs = new FileOutputStream(filePath);

            byte[] buffer = new byte[8192];
            int length;
            while ((length = reader.read(buffer)) != -1) {
                fs.write(buffer, 0, length);
            }
            return 1;
        } catch (Exception e) {
            LOGGER.error( e.getMessage(), e);
        } finally {
            close(reader);
            close(inputStream);
            close(fs);
        }
        return 0;
    }

    public static boolean transToMp3(String srcAudioPath, String targetAudioPath) {
        boolean succeeded;
        try {
            File source = new File(srcAudioPath);
            File target = new File(targetAudioPath);
            AudioUtils.amrToMp3(source, target);
            succeeded = true;
            if (target.exists()) {
                source.delete();
            }
        } catch (Exception ex) {
            LOGGER.error(ex, "");
            succeeded = false;
        }
        return succeeded;
    }

    public static String builderParams(Map<String, Object> params, String charset) throws Exception {
        StringBuilder sb = new StringBuilder();
        if (!params.isEmpty()) {
            for (String key : params.keySet()) {
                Object value = params.get(key);
                sb.append(key);
                sb.append("=");
                sb.append(URLEncoder.encode(String.valueOf(value), charset));
                sb.append("&");
            }
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    public static String encode(String str) {
        Base64.Encoder encoder = Base64.getEncoder();
        byte[] bytes = encoder.encode(str.getBytes());
        return new String(bytes, StandardCharsets.UTF_8);
    }

    public static void close(BufferedReader bufferedReader) {
        if (bufferedReader != null) {
            try {
                bufferedReader.close();
            } catch (IOException e) {
                LOGGER.error(e, "关闭BufferedReader");
            }
        }
    }

    public static void close(BufferedWriter writer) {
        if (writer != null) {
            try {
                writer.close();
            } catch (IOException e) {
                LOGGER.error(e, "关闭BufferedWriter");
            }
        }
    }

    public static void close(InputStreamReader streamReader) {
        if (streamReader != null) {
            try {
                streamReader.close();
            } catch (IOException e) {
                LOGGER.error(e, "关闭InputStreamReader");
            }
        }
    }

    public static void close(OutputStream outputStream) {
        if (outputStream != null) {
            try {
                outputStream.close();
            } catch (IOException e) {
                LOGGER.error(e, "关闭OutputStream");
            }
        }
    }

    public static void close(OutputStreamWriter streamWriter) {
        if (streamWriter != null) {
            try {
                streamWriter.close();
            } catch (IOException e) {
                LOGGER.error(e, "关闭OutputStreamWriter");
            }
        }
    }

    /**
     * close inputStream
     *
     * @param inputStream
     * <AUTHOR>
     * @Date 2022/1/25 10:48
     */
    public static void close(InputStream inputStream) {
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                LOGGER.error(e, e.toString());
            }
        }
    }

    private static final HostnameVerifier NOT_VERIFY = (hostname, sslSession) -> true;

    /**
     * @return SSLSocketFactory
     * @throws NoSuchAlgorithmException,KeyManagementException
     * @Description: 返回的SSLSocketFactory信任所有安全证书
     * <AUTHOR>
     * @Date 2022/1/21 10:13
     */
    private static SSLSocketFactory getTrustAllCertificateSSLSocketFactory() throws NoSuchAlgorithmException, KeyManagementException {
        TrustManager[] tm = {new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        }};

        SSLContext sc = SSLContext.getInstance("TLS");
        sc.init(null, tm, new SecureRandom());
        return sc.getSocketFactory();
    }

    /**
     * <AUTHOR>
     * @Description: 先读取ByteArray输入流中的数据, 再读取其他输入流中的数据
     * @Date 2022/1/21 15:23
     */
    public static class ByteArrayPlusInputStream extends InputStream {

        private final ByteArrayInputStream baIn;
        private final InputStream otherIn;

        public ByteArrayPlusInputStream(byte[] bytes, InputStream otherIn) {
            this.baIn = new ByteArrayInputStream(bytes);
            this.otherIn = otherIn;
        }

        public ByteArrayPlusInputStream(byte[] bytes, int offset, int length, InputStream otherIn) {
            this.baIn = new ByteArrayInputStream(bytes, offset, length);
            this.otherIn = otherIn;
        }

        @Override
        public int read() throws IOException {
            if (baIn.available() > 0) {
                return baIn.read();
            }
            return otherIn.read();
        }

        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            if (baIn.available() > 0) {
                return baIn.read(b, off, len);
            }
            return otherIn.read(b, off, len);
        }

        @Override
        public long skip(long n) throws IOException {
            if (baIn.available() > 0) {
                return baIn.skip(n);
            }
            return otherIn.skip(n);
        }

        @Override
        public int available() throws IOException {
            return baIn.available() + otherIn.available();
        }

        @Override
        public void close() throws IOException {
            baIn.close();
            otherIn.close();
        }

        @Override
        public synchronized void mark(int readlimit) {
            if (baIn.available() > 0) {
                baIn.mark(readlimit);
            }
            otherIn.mark(readlimit);
        }

        @Override
        public synchronized void reset() throws IOException {
            if (baIn.available() > 0) {
                baIn.reset();
            }
            otherIn.reset();
        }

        @Override
        public boolean markSupported() {
            return baIn.markSupported() && otherIn.markSupported();
        }

    }

}
