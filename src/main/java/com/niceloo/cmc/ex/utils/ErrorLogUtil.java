package com.niceloo.cmc.ex.utils;

import com.niceloo.cmc.ex.pojo.dto.jdyx.JDBaseDTO;
import com.niceloo.framework.json.JSONUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 错误日志处理类
 * <AUTHOR>
 * @since 2024-12-16 16:26:53
 */
public class ErrorLogUtil {

    private static final String ERROR_CODE = "D1000";
    private static final int ERROR_STATE = 0;
    private static final int INITIAL_CAPACITY = 4; // 合理的初始容量

    /**
     * 将输入的消息格式化为错误响应的 JSON 字符串。
     *
     * @param message 输入的消息字符串，可能是一个未格式化的错误消息或已格式化的 JSON 字符串。
     * @return 格式化后的错误响应 JSON 字符串。如果输入的消息已经是 JSON 格式，则直接返回该消息。
     */
    public static String formatErrorResponse(String message) {
        // 清理多余的转义字符
        message = cleanEscapedCharacters(message);

        // 检查是否可以解析成 JSON
        if (isJSON(message)) {
            return message; // 如果是 JSON，直接返回
        }

        // 构建错误响应对象，使用带参构造函数指定初始容量
        Map<String, Object> errorResponse = new HashMap<>(INITIAL_CAPACITY);
        errorResponse.put("code", ERROR_CODE); // 错误代码
        errorResponse.put("data", null); // 数据部分可以为 null
        errorResponse.put("message", message); // 使用提取的错误消息
        errorResponse.put("state", ERROR_STATE); // 状态

        // 将 Map 转换为 JSON 字符串，并返回
        return JSONUtils.toJSONString(errorResponse);
    }

    /**
     * 清理多余的转义字符
     *
     * @param message 要处理的消息字符串
     * @return 清理后的字符串
     */
    private static String cleanEscapedCharacters(String message) {
        return message.replace("\\\"", "\"") // 替换转义的双引号
                .replace("\\\\", "\\"); // 替换转义的反斜杠
    }

    /**
     * 判断给定的字符串是否为有效的 JSON 格式。
     *
     * @param message 要检查的字符串。
     * @return 如果字符串是有效的 JSON 格式，则返回 true；否则返回 false。
     * @throws Exception 在解析过程中，如果发生任何异常，则捕获并返回 false。
     */
    private static boolean isJSON(String message) {
        try {
            JSONUtils.toObject(message, JDBaseDTO.class); // 尝试解析 JSON
            return true; // 解析成功，返回 true
        } catch (Exception e) {
            return false; // 解析失败，返回 false
        }
    }
}
