package com.niceloo.cmc.ex.utils;

import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 * @date 2022年8月3日14:37:54
 */
public class JDAESUtil {
    private static final Logger logger = LoggerFactory.getLogger(JDAESUtil.class);
    private static final String KEY_ALGORITHM = "AES";
    //默认的加密算法
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";
    //加解密 key
    private static final String AES_KEY = "ZTcJdHQx7LkYdeCH0##f9oPXi58aw9yRfMEqPuJIItqJL1jnsp#fdojIYGtNWqaU";

    /**
     * AES 加密操作
     *
     * @param content 待加密内容
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String content) {
        try {
            // 创建密码器
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
            SecretKey secretKey = new SecretKeySpec(hexStringToBytes(AES_KEY), KEY_ALGORITHM);
            // 初始化为加密模式的密码器
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            // 加密
            byte[] result = cipher.doFinal(byteContent);
            //通过Base64转码返回
            return Base64.encodeBase64String(result);
        } catch (Exception ex) {
            logger.error(ex, "京东言犀数据加密异常,异常信息:{}", ex.getMessage());
        }
        return null;
    }

    /**
     * AES 解密操作
     *
     * @param content 待解密内容
     * @return 返回原文
     */
    public static String decrypt(String content) {
        try {
            //实例化
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            SecretKey secretKey = new SecretKeySpec(hexStringToBytes(AES_KEY), KEY_ALGORITHM);
            //使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            //执行操作
            byte[] result = cipher.doFinal(Base64.decodeBase64(content));
            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception ex) {
            logger.error(ex, "京东言犀数据数据解密异常,异常信息:{}", ex.getMessage());
        }
        return null;
    }


    /**
     * 将Hex String转换为Byte数组
     *
     * @param hexString the hex string
     * @return the byte [ ]
     */
    public static byte[] hexStringToBytes(String hexString) {
        if (StringUtils.isEmpty(hexString)) {
            return null;
        }
        hexString = hexString.toLowerCase();
        final byte[] byteArray = new byte[hexString.length() >> 1];
        int index = 0;
        for (int i = 0; i < hexString.length(); i++) {
            if (index > hexString.length() - 1) {
                return byteArray;
            }
            byte highDit = (byte) (Character.digit(hexString.charAt(index), 16) & 0xFF);
            byte lowDit = (byte) (Character.digit(hexString.charAt(index + 1), 16) & 0xFF);
            byteArray[i] = (byte) (highDit << 4 | lowDit & 0xff);
            index += 2;
        }
        return byteArray;
    }

    public static void main(String[] args) {
        String encrypt = JDAESUtil.encrypt("17633963865");
        String decrypt = JDAESUtil.decrypt("73LAu7NWBV88WAPKGT0mbQ==");
        System.out.println(encrypt);
        System.out.println(decrypt);
        String encrypt1 = FieldCipherUtil.encrypt(JDAESUtil.decrypt("CmdTk9mkSRdYHdMuGjJ3bQ=="));
        String encrypt2 = JDAESUtil.encrypt(FieldCipherUtil.decrypt("6lM+yGW8JXEQveGgXKbXpA=="));
        System.out.println(encrypt1);
        System.out.println(encrypt2);
    }
}
