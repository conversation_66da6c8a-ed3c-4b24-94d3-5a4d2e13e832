package com.niceloo.cmc.ex.utils;

import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONObject;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

public class FileDownloadUrlGenerator {

    private static final String TOKEN_URL = "http://msapi.zywinner.com/fileservice/api/file/download/url"; // 接口URL

    public static void main(String[] args) {
        String inputFilePath = "input2.txt";  // 输入文件路径
        String outputFilePath = "output.txt"; // 输出文件路径

        List<String> filePaths = new ArrayList<>();
        List<String> fileKeys = new ArrayList<>();

        // 读取所有文件路径和文件键
        try (BufferedReader br = new BufferedReader(new FileReader(inputFilePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] parts = line.split("\\s+|\\t+");
                if (parts.length < 2) {
                    System.out.println("Invalid line: " + line);
                    continue;
                }
                filePaths.add(parts[0]); // 添加filePath
                fileKeys.add(parts[1]);   // 添加fileKey
            }
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }

        // 请求下载URL
        try (BufferedWriter bw = new BufferedWriter(new FileWriter(outputFilePath))) {
            String downloadUrls = getDownloadUrls(filePaths);
            // 写入到输出文件
            bw.write(downloadUrls);
            System.out.println("Download URLs generated successfully.");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 从指定路径获取下载URL
     *
     * @param filePaths 文件路径列表
     * @return 下载URLs
     * @throws Exception 抛出异常
     */
    private static String getDownloadUrls(List<String> filePaths) throws Exception {
        // 构建filePaths的JSON字符串
        JSONArray jsonFilePaths = new JSONArray();
        for (String filePath : filePaths) {
            jsonFilePaths.put(filePath);
        }

        String params = "params=" + URLEncoder.encode("{\"filePaths\":" + jsonFilePaths.toString() + ",\"fileIds\":\"\",\"expire\":\"\"}", "UTF-8");

        HttpURLConnection connection = createConnection(TOKEN_URL, "POST", params);

        int responseCode = connection.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            throw new RuntimeException("Failed : HTTP error code : " + responseCode);
        }

        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();

        // 输出完整响应以供调试
        System.out.println("Response: " + response.toString());

        JSONObject jsonResponse = new JSONObject(response.toString());
        if (!jsonResponse.has("data") || jsonResponse.isNull("data")) {
            throw new RuntimeException("Invalid response: 'data' is null");
        }

        JSONArray dataArray = jsonResponse.getJSONObject("data").getJSONArray("data");
        StringBuilder downloadUrls = new StringBuilder();
        for (int i = 0; i < dataArray.length(); i++) {
            String url = dataArray.getJSONObject(i).getString("url");
            downloadUrls.append(url).append(System.lineSeparator());
        }

        return downloadUrls.toString();
    }

    /**
     * 创建一个HttpURLConnection连接对象
     *
     * @param urlString 目标URL地址
     * @param method    HTTP请求方法，如GET、POST等
     * @param params    请求参数，以application/x-www-form-urlencoded格式编码
     * @return          返回创建的HttpURLConnection连接对象
     * @throws Exception 抛出异常
     */
    private static HttpURLConnection createConnection(String urlString, String method, String params) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod(method);
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        connection.setDoOutput(true);

        if (params != null) {
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = params.getBytes("utf-8");
                os.write(input, 0, input.length);
            }
        }

        return connection;
    }
}