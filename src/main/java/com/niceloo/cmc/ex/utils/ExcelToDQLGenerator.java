package com.niceloo.cmc.ex.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: TODO
 * @author: <PERSON><PERSON>
 * @date: 2025/5/8 17:52 2025-05-08
 */
public class ExcelToDQLGenerator {

    public static void main(String[] args) {
        String inputFilePath = "D:\\git2\\usercenter\\export.xlsx"; // 输入文件路径
        String outputFilePath = "output_dql.txt"; // 输出文件路径

        generateDQLScript(inputFilePath, outputFilePath);
    }

    public static void generateDQLScript(String inputFilePath, String outputFilePath) {
        try (FileInputStream fis = new FileInputStream(inputFilePath);
             Workbook workbook = new XSSFWorkbook(fis);
             FileWriter writer = new FileWriter(outputFilePath)) {

            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            Map<Integer, String> headerMap = new HashMap<>();

            // Read header row to map column indices to field names
            for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    headerMap.put(i, cell.getStringCellValue());
                }
            }

            // Process each row after the header
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    Cell idCell = row.getCell(0);
                    if (idCell != null) {
                        String id = idCell.getStringCellValue();
                        writer.write("{ \"index\": { \"_id\": \"" + id + "\" } }\n");

                        writer.write("{ ");
                        for (int i = 1; i < row.getPhysicalNumberOfCells(); i++) {
                            Cell cell = row.getCell(i);
                            String fieldName = headerMap.get(i); // Use field name from header
                            String fieldValue = (cell != null) ? cell.toString() : "";
                            writer.write("\"" + fieldName + "\": \"" + fieldValue + "\", ");
                        }
                        writer.write("\"serverFolder\": \"\" } \n"); // Set serverFolder to empty string
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
