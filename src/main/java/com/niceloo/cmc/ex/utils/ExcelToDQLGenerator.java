package com.niceloo.cmc.ex.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;

import java.util.HashMap;
import java.util.Map;

/**
 * 根据Excel生成DQL脚本
 *
 * @author: <PERSON><PERSON>
 * @date: 2025/5/8 17:52 2025-05-08
 */
public class ExcelToDQLGenerator {

    public static void main(String[] args) {
        String inputFilePath = "D:\\git2\\temp\\5月.xlsx"; // 输入文件路径
        String outputFilePath = "D:\\git2\\temp\\5.txt"; // 输出文件路径

        generateDQLScript(inputFilePath, outputFilePath);
    }

    public static void generateDQLScript(String inputFilePath, String outputFilePath) {
        try (FileInputStream fis = new FileInputStream(inputFilePath);
             Workbook workbook = new XSSFWorkbook(fis);
             FileWriter writer = new FileWriter(outputFilePath)) {

            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            Map<Integer, String> headerMap = new HashMap<>();

            // Read header row to map column indices to field names
            for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    headerMap.put(i, cell.getStringCellValue());
                }
            }

            // Process each row after the header
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    Cell idCell = row.getCell(0);
                    if (idCell != null) {
                        String id = idCell.getStringCellValue();
                        writer.write("{ \"index\": { \"_id\": \"" + id + "\" } }\n");

                        StringBuilder jsonBuilder = new StringBuilder("{ ");
                        boolean isFirst = true;

                        for (int i = 1; i < row.getPhysicalNumberOfCells(); i++) {
                            Cell cell = row.getCell(i);
                            String fieldName = headerMap.get(i); // Use field name from header
                            if (fieldName != null && !fieldName.trim().isEmpty()) {
                                String fieldValue = "";

                                // 处理不同类型的单元格值
                                if (cell != null) {
                                    switch (cell.getCellType()) {
                                        case NUMERIC:
                                            // 检查是否为整数
                                            double numValue = cell.getNumericCellValue();
                                            if (numValue == Math.floor(numValue)) {
                                                // 是整数，转为整数格式
                                                fieldValue = String.valueOf((long) numValue);
                                            } else {
                                                // 是小数，保持原样
                                                fieldValue = String.valueOf(numValue);
                                            }
                                            break;
                                        case STRING:
                                            fieldValue = cell.getStringCellValue();
                                            break;
                                        case BOOLEAN:
                                            fieldValue = String.valueOf(cell.getBooleanCellValue());
                                            break;
                                        case FORMULA:
                                            try {
                                                // 尝试获取公式计算结果的数值
                                                double formulaValue = cell.getNumericCellValue();
                                                if (formulaValue == Math.floor(formulaValue)) {
                                                    fieldValue = String.valueOf((long) formulaValue);
                                                } else {
                                                    fieldValue = String.valueOf(formulaValue);
                                                }
                                            } catch (Exception e) {
                                                // 如果不是数值，尝试获取字符串结果
                                                try {
                                                    fieldValue = cell.getStringCellValue();
                                                } catch (Exception ex) {
                                                    fieldValue = "";
                                                }
                                            }
                                            break;
                                        default:
                                            fieldValue = "";
                                    }
                                }

                                if (!isFirst) {
                                    jsonBuilder.append(", ");
                                } else {
                                    isFirst = false;
                                }

                                jsonBuilder.append("\"" + fieldName + "\": \"" + fieldValue + "\"");
                            }
                        }

                        jsonBuilder.append(" }\n");
                        writer.write(jsonBuilder.toString());
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
