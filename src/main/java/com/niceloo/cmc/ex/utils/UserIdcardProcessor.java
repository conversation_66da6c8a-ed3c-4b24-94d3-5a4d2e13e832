package com.niceloo.cmc.ex.utils;

import lombok.CustomLog;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import java.io.FileOutputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@CustomLog
public class UserIdcardProcessor {

    // 数据库连接池配置
    private static final String DB_URL = "*******************************************";
    private static final String DB_USER = "uaedyok";
    private static final String DB_PASSWORD = "SDZkCxCTRPteQHb8cPYgdzpanSxF9KsP";
    private static final int PAGE_SIZE = 10000; // 每页查询的记录数
    private static final int THREAD_POOL_SIZE = 4; // 线程池大小

    private static HikariDataSource dataSource;

    static {
        // 初始化数据库连接池
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(DB_URL);
        config.setUsername(DB_USER);
        config.setPassword(DB_PASSWORD);
        config.setMaximumPoolSize(THREAD_POOL_SIZE);
        dataSource = new HikariDataSource(config);
    }

    /**
     * 主函数，用于从数据库中查询用户记录，处理用户身份证信息，并导出到Excel文件中。
     *
     * @param args 命令行参数（此程序中未使用）
     */
    public static void main(String[] args) {
        List<UserRecord> records = new ArrayList<>();
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);

        try (Connection conn = dataSource.getConnection()) {
            int totalRecords = getTotalRecords(conn); // 获取总记录数
            int totalPages = (int) Math.ceil((double) totalRecords / PAGE_SIZE);
            List<Future<List<UserRecord>>> futures = new ArrayList<>();

            // 分页查询并多线程处理
            for (int page = 0; page < totalPages; page++) {
                int offset = page * PAGE_SIZE;
                Future<List<UserRecord>> future = executor.submit(() -> processPage(offset, PAGE_SIZE));
                futures.add(future);
            }

            // 收集所有线程的结果并显示进度
            int completedPages = 0;
            for (Future<List<UserRecord>> future : futures) {
                records.addAll(future.get());
                completedPages++;
                double progress = (double) completedPages / totalPages * 100;
                System.out.printf("处理进度: %.2f%% (%d/%d 页)%n", progress, completedPages, totalPages);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }

        // 导出到Excel
        LOGGER.info("数据处理完成，开始导出到Excel...");
        exportToExcel(records);
        LOGGER.info("导出完成！");
    }


    /**
     * 获取总记录数。
     *
     * @param conn 数据库连接
     * @return 总记录数
     */
    private static int getTotalRecords(Connection conn) {
        try (PreparedStatement pstmt = conn.prepareStatement("SELECT COUNT(*) FROM UcUser WHERE userIdcard IS NOT NULL AND userIdcard != ''");
             ResultSet rs = pstmt.executeQuery()) {
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 处理单页数据。
     *
     * @param offset 偏移量
     * @param limit  每页记录数
     * @return 处理后的记录列表
     */
    private static List<UserRecord> processPage(int offset, int limit) {
        List<UserRecord> records = new ArrayList<>();

        try (Connection conn = dataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement("SELECT userId, userIdcard FROM UcUser WHERE userIdcard IS NOT NULL AND userIdcard != '' LIMIT ? OFFSET ?")) {
            pstmt.setInt(1, limit);
            pstmt.setInt(2, offset);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String userId = rs.getString("userId");
                String encryptedIdcard = rs.getString("userIdcard");
                String decryptedIdcard = FieldCipherUtil.decrypt(encryptedIdcard);

                // 检查解密后的userIdcard末尾是否有字母，且是否为小写
                if (decryptedIdcard.length() > 0 && Character.isLetter(decryptedIdcard.charAt(decryptedIdcard.length() - 1))) {
                    char lastChar = decryptedIdcard.charAt(decryptedIdcard.length() - 1);
                    if (Character.isLowerCase(lastChar)) {
                        // 将末尾字母转为大写并重新加密
                        String modifiedIdcard = decryptedIdcard.substring(0, decryptedIdcard.length() - 1) + Character.toUpperCase(lastChar);
                        String encryptedModifiedIdcard = FieldCipherUtil.encrypt(modifiedIdcard);

                        // 记录结果
                        records.add(new UserRecord(userId, decryptedIdcard, encryptedModifiedIdcard));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return records;
    }

    /**
     * 将用户记录导出到Excel文件中。
     *
     * @param records 用户记录列表
     */
    private static void exportToExcel(List<UserRecord> records) {
        try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) { // 使用流式Excel写入
            Sheet sheet = workbook.createSheet("User Records");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("userId");
            headerRow.createCell(1).setCellValue("userIdcard (明文)");
            headerRow.createCell(2).setCellValue("userIdcard (处理后加密)");

            // 填充数据
            int rowNum = 1;
            for (UserRecord record : records) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(record.getUserId());
                row.createCell(1).setCellValue(record.getDecryptedIdcard());
                row.createCell(2).setCellValue(record.getEncryptedModifiedIdcard());

                if (rowNum % 1000 == 0) {
                    ((SXSSFSheet) sheet).flushRows(1000); // 每隔1000条记录刷新一次
                }
            }

            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream("UserRecords.xlsx")) {
                workbook.write(fileOut);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 用于存储每条记录的数据
    private static class UserRecord {
        private final String userId;
        private final String decryptedIdcard;
        private final String encryptedModifiedIdcard;

        public UserRecord(String userId, String decryptedIdcard, String encryptedModifiedIdcard) {
            this.userId = userId;
            this.decryptedIdcard = decryptedIdcard;
            this.encryptedModifiedIdcard = encryptedModifiedIdcard;
        }

        public String getUserId() {
            return userId;
        }

        public String getDecryptedIdcard() {
            return decryptedIdcard;
        }

        public String getEncryptedModifiedIdcard() {
            return encryptedModifiedIdcard;
        }
    }
}
