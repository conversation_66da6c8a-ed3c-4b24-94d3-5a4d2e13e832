package com.niceloo.cmc.ex.controller.callback;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.aicall.ByCallBackTypeEnum;
import com.niceloo.cmc.ex.pojo.dto.*;
import com.niceloo.cmc.ex.pojo.dto.byai.BYAICallRecordCallback;
import com.niceloo.cmc.ex.pojo.dto.byai.BYAIJobInfoDTO;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDCallRecordCallback;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDJobInfoDTO;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.mq.client.Client;
import com.niceloo.mq.client.MessageProp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Getter;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_JDYX;
import static com.niceloo.cmc.ex.common.aicall.ByCallBackTypeEnum.CALL_INSTANCE_RESULT;
import static com.niceloo.cmc.ex.common.aicall.ByCallBackTypeEnum.JOB_INFO_RESULT;


/**
 * 通话记录回调接口
 *
 * <AUTHOR>
 * @since 2022-02-16 15:07
 */
@RestController
@RequestMapping("callback")
@Api("通话记录回调接口")
public class CallRecordsCallBackController {

    private static final Logger logger = LoggerFactory.getLogger(CallRecordsCallBackController.class);

    @Resource
    private Client client;

    // 名字在apollo配置
    @SuppressWarnings("unchecked")
    @Resource(name = "jobAddCustomerRabbitMqClient")
    private Client customerMarketingMqClient;
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Resource
    private CcAiCallJobService ccAiCallJobService;

    @PostMapping("/ykCallRecords")
    @ApiOperation(value = "云客通话记录回调接口")
    public YKResult ykCallRecordsCallBack(@RequestBody(required = false) List<YKCallRecordsDTO> request) {
        if (CollectionUtils.isEmpty(request)) {
            return Result.ykSuccess();
        }
        logger.info("云客通话记录回调接口请求:{},参数:{}", "/commu/callback/ykCallRecords", JSONUtils.toJSONString(request));
        Map<String, String> queue = new HashMap<>(2);
        queue.put("channelType", CallChannelEnum.CALL_TYPE_YPHONE.getType());
        queue.put("data", JSONUtils.toJSONString(request));
        client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));
        return Result.ykSuccess();
    }

    @PostMapping("/zhzxCallRecords")
    @ApiOperation(value = "中弘智享通话记录回调接口")
    public ZHZXResult zhzxCallRecordsCallBack(@RequestBody(required = false) ZHZXCallRecordsDTO request) {
        if (ObjectUtils.isEmpty(request)) {
            return Result.zhzxSuccess();
        }
        logger.info("中弘智享通话记录回调接口请求:{},参数:{}", "/commu/callback/zhzxCallRecords", JSONUtils.toJSONString(request));
        Map<String, String> queue = new HashMap<>(2);
        queue.put("channelType", CallChannelEnum.CALL_TYPE_ZHZX.getType());
        queue.put("data", JSONUtils.toJSONString(request));
        client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));
        return Result.zhzxSuccess();
    }

    @PostMapping("/yxCallRecords")
    @ApiOperation(value = "亿迅(中科云)通话记录回调接口")
    public YXResult yxCallRecordsCallBack(@RequestBody(required = false) YXCallbackDTO request, @RequestParam() @ApiParam("账号信息") Map<String, String> parameterMap) {
        if (null == request) {
            return Result.yxSuccess();
        }
        YXCallRecordsDTO recordsDTO = request.getNotify();
        if (recordsDTO == null || StringUtils.isEmpty(recordsDTO.getCallee())) {
            return Result.yxSuccess();
        }
        // 处理 callerInviteTime 为空的情况
        if (recordsDTO.getCallerInviteTime() == null || "0".equals(recordsDTO.getCallerInviteTime())) {
            recordsDTO.setCallerInviteTime(
                    DateUtils.toStr(
                            DateUtil.getFirstNonNullDate(
                                    recordsDTO.getCallerRingingBeginTime(),
                                    recordsDTO.getCallerAnswerTime(),
                                    recordsDTO.getCalleeInviteTime(),
                                    recordsDTO.getCalleeRingingBeginTime(),
                                    recordsDTO.getCalleeAnswerTime(),
                                    recordsDTO.getCallerHangupTime(),
                                    recordsDTO.getCalleeHangupTime()
                            )
                    )
            );
        }
        this.yxAddAccountInfoParams(parameterMap, recordsDTO);
        // 只回调[五十一、API话单推送]
        if (recordsDTO.isSign() && !"callApiBilling".equals(recordsDTO.getType())) {
            return Result.yxSuccess();
        }
        logger.info("亿迅通话记录回调接口请求:{},参数:{}", "/commu/callback/yxCallRecords", JSONUtils.toJSONString(request));

        //----------------------------数据到发送MQ-------------
        Map<String, Object> queue = new HashMap<>(2);
        queue.put("channelType", CallChannelEnum.CALL_TYPE_ZK.getType());
        queue.put("data", JSONUtils.toJSONString(recordsDTO));
        client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));
        return Result.yxSuccess();
    }


    @PostMapping("/yxSipCallRecords")
    @ApiOperation(value = "亿迅SIP通话记录回调接口")
    public YXResult yxSipCallRecordsCallBack(@RequestBody(required = false) YXSipCallbackDTO request, @RequestParam() @ApiParam("账号信息") Map<String, String> parameterMap) {
        if (null == request) {
            return Result.yxSuccess();
        }

        // 判断 notify 属性是否为单个对象
        if (request.getNotify() instanceof YXSipCallRecordsDTO) {
            YXSipCallRecordsDTO recordsDTO = (YXSipCallRecordsDTO) request.getNotify();
            if (StringUtils.isEmpty(recordsDTO.getCallee())) {
                return Result.yxSuccess();
            }
            this.yxSipAddAccountInfoParams(parameterMap, recordsDTO);

            if (recordsDTO.isSign() && !"billing".equals(recordsDTO.getType())) {
                return Result.yxSuccess();
            }
            logger.info("亿迅SIP通话记录回调接口请求:{},参数:{}", "/commu/callback/yxSipCallRecords", JSONUtils.toJSONString(request));

            // 发送单条数据到 MQ
            Map<String, Object> queue = new HashMap<>(2);
            queue.put("channelType", CallChannelEnum.CALL_TYPE_YX_SIP.getType());
            queue.put("data", JSONUtils.toJSONString(recordsDTO));
            client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));
        } else if (request.getNotify() instanceof List) {
            // 判断 notify 属性是否为数组
            List<YXSipCallRecordsDTO> recordsDTOList = (List<YXSipCallRecordsDTO>) request.getNotify();
            for (YXSipCallRecordsDTO recordsDTO : recordsDTOList) {
                if (StringUtils.isEmpty(recordsDTO.getCallee())) {
                    continue;
                }
                this.yxSipAddAccountInfoParams(parameterMap, recordsDTO);

                if (recordsDTO.isSign() && !"billing".equals(recordsDTO.getType())) {
                    continue;
                }
                logger.info("亿迅SIP通话记录回调接口请求:{},参数:{}", "/commu/callback/yxSipCallRecords", JSONUtils.toJSONString(recordsDTO));

                // 发送多条数据到 MQ
                Map<String, Object> queue = new HashMap<>(2);
                queue.put("channelType", CallChannelEnum.CALL_TYPE_YX_SIP.getType());
                queue.put("data", JSONUtils.toJSONString(recordsDTO));
                client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));
            }
        }
        return Result.yxSuccess();
    }

    /**
     * 巨量飞鱼事件接收接口
     *
     * @return java.lang.Object
     * @param request 回调真正的参数
     * @param challenge 验证回调地址使用
     * @param event 验证回调地址使用,不等于-------->//事件类型: CLUE_REFUND_TAG-> 返款标签, CONTACT_LOG-> 话单到达, REFUND_REPORT-> 账单到达
     * <AUTHOR>
     * @since 11:10 2022/5/31
     **/
    @PostMapping("/jlfyEventReceive")
    @ApiOperation(value = "巨量飞鱼事件接收接口")
    public JLFYResult jlfyCallRecordsCallBack(@RequestBody(required = false) Map<String, Object> request, Integer challenge, String event) {
        // 回调地址验证
        if (StringUtils.isNotEmpty(event) && "verify_webhook".equals(event)) {
            logger.info("巨量飞鱼事件回调接口验证请求:{},参数:{}", "/commu/callback/jlfyEventReceive", challenge + "; " + event);
            return Result.jlfySuccess(challenge);
        }
        if (MapUtils.isEmpty(request)) {
            return Result.jlfySuccess();
        }
        logger.info("巨量飞鱼事件回调接口请求:{},参数:{}", "/commu/callback/jlfyEventReceive", JSONUtils.toJSONString(request));
        Map<String, Object> data = JSONUtils.toMap(request.get("data").toString());
        //事件类型: CLUE_REFUND_TAG-> 返款标签, CONTACT_LOG-> 话单到达, REFUND_REPORT-> 账单到达
        // 只处理话单到达事件
        if (data.containsKey("event") && "CONTACT_LOG".equalsIgnoreCase(data.get("event").toString())) {
            Object content = data.get("content");
            if (null != content) {
                JLFYCallRecordsDTO jlfyCallRecordsDTO = JSONUtils.toObject(content.toString(), JLFYCallRecordsDTO.class);
                // 广告主ID
                jlfyCallRecordsDTO.setAdId(data.get("user_id").toString());
                Map<String, String> queue = new HashMap<>(2);
                queue.put("channelType", CallChannelEnum.CALL_TYPE_JLFY.getType());
                queue.put("data", JSONUtils.toJSONString(jlfyCallRecordsDTO));
                client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));
            }
        }
        return Result.jlfySuccess();
    }

    @PostMapping("/jdCallRecords")
    @ApiOperation(value = "京东言犀AI机器人外呼通话记录回调接口")
    public JDResult jdAICallRecordsCallBack(@RequestBody(required = false) Map<String, Object> request) {
        logger.info("京东言犀AI机器人外呼通话记录回调接口参数:{}", JSONUtils.toJSONString(request));
        if (request.get("code").equals("10000")) {
            Object data = request.get("data");
            JDCallRecordCallback callRecordCallback = JSONUtils.toObject(JSONUtils.toJSONString(data), JDCallRecordCallback.class);
            Map<String, String> queue = new HashMap<>(3);
            queue.put("channelType", CallChannelEnum.CALL_TYPE_JDYX.getType());
            queue.put("data", JSONUtils.toJSONString(callRecordCallback));
            queue.put("type", "callRecordEvent");
            client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));
            // 发送MQ到客户营销
            MessageProp messageProp = new MessageProp();
            messageProp.setHeaders(Map.of("cap-msg-name", "call_jdyx_detailresult"));
            customerMarketingMqClient.publish(MQConst.CALL_JDYX_DETAIL_RESULT_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(request), messageProp);
        }
        return Result.jdSuccess();
    }


    @PostMapping("/jdAIJobStatusChangeCallBack")
    @ApiOperation(value = "京东言犀AI机器人任务状态变更回调接口")
    public JDResult jdAIJobStatusChangeCallBack(@RequestBody(required = false) Map<String, Object> request) {
        logger.info("京东言犀AI机器人任务状态变更回调接口:{}", JSONUtils.toJSONString(request));
        if (request.get("code").equals("10000")) {
            JDJobInfoDTO jdJobInfoDTO = JSONUtils.toObject(JSONUtils.toJSONString(request.get("data")), JDJobInfoDTO.class);
            Map<String, String> queue = new HashMap<>(2);
            queue.put("channelType", CallChannelEnum.CALL_TYPE_JDYX.getType());
            queue.put("data", JSONUtils.toJSONString(jdJobInfoDTO));
            queue.put("type", "statusChangeEvent");
            client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));

            // 先根据言犀回调的任务ID查询通讯中心的jobId，将查出的cmcJobId放到data中，发送MQ到业务接收方
            // 解析data部分
            Map<String, Object> data = (Map<String, Object>) request.get("data");
            Integer jobId = (Integer) data.get("jobId");
            String cmcJobId = ccAiCallJobService.getJobIdByVendorJobId(jobId, CALL_TYPE_JDYX.getType());
            data.put("cmcJobId", cmcJobId);
            request.put("data", data);

            client.publish(MQConst.CALL_JDYX_JOB_RESULT_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(request));
        }
        return Result.jdSuccess();
    }

    @RequestMapping("/byaicall")
    @ApiOperation(value = "百应ai外呼回调接口")
    public ResponseEntity<String> byAiCallBack(@RequestBody(required = false) Map<String, Object> request) {
        logger.info("百应AI外呼回调接口参数:{}", JSONUtils.toJSONString(request));
        if (Integer.toString(HttpStatus.OK.value()).equals(request.get("code").toString())) {
            Map<String, Object> outData = (Map<String, Object>) request.get("data");
            ByCallBackTypeEnum byCallBackTypeEnum = ByCallBackTypeEnum.valueOf((String) outData.get("callbackType"));
            switch (byCallBackTypeEnum) {
                case CALL_INSTANCE_RESULT:
                    Map<String, Object> innerData = (Map<String, Object>) outData.get("data");
                    BYAICallRecordCallback callRecordCallback = JSONUtils.toObject(JSONUtils.toJSONString(innerData.get("callInstance")), BYAICallRecordCallback.class);
                    callRecordCallback.setChatText(JSONUtils.toJSONString(innerData.remove("phoneLogs")));

                    MessageProp messageProp = new MessageProp();
                    messageProp.setHeaders(Map.of("cap-msg-name", "call_by_detailresult"));
                    customerMarketingMqClient.publish(MQConst.CALL_BY_DETAIL_RESULT_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(request), messageProp);

                    callRecordCallback.populateIntentLabels((List<Map<String, Object>>) innerData.get("taskResult"));
                    Map<String, String> queue = new HashMap<>(3);
                    queue.put("channelType", CallChannelEnum.CALL_TYPE_BYAI.getType());
                    queue.put("data", JSONUtils.toJSONString(callRecordCallback));
                    queue.put("type", CALL_INSTANCE_RESULT.name());
                    client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));
                    break;
                case JOB_INFO_RESULT:
                    BYAIJobInfoDTO jobInfo = JSONUtils.toObject(JSONUtils.toJSONString(outData.get("data")), BYAIJobInfoDTO.class);
                    Map<String, String> message = new HashMap<>(3);
                    message.put("channelType", CallChannelEnum.CALL_TYPE_BYAI.getType());
                    message.put("data", JSONUtils.toJSONString(jobInfo));
                    message.put("type", JOB_INFO_RESULT.name());
                    client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(message));
                    break;
                default:
                    logger.info("暂不处理百应{}回调类型: {}", byCallBackTypeEnum, byCallBackTypeEnum.getName());
            }
        } else {
            logger.warn("百应AI外呼回调数据异常, code: {}, resultMsg: {}", request.get("code"), request.get("resultMsg"));
        }
        return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body("success");
    }

    /**
     * 添加亿迅不同分校的账户信息
     *
     * @param parameterMap
     * @param yxCallRecordsDTO
     * <AUTHOR>
     * @since 14:45 2022/1/3
     **/
    private void yxAddAccountInfoParams(Map<String, String> parameterMap, YXCallRecordsDTO yxCallRecordsDTO) {
        String accounts = parameterMap.get("account");
        String accountNames = parameterMap.get("accountName");
        String signs = parameterMap.get("sign");
        if (StringUtils.isEmpty(accounts) || StringUtils.isEmpty(accountNames) || StringUtils.isEmpty(signs)) {
            throw new RuntimeException("请在URL后拼接账号信息,例如:(URL?account=C120&sign=Y&accountName=ceshi)");
        }
        // URL后面的参数
        yxCallRecordsDTO.setAccount(accounts.trim());
        yxCallRecordsDTO.setAccountName(accountNames.trim());
        yxCallRecordsDTO.setSign("Y".equals(signs.trim()));
    }

    /**
     * 添加亿迅SIP不同分校的账户信息
     *
     * @param parameterMap
     * @param yxSipCallRecordsDTO
     * <AUTHOR>
     * @since 2024-08-26 17:04:55
     **/
    private void yxSipAddAccountInfoParams(Map<String, String> parameterMap, YXSipCallRecordsDTO yxSipCallRecordsDTO) {
        String accounts = parameterMap.get("account");
        String accountNames = parameterMap.get("accountName");
        String signs = parameterMap.get("sign");
        if (StringUtils.isEmpty(accounts) || StringUtils.isEmpty(accountNames) || StringUtils.isEmpty(signs)) {
            throw new RuntimeException("请在URL后拼接账号信息,例如:(URL?account=C120&sign=Y&accountName=ceshi)");
        }
        // URL后面的参数
        yxSipCallRecordsDTO.setAccount(accounts.trim());
        yxSipCallRecordsDTO.setAccountName(accountNames.trim());
        yxSipCallRecordsDTO.setSign("Y".equals(signs.trim()));
    }


    /**
     * 回调接口的返回值
     *
     * <AUTHOR>
     * @since 2022-02-16 15:07
     */
    static class Result {
        private static final YKResult YK_RESULT = new YKResult();
        private static final JDResult JD_RESULT = new JDResult();
        private static final YXResult YX_RESULT = new YXResult();
        private static final JLFYResult JLFY_RESULT = new JLFYResult();
        private static final ZHZXResult ZHZX_RESULT = new ZHZXResult();


        public static JDResult jdSuccess() {
            JD_RESULT.code = "10000";
            JD_RESULT.message = "接收成功";
            return JD_RESULT;
        }

        public static YKResult ykSuccess() {
            YK_RESULT.code = "200";
            YK_RESULT.message = "成功";
            return YK_RESULT;
        }

        public static JLFYResult jlfySuccess(Integer challenge) {
            JLFY_RESULT.challenge = challenge;
            JLFY_RESULT.baseResp.put("StatusCode", 200);
            JLFY_RESULT.baseResp.put("StatusMessage", "ok");
            return JLFY_RESULT;
        }

        public static JLFYResult jlfySuccess() {
            JLFY_RESULT.baseResp.put("StatusCode", 200);
            JLFY_RESULT.baseResp.put("StatusMessage", "ok");
            return JLFY_RESULT;
        }

        public static YXResult yxSuccess() {
            YX_RESULT.result.put("error", 200);
            YX_RESULT.result.put("msg", "回调成功");
            return YX_RESULT;
        }

        public static ZHZXResult zhzxSuccess() {
            ZHZX_RESULT.result.put("error", 200);
            ZHZX_RESULT.result.put("msg", "回调成功");
            return ZHZX_RESULT;
        }
    }

    @Getter
    static class YKResult {
        private String code;
        private String message;
    }

    @Getter
    static class JDResult {
        private String code;
        private String message;
    }

    @Getter
    static class JLFYResult {
        private Integer challenge;
        private final Map<String, Object> baseResp = new HashMap<>(2);
    }

    @Getter
    static class YXResult {
        private final Map<String, Object> result = new HashMap<>(2);
    }

    @Getter
    static class ZHZXResult {
        private final Map<String, Object> result = new HashMap<>(2);
    }
}
