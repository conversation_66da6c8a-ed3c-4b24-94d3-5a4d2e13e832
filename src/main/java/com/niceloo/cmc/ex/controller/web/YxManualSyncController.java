package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.CustomThreadFactory;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.AccountDTO;
import com.niceloo.cmc.ex.pojo.param.YXRecordsParam;
import com.niceloo.cmc.ex.service.BdCallaccountinfoService;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.call.YXService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.RedisUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @desc: 亿讯(中科云) 拉取历史通话记录
 * @author: song
 * @date: 2022/2/28
 */
@Validated
@Api("亿讯(中科云) 拉取历史通话记录")
@RestController
@RequestMapping("sync/yx")
public class YxManualSyncController {

    private static final Logger LOGGER = LoggerFactory.getLogger(YxManualSyncController.class);
    private static final ExecutorService singleExecutorService = Executors.newSingleThreadExecutor(new CustomThreadFactory("yx_records_manual_sync"));

    @Autowired
    private CallRecordService recordService;
    @Autowired
    private BdCallaccountinfoService bdCallAccountInfoService;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @ApiOperation("按账号补充亿讯通话记录")
    @PostMapping("records")
    public Map<String, Object> syncYXRecordsByAccount(@Valid YXRecordsParam param) {
        Date startDate = DateUtils.toDate(param.getStartDateStr());
        Date endDate = DateUtils.toDate(param.getEndDateStr());

        if (DateUtils.compare(startDate, endDate) > 0) {
            throw new ApplicationException("1003", "起始时间大于结束时间", null, null);
        }
        if (DateUtil.inSameMonth(startDate, endDate)) {
            throw new ApplicationException("1003",  "同步时间段不能跨月", null, null);
        }

        AccountDTO account = bdCallAccountInfoService.selectAccount(param.getAccountId(), param.getChannelType());
        if (account == null) {
            throw new ApplicationException("1003", "账号未配置:" + param.getChannelType() + " " + param.getAccountId(), null, null);
        }

        Date threeMonthAgo = DateUtils.addMonth(new Date(), -3);
        if(DateUtils.compare(threeMonthAgo, startDate) >= 0){
            // 校验通话记录是否重复的redis key三个月会失效, 同步三个月前的数据重复校验有可能失效
            // 继续校验同步时间段内是否已拉取过数据, 尽可能避免重复拉取数据
            long count;
            String index = BizConst.CALL_RECORD_ES_PREFIX + DateUtils.getYear(startDate) + DateUtils.getMonth(startDate);
            if (DateUtils.compare(threeMonthAgo, endDate) >= 0) {
                BoolQueryBuilder bqb = new BoolQueryBuilder();
                bqb.filter(QueryBuilders.rangeQuery("callTime").gte(param.getStartDateStr()).lte(param.getEndDateStr()));
                bqb.filter(QueryBuilders.matchQuery("channelType", param.getChannelType()));
                bqb.filter(QueryBuilders.matchQuery("callAccount", account));
                NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                        .withIndices(index)
                        .withTypes("_doc")
                        .withQuery(bqb)
                        .build();
                count = elasticsearchTemplate.count(searchQuery, CallRecord.class);
            } else {
                BoolQueryBuilder bqb = new BoolQueryBuilder();
                bqb.filter(QueryBuilders.rangeQuery("callTime").gte(param.getStartDateStr()).lte(DateUtils.toStr(threeMonthAgo)));
                bqb.filter(QueryBuilders.matchQuery("channelType", param.getChannelType()));
                bqb.filter(QueryBuilders.matchQuery("callAccount", account));

                NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                        .withIndices(index)
                        .withTypes("_doc")
                        .withQuery(bqb)
                        .build();
                count = elasticsearchTemplate.count(searchQuery, CallRecord.class);
            }
            if (count > 0) {
                throw new ApplicationException("1003", "起始时间位于三个月前,且在同步时间段内已拉取过数据", null, null);
            }
        }

        String idemCheckKey = RedisConst.IDEMPOTENT_CHECK + param.getChannelType() + ":" + param.getAccountId() + ":" + startDate.getTime() + "_" + endDate.getTime();
        //设置失败如何做补偿
        RedisUtil.stringRedisTemplate.opsForValue().set(idemCheckKey, "1", 7, TimeUnit.DAYS);

        YxRecordsManualSyncTask yxRecordsManualSyncTask = new YxRecordsManualSyncTask(account, param.getChannelType(), param.getStartDateStr(), param.getEndDateStr());
        if ("N".equals(param.getDownloadVoice())) {
            yxRecordsManualSyncTask.setDownloadVoice(false);
        }
        if (param.getStepHour() > 1) {
            yxRecordsManualSyncTask.setStepHour(param.getStepHour());
        }
        singleExecutorService.execute(yxRecordsManualSyncTask);
        Map<String, Object> result = new HashMap<>(1);
        result.put("msg", "开始同步!");
        return result;
    }

    /**
     * @Description: YxRecordsManualSyncTask
     * <AUTHOR>
     * @Date 2021/11/9 17:50
     */
    private static class YxRecordsManualSyncTask implements Runnable {

        private static final Logger LOGGER = LoggerFactory.getLogger(YxRecordsManualSyncTask.class);
        private final AccountDTO account;
        private final String channelType;
        private final String startDateStr;
        private final String endDateStr;
        /**
         * 是否下载通话记录对应的录音
         */
        private boolean downloadVoice = true;
        /**
         * 同步时间段的执行步长, 单位小时
         */
        private int stepHour = 1;

        public YxRecordsManualSyncTask(AccountDTO account, String channelType, String startDateStr, String endDateStr) {
            this.account = account;
            this.channelType = channelType;
            this.startDateStr = startDateStr;
            this.endDateStr = endDateStr;
        }

        public void setDownloadVoice(boolean downloadVoice) {
            this.downloadVoice = downloadVoice;
        }

        public void setStepHour(int stepHour) {
            this.stepHour = stepHour;
        }

        @Override
        public void run() {
            YXService zkService;
            if (CallChannelEnum.CALL_TYPE_ZK.getType().equals(channelType)) {
                zkService = new YXService(CallChannelEnum.CALL_TYPE_ZK.getType(), account, downloadVoice);
            } else if (CallChannelEnum.CALL_TYPE_YH.getType().equals(channelType)){
                zkService = new YXService(CallChannelEnum.CALL_TYPE_YH.getType(), account, downloadVoice);
            } else if (CallChannelEnum.CALL_TYPE_YX_SIP.getType().equals(channelType)) {
                zkService = new YXService(CallChannelEnum.CALL_TYPE_YX_SIP.getType(), account, downloadVoice);
            } else {
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "不支持的渠道类型:" + channelType, null, null);
            }

            LOGGER.info(this + " 任务开始执行...");
            try {
                zkService.sync(channelType, startDateStr, endDateStr, stepHour);
            } catch (ApplicationException e) {
                LOGGER.error(e, e.getMessage());
            }

            // 幂等校验使用的key,任务执行完成后删除
            String idemCheckKey = RedisConst.IDEMPOTENT_CHECK + channelType
                    + ":" + account.getApiAccount()
                    + ":" + DateUtils.toDate(startDateStr).getTime() + "_" + DateUtils.toDate(endDateStr).getTime();
            try {
                RedisUtil.stringRedisTemplate.delete(idemCheckKey);
            } catch (Exception e) {
                LOGGER.error(e, e.getMessage());
            }
            LOGGER.info(this + " 任务执行完成!!!");
        }

        @Override
        public String toString() {
            return "YxRecordsManualSyncTask{" +
                    "account=" + account.getAccountName() +
                    ", channelType='" + channelType + '\'' +
                    ", startDateStr='" + startDateStr + '\'' +
                    ", endDateStr='" + endDateStr + '\'' +
                    ", downloadVoice=" + downloadVoice +
                    ", stepHour=" + stepHour +
                    '}';
        }
    }
}
