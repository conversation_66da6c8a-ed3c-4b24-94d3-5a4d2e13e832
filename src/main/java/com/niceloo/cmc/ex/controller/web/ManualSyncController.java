package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.common.*;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.RecordQueryDTO;
import com.niceloo.cmc.ex.pojo.param.AddRecordParam;
import com.niceloo.cmc.ex.pojo.param.ManualSyncParam;
import com.niceloo.cmc.ex.pojo.param.SyncVoiceParam;
import com.niceloo.cmc.ex.service.call.CallRecordUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.cmc.ex.utils.RedisUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.mq.client.Client;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @desc: 手动同步接口
 * @author: song
 * @date: 2022/2/25
 */
@Api("手动同步接口")
@Validated
@RestController
@RequestMapping("manual/sync")
public class ManualSyncController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManualSyncController.class);

    private static final ExecutorService manualSyncVoiceExecutorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2, new CustomThreadFactory("manual_sync_voice"));

    /*默认MQClient*/
    @Resource
    private Client client;

    /*拉取通话记录特用MQClient*/
    @Resource
    private Client syncRecordRabbitMqClient;
    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;


    @ApiOperation("补充员工信息")
    @PostMapping("record/addEmployeeInfo")
    public String addCallerEmployeeInfo(@Valid AddRecordParam param) {
        List<String> indexNames;
        List<String> allIndexNames = RecordUtil.getAllIndexNames();
        if (BizConst.CALL_RECORD_ES_ALL_PREFIXS.equals(param.getIndexes())) {
            indexNames = allIndexNames;
        } else {
            indexNames = Arrays.asList(param.getIndexes().split(","));
            if (!allIndexNames.containsAll(indexNames)) {
                throw new ApplicationException("1003", param.getIndexes() + ": 全部或某个索引不存在");
            }
        }
        List<String> failedIndexNames = new ArrayList<>();
        RecordQueryDTO recordQueryDTO = new RecordQueryDTO();
        BeanCopier copier = BeanCopier.create(AddRecordParam.class, RecordQueryDTO.class, false);
        copier.copy(param, recordQueryDTO, null);
        for (String indexName : indexNames) {
            try {
                recordQueryDTO.setIndexes(indexName);
                client.publish(MQConst.ADD_EE_INFO_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordQueryDTO), 1000);
            } catch (Exception e) {
                LOGGER.error(e, e.toString());
                failedIndexNames.add(indexName);
            }
        }
        if (failedIndexNames.size() == indexNames.size()) {
            return String.format("补充员工信息(%s--%s--%s--%s):任务提交失败", param.getIndexes(), param.getCallTimeStart(), param.getCallTimeEnd(), param.getChannelType());
        }
        if (!failedIndexNames.isEmpty()) {
            return String.format("补充员工信息:(%s--%s--%s--%s)任务提交失败,其他任务提交成功", failedIndexNames, param.getCallTimeStart(), param.getCallTimeEnd(), param.getChannelType());
        }
        return String.format("补充员工信息(%s--%s--%s--%s):任务已提交", param.getIndexes(), param.getCallTimeStart(), param.getCallTimeEnd(), param.getChannelType());
    }

    @ApiOperation("补充客户信息")
    @PostMapping("record/addCustomerInfo")
    public String addCustomerInfo(@Valid AddRecordParam param) {
        String taskName = String.format("补充客户信息(%s--%s--%s--%s):任务已提交", param.getIndexes(), param.getCallTimeStart(), param.getCallTimeEnd(), param.getChannelType());
        for (String indexName : splitToIndexNames(param.getIndexes())) {
            CallRecordUtil.addCustomerInfo(indexName, param.getCallTimeStart(), param.getCallTimeEnd(), param.getChannelType());
        }
        return taskName;
    }

    @ApiOperation("同步录音")
    @PostMapping("voice")
    public String syncVoiceToEs(@Valid SyncVoiceParam param) {
        for (String indexName : splitToIndexNames(param.getIndexes())) {
            manualSyncVoiceExecutorService.submit(CallRecordUtil.syncVoice(indexName, param.getCallTimeStart(), param.getCallTimeEnd(), param.getVoiceSyncStatusStart(), param.getVoiceSyncStatusEnd(), param.getChannelType()));
        }
        return String.format("同步录音任务提交成功(%s--%s--%s--%s--%s--%s)!多任务并发执行时请确保各任务处理的数据不交叉,否则录音有可能重复上传!!!",
                param.getIndexes(), param.getCallTimeStart(), param.getCallTimeEnd(), param.getVoiceSyncStatusStart(), param.getVoiceSyncStatusEnd(), param.getChannelType());
    }

    /**
     * @param indexes 逗号拼接的索引名,call_record_*为全部索引
     * @return 索引名列表
     * @throws ApplicationException-指定的索引名不存在
     * <AUTHOR>
     * @Date 2022/2/14 11:25
     */
    private List<String> splitToIndexNames(String indexes) throws ApplicationException {
        List<String> indexNames;
        List<String> allIndexNames = RecordUtil.getAllIndexNames();
        if (BizConst.CALL_RECORD_ES_ALL_PREFIXS.equals(indexes)) {
            indexNames = allIndexNames;
        } else {
            indexNames = Arrays.asList(indexes.split(","));
            if (!allIndexNames.containsAll(indexNames)) {
                throw new ApplicationException("1003", indexes + ": 全部或某个索引不存在", null);
            }
        }
        return indexNames;
    }


    /**
     * 手动同步通话记录
     * 补充操作:
     * 1 不可以补充两个月之前的数据
     * 2 同一个通道,同一同步时间段: 24H内只处理一次
     * 非补充操作:
     * 1 ES中存在创建时间在同步时间段内的通话记录不允许操作(在开始时间对应的ES索引中查询)
     * 2 同一个通道,同一同步时间段: (7D+同步时间段)内只处理一次
     */
    @ApiOperation("拉取通话记录")
    @PostMapping("record")
    public Map<String, String> recordSync(@Valid ManualSyncParam param) {
        String setData = param.getStartDate() + "_" + param.getEndDate();
        if("Y".equals(param.getReplenish())){
            // 存储同步周期，防止重复提交，过期时间为一天
            String key = RedisConst.RECORD_SYNC_KEY + "_" + param.getChannelType() + "_" + setData;
            LOGGER.info("拉取通话记录时，同步周期的key:{}", key);
            Object value = RedisUtil.redisTemplate.opsForHash().get(key, setData);
            if(!ObjectUtils.isEmpty(value)){
                throw new ApplicationException("1003", "此周期内的通话记录正在同步，时间段为 " + param.getStartDate() + " " + value);
            }
            RedisUtil.redisTemplate.opsForHash().put(key, setData, setData);
            RedisUtil.redisTemplate.expire(key, 1, TimeUnit.DAYS);

            // 如果是同步两个月之前的数据，则拒绝
            String[] nows = DateUtils.getNowDString().split("-");
            Date firstDayOfMonth = DateUtils.getFirstDayOfMonth(Integer.parseInt(nows[0]), Integer.parseInt(nows[1]));
            Date twoMonthAgo = DateUtils.addMonth(firstDayOfMonth, -2);
            Date start = DateUtils.toDate(param.getStartDate());
            if(DateUtils.compare(twoMonthAgo, start) >= 0){
                throw new ApplicationException("1003", "不可以补充两个月之前的数据");
            }
        }else {
            // 先检查 ES 是否已经同步过，是则不允许同步
            // bug: 以startDate确定索引, 当同步时间段跨月时, 此限制条件只在首月生效
            String[] splits = param.getStartDate().split("-");
            String indexName = BizConst.CALL_RECORD_ES_PREFIX + splits[0] + splits[1];
            NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
            builder.withIndices(indexName);
            builder.withTypes("_doc");
            BoolQueryBuilder bqb = new BoolQueryBuilder();
            bqb.filter(QueryBuilders.rangeQuery("callTime").gte(param.getStartDate()).lte(param.getEndDate()));
            if (CallChannelEnum.CALL_TYPE_YH.getType().equals(param.getChannelType())) {
                bqb.filter(QueryBuilders.matchQuery("channelType", param.getChannelType()));
                bqb.filter(QueryBuilders.matchQuery("field3",  CallRecord.CALL_TYPE_YH_FIELD3));
            } else {
                bqb.filter(QueryBuilders.matchQuery("channelType", param.getChannelType()));
            }
            builder.withQuery(bqb);
            builder.withPageable(PageRequest.of(0, 1));
            NativeSearchQuery searchQuery = builder.build();
            List<CallRecord> callRecords = elasticsearchTemplate.queryForList(searchQuery, CallRecord.class);
            if(!CollectionUtils.isEmpty(callRecords)){
                throw new ApplicationException("1003", "此周期内的通话记录同步完成或正在同步", null, null);
            }
            // 存储同步周期，防止重复提交，过期时间为整体时间加一周
            String key = RedisConst.RECORD_SYNC_KEY + "_" + param.getChannelType() + "_" + setData;
            Object value = RedisUtil.redisTemplate.opsForHash().get(key, setData);
            if(null != value){
                throw new ApplicationException("1003", "此周期内的通话记录正在同步", null, null);
            }
            RedisUtil.redisTemplate.opsForHash().put(key, setData, setData);
            RedisUtil.redisTemplate.expire(key, DateUtils.compare(param.getEndDate(), param.getStartDate()), TimeUnit.DAYS);
        }

        String uuid = UUID.randomUUID().toString();
        if (param.getStartDate().contains(" CST ")) {
            LOGGER.warn(String.format("ManualSyncController#recordSync 的 param.getStartDate(): %s, uuid: %s", param.getStartDate(), uuid));
        }

        // 根据入参的actionType确定发布的队列的操作类型
        String actionType = param.getActionType();
        syncRecordRabbitMqClient.publish(MQConst.CALL_RECORD_TOPIC, uuid, param.getChannelType() + BizConst.LINK_SYMBOL + param.getStartDate() + BizConst.LINK_SYMBOL + param.getEndDate() + BizConst.LINK_SYMBOL + BizConst.MANUAL+ BizConst.LINK_SYMBOL + actionType);
        Map<String, String> result = new HashMap<>(1);
        result.put("msg", "同步开始");
        return result;
    }



}
