package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.common.AICallJobStatus;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.pojo.dto.jdyx.*;
import com.niceloo.cmc.ex.pojo.request.*;
import com.niceloo.cmc.ex.pojo.vo.*;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.service.CcCallAccountConfigService;
import com.niceloo.cmc.ex.service.ai.JDYXService;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.ApiParamBody;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import com.niceloo.segment.core.NicelooIdTemplate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI外呼任务管理控制层
 *
 * <AUTHOR>
 * @since 2022-08-11 16:05
 */
@RestController
@RequestMapping("/aiCallJob/")
@Validated
@Api("AI外呼任务管理控制层")
public class AICallJobManageController {

    private static final Logger logger = LoggerFactory.getLogger(AICallJobManageController.class);
    @Resource
    private NicelooIdTemplate nicelooIdTemplate;
    @Resource
    private JDYXService jdyxService;
    @Resource
    private CcAiCallJobService ccAiCallJobService;
    @Resource
    private CcCallAccountConfigService callAccountConfigService;
    @Resource
    private AICallJobCustomerInfoService aiCallJobCustomerInfoService;

    private final CallProperties.YanxiProperty yanxiProperty;

    public AICallJobManageController(CallProperties.YanxiProperty yanxiProperty) {
        this.yanxiProperty = yanxiProperty;
    }

    @PostMapping("/createCallRecordIndex")
    @ApiOperation(value = "创建AI外呼任务通话记录索引")
    // 生产近6个月调用次数= 0
    public String createCallRecordIndex(String indexName) {
        logger.info("接口请求创建AI外呼任务通话记录索引,索引名称:{}", indexName);
        aiCallJobCustomerInfoService.createIndex(indexName);
        return "创建成功";
    }

    @PostMapping("/getContextFullList")
    @ApiOperation(value = "查询机器人对应的话术模板列表（言犀话术模板全量信息）", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 1300
    public List<JDContextFullDTO> getContextFullList(@NotBlank(message = "租户ID不能为空") @ApiParam("租户ID")  String tenantId) throws ApplicationException {
        List<JDContextFullDTO> contextList = jdyxService.getContextFullList(tenantId);
        if (null == contextList) {
            contextList = new ArrayList<>();
        }
        return contextList;
    }

    @PostMapping("/getContextList")
    @ApiOperation(value = "查询机器人对应的话术模板列表", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 1300
    public List<JDContextListDTO> getContextList(String userPin, @ApiIgnore NgExpand ngExpand) throws ApplicationException {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<JDContextListDTO> contextList = jdyxService.getContextList(userPin, ngExpand);
        if (null == contextList) {
            contextList = new ArrayList<>();
        }
        return contextList;
    }

    @PostMapping("/getContextListLikeName")
    @ApiOperation(value = "根据名称模糊查询机器人对应的话术模板列表", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 0
    public List<JDContextListDTO> getContextListLikeName(String contextName, @ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<JDContextListDTO> contextList = jdyxService.getContextList(null, ngExpand);
        if (null == contextList) {
            contextList = new ArrayList<>();
        }
        // 模糊匹配
        if (StringUtils.isNotEmpty(contextName)) {
            Iterator<JDContextListDTO> iterator = contextList.iterator();
            while (iterator.hasNext()) {
                JDContextListDTO contextDTO = iterator.next();
                String contextDTOName = contextDTO.getName();
                if (!contextDTOName.contains(contextName)) {
                    iterator.remove();
                }
            }
        }
        return contextList;
    }

    @PostMapping("/getTagList")
    @ApiOperation(value = "查询机器人对应的话术意向标签", consumes = "application/x-www-form-urlencoded")
    public List<JDTagListDTO.TagDTO> getTagList(@Valid @ApiParamBody JDTagListRequest jdTagListRequest, @ApiIgnore NgExpand ngExpand) throws ApplicationException {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<JDTagListDTO.TagDTO> tagList = jdyxService.getTagList(jdTagListRequest, ngExpand);
        if (null == tagList) {
            tagList = new ArrayList<>();
        }
        return tagList;
    }

    @PostMapping("/getGroupList")
    @ApiOperation(value = "查询机器人对应的意向标签分组", consumes = "application/x-www-form-urlencoded")
    public List<JDGroupListDTO> getGroupList(@NotBlank(message = "话术ID不能为空") @ApiParam("话术ID")  String cid, @ApiIgnore NgExpand ngExpand) throws ApplicationException {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<JDGroupListDTO> groupList = jdyxService.getGroupList(cid, ngExpand);
        if (null == groupList) {
            groupList = new ArrayList<>();
        }
        return groupList;
    }

    @PostMapping("/getLineList")
    @ApiOperation(value = "查询电话线路列表", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 1050
    public List<JDLineListDTO> getLineList(@ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<JDLineListDTO> lineList = jdyxService.getLineList(ngExpand);
        if (null == lineList) {
            lineList = new ArrayList<>();
        }
        return lineList;
    }

    /**
     * 一、
     * 1、传入任务的信息调用创建任务的接口
     * 2、校验任务名称和参数问题
     * 3、创建任务，保存到mysql内
     * 4、响应接口，内容为父任务的id
     * <br/>
     * 二、
     * 1、客户营销调用追加客户的接口，需要带着前面返回的ID，一次只能传递一万条客户信息
     * 2、校验任务id是否存在，查数据库得到父任务的信息
     * 3、对任务列表根据分校进行分组
     * 4、循环分校ID分组的key列表，用来创建子任务
     * 5、使用分布式锁，来insert into 子任务，避免重复创建，创建前先根据父任务id查询出全部的子任务，再根据schoolId校验该子任务是否存在，不
     * 存在的创建，
     * 6、在分布式锁内批量调用外呼任务创建接口，来创建子任务
     * 7、将客户信息放入ES内 (加一个批次号用来区分是那一次请求加入的ES)
     * 8、任务创建完成或者没有需要创建的任务，响应客户营销
     * 9、发送MQ来追加客户信息(发送的时候需要带着批次号，防止重复追加)
     * 9.1、消费MQ，查询ES得到需要追加的客户信息，进行滚动查询来调用京东的接口来追加客户
     *
     * @param request  创建任务的参数
     * @param ngExpand 操作人信息
     * @return java.lang.String
     * <AUTHOR>
     * @since 10:38 2022/9/30
     **/
    @PostMapping("/createJob")
    @ApiOperation(value = "创建外呼任务", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 950
    public String createJob(@Valid @ApiParamBody JDCreateJobRequest request, @ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }

        request.check(ccAiCallJobService);

        // 创建人userId
        UcUser ucUser = ngExpand.getUcUser();
        // 校验账号的正确性
        CcCallAccountConfig ccCallAccountConfig = callAccountConfigService.searchCallAccountInfoByUserId(ucUser.getUserId(), CallChannelEnum.CALL_TYPE_JDYX.getType());
        if (null == ccCallAccountConfig || !Objects.equals(ccCallAccountConfig.getDisableStatus(), "N")) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "请检查您的京东AI外呼账号配置!!!");
        }

        // 创建父任务到MySQL
        List<JDLineListDTO> lineList = jdyxService.getLineList(ngExpand);
        CallProperties.YanxiProperty.Tenant tenant = jdyxService.getTenantByToken(ngExpand);
        CcAiCallJob parentAiCallJob = request.convertToAiCallJobEntity(CallChannelEnum.CALL_TYPE_JDYX, null, lineList, request.getCustomerNum(), ucUser, tenant);
        String aiJobId = nicelooIdTemplate.get("AiCallJob");
        parentAiCallJob.setJobId(aiJobId);
        ccAiCallJobService.save(parentAiCallJob);
        return aiJobId;
    }

    @PostMapping("/addCustomerList")
    @ApiOperation(value = "创建子任务并追加客户信息", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 38500
    public String createSubJobAndAddCustomer(@Valid @ApiParamBody JDCallCustomerAddRequest request, @ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        logger.info("创建子任务并追加客户信息:{},父任务ID:{},客户数量:{}", "/api/commu/aiCallJob/addCustomerList", request.getJobId(), request.getJdCallCustomerRequestList() == null ? 0 : request.getJdCallCustomerRequestList().size());
        if (request.getJdCallCustomerRequestList() != null && request.getJdCallCustomerRequestList().size() > 500) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "客户数量一次传递数量不能超过500条");
        }
        String jobId = request.getJobId();
        // 获取父任务信息
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(jobId);
        if (aiCallJob == null || StringUtils.isEmpty(aiCallJob.getJobName())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!任务对象或者任务名称不能为空!!!");
        }
        // 创建子任务并追加客户的信息到子任务
        jdyxService.createSubJobAndAddCustomer(aiCallJob, request, ngExpand);
        return "任务创建完成,客户信息追加中";
    }

    @PostMapping("/selectJobPage")
    @ApiOperation(value = "分页查询外呼任务列表", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 7000
    public BasePageVO<AiCallJobVO> selectJobPage(@ApiParamBody AICallJobListRequest request, @ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        request.setAiJobType(CallChannelEnum.CALL_TYPE_JDYX.getType());
        // 根据条件分页查询
        BasePageVO<AiCallJobVO> basePageVO = ccAiCallJobService.selectJobPage(request);
        if (basePageVO == null || basePageVO.getData().isEmpty()) {
            return basePageVO;
        }
        return basePageVO;
    }


    @PostMapping("/editJob")
    @ApiOperation(value = "编辑京东言犀外呼任务", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 0
    public AiCallJobVO editJob(@Valid @ApiParamBody JDEditJobRequest request, @ApiIgnore NgExpand ngExpand) {
        if (ngExpand == null || ngExpand.getUcUser() == null || StringUtils.isEmpty(ngExpand.getUcUser().getUserId())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法接口调用!!!");
        }

        // 根据主键id查询外呼任务
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(request.getId());
        AiCallJobVO aiCallJobVO = request.check(aiCallJob, ccAiCallJobService);
        if (aiCallJobVO != null) {
            return aiCallJobVO;
        }

        // 更新AI外呼数据库
        CcAiCallJob editAiCallJob = BeanUtils.copyFromObjToClass(CcAiCallJob.class, request);
        editAiCallJob.setConcurrency(request.getConcurrentNumber());
        editAiCallJob.setJobId(aiCallJob.getJobId());
        editAiCallJob.setJobName(request.getName());
        editAiCallJob.setErrorLog("");
        UcUser ucUser = ngExpand.getUcUser();
        editAiCallJob.setModifier(ucUser.getUserId());
        editAiCallJob.setModifyName(ucUser.getUserName());
        editAiCallJob.setModifyDate(DateUtils.getNowDString());
        // 线路属性赋值
        if (null != editAiCallJob.getLineId() && !aiCallJob.getLineId().equals(editAiCallJob.getLineId())) {
            List<JDLineListDTO> lineList = jdyxService.getLineList(ngExpand);
            lineList.forEach(t -> {
                if (editAiCallJob.getLineId().equals(t.getLineId())) {
                    editAiCallJob.setLineName(t.getLineName());
                    editAiCallJob.setDisplayNumber(t.getDisplayNumbers().get(0));
                }
            });
        }
        ccAiCallJobService.updateById(editAiCallJob);
        // 调用京东言犀外呼接口创建AI外呼任务
        jdyxService.createJobFromVendor(aiCallJob.getJobId(), ngExpand);
        aiCallJob = ccAiCallJobService.getById(request.getId());
        return BeanUtils.copyFromObjToClass(AiCallJobVO.class, aiCallJob);
    }

    @PostMapping("/operatingJob")
    @ApiOperation(value = "AI外呼任务操作", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 5300
    public AiCallJobVO operatingJob(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId,
                                    @ApiParam(value = "操作类型(1->启动,2->暂停,3->继续,4->删除)")
                                    @Max(value = 4, message = "操作类型为非法参数")
                                    @Min(value = 1, message = "操作类型为非法参数") Integer type,
                                    @ApiIgnore NgExpand ngExpand) {
        if (ngExpand == null || ngExpand.getUcUser() == null || StringUtils.isEmpty(ngExpand.getUcUser().getUserId())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法接口调用!!!");
        }
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数！！！根据任务ID未找到任务!!");
        }
        if (aiCallJob.getStatus().equals(AICallJobStatus.canceled.name())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "该任务已处于删除状态");
        }
        UcUser ucUser = ngExpand.getUcUser();
        aiCallJob.setModifier(ucUser.getUserId());
        aiCallJob.setModifyName(ucUser.getUserName());
        Integer jobProgress = aiCallJob.getJobProgress();
        //type=4为删除操作,删除操作需要先更新数据,再删除
        if (type == 4) {
            String userId = ngExpand.getUcUser().getUserId();
            if (!userId.equals(aiCallJob.getCreator())) {
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "当前仅支持删除本人创建的任务。");
            }
            // 只有任务已经在京东言犀后台创建成功了才需要更新
            if (!jobProgress.equals(3)) {
                this.updateJobInfo(aiCallJob, ngExpand);
            }
        }
        //AI外呼任务操作
        if (!jobProgress.equals(3)) {
            jdyxService.operatingJob(aiCallJob.getVendorJobId(), type, aiCallJob.getUserPin(), ngExpand);
        }
        if (type != 4) {
            this.updateJobInfo(aiCallJob, ngExpand);
        } else {
            aiCallJob.setStatus(AICallJobStatus.canceled.name());
        }
        //更新最新数据到数据库
        ccAiCallJobService.updateById(aiCallJob);
        return BeanUtils.copyFromObjToClass(AiCallJobVO.class, aiCallJob);
    }

    @PostMapping("/batchUpdateAICallJob")
    @ApiOperation(value = "批量更新AI外呼任务(只返回需要更新的外呼任务列表,比如已经被删除的不会返回)", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 6600
    public List<AiCallJobVO> batchUpdateAICallJob(@ApiParam("AI外呼任务主键id")
                                                  @RequestParam(value = "jobIds")
                                                  @NotNull(message = "jobIds不能为空") List<String> jobIds, @ApiIgnore NgExpand ngExpand) {
        if (ngExpand == null || ngExpand.getUcUser() == null || StringUtils.isEmpty(ngExpand.getUcUser().getUserId())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法接口调用!!!");
        }
        UcUser ucUser = ngExpand.getUcUser();
        // 批量更新外呼任务
        List<CcAiCallJob> filteredCallJobList = jdyxService.batchUpdateAICallJob(jobIds, ucUser, null);
        // 数据copy返回前端
        return BeanUtils.copyFromMultiObjToClass(AiCallJobVO.class, filteredCallJobList);
    }


    @PostMapping("/aiJobInfo")
    @ApiOperation(value = "根据AI任务id查询任务详情信息", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 1
    public AiCallJobInfoVO aiJobInfo(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!根据任务ID未找到任务!!");
        }
        return BeanUtils.copyFromObjToClass(AiCallJobInfoVO.class, aiCallJob);
    }

    @PostMapping("/aiSubJob/callNumStat")
    @ApiOperation(value = "AI外呼子任务统计数据总览接口", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 3500
    public AiJobCallNumStateVO callNumStat(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!根据任务ID未找到任务!!");
        }
        return aiCallJobCustomerInfoService.callNumStatAggs(aiJobId, aiCallJob.getCustomerIndex());
    }

    @PostMapping("/aiSubJob/aiJobAggsState")
    @ApiOperation(value = "AI外呼子任务数据聚合统计接口", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 3500
    public AiJobAggsStateVO aiJobAggsState(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!根据任务ID未找到任务!!");
        }
        return aiCallJobCustomerInfoService.aiJobAggsState(aiJobId, aiCallJob.getCustomerIndex());
    }

    @PostMapping("/aiSubJob/selectAiJobInfoDetail")
    @ApiOperation(value = "AI外呼子任务客户明细分页查询接口", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 3800
    public BasePageVO<AiJobInfoDetailVO> selectAiJobInfoDetail(@ApiParam("请求参数") @Valid AiJobInfoDetailRequest request) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(request.getAiJobId());
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!根据任务ID未找到任务!!");
        }
        return aiCallJobCustomerInfoService.selectAiJobInfoDetail(request, aiCallJob.getCustomerIndex());
    }

    @PostMapping("/aiSubJob/selectAiJobCustomerInfo")
    @ApiOperation(value = "AI外呼子任务客户详情查询接口", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 25
    public AiJobInfoDetailVO selectAiJobCustomerInfo(@ApiParam(value = "主键id") @NotNull(message = "主键id为空") String id,
                                                     @ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id为空") String aiJobId) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!根据任务ID未找到任务!!");
        }
        return aiCallJobCustomerInfoService.selectAiJobCustomerInfo(id, aiCallJob.getCustomerIndex());
    }

    @PostMapping("/aiSubJob/aiJobInfoDetail/excel")
    @ApiOperation(value = "AI外呼子任务客户列表Excel下载接口", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 0
    public String aiJobInfoDetailExcel(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId,
                                       @ApiIgnore NgExpand ngExpand) {
        UcUser user = ngExpand.getUcUser();
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!根据任务ID未找到任务!!");
        }
        String jobName = aiCallJob.getJobName();
        return aiCallJobCustomerInfoService.aiJobInfoDetailExcel(aiJobId, aiCallJob.getCustomerIndex(), jobName, user);
    }

    /**
     * 更新外呼任务
     *
     * @param aiCallJob 外呼任务
     * <AUTHOR>
     * @since 17:35 2022/8/13
     **/
    private void updateJobInfo(CcAiCallJob aiCallJob, NgExpand ngExpand) {
        JDJobInfoDTO jobInfo = jdyxService.getJobInfo(aiCallJob.getVendorJobId(), null, ngExpand);
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        if (jobInfo == null) {
            // 任务在京东言犀后台删除
            throw new RuntimeException("未在京东言犀后台查询到此AI外呼任务,请检查是否已经在京东言犀删除!!!");
        }
        List<JDContextListDTO> contextList = jdyxService.getContextList(null, ngExpand);
        Map<Integer, String> contextMap = contextList.stream().collect(Collectors.toMap(JDContextListDTO::getId, JDContextListDTO::getName));
        String contextName = contextMap.get(aiCallJob.getContextId());
        aiCallJob.setContextName(contextName);
        aiCallJob.setJobName(jobInfo.getJobName());
        aiCallJob.setStatus(jobInfo.getStatus());
        aiCallJob.setModifyDate(DateUtils.getNowDString());
    }

    @PostMapping("/getTenants")
    @ApiOperation(value = "查询租户列表", consumes = "application/x-www-form-urlencoded")
    // 生产近6个月调用次数= 4
    public List<String> getTenants() {
        List<CallProperties.YanxiProperty.Tenant> tenants = yanxiProperty.getTenants();

        return tenants.stream().map(CallProperties.YanxiProperty.Tenant::getTenantId).collect(Collectors.toList());
    }
}
