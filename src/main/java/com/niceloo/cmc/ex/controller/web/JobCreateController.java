package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.pojo.request.JDCallCustomerAddRequest;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.RandomUtils;
import com.niceloo.framework.web.NgExpand;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.niceloo.cmc.ex.common.BizConst.TOP_SCHOOL_ID;

/**
 * <AUTHOR>
 * @Date 2022-10-06 14:56
 */
@RestController
@RequestMapping("/aiCallJob/")
@Validated
@Api("外呼任务创建接口测试接口")
public class JobCreateController {

    private static final Logger logger = LoggerFactory.getLogger(JobCreateController.class);
    @Resource
    private AICallJobManageController aiCallJobManageController;

    @GetMapping("/addCustomerFormJobTest")
    @ApiOperation(value = "外呼任务添加客户信息", consumes = "application/x-www-form-urlencoded")
    public long addCustomerTest(String jobId,Integer total,Integer schoolNum) {
        int pageSize = 500;
        int page = (total + pageSize - 1) / pageSize;
        long totalTime = 0;
        for (int i = 0; i < page; i++) {
            List<JDCallCustomerRequest> jdCallCustomerRequestList = new ArrayList<>();
            JDCallCustomerAddRequest jdCreateJobRequest = new JDCallCustomerAddRequest();
            jdCreateJobRequest.setJobId(jobId);
            jdCreateJobRequest.setFinishFlag("N");
            if (i == page - 1) {
                jdCreateJobRequest.setFinishFlag("Y");
            }
            for (int j = 0; j < pageSize; j++) {
                JDCallCustomerRequest customerRequest = new JDCallCustomerRequest();
                String k = (i < 10 ? "000" + i : i < 100 ? "00" + i : i < 1000 ? "0" + i : String.valueOf(i));
                String q = k + (j < 10 ? "000" + j : j < 100 ? "00" + j : "0" + j);
                customerRequest.setName("王晨雨" + q);
                customerRequest.setPhone("176339" + q);
                int i1 = RandomUtils.randomInt(schoolNum);
                customerRequest.setSchoolId(TOP_SCHOOL_ID + i1);
                customerRequest.setSchoolName("环球优路" + i1);
                customerRequest.setCustId("CUST7209754010184" + q);
                jdCallCustomerRequestList.add(customerRequest);
            }
            jdCreateJobRequest.setJdCallCustomerRequestList(jdCallCustomerRequestList);
            long l = System.currentTimeMillis();
            aiCallJobManageController.createSubJobAndAddCustomer(jdCreateJobRequest, new NgExpand());
            long l2 = System.currentTimeMillis();
            totalTime += (l2 - l);
            logger.info("子接口耗时:->" + (l2 - l) + "毫秒");
        }
        logger.info("接口总耗时:->" + (totalTime / 1000) + "秒");
        return totalTime;
    }
}
