package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.service.CcCountdayEncryptService;
import com.niceloo.cmc.ex.service.CcCountmonthEncryptService;
import com.niceloo.cmc.ex.service.ConfigEncryptService;
import com.niceloo.cmc.ex.service.ModifyLogEncryptService;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.web.validation.anno.IsSafe;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicInteger;

import static java.lang.Thread.sleep;

/**
 * 日、月统计表加密
 *
 * <AUTHOR>
 * @since 2023-11-20 17:05:37
 */
@RestController
@RequestMapping("/encryption/")
@Validated
@Api(tags = "日、月统计表加密")
public class CcCountEncryptController {

    private static final Logger logger = LoggerFactory.getLogger(CcCountEncryptController.class);

    @Resource
    private CcCountdayEncryptService ccCountdayEncryptService;
    @Resource
    private CcCountmonthEncryptService ccCountmonthEncryptService;
    @Resource
    private ConfigEncryptService configEncryptService;
    @Resource
    private ModifyLogEncryptService modifyLogEncryptService;

    /**
     * 加密CcCountday和CcCountmonth表数据
     *
     * @return 加密结果
     */
    @ApiOperation(value = "加密统计表数据")
    @PostMapping("cc/count")
    public String encryptTable(
            @IsSafe @ApiParam("表名") @RequestParam(defaultValue = "McMsg") String tableName,
            @IsSafe @ApiParam("刷新总条数") @RequestParam(defaultValue = "1000") String total,
            @IsSafe @ApiParam("每批次刷新条数") @RequestParam(defaultValue = "200") String batch) throws InterruptedException {

        AtomicInteger totalSize = new AtomicInteger(Integer.parseInt(total));
        AtomicInteger batchSize = new AtomicInteger(Integer.parseInt(batch));
        AtomicInteger currentSize = new AtomicInteger(0);

        while (totalSize.get() > 0) {

            currentSize.set(Math.min(totalSize.get(), batchSize.get()));

            if ( "CcCountday".equals(tableName)) {
                ccCountdayEncryptService.encryptTable(currentSize.get());
            } else if ( "CcCountmonth".equals(tableName)) {
                ccCountmonthEncryptService.encryptTable(currentSize.get());
            }

            totalSize.getAndUpdate(size -> size - currentSize.get());

            if (currentSize.get() == batchSize.get()) {
                sleep(2000);
            }
        }

        return "加密完成";
    }

    /**
     * 表CcCallAccountConfig的eeUserName、account、accountCreatorName、accountModifierName字段加密
     *
     * @return 加密结果
     */
    @ApiOperation(value = "加密CcCallAccountConfig表数据")
    @PostMapping("cc/callaccount/config")
    public String encryptCcCallAccountConfig(
            @IsSafe @ApiParam("刷新总条数") @RequestParam(defaultValue = "1000") String total,
            @IsSafe @ApiParam("每批次刷新条数") @RequestParam(defaultValue = "200") String batch) throws InterruptedException {

        AtomicInteger totalSize = new AtomicInteger(Integer.parseInt(total));
        AtomicInteger batchSize = new AtomicInteger(Integer.parseInt(batch));
        AtomicInteger currentSize = new AtomicInteger(0);

        while (totalSize.get() > 0) {

            currentSize.set(Math.min(totalSize.get(), batchSize.get()));

            configEncryptService.encryptCcCallAccountConfig(currentSize.get());

            totalSize.getAndUpdate(size -> size - currentSize.get());

            if (currentSize.get() == batchSize.get()) {
                sleep(2000);
            }
        }

        return "加密完成";
    }

    /**
     * 表CcCallAccountModifyLog的beforeEeUserName、afterEeUserName、accountLogCreatorName字段加密
     *
     * @return 加密结果
     */
    @ApiOperation(value = "加密CcCallAccountModifyLog表数据")
    @PostMapping("cc/callaccount/modifylog")
    public String encryptCcCallAccountModifyLog(
            @IsSafe @ApiParam("刷新总条数") @RequestParam(defaultValue = "1000") String total,
            @IsSafe @ApiParam("每批次刷新条数") @RequestParam(defaultValue = "200") String batch) throws InterruptedException {

        AtomicInteger totalSize = new AtomicInteger(Integer.parseInt(total));
        AtomicInteger batchSize = new AtomicInteger(Integer.parseInt(batch));
        AtomicInteger currentSize = new AtomicInteger(0);

        while (totalSize.get() > 0) {

            currentSize.set(Math.min(totalSize.get(), batchSize.get()));

            modifyLogEncryptService.encryptCcCallAccountModifyLog(currentSize.get());

            totalSize.getAndUpdate(size -> size - currentSize.get());

            if (currentSize.get() == batchSize.get()) {
                sleep(2000);
            }
        }

        return "加密完成";
    }
}
