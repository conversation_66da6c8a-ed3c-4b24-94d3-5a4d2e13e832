package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.common.AICallJobStatus;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.dto.byai.BYAIContextListDTO;
import com.niceloo.cmc.ex.pojo.dto.byai.BYAIJobInfoDTO;
import com.niceloo.cmc.ex.pojo.dto.byai.BYAILineListDTO;
import com.niceloo.cmc.ex.pojo.dto.byai.BYAITiandunListDTO;
import com.niceloo.cmc.ex.pojo.request.*;
import com.niceloo.cmc.ex.pojo.vo.*;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.service.ai.BYAIService;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.ApiParamBody;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import com.niceloo.segment.core.NicelooIdTemplate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/by/aiCallJob/")
@Validated
@Api("百应AI外呼任务管理控制层")
public class BYAICallJobManageController {

    private static final Logger logger = LoggerFactory.getLogger(BYAICallJobManageController.class);
    @Resource
    private NicelooIdTemplate nicelooIdTemplate;
    @Resource
    private BYAIService byaiService;
    @Resource
    private CcAiCallJobService ccAiCallJobService;
    @Resource
    private AICallJobCustomerInfoService aiCallJobCustomerInfoService;
    @Resource
    private CallProperties.ByAiProperty byAiProperty;


    @PostMapping("/createCallRecordIndex")
    @ApiOperation(value = "创建AI外呼任务通话记录索引")
    public String createCallRecordIndex(String indexName) {
        logger.info("接口请求创建AI外呼任务通话记录索引,索引名称:{}", indexName);
        aiCallJobCustomerInfoService.createIndex(indexName);
        return "创建成功";
    }

    @PostMapping("/getContextList")
    @ApiOperation(value = "查询机器人对应的话术模板列表", consumes = "application/x-www-form-urlencoded")
    public List<BYAIContextListDTO> getContextList(@ApiIgnore NgExpand ngExpand) throws ApplicationException {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<BYAIContextListDTO> contextList = byaiService.getContextList();
        if (null == contextList) {
            contextList = new ArrayList<>();
        }
        return contextList;
    }


    @PostMapping("/getContextListLikeName")
    @ApiOperation(value = "根据名称模糊查询机器人对应的话术模板列表", consumes = "application/x-www-form-urlencoded")
    public List<BYAIContextListDTO> getContextListLikeName(String contextName, @ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<BYAIContextListDTO> contextList = byaiService.getContextList();
        if (null == contextList) {
            contextList = new ArrayList<>();
        }
        // 模糊匹配
        if (StringUtils.isNotEmpty(contextName)) {
            Iterator<BYAIContextListDTO> iterator = contextList.iterator();
            while (iterator.hasNext()) {
                BYAIContextListDTO contextDTO = iterator.next();
                String contextDTOName = contextDTO.getName();
                if (!contextDTOName.contains(contextName)) {
                    iterator.remove();
                }
            }
        }
        return contextList;
    }

    @PostMapping("/getLineList")
    @ApiOperation(value = "查询电话线路列表", consumes = "application/x-www-form-urlencoded")
    public List<BYAILineListDTO> getLineList(@ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<BYAILineListDTO> lineList = byaiService.getLineList();
        if (null == lineList) {
            lineList = new ArrayList<>();
        }
        return lineList;
    }

    @PostMapping("/getTiandunList")
    @ApiOperation(value = "查询天盾策略列表", consumes = "application/x-www-form-urlencoded")
    public List<BYAITiandunListDTO> getTiandunList(@ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        List<BYAITiandunListDTO> lineList = byaiService.getTiandunList();
        if (null == lineList) {
            lineList = new ArrayList<>();
        }
        return lineList;
    }


    @PostMapping("/createJob")
    @ApiOperation(value = "创建外呼任务", consumes = "application/x-www-form-urlencoded")
    public String createJob(@Valid @ApiParamBody JDCreateJobRequest request, @ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }

        request.check(ccAiCallJobService);
        if (request.getIsRedial() == 1) {
            if (!(request.getRedialTimes() >= 1 && request.getRedialTimes() <= 5)) {
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "重拨次数（1-5）");
            } else if (!(request.getRedialInterval() >= 0 && request.getRedialInterval() <= 120)) {
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "间隔时间（0-120min）");
            }
        }

        CcAiCallJob parentAiCallJob = request.convertToAiCallJobEntity(CallChannelEnum.CALL_TYPE_BYAI, null, List.of(), request.getCustomerNum(), ngExpand.getUcUser(), null);
        String aiJobId = nicelooIdTemplate.get("AiCallJob");
        parentAiCallJob.setJobId(aiJobId);
        parentAiCallJob.setCompanyId(byAiProperty.getCompanyId());
        byaiService.getLineNameById(request.getLineId()).ifPresent(parentAiCallJob::setLineName);
        ccAiCallJobService.save(parentAiCallJob);
        return aiJobId;
    }

    @PostMapping("/addCustomerList")
    @ApiOperation(value = "创建子任务并追加客户信息", consumes = "application/x-www-form-urlencoded")
    public String createSubJobAndAddCustomer(@Valid @ApiParamBody JDCallCustomerAddRequest request, @ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        logger.info("创建子任务并追加客户信息:父任务ID:{},客户数量:{}", request.getJobId(), request.getJdCallCustomerRequestList().size());
        if (request.getJdCallCustomerRequestList().size() > 500) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "客户数量一次传递数量不能超过500条");
        }
        String jobId = request.getJobId();
        // 获取父任务信息
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(jobId);
        if (aiCallJob == null || StringUtils.isEmpty(aiCallJob.getJobName())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!");
        }
        // 创建子任务并追加客户的信息到子任务
        byaiService.createSubJobAndAddCustomer(aiCallJob, request, ngExpand);
        return "任务创建完成,客户信息追加中";
    }

    @PostMapping("/selectJobPage")
    @ApiOperation(value = "分页查询外呼任务列表", consumes = "application/x-www-form-urlencoded")
    public BasePageVO<AiCallJobVO> selectJobPage(@ApiParamBody AICallJobListRequest request) {
        request.setAiJobType(CallChannelEnum.CALL_TYPE_BYAI.getType());
        // 根据条件分页查询
        BasePageVO<AiCallJobVO> basePageVO = ccAiCallJobService.selectJobPage(request);
        if (basePageVO == null || basePageVO.getData().isEmpty()) {
            return basePageVO;
        }
        return basePageVO;
    }


    @PostMapping("/editJob")
    @ApiOperation(value = "编辑百应外呼任务", consumes = "application/x-www-form-urlencoded")
    public AiCallJobVO editJob(@Valid @ApiParamBody JDEditJobRequest request, @ApiIgnore NgExpand ngExpand) {
        if (ngExpand == null || ngExpand.getUcUser() == null || StringUtils.isEmpty(ngExpand.getUcUser().getUserId())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法接口调用!!!");
        }
        // 根据主键id查询外呼任务
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(request.getId());

        AiCallJobVO aiCallJobVO = request.check(aiCallJob, ccAiCallJobService);
        if (aiCallJobVO != null) {
            return aiCallJobVO;
        }

        // 更新AI外呼数据库
        CcAiCallJob editAiCallJob = BeanUtils.copyFromObjToClass(CcAiCallJob.class, request);
        editAiCallJob.setConcurrency(request.getConcurrentNumber());
        editAiCallJob.setJobId(aiCallJob.getJobId());
        editAiCallJob.setJobName(request.getName());
        editAiCallJob.setErrorLog("");
        UcUser ucUser = ngExpand.getUcUser();
        editAiCallJob.setModifier(ucUser.getUserId());
        editAiCallJob.setModifyName(ucUser.getUserName());
        editAiCallJob.setModifyDate(DateUtils.getNowDString());
        // 线路属性赋值
        if (null != editAiCallJob.getLineId() && !aiCallJob.getLineId().equals(editAiCallJob.getLineId())) {
            byaiService.getLineNameById(request.getLineId()).ifPresent(editAiCallJob::setLineName);
        }
        ccAiCallJobService.updateById(editAiCallJob);
        // 调用百应外呼接口创建AI外呼任务
        byaiService.createJobFromVendor(aiCallJob.getJobId(), ngExpand);
        aiCallJob = ccAiCallJobService.getById(request.getId());
        return BeanUtils.copyFromObjToClass(AiCallJobVO.class, aiCallJob);
    }

    @PostMapping("/operatingJob")
    @ApiOperation(value = "AI外呼任务操作", consumes = "application/x-www-form-urlencoded")
    public AiCallJobVO operatingJob(
            @ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId,
            @ApiParam(value = "操作类型(1->启动,2->暂停,3->继续,4->删除)")
            @Max(value = 4, message = "操作类型为非法参数")
            @Min(value = 1, message = "操作类型为非法参数") Integer type,
            @ApiIgnore NgExpand ngExpand
    ) {
        if (ngExpand == null || ngExpand.getUcUser() == null || StringUtils.isEmpty(ngExpand.getUcUser().getUserId())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法接口调用!!!");
        }
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob || aiCallJob.getJobLevel() != 1) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数");
        }
        // 只能更改百应AI外呼任务
        if (!CallChannelEnum.CALL_TYPE_BYAI.getType().equals(aiCallJob.getAiJobType())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数");
        }
        if (AICallJobStatus.canceled.name().equals(aiCallJob.getStatus())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "该任务已处于删除状态");
        }
        UcUser ucUser = ngExpand.getUcUser();
        aiCallJob.setModifier(ucUser.getUserId());
        aiCallJob.setModifyName(ucUser.getUserName());
        Integer jobProgress = aiCallJob.getJobProgress();
        //type=4为删除操作,删除操作需要先更新数据,再删除
        if (type == 4) {
            String userId = ngExpand.getUcUser().getUserId();
            if (!userId.equals(aiCallJob.getCreator())) {
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "当前仅支持删除本人创建的任务。");
            }
            // 只有任务已经在百应后台创建成功了才需要更新
            if (!jobProgress.equals(3)) {
                this.updateJobInfo(aiCallJob);
            }
        }
        //AI外呼任务操作
        if (!jobProgress.equals(3)) {
            byaiService.operatingJob(Long.valueOf(aiCallJob.getVendorJobId()), type);
        }
        if (type != 4) {
            this.updateJobInfo(aiCallJob);
        } else {
            aiCallJob.setStatus(AICallJobStatus.canceled.name());
        }
        //更新最新数据到数据库
        ccAiCallJobService.updateById(aiCallJob);
        return BeanUtils.copyFromObjToClass(AiCallJobVO.class, aiCallJob);
    }

    @PostMapping("/batchUpdateAICallJob")
    @ApiOperation(value = "批量更新AI外呼任务(只返回需要更新的外呼任务列表,比如已经被删除的不会返回)", consumes = "application/x-www-form-urlencoded")
    public List<AiCallJobVO> batchUpdateAICallJob(
            @ApiParam("AI外呼任务主键id") @RequestParam(value = "jobIds") @NotNull(message = "AI外呼任务主键id不能为空") List<String> jobIds,
            @ApiIgnore NgExpand ngExpand
    ) throws IOException {
        if (ngExpand == null || ngExpand.getUcUser() == null || StringUtils.isEmpty(ngExpand.getUcUser().getUserId())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法接口调用!!!");
        }
        UcUser ucUser = ngExpand.getUcUser();
        // 批量更新外呼任务
        List<CcAiCallJob> filteredCallJobList = byaiService.batchUpdateAICallJob(jobIds, ucUser);
        // 数据copy返回前端
        return BeanUtils.copyFromMultiObjToClass(AiCallJobVO.class, filteredCallJobList);
    }


    @PostMapping("/aiJobInfo")
    @ApiOperation(value = "根据AI任务id查询任务详情信息", consumes = "application/x-www-form-urlencoded")
    public AiCallJobInfoVO aiJobInfo(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数");
        }
        return BeanUtils.copyFromObjToClass(AiCallJobInfoVO.class, aiCallJob);
    }

    @PostMapping("/aiSubJob/callNumStat")
    @ApiOperation(value = "AI外呼子任务统计数据总览接口", consumes = "application/x-www-form-urlencoded")
    public AiJobCallNumStateVO callNumStat(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数");
        }
        return aiCallJobCustomerInfoService.callNumStatAggs(aiJobId, aiCallJob.getCustomerIndex());
    }

    @PostMapping("/aiSubJob/aiJobAggsState")
    @ApiOperation(value = "AI外呼子任务数据聚合统计接口", consumes = "application/x-www-form-urlencoded")
    public AiJobAggsStateVO aiJobAggsState(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数");
        }
        return aiCallJobCustomerInfoService.aiJobAggsState(aiJobId, aiCallJob.getCustomerIndex());
    }

    @PostMapping("/aiSubJob/selectAiJobInfoDetail")
    @ApiOperation(value = "AI外呼子任务客户明细分页查询接口", consumes = "application/x-www-form-urlencoded")
    public BasePageVO<AiJobInfoDetailVO> selectAiJobInfoDetail(@ApiParam("请求参数") @Valid AiJobInfoDetailRequest request) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(request.getAiJobId());
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数");
        }
        return aiCallJobCustomerInfoService.selectAiJobInfoDetail(request, aiCallJob.getCustomerIndex());
    }

    @PostMapping("/aiSubJob/selectAiJobCustomerInfo")
    @ApiOperation(value = "AI外呼子任务客户详情查询接口", consumes = "application/x-www-form-urlencoded")
    public AiJobInfoDetailVO selectAiJobCustomerInfo(@ApiParam(value = "主键id") @NotNull(message = "主键id不能为空") String id,
                                                     @ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id不能为空") String aiJobId) {
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数");
        }
        return aiCallJobCustomerInfoService.selectAiJobCustomerInfo(id, aiCallJob.getCustomerIndex());
    }

    @PostMapping("/aiSubJob/aiJobInfoDetail/excel")
    @ApiOperation(value = "AI外呼子任务客户列表Excel下载接口", consumes = "application/x-www-form-urlencoded")
    public String aiJobInfoDetailExcel(@ApiParam(value = "AI外呼任务主键id") @NotNull(message = "AI外呼任务主键id为空") String aiJobId,
                                       @ApiIgnore NgExpand ngExpand) {
        UcUser user = ngExpand.getUcUser();
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        if (null == aiCallJob) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数！根据aiJobId未查询到外呼任务！");
        }
        String jobName = aiCallJob.getJobName();
        return aiCallJobCustomerInfoService.aiJobInfoDetailExcel(aiJobId, aiCallJob.getCustomerIndex(), jobName, user);
    }

    /**
     * 更新外呼任务
     *
     * @param aiCallJob 外呼任务
     **/
    private void updateJobInfo(CcAiCallJob aiCallJob) {
        BYAIJobInfoDTO jobInfo = byaiService.getJobInfo(Long.valueOf(aiCallJob.getVendorJobId()));
        if (jobInfo == null) {
            // 任务在百应后台删除
            throw new ApplicationException(ApiErrorCodes.execute_failed, "未在百应后台查询到此AI外呼任务,请检查是否已经在百应删除!!!");
        }
        List<BYAIContextListDTO> contextList = byaiService.getContextList();
        Map<Integer, String> contextMap = contextList.stream().collect(Collectors.toMap(BYAIContextListDTO::getId, BYAIContextListDTO::getName));
        String contextName = contextMap.get(aiCallJob.getContextId());
        aiCallJob.setContextName(contextName);
        aiCallJob.setJobName(jobInfo.getJobName());
        aiCallJob.setStatus(jobInfo.getStatus());
        aiCallJob.setModifyDate(DateUtils.getNowDString());
    }

}
