package com.niceloo.cmc.ex.controller.mq;

import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.service.ai.JDYXService;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.mq.client.Client;
import com.niceloo.mq.client.Handler;
import com.niceloo.mq.client.annotation.ConsumerMethod;
import com.niceloo.mq.client.annotation.Subscriber;
import lombok.CustomLog;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 追加客户信息到外呼任务 TOPIC
 *
 * <AUTHOR>
 * @create: 2022-02-16 15:07
 */
@CustomLog
@Subscriber(topics = MQConst.ADD_CUSTOMER_INFO_TO_JOB_TOPIC, clientName = "jobAddCustomerRabbitMqClient", group = "CMC")
public class AddCustomerInfoToJobMQConsumer {
    private static final Logger logger = LoggerFactory.getLogger(AddCustomerInfoToJobMQConsumer.class);

    @Resource
    private JDYXService jdyxService;

    @Resource
    private CcAiCallJobService ccAiCallJobService;

    @Resource(name = "jobAddCustomerRabbitMqClient")
    private Client client;

    /**
     * MQ消息消费方法
     *
     * @return com.niceloo.mq.client.Handler.Ack
     * @paramter data 消息内容
     * <AUTHOR>
     * @Date 14:07 2022/2/17
     **/
    @ConsumerMethod
    public Handler.Ack subscribe(String data) {
        try {
            logger.info("追加客户信息到外呼任务MQ消费接收成功,参数:{}", data);
            Map<String, Object> map = JSONUtils.toMap(data);
            String indexName = map.get("indexName").toString();
            String lotNo = (String) map.get("lotNo");
            String jobId = map.get("jobId").toString();
            String firstCustomerPhone = (String) Optional.ofNullable(map.get("firstCustomerPhone")).orElse("");

            // 任务状态为已取消则不再追加客户信息
            String jobProgress = map.get("jobProgress").toString();
            if(jobProgress.equals("3"))  {
                return Handler.Ack.DROP;
            }

            String userId = map.get("userId").toString();

            NgExpand ngExpand = new NgExpand();
            UcUser ucUser = new UcUser();
            ucUser.setUserId(userId);
            ngExpand.setUcUser(ucUser);

            // 追加客户信息到京东言犀，创建失败和创建完成任务进度的任务不再追加客户
            // 若希望创建完成的任务也可继续追加，则下面条件增加|| jobProgress.equals("4")
            if (jobProgress.equals("1") || jobProgress.equals("2")) {
                jdyxService.addCustomerInfoToJob(indexName, lotNo, jobId, firstCustomerPhone, ngExpand);
            }
            // 全部更新完成后更新任务的状态
            String finishFlag = map.get("finishFlag").toString();
            if ("Y".equals(finishFlag)) {
                logger.info("任务追加完成,更新任务状态为已完成,参数:{}", data);
                String jobLevel = (String) map.get("jobLevel");
                // 更新外呼任务的进度为已完成
                ccAiCallJobService.updateJobProgressToFinish(jobId, jobLevel);

                // 向业务发送AI外呼任务状态变更MQ
                CcAiCallJob ccAiCallJob = ccAiCallJobService.getById(jobId);
                String aiTaskStatusChange = JSONUtils.toJSONString(ccAiCallJob);
                LOGGER.info("给业务发任务状态变更MQ通知，通知内容是：{}", aiTaskStatusChange);
                client.publish(MQConst.AI_TASK_STATUS_CHANGE_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(ccAiCallJob));
            }
        } catch (Exception e) {
            logger.error(e, "追加客户信息到外呼任务消费失败,异常信息:{},参数:{}", e.getMessage(), data);
        }
        return Handler.Ack.OK;
    }
}
