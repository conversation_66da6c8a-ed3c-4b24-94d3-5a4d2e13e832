package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.service.CcCountdayService;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.niceloo.cmc.ex.pojo.vo.CallStatsVO;
import lombok.CustomLog;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2023/12/13 15:51 2023-12-13
 */
@RestController
@RequestMapping("/stats")
@Api("今日外呼统计")
@Validated
@CustomLog
public class CallStatsController {

    @Resource
    private CcCountdayService ccCountdayService;

    @PostMapping("/call")
    @ApiOperation(value = "今日通话量和通话时长统计")
    public CallStatsVO getCallStatsByUserId(@ApiIgnore NgExpand ngExpand) {
        if (null == ngExpand) {
            throw new ApplicationException(ApiErrorCodes.un_login, "未登录");
        }
        UcUser ucUser = ngExpand.getUcUser();

        // 获取当天日期的字符串形式
        LocalDate now = LocalDate.now();
        String nowString = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LOGGER.info("待查询的日期是：{}", nowString);

        List<String> queryDate = Collections.singletonList(nowString);

        CallStatsVO callStats = ccCountdayService.getCallStatsByUserId(ucUser.getUserId(), queryDate);

        if (callStats == null) {
            callStats = new CallStatsVO();
            callStats.setCallerUserId(ucUser.getUserId());
            callStats.setValidCallNum(0);
            callStats.setDurationTotal(0);
        }

        return callStats;
    }
}
