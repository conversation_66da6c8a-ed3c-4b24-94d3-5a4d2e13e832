package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.pojo.param.ManualSyncAiParam;
import com.niceloo.cmc.ex.pojo.param.SyncAiVoiceParam;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.concurrent.CompletableFuture;

/**
 * AI外呼手动同步接口
 * <AUTHOR>
 * @since 2024-12-02 17:17:03
 */
@Api("AI外呼手动同步接口")
@Validated
@RestController
@RequestMapping("ai/manual/sync")
@CustomLog
public class AiManualSyncController {

    @Resource
    private AICallJobCustomerInfoService aiCallJobCustomerInfoService;

    @ApiOperation("同步录音")
    @PostMapping("voice")
    public String syncVoiceToEs(@Valid SyncAiVoiceParam param) {
        CompletableFuture.runAsync(() -> {
            aiCallJobCustomerInfoService.downloadAndUploadRecordings(param);
        });
        return "录音同步已启动，稍后会完成。";

    }

    @ApiOperation("同步通话记录")
    @PostMapping("record")
    public String syncRecordToEs(@Valid ManualSyncAiParam param) {
        CompletableFuture.runAsync(() -> {
            aiCallJobCustomerInfoService.pullCallRecord(param);
        });
        return "记录同步已启动，稍后会完成。";
    }
}
