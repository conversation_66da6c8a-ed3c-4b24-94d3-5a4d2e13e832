package com.niceloo.cmc.ex.controller.mq;

import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.pojo.dto.RecordQueryDTO;
import com.niceloo.cmc.ex.service.call.CallRecordUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.mq.client.Client;
import com.niceloo.mq.client.Handler;
import com.niceloo.mq.client.annotation.ConsumerMethod;
import com.niceloo.mq.client.annotation.Subscriber;
import lombok.CustomLog;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * {@code @desc} 重构-自动补充通话记录的员工信息
 * {@code @date} 2022/3/7
 */
@CustomLog
@Subscriber(topics = MQConst.ADD_EE_INFO_TOPIC, group = "CMC")
public class AddEmployeeInfoConsumer {

    private final Client client;

    @Autowired
    public AddEmployeeInfoConsumer(Client client) {
        this.client = client;
    }

    @ConsumerMethod
    public Handler.Ack subscribe(String data) {
        try {
            RecordQueryDTO recordQueryDTO = JSONUtils.toObject(data, RecordQueryDTO.class);
            LOGGER.info("MQ补充通话记录员工信息,参数: {}", recordQueryDTO);

            recordQueryDTO.incrementQueryCount();
            Future<CallRecordUtil.BulkUpdateByQueryResult> resultFuture = CallRecordUtil.addEmployeeInfo(
                    recordQueryDTO.getIndexes(),
                    recordQueryDTO.getCallTimeStart(),
                    recordQueryDTO.getCallTimeEnd(),
                    recordQueryDTO.getChannelType()
            );

            CallRecordUtil.BulkUpdateByQueryResult result;
            result = getResult(resultFuture, recordQueryDTO);
            if (result != null && result.getUpdateFailed() > 0) {
                recordQueryDTO.incrementQueryErrorCount();
            }

            int delayMilliseconds = needRunAgainAfterMilliseconds(recordQueryDTO, result);
            if (delayMilliseconds >= 0) {
                client.publish(MQConst.ADD_EE_INFO_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordQueryDTO), delayMilliseconds);
            } else {
                LOGGER.info("[补充员工信息][处理结束] :{}", recordQueryDTO);
            }
            return Handler.Ack.OK;
        } catch (Exception e){
            LOGGER.error(e,"[补充员工信息][未知异常] ", data);
            return Handler.Ack.DROP;
        }
    }

    /**
     * 获得异步执行结果，如果出现异常则记录错误计数并返回 null
     *
     * @param resultFuture 异步执行结果
     * @param recordQueryDTO 记录查询 DTO 对象
     * @return 执行结果，可能为 null
     */
    private CallRecordUtil.BulkUpdateByQueryResult getResult(Future<CallRecordUtil.BulkUpdateByQueryResult> resultFuture, RecordQueryDTO recordQueryDTO) {
        try {
            return resultFuture.get();
        } catch (InterruptedException e) {
            LOGGER.error("getResult方法执行出现InterruptedException错误:{}", e.getMessage());
            Thread.currentThread().interrupt();
            return null;
        } catch (Exception e) {
            recordQueryDTO.incrementQueryErrorCount();
            LOGGER.error("getResult方法执行出现其他异常:{}", e.getMessage());
            return null;
        }
    }

    /**
     * {@code @Description:} 是否需要重试,需要在多少毫秒后重试
     * @param recordQueryDTO 请求
     * @param result 执行结果
     * @return -1:不需要重试
     * <AUTHOR>
     * {@code @Date} 2021/12/8 11:32
     */
    private int needRunAgainAfterMilliseconds(RecordQueryDTO recordQueryDTO, CallRecordUtil.BulkUpdateByQueryResult result) {
        if (recordQueryDTO.reachQueryErrorCountThreshold() && recordQueryDTO.reachMaxQueryErrorRate()) {
            LOGGER.error("[补充员工信息][异常请求] :{}", recordQueryDTO);
            return -1;
        }

        if (result == null) {
            return 60 * 1000;
        }

        if (result.getUpdateFailed() > 0) {
            return 5 * 60 * 1000;
        }

        if (recordQueryDTO.getIndexes().equals(RecordUtil.getCurrentMonthRecordIndexName())) {
            return (int) RedisConst.ONE_DAY_MILLIS;
        }

        return -1;
    }


}
