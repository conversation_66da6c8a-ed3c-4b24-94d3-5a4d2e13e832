package com.niceloo.cmc.ex.controller.mq;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.pojo.dto.AccountDTO;
import com.niceloo.cmc.ex.service.BdCallaccountinfoService;
import com.niceloo.cmc.ex.service.call.*;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.mq.client.Client;
import com.niceloo.mq.client.Handler;
import com.niceloo.mq.client.annotation.ConsumerMethod;
import com.niceloo.mq.client.annotation.Subscriber;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @description: 通话记录拉取消费者
 * @author: WangChenyu
 * @create: 2022-02-25 10:16
 */
@Subscriber(topics = MQConst.CALL_RECORD_TOPIC, clientName = "syncRecordRabbitMqClient", group = "CMC")
public class CallRecordPullConsumer {
    private static final Logger logger = LoggerFactory.getLogger(CallRecordPullConsumer.class);
    @Resource(name = "syncRecordRabbitMqClient")
    private Client client;
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Resource
    private BdCallaccountinfoService bdCallaccountinfoService;

    /**
     * MQ消息消费方法
     *
     * @return com.niceloo.mq.client.Handler.Ack
     * @paramter data 消息内容
     * <AUTHOR>
     * @Date 14:07 2022/2/17
     **/
    @ConsumerMethod
    public Handler.Ack subscribe(String data) {
        try {
            logger.info("MQ通话记录拉取消费者->请求参数:{}",data);
            handleConsumer(data);
        } catch (Exception e) {
            logger.error(e, "通话记录拉取MQ消费出现异常,异常信息: " + e.getMessage());
            // 如果是手动触发的消息，因为要保持安全性会拒绝重复的消息。所以在发生异常时，要改为自动消息
            if (data.endsWith(BizConst.MANUAL)) {
                try {
                    String uuid = UUID.randomUUID().toString();
                    if (data.contains(" CST ")) {
                        logger.warn(String.format("CallRecordPullConsumer.subscribe 的 data: %s, uuid: %s", data, uuid));
                    }
                    client.publish(MQConst.CALL_RECORD_TOPIC, uuid, data.replace(BizConst.MANUAL, BizConst.AUTO));
                } catch (Exception e1) {
                    logger.error(e1, "MQ发送消息失败,异常信息:{}",e1.getMessage());
                }
                return Handler.Ack.DROP;
            } else {
                return Handler.Ack.REQUEUE;
            }
        }
        return Handler.Ack.OK;
    }

    /**
     * @return 24小时内, 同一通道类型, 相同的时间段是否已手动处理过: 是返回true
     * @Description: 24小时内, 同一通道类型, 相同的时间段: 只能手动处理一次
     * <AUTHOR>
     */
    public boolean checkRecordTask(String channelType, String startDate, String endDate) {
        String key = RedisConst.RECORD_SYNC_KEY + "_MQ_" + channelType;
        String setData = startDate + "_" + endDate;
        Object value = redisTemplate.opsForHash().get(key, setData);
        if (!ObjectUtils.isEmpty(value)) {
            return true;
        }
        redisTemplate.opsForHash().put(key, setData, setData);
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return false;
    }


    /**
     * 消费拉取通话记录
     *
     * @paramter data
     * <AUTHOR>
     * @Date 11:06 2022/2/25
     **/
    private void handleConsumer(String data) {
        String[] pullRecordRequestParam = data.split(BizConst.LINK_SYMBOL);
        String channelType = pullRecordRequestParam[0];
        CallChannelEnum callChannel = CallChannelEnum.getCallChannel(channelType);
        if (null == callChannel) {
            return;
        }
        int length = pullRecordRequestParam.length;
        // 操作类型 C->创建通话记录,U->更新通话记录
        String actionType = BizConst.CREATE;
        if (length >= 5) {
            actionType = pullRecordRequestParam[4];
            if (!BizConst.CREATE.equals(actionType) && !BizConst.UPDATE.equals(actionType)) {
                actionType = BizConst.CREATE;
            }
        }
        String start = pullRecordRequestParam[1];
        String end = pullRecordRequestParam[2];
        // 触发模式 M->主动调用接口,A->定时任务
        String triggerMode = pullRecordRequestParam[3];
        String startKey = null;
        String accountId;
        AccountDTO account = null;
        if (length >= 6) {
            if (!"null".equalsIgnoreCase(pullRecordRequestParam[length - 2])) {
                startKey = pullRecordRequestParam[length - 2];
            }
            accountId = pullRecordRequestParam[length - 1];
            if (!StringUtils.isEmpty(accountId)) {
                try {
                    account = bdCallaccountinfoService.selectAccount(accountId, channelType);
                } catch (Exception e) {
                    logger.info(e, "查询厂商外呼账号失败");
                }
            }
        }
        // 检查是否已经消费过该任务，且是手动触发的任务，有效期为一天
        if (BizConst.MANUAL.equals(triggerMode) && checkRecordTask(channelType, start, end)) {
            return;
        }
        //开始消费
        CallRecordsBaseService syncService = null;
        switch (callChannel){
            case CALL_TYPE_TQ_MOBILE:
                syncService = new TQService(actionType);
                break;
            case CALL_TYPE_FY:
                syncService = new FYService(actionType);
                break;
            case CALL_TYPE_YPHONE:
                syncService = new YKService(actionType);
                break;
            case CALL_TYPE_ZK:
            case CALL_TYPE_YH:
            case CALL_TYPE_YX_SIP:
                syncService = new YXService(channelType, account, startKey, actionType);
                break;
            case CALL_TYPE_OI:
                syncService = new OIService();
                break;
        }

        if(syncService != null){
            syncService.sync(channelType, start, end);
        }
    }
}
