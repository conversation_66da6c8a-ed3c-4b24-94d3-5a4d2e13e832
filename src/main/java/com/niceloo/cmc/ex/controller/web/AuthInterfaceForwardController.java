package com.niceloo.cmc.ex.controller.web;

import com.niceloo.auth.service.AuthService;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.cmc.ex.feign.UserFeignClient;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.ApiParamBody;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权限接口请求转发前端控制器<br/>
 * *说明*：该接口控制器是为了解决需要前端需要调用其他服务接口,而要调用的接口不支持权限校验,特在这个前端控制器做权限校验后转发请求要调用的接口
 *
 * <AUTHOR>
 * @create: 2022-05-26 15:52
 */
@RestController
@RequestMapping("/forward")
@Validated
@Api("权限接口请求转发前端控制器")
public class AuthInterfaceForwardController {
    private static final Logger logger = LoggerFactory.getLogger(AuthInterfaceForwardController.class);

    @Autowired
    private AuthService authService;

    @Autowired
    private UserFeignClient userFeignClient;

    @PostMapping("/uc/ee/list")
    @ApiOperation(value = "转发调用用户中心接口:/uc/ee/list", consumes = "application/x-www-form-urlencoded")
    public Object forwardUCEeList(@NotNull(message = "必传参数为空")
                                  @ApiParam("调用原地址的参数加上menuCode")
                                  @ApiParamBody
                                      Map<String, Object> request,
                                  @ApiIgnore NgExpand ngExpand) {
        logger.info("转发调用用户中心接口请求:{},参数:{}", "/api/commu/forward/uc/ee/list", request.toString());
        // 权限校验
        List<String> dataPolicy = this.checkAuth(request, ngExpand);
        UcUser ucUser = ngExpand.getUcUser();
        // 仅包含mine==>只能检索自己的数据
        if (dataPolicy.size() == 1 && dataPolicy.contains("mine")) {
            request.put("eeNo", ucUser.getEeNo());
        }
        boolean authFlag = dataPolicy.contains("all");
        // 包含mySchool==>只能创建自己分校下的外呼账号
        if (!authFlag && dataPolicy.contains("mySchool")) {
            authFlag = true;
            request.put("schoolId", ucUser.getSchoolId());
        }
        // 包含myDept==>只能创建自己部门下的外呼账号
        if (!authFlag && dataPolicy.contains("myDept")) {
            request.put("dptId", ucUser.getDptId());
        }
        Object eeInfoPage = userFeignClient.getEeInfoPage(JSONUtils.toJSONString(request));
        logger.info("转发调用用户中心接口返回:{},返回值:{}", "/api/commu/forward/uc/ee/list", JSONUtils.toJSONString(eeInfoPage));
        return eeInfoPage;
    }


    @PostMapping("/uc/school/tree/school")
    @ApiOperation(value = "转发调用用户中心接口:/uc/school/tree/school", consumes = "application/x-www-form-urlencoded")
    public Object forwardUCDptTree(@NotNull(message = "必传参数为空")
                                   @ApiParam("调用原地址的参数加上menuCode")
                                   @ApiParamBody()
                                       Map<String, Object> request,
                                   @ApiIgnore NgExpand ngExpand) {
        Object schoolId = request.get("schoolId");
        if (null == schoolId || StringUtils.isEmpty(schoolId.toString())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "schoolId不能为空");
        }
        List<String> dataPolicy = this.checkAuth(request, ngExpand);
        String userId = ngExpand.getUcUser().getUserId();
        // 仅包含mine==>只能检索自己的数据
        boolean flag = false;
        boolean hasMine = false;
        if (dataPolicy.size() == 1) {
            if (dataPolicy.contains("mine") || dataPolicy.contains("myDept")) {
                flag = true;
            }
            if (dataPolicy.contains("mine")) {
                hasMine = true;
            }
        }
        if (dataPolicy.size() == 2 && dataPolicy.contains("mine") && dataPolicy.contains("myDept")) {
            flag = true;
        }
        if (flag) {
            // 根据员工的UserId获取当前员工的部门树
            request.put("userId", userId);
            Map<String, Object> dptTree = userFeignClient.findDptByUserId(JSONUtils.toJSONString(request));
            List<Map<String, Object>> targetDbpTreeList = new ArrayList<>();
            Map<String, Object> targetDbpTree = new HashMap<>();
            targetDbpTreeList.add(targetDbpTree);
            // 转换部门树
            Map<String, Object> map = JSONUtils.toMap(JSONUtils.toJSONString(dptTree.get("tree")));
            this.dptTreeConverter(map, targetDbpTreeList);
            Map<String, Object> targetDbpTreeMap = targetDbpTreeList.get(0);
            List<Map<String, Object>> dbpTreeMap = (List<Map<String, Object>>) targetDbpTreeMap.get("children");
            if (hasMine) {
                return dbpTreeMap;
            }
            // 当前部门的id(需要用来获取到该部门下的所有子部门树)
            String dptId = map.get("dptId").toString();
            request.put("dptAvlstatus", "Y");
            Map<String, Object> dptTreeBySchoolId = userFeignClient.getDptTreeBySchoolId(JSONUtils.toJSONString(request));
            // 拼接截取的子部门树
            List<Map<String, Object>> dbpTreeMapTmp = dbpTreeMap;
            while (dbpTreeMapTmp.get(0).containsKey("children")) {
                dbpTreeMapTmp = (List<Map<String, Object>>) dbpTreeMapTmp.get(0).get("children");
            }
            List<Object> result = new ArrayList<>();
            this.captureDptTree(dptId, (List<Map<String, Object>>) dptTreeBySchoolId.get("data"), result);
            dbpTreeMapTmp.get(0).put("children", result.get(0));
            return dbpTreeMap;
        }
        // 根据分校id获取该分校的部门树
        request.put("dptAvlstatus", "Y");
        Map<String, Object> dptTree = userFeignClient.getDptTreeBySchoolId(JSONUtils.toJSONString(request));
        return dptTree.get("data");
    }

    /**
     * 拼接截取的子部门树,截取后放在参数`children`内
     *
     * @param dptId    要从哪里截取的部门id
     * @param data     源部门树
     * @param children 目标子树
     * <AUTHOR>
     * @Date 14:24 2022/11/16
     **/
    private void captureDptTree(String dptId, List<Map<String, Object>> data, List<Object> children) {
        if (!children.isEmpty()) {
            return;
        }
        for (Map<String, Object> map : data) {
            String dptId1 = map.get("dptId").toString();
            if (dptId.equals(dptId1)) {
                children.add(map.get("children"));
            } else {
                if (children.isEmpty() && map.containsKey("children")) {
                    this.captureDptTree(dptId, (List<Map<String, Object>>) map.get("children"), children);
                }
            }
        }
    }

    private List<String> checkAuth(Map<String, Object> request, NgExpand ngExpand) {
        // 权限校验
        Object menuCodeObj = request.get("menuCode");
        if (null == menuCodeObj || StringUtils.isEmpty(menuCodeObj.toString())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "menuCode不能为空");
        }
        UcUser ucUser = ngExpand.getUcUser();
        String userId = ucUser.getUserId();
        String menuCode = menuCodeObj.toString();
        List<String> dataPolicy = authService.getDatapolicy(menuCode, userId);
        if (CollectionUtils.isEmpty(dataPolicy)) {
            throw new ApplicationException(ApiErrorCodes.execute_failed, "未找到您的权限");
        }
        return dataPolicy;
    }


    /**
     * 员工的部门树转换器
     *
     * @param dptTree 从用户中心获取到的部门树
     * <AUTHOR>
     * @Date 14:08 2022/11/8
     **/
    private void dptTreeConverter(Map<String, Object> dptTree, List<Map<String, Object>> target) {
        if (dptTree.containsKey("parent")) {
            this.dptTreeConverter(JSONUtils.toMap(JSONUtils.toJSONString(dptTree.get("parent"))), target);
        }
        List<Map<String, Object>> temList = target;
        Map<String, Object> tem = temList.get(0);
        // 获取最里面的部门节点，方便后面为他添加children
        while (tem.containsKey("children")) {
            temList = (List<Map<String, Object>>) tem.get("children");
            tem = temList.get(0);
        }
        Map<String, Object> children = new HashMap<>();
        children.put("dptLevelcode", dptTree.get("dptLevelcode"));
        children.put("dptAvlstatus", dptTree.get("dptAvlstatus"));
        children.put("dptSeq", dptTree.get("dptSeq"));
        children.put("dptCode", dptTree.get("dptCode"));
        children.put("dptId", dptTree.get("dptId"));
        children.put("dptName", dptTree.get("dptName"));
        children.put("dptRelationid", dptTree.get("dptRelationid"));
        children.put("dptType", dptTree.get("dptType"));
        // 将子部门的数据加入到children内
        List<Map<String, Object>> targetDbpTreeList = new ArrayList<>();
        targetDbpTreeList.add(children);
        tem.put("children", targetDbpTreeList);
    }
}
