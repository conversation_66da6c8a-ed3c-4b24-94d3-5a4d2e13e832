package com.niceloo.cmc.ex.controller.mq;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.aicall.ByCallBackTypeEnum;
import com.niceloo.cmc.ex.pojo.dto.*;
import com.niceloo.cmc.ex.pojo.dto.byai.BYAICallRecordCallback;
import com.niceloo.cmc.ex.pojo.dto.byai.BYAIJobInfoDTO;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDCallRecordCallback;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDJobInfoDTO;
import com.niceloo.cmc.ex.service.ai.BYAIService;
import com.niceloo.cmc.ex.service.ai.JDYXService;
import com.niceloo.cmc.ex.service.call.JLFYService;
import com.niceloo.cmc.ex.service.call.YKService;
import com.niceloo.cmc.ex.service.call.YXService;
import com.niceloo.cmc.ex.service.call.ZHZXService;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.mq.client.Client;
import com.niceloo.mq.client.Handler;
import com.niceloo.mq.client.annotation.ConsumerMethod;
import com.niceloo.mq.client.annotation.Subscriber;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 通话记录回调MQ消息消费者
 *
 * <AUTHOR>
 * @since 2022-02-16 15:07
 */
@Subscriber(topics = MQConst.CALL_RECORD_CALLBACK_TOPIC, group = "CMC")
public class CallRecordsCallBackMQConsumer {

    @Resource
    private ZHZXService zhzxService;
    @Resource
    private YKService ykService;
    @Resource
    private YXService yxService;
    @Resource
    private JLFYService jlfyService;
    @Resource
    private JDYXService jdyxService;
    @Resource
    private BYAIService byaiService;
    @Resource
    private Client client;

    private static final Logger logger = LoggerFactory.getLogger(CallRecordsCallBackMQConsumer.class);

    /**
     * MQ消息消费方法
     *
     * @return com.niceloo.mq.client.Handler.Ack
     * @param data 消息内容
     * <AUTHOR>
     * @since 14:07 2022/2/17
     **/
    @ConsumerMethod
    public Handler.Ack subscribe(String data) {
        Map<String, Object> map = JSONUtils.toMap(data);
        String channelType = map.get("channelType").toString();
        CallChannelEnum callChannelEnum = CallChannelEnum.getCallChannel(channelType);
        if (null == callChannelEnum) {
            return Handler.Ack.OK;
        }
        logger.info("通话记录回调MQ消费接收成功,外呼通道:" + channelType + ",回调参数:" + data);
        switch (callChannelEnum) {
            case CALL_TYPE_ZHZX: {
                ZHZXCallRecordsDTO zhzxCallRecordsDTO = JSONUtils.toObject(map.get("data").toString(), ZHZXCallRecordsDTO.class);
                try {
                    zhzxService.supplementaryCallRecordInfoToES(zhzxCallRecordsDTO);
                } catch (Exception e) {
                    logger.warn(e, "通话记录回调MQ[ZHZX]消费执行失败,异常信息:" + e.getMessage());
                    return Handler.Ack.DROP;
                }
                break;
            }
            case CALL_TYPE_YPHONE: {
                List<YKCallRecordsDTO> ykCallRecordsDTOS = JSONUtils.toList(map.get("data").toString(), YKCallRecordsDTO.class);
                try {
                    ykService.supplementaryCallRecordInfoToES(ykCallRecordsDTOS);
                } catch (Exception e) {
                    logger.warn(e, "通话记录回调MQ[YP]消费执行失败,异常信息:" + e.getMessage());
                    return Handler.Ack.DROP;
                }
                break;
            }
            case CALL_TYPE_ZK: {
                YXCallRecordsDTO yxCallRecordsDTO = JSONUtils.toObject(map.get("data").toString(), YXCallRecordsDTO.class);
                try {
                    yxService.supplementaryCallRecordInfoToES(yxCallRecordsDTO);
                } catch (Exception e) {
                    logger.warn(e, "通话记录回调MQ[YX]消费执行失败,异常信息:" + e.getMessage());
                    return Handler.Ack.DROP;
                }
                break;
            }
            case CALL_TYPE_YX_SIP: {
                YXSipCallRecordsDTO yxSipCallRecordsDTO = JSONUtils.toObject(map.get("data").toString(), YXSipCallRecordsDTO.class);
                try {
                    yxService.supplementaryCallRecordInfoToES(yxSipCallRecordsDTO);
                } catch (Exception e) {
                    logger.warn(e, "通话记录回调MQ[YX]消费执行失败,异常信息:" + e.getMessage());
                    return Handler.Ack.DROP;
                }
                break;
            }
            case CALL_TYPE_JLFY: {
                JLFYCallRecordsDTO jlfyCallRecordsDTO = JSONUtils.toObject(map.get("data").toString(), JLFYCallRecordsDTO.class);
                try {
                    jlfyService.supplementaryCallRecordInfoToES(jlfyCallRecordsDTO);
                } catch (Exception e) {
                    logger.warn(e, "通话记录回调MQ[JLFY]消费执行失败,异常信息:" + e.getMessage());
                    return Handler.Ack.DROP;
                }
                break;
            }
            case CALL_TYPE_JDYX: {
                String event = map.get("type").toString();
                // 京东言犀AI外呼回调有两种,分别为任务状态变化推送和每个外呼结束外呼详情推送
                if ("statusChangeEvent".equals(event)) {
                    try {
                        JDJobInfoDTO jobInfoDTO = JSONUtils.toObject(map.get("data").toString(), JDJobInfoDTO.class);
                        // 更新外呼任务到数据库
                        jdyxService.updateJobStatusByJobId(jobInfoDTO.getJobId(), jobInfoDTO.getJobName(), jobInfoDTO.getStatus(), jobInfoDTO.getTenantId());
                    } catch (Exception e) {
                        logger.warn(e, "任务状态变更回调MQ[JDYX]消费执行失败,异常信息:" + e.getMessage());
                        return Handler.Ack.DROP;
                    }
                }
                if ("callRecordEvent".equals(event)) {
                    try {
                        JDCallRecordCallback callRecordCallback = JSONUtils.toObject(map.get("data").toString(), JDCallRecordCallback.class);
                        // 更新外呼的详情到ES数据库
                        jdyxService.updateAIJobCustomerInfo(callRecordCallback);
                    } catch (Exception e) {
                        logger.warn(e, "通话记录回调MQ[JDYX]消费执行失败,异常信息:" + e.getMessage());
                        return Handler.Ack.DROP;
                    }
                }
                break;
            }
            case CALL_TYPE_BYAI: {
                ByCallBackTypeEnum byCallBackTypeEnum = ByCallBackTypeEnum.valueOf(map.get("type").toString());
                switch (byCallBackTypeEnum) {
                    case CALL_INSTANCE_RESULT:
                        try {
                            BYAICallRecordCallback callRecordCallback = JSONUtils.toObject(map.get("data").toString(), BYAICallRecordCallback.class);
                            byaiService.updateAIJobCustomerInfo(callRecordCallback);
                        } catch (Exception e) {
                            logger.warn(e, "通话记录回调MQ[BYAI]消费执行失败,异常信息:" + e.getMessage());
                            return Handler.Ack.DROP;
                        }
                        break;
                    case JOB_INFO_RESULT:
                        try {
                            BYAIJobInfoDTO jobInfoDTO = JSONUtils.toObject(map.get("data").toString(), BYAIJobInfoDTO.class);
                            // 更新外呼任务到数据库
                            byaiService.updateJobStatusByJobId(jobInfoDTO.getJobId(), jobInfoDTO.getCompanyId());
                        } catch (Exception e) {
                            logger.warn(e, "任务状态变更回调MQ[BYAI]消费执行失败,异常信息:" + e.getMessage());
                            return Handler.Ack.DROP;
                        }
                        break;
                    default:
                        logger.info("暂不处理百应{}回调类型: {}", byCallBackTypeEnum, byCallBackTypeEnum.getName());
                }
                break;
            }
            default:
        }
        return Handler.Ack.OK;
    }
}
