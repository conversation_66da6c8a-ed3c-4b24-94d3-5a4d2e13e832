package com.niceloo.cmc.ex.controller.mq;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.service.call.JLFYService;
import com.niceloo.cmc.ex.service.call.YKService;
import com.niceloo.cmc.ex.service.call.YXService;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.mq.client.Handler;
import com.niceloo.mq.client.annotation.ConsumerMethod;
import com.niceloo.mq.client.annotation.Subscriber;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 主动查询还没有补充完整的通话记录信息,延时队列 TOPIC
 *
 * <AUTHOR>
 * @create: 2022-02-16 15:07
 */
@Subscriber(topics = MQConst.ACTIVE_QUERY_RECORD_TOPIC, group = "CMC")
public class ActiveQueryRecordMQConsumer {
    private static final Logger logger = LoggerFactory.getLogger(ActiveQueryRecordMQConsumer.class);
    @Resource
    private JLFYService jlfyService;
    @Resource
    private YKService ykService;
    @Resource
    private YXService yxService;

    /**
     * MQ消息消费方法
     *
     * @return com.niceloo.mq.client.Handler.Ack
     * @param data 消息内容
     * <AUTHOR>
     * @since 14:07 2022/2/17
     **/
    @ConsumerMethod
    public Handler.Ack subscribe(String data) {
        try {
            Map<String, Object> map = JSONUtils.toMap(data);
            logger.info("主动查询还没有补充完整的通话记录信息MQ消费接收成功,参数:{}", data);
            Object channelType = map.get("channelType");
            CallChannelEnum callChannel = CallChannelEnum.getCallChannel((String) channelType);
            if (callChannel != null) {
                switch (callChannel) {
                    case CALL_TYPE_JLFY: {
                        jlfyService.activeQueryIncompleteRecord(map);
                        break;
                    }
                    case CALL_TYPE_YPHONE: {
                        ykService.activeQueryIncompleteRecord(map);
                        break;
                    }
                    case CALL_TYPE_ZK:
                    case CALL_TYPE_YH:
                    case CALL_TYPE_YX_SIP: {
                        yxService.activeQueryIncompleteRecord(map);
                        break;
                    }
                    default: {
                        logger.warn("MQ主动查询还没有补充完整的通话记录信息,请求参数:{},没有对应的渠道", data);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e, "MQ主动查询还没有补充完整的通话记录信息失败,请求参数:{},异常信息:{}", data, e.getMessage());
        }
        return Handler.Ack.OK;
    }
}
