package com.niceloo.cmc.ex.controller.web;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.param.ManualCountParam;
import com.niceloo.cmc.ex.service.TrafficStatisticService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SourceFilter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 手动统计接口
 * <AUTHOR>
 * @since 2022/3/1
 */

@Api("手动统计接口")
@Validated
@RestController
@RequestMapping("countManul")
public class ManualCountController {

    @Autowired
    private TrafficStatisticService trafficStatisticService;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    private static final Logger LOGGER = LoggerFactory.getLogger(ManualCountController.class);

    /** 警告: Elasticsearch-6.8.1-1fad4e1 "'y' year should be replaced with 'u'. Use 'y' for year-of-era. Prefix your date format with '8' to use the new specifier."] */
    @ApiOperation("手动统计")
    @PostMapping("saveToDB")
    public void saveAllCountToDB(@Valid ManualCountParam param){
        Set<String> dayDateList = new HashSet<>();
        Set<String> monthDateList = new HashSet<>();
        //获取需要日统计数据和月统计数据的时间列表
        String timeStart = param.getTimeStart();
        while (DateUtils.compare(DateUtils.toDate(timeStart), DateUtils.toDate(param.getTimeEnd())) <= 0){
            dayDateList.add(DateUtil.format(timeStart, DateUtil.YMD));
            timeStart =DateUtils.toStr(DateUtils.addDay(DateUtils.toDate(timeStart),1));
        }
        dayDateList.forEach(date -> monthDateList.add(date.substring(0,7)));
        //进行日统计
        LOGGER.info("手动统计:进行日话务统计,统计开始时间:" + DateUtils.getNowDString());
        long l = System.currentTimeMillis();
        for (String dayDate : dayDateList) {
            for (CallChannelEnum callChannelEnum : CallChannelEnum.values()) {
                // 京东言犀不需要进行话务统计
                if (CallChannelEnum.isNotTrafficStatistic(callChannelEnum)) {
                    continue;
                }
                try {
                    trafficStatisticService.addDayStatisticToDB(dayDate, callChannelEnum.getType());
                } catch (Exception e) {
                    LOGGER.error(e, "进行手动话务统计->日统计表出现异常,统计时间:" + dayDate + "外呼类型:" + callChannelEnum.getType());
                }
            }
        }
        long l2 = System.currentTimeMillis();
        LOGGER.info("手动统计:日话务统计完成,完成时间:" + DateUtils.getNowDString() + ",耗时:" + (l2-l)/1000 + "秒");
        //进行月统计
        LOGGER.info("手动统计:进行月话务统计,统计开始时间:" + DateUtils.getNowDString());
        for (String monthDate : monthDateList) {
            for (CallChannelEnum callChannelEnum : CallChannelEnum.values()) {
                // 京东言犀不需要进行话务统计
                if (CallChannelEnum.isNotTrafficStatistic(callChannelEnum)) {
                    continue;
                }
                try {
                    trafficStatisticService.addMonthStatisticToDB(monthDate, callChannelEnum.getType());
                } catch (Exception e) {
                    LOGGER.error(e, "进行手动话务统计->月统计表出现异常,统计时间:{}外呼类型:{},异常信息:{}", monthDate, callChannelEnum.getType(), e.getMessage());
                }
            }
        }
        long l3 = System.currentTimeMillis();
        LOGGER.info("手动统计:月话务统计完成,完成时间:" + DateUtils.getNowDString() + ",耗时:" + (l3-l2)/1000 + "秒");
    }

    @ApiOperation("统计录音总数及时长")
    @PostMapping("countAllVoice")
    public void saveAB(@Valid ManualCountParam param) {
        long totalCount = 0;
        BigDecimal totalDura = new BigDecimal("0");

        String result;
        String indexs = RecordUtil.getIndexStr(param.getTimeStart(), param.getTimeEnd());
        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("_doc");
        SourceFilter sourceFilter = new FetchSourceFilter(new String[]{"channelType", "duration"}, null);

        BoolQueryBuilder bqb = new BoolQueryBuilder();
        bqb.filter(QueryBuilders.rangeQuery("duration").gte(0));
        bqb.filter(QueryBuilders.matchQuery("channelType", "TQ"));
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder()
                .withTypes("_doc")
                .withQuery(bqb)
                .withSourceFilter(sourceFilter)
                .withSort(sortBuilder)
                .withPageable(PageRequest.of(0, 1000));//从0页开始查，每页1000个结果

        ScrolledPage<CallRecord> scroll = null;
        for(String index : indexs.split(",")){
            builder.withIndices(index);
            NativeSearchQuery searchQuery = builder.build();
            scroll = elasticsearchTemplate.startScroll(60 * 1000L, searchQuery, CallRecord.class);
            while (scroll.hasContent()) {
                List<CallRecord> content = scroll.getContent();
                BigDecimal currentDura = content.stream().map(item -> {return new BigDecimal(item.getDuration().toString());}).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalDura = totalDura.add(currentDura);
                totalCount = totalCount + content.size();

                scroll = elasticsearchTemplate.continueScroll(scroll.getScrollId(),60 * 1000L, CallRecord.class);
            }
        }
        assert scroll != null;
        elasticsearchTemplate.clearScroll(scroll.getScrollId());
        LOGGER.info("总个数 == [{}]", totalCount);
        LOGGER.info("总时长 == [{}]", totalDura.toString());
    }

}
