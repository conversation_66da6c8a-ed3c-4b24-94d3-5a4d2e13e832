package com.niceloo.cmc.ex.controller.mq;

import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.service.ai.BYAIService;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.mq.client.Handler;
import com.niceloo.mq.client.annotation.ConsumerMethod;
import com.niceloo.mq.client.annotation.Subscriber;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * 追加百应客户信息到外呼任务
 *
 * <AUTHOR>
 * @create: 2022-02-16 15:07
 * @update: 2024-11-27 10:52
 */
@Subscriber(topics = MQConst.ADD_CUSTOMER_INFO_TO_BY_JOB_TOPIC, clientName = "jobAddCustomerRabbitMqClient", group = "CMC")
public class AddCustomerInfoToByJobMQConsumer {
    private static final Logger logger = LoggerFactory.getLogger(AddCustomerInfoToByJobMQConsumer.class);

    @Resource
    private BYAIService byaiService;

    @Resource
    private CcAiCallJobService ccAiCallJobService;

    /**
     * MQ消息消费方法
     *
     * @return com.niceloo.mq.client.Handler.Ack
     * @paramter data 消息内容
     * <AUTHOR>
     * @Date 14:07 2022/2/17
     **/
    @ConsumerMethod
    public Handler.Ack subscribe(String data) {
        try {
            logger.info("追加客户信息到百应外呼任务MQ消费接收成功,参数:{}", data);
            Map<String, Object> map = JSONUtils.toMap(data);
            String indexName = map.get("indexName").toString();
            String lotNo = (String) map.get("lotNo");
            String jobId = map.get("jobId").toString();
            String firstCustomerPhone = (String) Optional.ofNullable(map.get("firstCustomerPhone")).orElse("");
            String jobProgress = map.get("jobProgress").toString();
            String userId = map.get("userId").toString();

            NgExpand ngExpand = new NgExpand();
            UcUser ucUser = new UcUser();
            ucUser.setUserId(userId);
            ngExpand.setUcUser(ucUser);

            // 追加客户信息到百应
            if (jobProgress.equals("2")) {
                byaiService.addCustomerInfoToJob(indexName, lotNo, jobId, firstCustomerPhone, ngExpand);
            }
            // 全部更新完成后更新任务的状态
            String finishFlag = map.get("finishFlag").toString();
            if ("Y".equals(finishFlag)) {
                logger.info("百应任务追加完成,更新任务状态为已完成,参数:{}", data);
                String jobLevel = (String) map.get("jobLevel");
                // 更新外呼任务的进度为已完成
                ccAiCallJobService.updateJobProgressToFinish(jobId, jobLevel);
            }
        } catch (Exception e) {
            logger.error(e, "追加客户信息到百应外呼任务消费失败,异常信息:{},参数:{}", e.getMessage(), data);
        }
        return Handler.Ack.OK;
    }
}
