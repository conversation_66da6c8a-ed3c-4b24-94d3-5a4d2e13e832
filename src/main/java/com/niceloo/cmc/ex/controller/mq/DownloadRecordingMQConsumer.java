package com.niceloo.cmc.ex.controller.mq;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.service.ai.BYAIService;
import com.niceloo.cmc.ex.service.ai.JDYXService;
import com.niceloo.cmc.ex.service.call.*;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.mq.client.Handler;
import com.niceloo.mq.client.annotation.ConsumerMethod;
import com.niceloo.mq.client.annotation.Subscriber;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 下载通话录音 TOPIC[下载通话记录的录音更新到ES]
 *
 * <AUTHOR>
 * @create: 2022-02-16 15:07
 */
@Subscriber(topics = MQConst.DOWNLOAD_RECORDING_TOPIC, group = "CMC")
public class DownloadRecordingMQConsumer {
    private static final Logger logger = LoggerFactory.getLogger(DownloadRecordingMQConsumer.class);
    @Resource
    private ZHZXService zhzxService;
    @Resource
    private JLFYService jlfyService;
    @Resource
    private YKService ykService;
    @Resource
    private YXService yxService;
    @Resource
    private TQService tqService;
    @Resource
    private FYService fyService;
    @Resource
    private JDYXService jdyxService;
    @Resource
    private BYAIService byaiService;

    /**
     * MQ消息消费方法
     *
     * @return com.niceloo.mq.client.Handler.Ack
     * @paramter data 消息内容
     * <AUTHOR>
     * @Date 14:07 2022/2/17
     **/
    @ConsumerMethod
    public Handler.Ack subscribe(String data) {
        try {
            Map<String, Object> map = JSONUtils.toMap(data);
            String channelType = map.get("channelType").toString();
            CallChannelEnum callChannelEnum = CallChannelEnum.getCallChannel(channelType);
            if (null == callChannelEnum) {
                return Handler.Ack.OK;
            }
            logger.info("下载通话录音MQ消费接收成功,外呼通道:{},参数:{}", channelType, data);
            switch (callChannelEnum) {
                case CALL_TYPE_ZHZX: {
                    zhzxService.downLoadRecording(map);
                    break;
                }
                case CALL_TYPE_JLFY: {
                    jlfyService.downLoadRecording(map);
                    break;
                }
                case CALL_TYPE_YPHONE: {
                    ykService.downLoadRecording(map);
                    break;
                }
                case CALL_TYPE_ZK:
                case CALL_TYPE_YH:
                case CALL_TYPE_YX_SIP: {
                    yxService.downLoadRecording(map);
                    break;
                }
                case CALL_TYPE_TQ_MOBILE:
                case CALL_TYPE_TQ_FIX: {
                    tqService.downLoadRecording(map);
                    break;
                }
                case CALL_TYPE_FY: {
                    fyService.downLoadRecording(map);
                    break;
                }
                case CALL_TYPE_JDYX: {
                    jdyxService.downLoadRecording(map);
                    break;
                }
                case CALL_TYPE_BYAI: {
                    byaiService.downLoadRecording(map);
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("MQ下载通话录音失败,请求参数:" + data + ",错误信息:" + e.getMessage(), e);
        }
        return Handler.Ack.OK;
    }
}
