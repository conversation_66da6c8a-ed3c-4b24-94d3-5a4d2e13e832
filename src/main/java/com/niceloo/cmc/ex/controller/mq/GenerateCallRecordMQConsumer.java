package com.niceloo.cmc.ex.controller.mq;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.pojo.request.GenerateCallRecordRequest;
import com.niceloo.cmc.ex.pojo.request.GenerateJLYQCallRecordRequest;
import com.niceloo.cmc.ex.service.call.*;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.mq.client.Handler;
import com.niceloo.mq.client.annotation.ConsumerMethod;
import com.niceloo.mq.client.annotation.Subscriber;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 生成通话记录MQ消息消费者[智能外呼打通电话后发送MQ(不支持一号互联)]
 *
 * <AUTHOR>
 * @create: 2022-02-16 15:07
 */
@Subscriber(topics = MQConst.GENERATE_CALL_RECORD_TOPIC, group = "CMC")
public class GenerateCallRecordMQConsumer {
    private static final Logger logger = LoggerFactory.getLogger(GenerateCallRecordMQConsumer.class);
    @Resource
    private ZHZXService zhzxService;
    @Resource
    private JLFYService jlfyService;
    @Resource
    private YXService yxService;
    @Resource
    private YKService ykService;
    @Resource
    private TQService tqService;
    @Resource
    private FYService fyService;

    /**
     * MQ消息消费方法
     *
     * @return com.niceloo.mq.client.Handler.Ack
     * @paramter data 消息内容
     * <AUTHOR>
     * @Date 14:07 2022/2/17
     **/
    @ConsumerMethod
    public Handler.Ack subscribe(String data) {
        logger.info("外呼完成MQ消费生成通话记录参数:{}", data);
        Map<String, Object> map = JSONUtils.toMap(data);
        String channelType = map.get("channelType").toString();
        CallChannelEnum callChannelEnum = CallChannelEnum.getCallChannel(channelType);
        if (null == callChannelEnum) {
            return Handler.Ack.OK;
        }
        switch (callChannelEnum) {
            case CALL_TYPE_ZHZX: {
                GenerateCallRecordRequest request = JSONUtils.toObject(data, GenerateCallRecordRequest.class);
                // 生成中弘智享通话记录[失败后直接重投]
                zhzxService.generateCallRecord(request);
                break;
            }
            case CALL_TYPE_JLFY: {
                GenerateJLYQCallRecordRequest request = JSONUtils.toObject(data, GenerateJLYQCallRecordRequest.class);
                // 生成巨量飞鱼通话记录[失败后直接重投]
                jlfyService.generateCallRecord(request);
                break;
            }
            case CALL_TYPE_YPHONE: {
                GenerateCallRecordRequest request = JSONUtils.toObject(data, GenerateCallRecordRequest.class);
                // 生成云客手机通话记录[失败后直接重投]
                ykService.generateCallRecord(request);
                break;
            }
            case CALL_TYPE_ZK:
            case CALL_TYPE_YH:
            case CALL_TYPE_YX_SIP: {
                GenerateCallRecordRequest request = JSONUtils.toObject(data, GenerateCallRecordRequest.class);
                // 生成78亿迅双呼/175亿迅回拨/232亿讯SIP通话记录[失败后直接重投]
                yxService.generateCallRecord(request);
                break;
            }
            case CALL_TYPE_TQ_MOBILE:
            case CALL_TYPE_TQ_FIX: {
                GenerateCallRecordRequest request = JSONUtils.toObject(data, GenerateCallRecordRequest.class);
                // 生成TQ手机/TQ固话通话记录[失败后直接重投]
                tqService.generateCallRecord(request);
                break;
            }
            case CALL_TYPE_FY: {
                GenerateCallRecordRequest request = JSONUtils.toObject(data, GenerateCallRecordRequest.class);
                // 生成风云通话记录[失败后直接重投]
                fyService.generateCallRecord(request);
                break;
            }
            default:
                break;
        }
        return Handler.Ack.OK;
    }
}
