package com.niceloo.cmc.ex.controller.web;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 通讯中心健康检查接口
 *
 * <AUTHOR>
 * @Date 2022-02-14 11:36
 */

@Api("通讯中心健康检查")
@RestController
@RequestMapping("")
public class HelloController {

    @GetMapping("/hello")
    @ApiOperation(value = "Hello World")
    public String helloWorld() {
        return "Hello World !!!";
    }
}
