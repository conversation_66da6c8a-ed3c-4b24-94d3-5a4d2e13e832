package com.niceloo.cmc.ex.controller.web;

import com.alibaba.fastjson.JSONObject;
import com.niceloo.auth.service.AuthService;
import com.niceloo.cmc.ex.common.CallAccountMenuCodeConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MenuCodeEnum;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.feign.AuthFeignClient;
import com.niceloo.cmc.ex.feign.UserFeignClient;
import com.niceloo.cmc.ex.pojo.dto.ZHZXAccountInfoDTO;
import com.niceloo.cmc.ex.pojo.request.*;
import com.niceloo.cmc.ex.pojo.vo.*;
import com.niceloo.cmc.ex.service.BdCallaccountinfoService;
import com.niceloo.cmc.ex.service.CcCallAccountConfigService;
import com.niceloo.cmc.ex.service.call.ZHZXService;
import com.niceloo.cmc.ex.utils.InterfaceRequestAuthUtils;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.ApiParamBody;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.framework.web.validation.anno.IsRangeVal;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import io.swagger.annotations.*;
import lombok.CustomLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 外呼账号配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@RestController
@RequestMapping("/callAccount")
@Validated
@Api("外呼账号配置接口")
@CustomLog
public class CallAccountConfigController {

    @Autowired
    private ZHZXService zhzxService;

    @Autowired
    private BdCallaccountinfoService bdCallaccountinfoService;
    
    @Autowired
    @Qualifier("ccCallAccountConfigServiceImpl")
    private CcCallAccountConfigService accountConfigService;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private AuthService authService;

    @Autowired
    private AuthFeignClient authFeignClient;

    /**
     * 获取短信验证码（中弘智享专用）
     */
    @PostMapping("/sendSms")
    @ApiOperation(value = "中弘智享短信验证码发送", consumes = "application/x-www-form-urlencoded")
    @ResponseBody
    public String sendSms(
            ZHZXSendSmsRequest zhzxSendSmsRequest,
            @ApiIgnore @NotNull(message = "未登陆，请先登录后使用") NgExpand ngExpand
    ) {
        UcUser ucUser = ngExpand.getUcUser();
        String eeId = ucUser.getEeId();

        // 通过中弘智享接口发送短信验证码
        return zhzxService.sendSms(zhzxSendSmsRequest, eeId);
    }

    /**
     * 根据短信及个人信息设置白名单（中弘智享专用）
     */
    @PostMapping("/setupWhiteList")
    @ApiOperation(value = "中弘智享白名单设置", consumes = "application/x-www-form-urlencoded")
    @ResponseBody
    public String setupWhiteList(
            ZHZXSetupWhiteListRequest zhzxSetupWhiteListRequest,
            @ApiIgnore @NotNull(message = "未登陆，请先登录后使用") NgExpand ngExpand
    ) {
        UcUser ucUser = ngExpand.getUcUser();
        String eeId = ucUser.getEeId();

        // 通过中弘智享接口加白
        return zhzxService.setupWhiteList(zhzxSetupWhiteListRequest, eeId);
    }

    /**
     * 根据账号类型查询此账号类型的分校
     *
     * @param menuCode 菜单编码
     * @param ngExpand 扩展参数
     * @param accountType 账号类型
     * @return 查询到的分校列表
     */
    @PostMapping("/schools")
    @ApiOperation(value = "分配API-KEY的分校", consumes = "application/x-www-form-urlencoded")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "请求成功", response = SchoolAuthVO.class, responseContainer = "List"),
            @ApiResponse(code = 400, message = "请求参数不正确"),
            @ApiResponse(code = 401, message = "未授权或授权已过期"),
            @ApiResponse(code = 403, message = "无权访问"),
            @ApiResponse(code = 404, message = "请求路径不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public List<SchoolAuthVO> searchSchoolByAccountType(@NotNull(message = "menuCode 不能为空") String menuCode,
                                                        @ApiIgnore @NotNull(message = "未登陆，请先登录后使用") NgExpand  ngExpand,
                                                        @NotNull(message = "accountType 不能为空") @IsRangeVal(value = {"JLFY", "ZHZX"}) String accountType
    ) {
        // 获取登录用户信息
        UcUser ucUser = ngExpand.getUcUser();
        ngExpand.setUcUser(ucUser);
        String userId = ucUser.getUserId();
        String brandId = ngExpand.getUcUser().getBrandId();

        // 获取当前用户在当前菜单可访问的部门ID集合
        List<String> dptIds = authService.getDptIds(brandId, menuCode, userId);

        // 查询当前登录用户可查看的分校
        JSONObject data = new JSONObject();
        data.put("schoolName", null);
        data.put("dptIds", dptIds);
        data.put("schoolAvlstatus", "Y");
        String jsonString = data.toJSONString();
        List<Map<String, Object>> authSchoolList = userFeignClient.authSchoolList(jsonString);

        // 将 Map 类型的列表转换成 SchoolAuthVO 类型的列表
        List<SchoolAuthVO> schoolList = new ArrayList<>();
        if (!authSchoolList.isEmpty()) {
            for (Map<String, Object> authSchool : authSchoolList) {
                SchoolAuthVO schoolAuthVO = new SchoolAuthVO();
                BeanUtils.copyFromObjToObj(schoolAuthVO, authSchool);
                schoolList.add(schoolAuthVO);
            }
        }

        // 对上面的分校进行筛选，保留配置过当前传入的账号类型的分校
        List<String> schoolIds = bdCallaccountinfoService.selectSchoolIdByAccountType(accountType);
        return schoolList.stream()
                .filter(item -> schoolIds.contains(item.getSchoolId()) )
                .collect(Collectors.toList());
    }

    @PostMapping("/searchList")
    @ApiOperation(value = "外呼账号列表查询", consumes = "application/x-www-form-urlencoded")
    public BasePageVO<CallAccountVO> searchList(@Validated CallAccountSelectRequest request, @ApiIgnore NgExpand ngExpand) {
        // 权限校验
        String menuCode = CallAccountMenuCodeConst.getMenuCode(MenuCodeEnum.OperationTypeEnum.SEARCH.getType(), request.getChannelType());
        InterfaceRequestAuthUtils.interfaceAuthOfSelectType(request, ngExpand, menuCode);
        return accountConfigService.searchPage(request);
    }

    @PostMapping("/searchOperationList")
    @ApiOperation(value = "外呼账号操作记录列表查询", consumes = "application/x-www-form-urlencoded")
    public List<CallAccountOperationListVO> searchOperationList(@NotBlank(message = "外呼账号ID不能为空")
                                                                @ApiParam("外呼账号ID") String callAccountId) {
        return accountConfigService.searchAccountOperationLogList(callAccountId);
    }

    @PostMapping("/searchCallAccountInfoById")
    @ApiOperation(value = "根据外呼账号id查询详情", consumes = "application/x-www-form-urlencoded")
    public CallAccountVO searchCallAccountInfoById(@NotBlank(message = "外呼账号ID不能为空") @ApiParam("外呼账号ID") String callAccountId) {
        CcCallAccountConfig callAccountConfig = accountConfigService.searchCallAccountInfoById(callAccountId);
        if (null == callAccountConfig) {
            return null;
        }
        return CallAccountVO.typeConvertor(callAccountConfig);
    }

    @PostMapping("/accountOperation")
    @ApiOperation(value = "外呼账号操作(解绑、换绑、绑定、启用、禁用)", consumes = "application/x-www-form-urlencoded")
    public String accountOperation(@Validated CallAccountOperationRequest request, @ApiIgnore NgExpand ngExpand) {
        request.addOperatorInfo(ngExpand);
        // 权限校验
        Map<String, Object> userInfo = this.checkAccountOperationAuth(request.getOperationType(), request.getChannelType(), request.getCallAccountId(), ngExpand, request.getAfterEeUserId());
        // 添加员工的其他信息
        request.addEeOtherInfo(userInfo);
        // 修改操作外呼账号(解绑、换绑、绑定)[包括生成记录到记录表]
        accountConfigService.accountOperation(request);
        return "操作成功";
    }

    @PostMapping("/accountAdd")
    @ApiOperation(value = "外呼账号创建", consumes = "application/x-www-form-urlencoded")
    public String accountAdd(@Validated CallAccountAddRequest request, @ApiIgnore NgExpand ngExpand) {
        String eeId = ngExpand.getUcUser().getEeId();
        try {
            // 添加操作员信息
            request.addOperatorInfo(ngExpand);

            // 校验权限
            checkAccountOperationAuth(request, ngExpand);

            // 校验账号是否重复
            checkDuplicateAccount(request);

            // 校验员工是否已经绑定了启用状态的账号
            checkEmployeeAccountStatus(request);

            // 根据通道类型执行不同的操作
            if (CallChannelEnum.CALL_TYPE_ZHZX.getType().equals(request.getChannelType())) {
                // 中弘智享通道需要进行加白操作
                setupZhzxWhiteList(request, eeId);
            }
            // 保存账号信息
            accountConfigService.insertCallAccount(request);

            return "创建成功";
        } catch (ApplicationException e) {
            // 记录错误日志
            LOGGER.error("外呼账号创建失败，请求参数为：{}, 报错信息：{}", request, e);
            throw new ApplicationException(e.getCode(), e.getMessage());
        }
    }

    /**
     * 校验账号操作权限
     *
     * @param request   请求对象
     * @param ngExpand  NgExpand对象
     */
    private  void checkAccountOperationAuth(CallAccountAddRequest request, NgExpand ngExpand) {
        Map<String, Object> userInfo = this.checkAccountOperationAuth(MenuCodeEnum.OperationTypeEnum.ADD.getType(),
                request.getChannelType(), null, ngExpand, request.getEeUserId());

        // 添加员工的其他信息
        request.addEeOtherInfo(userInfo);
    }

    /**
     * 校验账号是否重复
     *
     * @param request   请求对象
     */
    private void checkDuplicateAccount(CallAccountAddRequest request) {
        if (!CallChannelEnum.CALL_TYPE_JDYX.getType().equals(request.getChannelType())) {
            boolean flag = accountConfigService.hasCallAccount(request.getAccount(),
                    CallChannelEnum.getCallChannel(request.getChannelType()));
            if (flag) {
                throw new ApplicationException(ApiErrorCodes.request_repeat, "该主叫账号已存在，无法重复配置。");
            }
        }
    }

    /**
     * 校验员工是否已经绑定了启用状态的账号
     *
     * @param request   请求对象
     */
    private void checkEmployeeAccountStatus(CallAccountAddRequest request) {
        boolean flag1 = accountConfigService.hasCallAccountByUserId(request.getEeUserId(),
                CallChannelEnum.getCallChannel(request.getChannelType()));
        if (flag1 && "N".equals(request.getDisableStatus())) {
            throw new ApplicationException(ApiErrorCodes.execute_failed_handler,
                    "一个员工只能有一个启用状态的外呼账号");
        }
    }

    /**
     * 中弘智享通道需要进行加白操作
     *
     * @param request   请求对象
     * @param eeId      员工标识
     */
    private void setupZhzxWhiteList(CallAccountAddRequest request, String eeId) {
        if (Boolean.FALSE.equals(zhzxService.isZhzxWhiteUser(request.getAccount(), eeId))) {
            // 请求中弘智享接口进行加白操作
            ZHZXAccountInfoDTO zhzxAccountInfoDTO = JSONUtils.toObject(request.getVendorAccountInfo(),
                    ZHZXAccountInfoDTO.class);
            ZHZXSetupWhiteListRequest zhzxSetupWhiteListRequest = new ZHZXSetupWhiteListRequest();
            zhzxSetupWhiteListRequest.setCode(zhzxAccountInfoDTO.getCaptcha());
            zhzxSetupWhiteListRequest.setName(zhzxAccountInfoDTO.getUserName());

            // 缓存中获取vid
            zhzxSetupWhiteListRequest.setVid(zhzxService.getVidFromCache(zhzxAccountInfoDTO.getMobile()));
            zhzxSetupWhiteListRequest.setNumber(zhzxAccountInfoDTO.getMobile());
            zhzxSetupWhiteListRequest.setIdCard(zhzxAccountInfoDTO.getIdNO());
            zhzxService.setupWhiteList(zhzxSetupWhiteListRequest, eeId);
        }
    }

    @PostMapping("/batchQueryEeCallChannel")
    @ApiOperation(value = "批量查询员工外呼通道", consumes = "application/x-www-form-urlencoded")
    public List<EeCallAccountChannelVO> batchQueryEeCallChannel(@ApiParam("员工userid列表(一次最多查询20条外呼通道信息)")
                                                                @RequestParam(value = "eeUserIds")
                                                                @NotNull(message = "员工userid列表不能为空") List<String> eeUserIds) {
        if (eeUserIds.isEmpty()) {
            return new ArrayList<>();
        }
        if (eeUserIds.size() > 20) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "一次最多查询20条外呼通道信息");
        }
        return accountConfigService.batchQueryEeCallChannel(eeUserIds);
    }

    @PostMapping("/batchUnbind")
    @ApiOperation(value = "根据id列表批量解绑", consumes = "application/x-www-form-urlencoded")
    public String batchUnbind(@Validated @ApiParamBody CallAccountBatchUnbindRequest request, @ApiIgnore NgExpand ngExpand) {
        request.addOperatorInfo(ngExpand);
        // 查询数据库得到未解绑的账号列表
        List<CcCallAccountConfig> accountConfigList = accountConfigService.searchBandingAccountListByIds(request.getCallAccountId());
        if (CollectionUtils.isEmpty(accountConfigList)){
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "请选择未解绑的账号");
        }
        // 批量解绑
        accountConfigService.batchUnbind(request, accountConfigList);
        return "批量解绑成功";
    }


    @PostMapping("/searchCallAccountInfoByUserId")
    @ApiOperation(value = "根据员工id查询外呼账号详情", consumes = "application/x-www-form-urlencoded")
    public CallAccountVO searchCallAccountInfoByUserId(
            @NotBlank(message = "员工id不能为空") @ApiParam("员工id") String userId,
            @ApiParam("外呼通道类型") @IsRangeVal(value = {"JLFY", "ZHZX", "JDYX"}) String channelType) {
        CcCallAccountConfig callAccountConfig = accountConfigService.searchCallAccountInfoByUserId(userId, channelType);
        if (null == callAccountConfig) {
            return null;
        }
        CallAccountVO callAccountVO = CallAccountVO.typeConvertor(callAccountConfig);
        callAccountVO.setAccount(callAccountConfig.getAccount());
        return callAccountVO;
    }

    /**
     * 账号操作接口权限校验并返回员工信息
     *
     * @param operationTypeEnum 操作类型 {@link MenuCodeEnum.OperationTypeEnum}
     * @param channelType       外呼类型 {@link CallChannelEnum}
     * @param ngExpand          请求扩展字段
     * @param eeUserId          被操作的账号userId
     * @return java.util.Map<java.lang.String, java.lang.Object> (被操作的账号员工信息)
     * <AUTHOR>
     * @Date 10:26 2022/5/27
     **/
    private Map<String, Object> checkAccountOperationAuth(Integer operationTypeEnum, String channelType, String callAccountId, NgExpand ngExpand, String eeUserId) {
        String menuCode = CallAccountMenuCodeConst.getMenuCode(operationTypeEnum, channelType);
        // 新增不需要校验操作前该账号所属的组织架构
        AuthBaseRequest authRequest = new AuthBaseRequest();
        if (StringUtils.isNotEmpty(callAccountId)) {
            authRequest = accountConfigService.getOrgStructureOfCreator(callAccountId);
            // 校验是否有操作该账号的权限(资源改变前的权限校验)
            InterfaceRequestAuthUtils.interfaceAuthOfOperationType(ngExpand, menuCode, authRequest);
        }
        // 如果改变后该资源不属于任何人，则不校验改变后权限
        if (StringUtils.isEmpty(eeUserId)) {
            return Collections.emptyMap();
        }
        // 查询用户中心接口获取员工的信息
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId", eeUserId);
        Map<String, Object> userInfo = userFeignClient.getUserInfoByUserId(JSONUtils.toJSONString(map));
        authRequest.setSchoolId(userInfo.get("schoolId").toString());
        authRequest.setDptId(userInfo.get("dptId").toString());
        authRequest.setEeUserId(eeUserId);
        // 资源改变后的权限校验
        InterfaceRequestAuthUtils.interfaceAuthOfOperationType(ngExpand, menuCode, authRequest);
        return userInfo;
    }
}

