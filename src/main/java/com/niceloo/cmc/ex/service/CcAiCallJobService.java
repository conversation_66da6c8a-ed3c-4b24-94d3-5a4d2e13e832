package com.niceloo.cmc.ex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.request.AICallJobListRequest;
import com.niceloo.cmc.ex.pojo.vo.AiCallJobVO;
import com.niceloo.cmc.ex.pojo.vo.BasePageVO;

import java.util.List;

/**
 * <p>
 * AI外呼任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
public interface CcAiCallJobService extends IService<CcAiCallJob> {

    /**
     * 分页查询AI外呼任务列表
     *
     * @param request 请求参数
     * @return 分页外呼任务
     */
    BasePageVO<AiCallJobVO> selectJobPage(AICallJobListRequest request);

    /**
     * 根据厂商的外呼id查询索引名称
     *
     * @param vendorJobId 厂商外呼id
     * @param aiJobType 外呼通道类型
     * @return 索引名称
     */
    String getIndexNameByVendorJobId(Integer vendorJobId, String aiJobType);

    /**
     * 根据厂商的外呼id查询主键id
     *
     * @param vendorJobId 厂商外呼id
     * @param aiJobType 外呼通道类型
     * @return 主键id
     */
    String getJobIdByVendorJobId(Integer vendorJobId, String aiJobType);

    /**
     * 根据厂商的外呼id查询主键id
     *
     * @param vendorJobId 厂商外呼id
     * @param aiJobType 外呼通道类型
     * @param companyId companyId
     * @return 主键id
     */
    String getJobIdByVendorJobIdAndCompanyId(Integer vendorJobId, String aiJobType, Long companyId);

    /**
     * 校验任务名称是否存在
     *
     * @param name 任务名称
     * @return 存在返回true
     */
    boolean existJobName(String name);

    /**
     * 根据任务id获取子任务列表
     *
     * @param parentId 父任务ID
     * @return 子任务列表
     */
    List<CcAiCallJob> selectJobListByParentId(String parentId);

    /**
     * 根据任务id和分校id获取子任务
     *
     * @param parentId 父任务ID
     * @param schoolId 子任务的分校id
     * @return 子任务
     */
    CcAiCallJob selectJobByParentIdAndSchoolId(String parentId, String schoolId);

    /**
     * 一、更新任务进度为已完成(同一个父id的都更新为已完成,创建失败的除外)<br/>
     * 二、更新客户追加状态为已完成
     *
     * @param jobId 任务id
     */
    void updateJobProgressToFinish(String jobId, String jobLevel);
}
