package com.niceloo.cmc.ex.service.ai;

import com.byai.client.auth.Token;
import com.byai.client.core.BYClient;
import com.byai.client.core.DefaultBYClient;
import com.byai.client.oauth.model.OAuthToken;
import com.byai.client.oauth.types.AuthorizePlatform;
import com.byai.gen.v1_0_0.api.*;
import com.byai.gen.v1_0_0.model.*;
import com.byai.util.hash.MD5Utils;
import com.niceloo.cmc.ex.common.*;
import com.niceloo.cmc.ex.common.aicall.AiCallJobTypeEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo;
import com.niceloo.cmc.ex.pojo.dto.byai.*;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerAddRequest;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.ErrorLogUtil;
import com.niceloo.cmc.ex.utils.HttpUtils;
import com.niceloo.cmc.ex.utils.OkHttpUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.mq.client.Client;
import com.niceloo.plugin.sdk.lang.Pair;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.BizConst.AI_ALL_TASK;
import static com.niceloo.cmc.ex.common.BizConst.AI_SCHOOL_TASK;
import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_BYAI;

@Service
public class BYAIServiceImpl implements BYAIService {
    private static final Logger logger = LoggerFactory.getLogger(BYAIServiceImpl.class);
    @Resource
    private OkHttpUtil okHttpUtil;

    @Resource
    private CcAiCallJobService ccAiCallJobService;

    @Resource
    private AICallJobCustomerInfoService aiCallJobCustomerInfoService;

    @Resource(name = "jobAddCustomerRabbitMqClient")
    private Client client;

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Resource
    private CallProperties.ByAiProperty byAiProperty;

    @Resource
    private BYAIServiceImpl byaiService;

    @Resource
    private JDYXServiceImpl jdyxService;

    private static final ExecutorService fixedThreadPool = Executors.newFixedThreadPool(50, new CustomThreadFactory("by_get_jobs_sync"));

    /**
     * 获取access-token
     */
    private String getBYAIAccessToken() throws ApplicationException {
        String accessToken = null;
        String refreshToken;
        Integer expiresIn;
        Boolean hasKey = redisTemplate.hasKey(RedisConst.BYAI_ACCESS_TOKEN);
        Boolean hasRefreshKey = redisTemplate.hasKey(RedisConst.BYAI_REFRESH_TOKEN);
        Long tokenExpire = redisTemplate.getExpire(RedisConst.BYAI_ACCESS_TOKEN);
        // accessToken有效期不足1天且未失效时，使用refreshToken进行刷新
        if (hasKey && hasRefreshKey && tokenExpire != null && tokenExpire < 86400) {
            refreshToken = redisTemplate.opsForValue().get(RedisConst.BYAI_REFRESH_TOKEN);
            // 刷新token，刷新后，原token会在5分钟后失效
            Map<String, Object> param = new HashMap<>();
            param.put("client_id", byAiProperty.getClientId());
            param.put("client_secret", byAiProperty.getClientSecret());
            param.put("company_id", byAiProperty.getCompanyId());
            param.put("grant_type", "refresh_token");
            param.put("refresh_token", refreshToken);
            String url = byAiProperty.getUrl() + byAiProperty.getOauthToken();
            BYAIBaseDTO byaiBaseDTO = sendRequest(url, param);
            if (byaiBaseDTO.getCode() == 200) {
                Map<String, Object> responseData = (Map<String, Object>) byaiBaseDTO.getData();
                if (responseData != null && !responseData.isEmpty()) {
                    accessToken = (String) responseData.get("access_token");
                    refreshToken = (String) responseData.get("refresh_token");
                    expiresIn = (Integer) responseData.get("expires_in");
                    if (StringUtils.isNotEmpty(accessToken)) {
                        redisTemplate.opsForValue().set(RedisConst.BYAI_ACCESS_TOKEN, accessToken, expiresIn, TimeUnit.SECONDS);
                    }
                    if (StringUtils.isNotEmpty(refreshToken)) {
                        redisTemplate.opsForValue().set(RedisConst.BYAI_REFRESH_TOKEN, refreshToken, expiresIn, TimeUnit.SECONDS);
                    }
                }
            }
        } else {
            // 无需刷新accessToken时，直接取Redis的值，若不存在则重新获取，并存入Redis
            if (hasKey) {
                accessToken = redisTemplate.opsForValue().get(RedisConst.BYAI_ACCESS_TOKEN);
            }
            if (StringUtils.isEmpty(accessToken)) {
                AuthorizePlatform platform = new AuthorizePlatform(byAiProperty.getClientId(), byAiProperty.getClientSecret(), byAiProperty.getCompanyId());
                OAuthToken oAuthToken = platform.getToken();
                accessToken = oAuthToken.getAccessToken();
                refreshToken = oAuthToken.getRefreshToken();
                expiresIn = oAuthToken.getExpiresIn();
                if (StringUtils.isNotEmpty(accessToken)) {
                    redisTemplate.opsForValue().set(RedisConst.BYAI_ACCESS_TOKEN, accessToken, expiresIn, TimeUnit.SECONDS);
                }
                if (StringUtils.isNotEmpty(refreshToken)) {
                    redisTemplate.opsForValue().set(RedisConst.BYAI_REFRESH_TOKEN, refreshToken, expiresIn, TimeUnit.SECONDS);
                }
            }
        }
        return accessToken;
    }

    /**
     * 查询某一个机器人对应的话术模板列表
     *
     */
    @Override
    public List<BYAIContextListDTO> getContextList() {
        try {
            String accessToken = getBYAIAccessToken();
            // 获取机器人话术列表
            ByaiOpenapiRobotListResult result;
            try (BYClient client = new DefaultBYClient(new Token(accessToken))) {
                ByaiOpenapiRobotListParams byaiOpenapiRobotListParams = new ByaiOpenapiRobotListParams();
                byaiOpenapiRobotListParams.setCompanyId(byAiProperty.getCompanyId());
                // 只要已上线话术（话术状态 0：所有话术，1：已上线话术，2：查询发布过(含已上线和上线后重新修改) 的话术 ；默认为0）
                byaiOpenapiRobotListParams.setRobotStatus(1L);
                ByaiOpenapiRobotList byaiOpenapiRobotList = new ByaiOpenapiRobotList();
                byaiOpenapiRobotList.setAPIParams(byaiOpenapiRobotListParams);
                result = client.invoke(byaiOpenapiRobotList);
            }
            ByaiOpenapiRobotListResult.RobotVO[] list = result.getList();

            return Arrays.stream(list)
                    .map(robotVO -> {
                        BYAIContextListDTO dto = new BYAIContextListDTO();
                        dto.setId(Math.toIntExact(robotVO.getRobotDefId()));
                        dto.setName(robotVO.getRobotName());
                        return dto;
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.warn("获取话术列表失败，报错信息：{}", e.getMessage());
            throw new ApplicationException(ApiErrorCodes.execute_failed, "获取话术列表失败");
        }
    }


    /**
     * 查询电话线路列表
     *
     **/
    @Override
    public List<BYAILineListDTO> getLineList() {
        try {
            String accessToken = getBYAIAccessToken();
            // 获取线路列表
            ByaiOpenapiPhoneListResult result;
            // 获取坐席详情
            ByaiOpenapiSeatinfoGetResult seatResult;
            try (BYClient client = new DefaultBYClient(new Token(accessToken))) {
                ByaiOpenapiPhoneListParams byaiOpenapiPhoneListParams = new ByaiOpenapiPhoneListParams();
                byaiOpenapiPhoneListParams.setCompanyId(byAiProperty.getCompanyId());
                ByaiOpenapiPhoneList byaiOpenapiPhoneList = new ByaiOpenapiPhoneList();
                byaiOpenapiPhoneList.setAPIParams(byaiOpenapiPhoneListParams);
                result = client.invoke(byaiOpenapiPhoneList);

                ByaiOpenapiSeatinfoGetParams byaiOpenapiSeatinfoGetParams = new ByaiOpenapiSeatinfoGetParams();
                byaiOpenapiSeatinfoGetParams.setCompanyId(byAiProperty.getCompanyId());
                ByaiOpenapiSeatinfoGet byaiOpenapiSeatinfoGet = new ByaiOpenapiSeatinfoGet();
                byaiOpenapiSeatinfoGet.setAPIParams(byaiOpenapiSeatinfoGetParams);
                seatResult = client.invoke(byaiOpenapiSeatinfoGet);
            }

            // 计算剩余可用坐席数量
            long concurrency = seatResult.getCompanyAllCallSeat() - seatResult.getCompanyUsingCallSeat();
            ByaiOpenapiPhoneListResult.UserPhoneNumberVO[] list = result.getList();

            return Arrays.stream(list)
                    .map(robotVO -> {
                        BYAILineListDTO dto = new BYAILineListDTO();
                        dto.setLineId(robotVO.getUserPhoneId());
                        dto.setDisplayNumbers(Collections.singletonList(robotVO.getPhone()));
                        dto.setLineName(robotVO.getPhone());
                        dto.setConcurrency(Math.toIntExact(concurrency));
                        return dto;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.warn("获取电话线路列表失败，报错信息：{}", e.getMessage());
            throw new ApplicationException(ApiErrorCodes.execute_failed, "获取电话线路列表失败");
        }
    }

    /**
     * 获取天盾策略列表
     */
    @Override
    public List<BYAITiandunListDTO> getTiandunList() {
        try {
            String url = byAiProperty.getUrl() + byAiProperty.getTiandunList();
            Map<String, Object> param = new HashMap<>();
            param.put("accessToken", getBYAIAccessToken());
            param.put("pageNum", 1);
            param.put("pageSize", Integer.MAX_VALUE);
            // 发起请求
            BYAIBaseDTO byaiBaseDTO = sendRequest(url, param);
            Map<String, Object> responseData = (Map<String, Object>) byaiBaseDTO.getData();

            if (responseData != null && !responseData.isEmpty() && responseData.containsKey("list")) {
                List<BYAITiandunListDTO> list = new ArrayList<>();
                List<Map<String, Object>> result = (List<Map<String, Object>>) responseData.get("list");
                for (Map<String, Object> map : result) {
                    BYAITiandunListDTO dto = new BYAITiandunListDTO();
                    dto.setTiandunRuleStrategyGroupId((Integer) map.get("id"));
                    dto.setTiandunRuleStrategyGroupName(ObjectUtils.defaultIfNull(map.get("name"), "").toString());
                    list.add(dto);
                }
                return list;
            } else {
                return new ArrayList<>();
            }
        } catch (Exception e) {
            logger.warn("获取天盾策略列表失败，报错信息：{}", e.getMessage());
            throw new ApplicationException(ApiErrorCodes.execute_failed, "获取天盾策略列表失败");
        }
    }

    public Optional<String> getLineNameById(Long lineId) {
        return this.getLineList().stream()
                .filter(line -> line.getLineId().equals(lineId))
                .map(BYAILineListDTO::getLineName)
                .findFirst();
    }

    /**
     * 查询指定任务ID执行状态
     *
     **/
    @Override
    public BYAIJobInfoDTO getJobInfo(Long jobId) {
        try {
            String accessToken = getBYAIAccessToken();
            ByaiOpenapiCalljobDetailGetResult result;
            try (BYClient client = new DefaultBYClient(new Token(accessToken))) {
                ByaiOpenapiCalljobDetailGetParams byaiOpenapiCalljobDetailGetParams = new ByaiOpenapiCalljobDetailGetParams();

                byaiOpenapiCalljobDetailGetParams.setCallJobId(jobId);
                byaiOpenapiCalljobDetailGetParams.setCompanyId(byAiProperty.getCompanyId());

                ByaiOpenapiCalljobDetailGet byaiOpenapiCalljobDetailGet = new ByaiOpenapiCalljobDetailGet();
                byaiOpenapiCalljobDetailGet.setAPIParams(byaiOpenapiCalljobDetailGetParams);
                result = client.invoke(byaiOpenapiCalljobDetailGet);
            }
            BYAIJobInfoDTO byaiJobInfoDTO = new BYAIJobInfoDTO();
            byaiJobInfoDTO.setJobId(result.getCallJobId() == null ? null : Math.toIntExact(result.getCallJobId()));
            byaiJobInfoDTO.setJobName(result.getJobName());
            byaiJobInfoDTO.setContextId(result.getRobotDefId() == null ? null : Math.toIntExact(result.getRobotDefId()));
            //任务状态
            // waiting  队列中 7:排队中
            // running  执行中 1:进行中 5:自动暂停
            // finished 已完成 2:已完成
            // paused   暂停中 0:未开始 3:可运行 4:手动暂停
            // canceled 已取消 8:AI到期 9:线路欠费 10:短信欠费 11:AI欠费
            // stopped  已结束 6:已终止
            long callJobStatus = result.getCallJobStatus();
            if (callJobStatus == 0 || callJobStatus == 3 || callJobStatus == 7) {
                byaiJobInfoDTO.setStatus("waiting");
            } else if (callJobStatus == 1 || callJobStatus == 5) {
                byaiJobInfoDTO.setStatus("running");
            } else if (callJobStatus == 2) {
                byaiJobInfoDTO.setStatus("finished");
            } else if (callJobStatus == 4 || callJobStatus == 8 || callJobStatus == 9 || callJobStatus == 10 || callJobStatus == 11) {
                byaiJobInfoDTO.setStatus("paused");
            } else if (callJobStatus == 6) {
                byaiJobInfoDTO.setStatus("stopped");
            } else {
                byaiJobInfoDTO.setStatus("canceled");
            }
            //任务开始时间
            byaiJobInfoDTO.setStartTime(result.getStartDate() == null ? null : DateUtils.toStr(result.getStartDate()));
            //任务拨打的号码总数
            Long totalCount = result.getTotalCount();
            byaiJobInfoDTO.setTotalCalledNum(totalCount == null ? null : Math.toIntExact(totalCount));
            //任务已完成拨打的号码总数
            Long doneCount = result.getDoneCount();
            byaiJobInfoDTO.setCalledTaskNum(doneCount == null ? null : Math.toIntExact(doneCount));
            //接通号码数
            byaiJobInfoDTO.setConnectedTaskNum(result.getCalledCount() == null ? null : Math.toIntExact(result.getCalledCount()));
            //未拨打号码数
            if (totalCount != null && doneCount != null) {
                long pendingTaskNum = totalCount - doneCount;
                byaiJobInfoDTO.setPendingTaskNum(Math.toIntExact(pendingTaskNum));
            }
            //还有一些字段未完善，此处不需要这么多字段信息，暂不处理
            return byaiJobInfoDTO;
        } catch (Exception e) {
            logger.warn("查询指定任务ID执行状态失败，报错信息：{}", e.getMessage());
            throw new ApplicationException(ApiErrorCodes.execute_failed, "查询指定任务ID执行状态失败");
        }
    }

    /**
     * 批量任务详情查询接口
     *
     * @param jobIds   任务id列表
     **/
    @Override
    public List<BYAIJobReportDTO> getJobReportList(List<Integer> jobIds) {
        try {
            String accessToken = getBYAIAccessToken();
            int batchSize = 5; // 每次处理的ID数量
            List<Future<List<BYAIJobReportDTO>>> futures = new ArrayList<>();

            // 分割jobIds并提交到线程池进行并发处理
            for (List<Integer> batchJobIds : partitionJobIds(jobIds, batchSize)) {
                futures.add(fixedThreadPool.submit(() -> {
                    try (BYClient client = new DefaultBYClient(new Token(accessToken))) {
                        ByaiOpenapiCalljobDetailListParams params = new ByaiOpenapiCalljobDetailListParams();
                        params.setCallJobIdList(JSONUtils.toJSONString(batchJobIds));
                        params.setCompanyId(byAiProperty.getCompanyId());
                        ByaiOpenapiCalljobDetailList request = new ByaiOpenapiCalljobDetailList();
                        request.setAPIParams(params);

                        ByaiOpenapiCalljobDetailListResult result = client.invoke(request);
                        ByaiOpenapiCalljobDetailListResult.CallJobDetailQueryResponse[] data = result.getList();

                        return Arrays.stream(data)
                                .map(item -> {
                                    BYAIJobReportDTO byaiJobReportDTO = new BYAIJobReportDTO();
                                    byaiJobReportDTO.setId(item.getCallJobId() == null ? null : Math.toIntExact(item.getCallJobId()));
                                    byaiJobReportDTO.setName(item.getJobName());

                                    //任务状态
                                    // waiting  队列中 7:排队中
                                    // running  执行中 1:进行中 5:自动暂停
                                    // finished 已完成 2:已完成
                                    // paused   暂停中 0:未开始 3:可运行 4:手动暂停
                                    // canceled 已取消 8:AI到期 9:线路欠费 10:短信欠费 11:AI欠费
                                    // stopped  已结束 6:已终止
                                    Long callJobStatus = item.getCallJobStatus();
                                    if (callJobStatus == 0 || callJobStatus == 3 || callJobStatus == 7) {
                                        byaiJobReportDTO.setStatus("waiting");
                                    } else if (callJobStatus == 1 || callJobStatus == 5) {
                                        byaiJobReportDTO.setStatus("running");
                                    } else if (callJobStatus == 2) {
                                        byaiJobReportDTO.setStatus("finished");
                                    } else if (callJobStatus == 4 || callJobStatus == 8 || callJobStatus == 9 || callJobStatus == 10 || callJobStatus == 11) {
                                        byaiJobReportDTO.setStatus("paused");
                                    } else if (callJobStatus == 6) {
                                        byaiJobReportDTO.setStatus("stopped");
                                    } else {
                                        byaiJobReportDTO.setStatus("canceled");
                                    }

                                    Long doneCount = item.getDoneCount();
                                    Long totalCount = item.getTotalCount();
                                    double outboundProcess = 0.0;
                                    if (doneCount != null && totalCount != null) {
                                        outboundProcess = totalCount == 0 ? 0.0 : (double) doneCount / totalCount;
                                    }
                                    byaiJobReportDTO.setOutboundProcess(outboundProcess);
                                    byaiJobReportDTO.setConnectedTaskNum(item.getCalledCount() == null ? null : Math.toIntExact(item.getCalledCount()));
                                    byaiJobReportDTO.setTotalTaskNum(totalCount == null ? null : Math.toIntExact(totalCount));

                                    return byaiJobReportDTO;
                                })
                                .collect(Collectors.toList());
                    } catch (Exception e) {
                        // 处理单个任务失败的情况
                        logger.error("单个任务失败IDS" + batchJobIds, e);
                        return new ArrayList<>();
                    }
                }));
            }

            // 收集所有Future的结果
            List<BYAIJobReportDTO> allReports = new ArrayList<>();
            for (Future<List<BYAIJobReportDTO>> future : futures) {
                try {
                    allReports.addAll(future.get()); // 这会阻塞，直到结果可用
                } catch (InterruptedException | ExecutionException e) {
                    // 处理获取结果时发生的异常
                    logger.error("Error getting result from Future", e);
                    // 可以选择抛出异常、记录日志或忽略错误
                }
            }

            return allReports;
        } catch (Exception e) {
            logger.warn("批量任务详情查询失败，报错信息：{}", e.getMessage());
            throw new ApplicationException(ApiErrorCodes.execute_failed, "批量任务详情查询失败");
        }
    }

    // 将jobIds分割成多个批次
    private List<List<Integer>> partitionJobIds(List<Integer> jobIds, int batchSize) {
        List<List<Integer>> partitions = new ArrayList<>();
        int index = 0;
        while (index < jobIds.size()) {
            int end = Math.min(index + batchSize, jobIds.size());
            partitions.add(jobIds.subList(index, end));
            index += batchSize;
        }
        return partitions;
    }

    /**
     * 根据不同的操作类型操作外呼任务
     *
     * @param vendorJobId 第三方厂商-任务id
     * @param type        操作类型 (1->启动,2->暂停,3->继续,4->删除)
     **/
    @Override
    public void operatingJob(Long vendorJobId, Integer type) {
        try {
            String accessToken = getBYAIAccessToken();
            try (BYClient client = new DefaultBYClient(new Token(accessToken))) {
                if (type == 1 || type == 2 || type == 3) {
                    Long command;
                    if (type == 2) {
                        command = 2L;
                    } else {
                        command = 1L;
                    }
                    // 启动、暂停、继续任务
                    ByaiOpenapiCalljobExecuteParams byaiOpenapiCalljobExecuteParams = new ByaiOpenapiCalljobExecuteParams();

                    byaiOpenapiCalljobExecuteParams.setCallJobId(vendorJobId);
                    byaiOpenapiCalljobExecuteParams.setCommand(command);
                    byaiOpenapiCalljobExecuteParams.setCompanyId(byAiProperty.getCompanyId());

                    ByaiOpenapiCalljobExecute byaiOpenapiCalljobExecute = new ByaiOpenapiCalljobExecute();
                    byaiOpenapiCalljobExecute.setAPIParams(byaiOpenapiCalljobExecuteParams);
                    client.invoke(byaiOpenapiCalljobExecute);
                } else if (type == 4) {
                    // 删除任务
                    ByaiOpenapiCalljobDeleteParams byaiOpenapiCalljobDeleteParams = new ByaiOpenapiCalljobDeleteParams();

                    byaiOpenapiCalljobDeleteParams.setCallJobId(vendorJobId);
                    byaiOpenapiCalljobDeleteParams.setCompanyId(byAiProperty.getCompanyId());

                    ByaiOpenapiCalljobDelete byaiOpenapiCalljobDelete = new ByaiOpenapiCalljobDelete();
                    byaiOpenapiCalljobDelete.setAPIParams(byaiOpenapiCalljobDeleteParams);
                    client.invoke(byaiOpenapiCalljobDelete);
                } else {
                    throw new ApplicationException(ApiErrorCodes.argument_invalided, "不支持此操作类型");
                }

            }

        } catch (Exception e) {
            logger.warn("根据不同的操作类型操作外呼任务失败，报错信息：{}", e.getMessage());
            throw new ApplicationException(ApiErrorCodes.execute_failed, "任务执行失败");
        }
    }

    /**
     * 从百应创建外呼任务和追加客户
     *
     * @param aiCallJob           任务信息
     * @param customerRequestList 一条客户信息,如果有的话
     * @param ngExpand            登录用户信息
     **/
    @Override
    public Integer createJobFromVendor(CcAiCallJob aiCallJob, List<JDCallCustomerRequest> customerRequestList, NgExpand ngExpand) {
        // 如果任务名称|父任务|客户信息列表校验失败,不进行创建
        if (StringUtils.isEmpty(aiCallJob.getCustomerIndex()) || 0 == aiCallJob.getJobLevel()) {
            return null;
        }

        CcAiCallJob aiCallJobForUpdate = new CcAiCallJob();
        aiCallJobForUpdate.setJobId(aiCallJob.getJobId());
        Integer vendorJobId = null;
        try (BYClient client = new DefaultBYClient(new Token(getBYAIAccessToken()))) {
            ByaiOpenapiCalljobCreateResult result = client.invoke(getByaiOpenapiCalljobCreate(aiCallJob));
            vendorJobId = result.getCallJobId().intValue();
            aiCallJobForUpdate.setVendorJobId(vendorJobId);
            aiCallJobForUpdate.setJobProgress(2);
        } catch (Exception exception) {
            String message = exception.getMessage();
            logger.warn(exception, "创建百应ai外呼任务失败，报错信息：{}", message);
            String jsonErrorLog = ErrorLogUtil.formatErrorResponse(message);
            aiCallJobForUpdate.setErrorLog(jsonErrorLog);
            aiCallJobForUpdate.setJobProgress(3);
        }
        if (!aiCallJobForUpdate.getJobProgress().equals(3)) {
            aiCallJobForUpdate.setStatus(AICallJobStatus.waiting.name());
        }
        aiCallJobForUpdate.setModifyDate(DateUtils.getNowDString());
        ccAiCallJobService.updateById(aiCallJobForUpdate);
        return vendorJobId;
    }

    private ByaiOpenapiCalljobCreate getByaiOpenapiCalljobCreate(CcAiCallJob aiCallJob) {
        ByaiOpenapiCalljobCreateParams byaiOpenapiCalljobCreateParams = new ByaiOpenapiCalljobCreateParams();
        byaiOpenapiCalljobCreateParams.setRepeatNoCallPriority(BizConst.REPEAT_NO_CALL_PRIORITY_ON);
        byaiOpenapiCalljobCreateParams.setCallJobName(aiCallJob.getJobName());
        byaiOpenapiCalljobCreateParams.setRemark(aiCallJob.getDescription());
        byaiOpenapiCalljobCreateParams.setRobotDefId(aiCallJob.getContextId().longValue());
        byaiOpenapiCalljobCreateParams.setUserPhoneIds(String.format("[%s]", aiCallJob.getLineId()));
        byaiOpenapiCalljobCreateParams.setCompanyId(byAiProperty.getCompanyId());

        AiCallJobTypeEnum aiCallJobType = AiCallJobTypeEnum.getByCode(aiCallJob.getStartupMode());
        byaiOpenapiCalljobCreateParams.setCallJobType(aiCallJobType.getNumber().longValue());
        if (aiCallJobType == AiCallJobTypeEnum.TIMING) {
            byaiOpenapiCalljobCreateParams.setStartDate(DateUtils.toDate(aiCallJob.getStartTime()));
        }
        if (aiCallJob.getConcurrency() != null) {
            byaiOpenapiCalljobCreateParams.setConcurrencyQuota(aiCallJob.getConcurrency().longValue());
        }
        if (aiCallJob.getPriority() != null) {
            byaiOpenapiCalljobCreateParams.setPriority(aiCallJob.getPriority().longValue());
        }
        byaiOpenapiCalljobCreateParams.setRepeatCall(1 == aiCallJob.getIsRedial());
        if (byaiOpenapiCalljobCreateParams.getRepeatCall()) {
            List<Map<String, Integer>> repeatCallRules = Arrays.stream(aiCallJob.getRedialReason().trim().split("\\|"))
                    .map(Integer::parseInt)
                    .filter(status -> status > 0)
                    .map(status -> Map.of(
                            "phoneStatus", status,
                            "interval", aiCallJob.getRedialInterval(),
                            "times", aiCallJob.getRedialTimes()
                    )).collect(Collectors.toList());
            byaiOpenapiCalljobCreateParams.setRepeatCallRule(JSONUtils.toJSONString(repeatCallRules));
        }
        if (aiCallJob.getTiandunRuleStrategyGroupId() != null && aiCallJob.getTiandunRuleStrategyGroupId() > 0) {
            byaiOpenapiCalljobCreateParams.setTiandunEnabled(true);
            byaiOpenapiCalljobCreateParams.setTiandunRuleStrategyGroupId(aiCallJob.getTiandunRuleStrategyGroupId().longValue());
        }
        if (StringUtils.isNotEmpty(aiCallJob.getJobOfflineWeek())
                && StringUtils.isNotEmpty(aiCallJob.getTimeBegin()) && StringUtils.isNotEmpty(aiCallJob.getTimeEnd())) {
            List<Map<String, Object>> workingTimeRuleList = List.of(
                    Map.of(
                            "weeks", StringUtils.removeEnd(aiCallJob.getJobOfflineWeek().trim(), ",").split(","),
                            "timeRanges", List.of(
                                    Map.of(
                                            "startTime", aiCallJob.getTimeBegin(),
                                            "endTime", aiCallJob.getTimeEnd()
                                    )
                            )
                    )
            );
            byaiOpenapiCalljobCreateParams.setWorkingTimeRuleList(JSONUtils.toJSONString(workingTimeRuleList));
        }
        logger.debug("创建百应ai外呼任务请求参数：{}", JSONUtils.toJSONString(byaiOpenapiCalljobCreateParams.toParams()));
        return new ByaiOpenapiCalljobCreate(byaiOpenapiCalljobCreateParams);
    }

    /**
     * 从百应创建外呼任务和追加客户
     *
     * @param aiJobId 外呼任务主键id
     **/
    @Override
    public void createJobFromVendor(String aiJobId, NgExpand ngExpand) {
        if (StringUtils.isEmpty(aiJobId)) {
            return;
        }
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        Integer vendorId = this.createJobFromVendor(aiCallJob, List.of(), ngExpand);
        aiCallJob = ccAiCallJobService.getById(aiJobId);
        // 将客户追加到百应
        if (vendorId != null) {
            // 发送MQ将客户信息追加到百应的外呼任务上
            Map<String, String> mqRequest = new HashMap<>();
            mqRequest.put("indexName", aiCallJob.getCustomerIndex());
            mqRequest.put("lotNo", null);
            mqRequest.put("jobLevel", "1");
            mqRequest.put("jobId", aiCallJob.getJobId());
            mqRequest.put("finishFlag", aiCallJob.getCustomerAddStatus());
            mqRequest.put("jobProgress", aiCallJob.getJobProgress().toString());
            mqRequest.put("firstCustomerPhone", "");
            mqRequest.put("userId", ngExpand.getUcUser().getUserId());
            client.publish(MQConst.ADD_CUSTOMER_INFO_TO_BY_JOB_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest));
        }
    }

    /**
     * 根据百应的外呼id更新任务的状态
     *
     * @param vendorJobId 百应后台外呼id
     * @param companyId     companyId
     **/
    @Override
    public void updateJobStatusByJobId(Integer vendorJobId, Long companyId) throws IOException {
        String jobId = ccAiCallJobService.getJobIdByVendorJobIdAndCompanyId(vendorJobId, CALL_TYPE_BYAI.getType(), companyId);
        if (StringUtils.isEmpty(jobId)) {
            return;
        }
        // 更新外呼任务的信息到数据库
        this.batchUpdateAICallJob(List.of(jobId), null);
    }

    /**
     * 接收百应回调外呼任务电话明细回调
     *
     * @param callRecordCallback 回调详情信息
     **/
    @Override
    public void updateAIJobCustomerInfo(BYAICallRecordCallback callRecordCallback) {
        // 将回调参数转成实体
        AICallJobCustomerInfo jobCustomerInfo = callRecordCallback.convertToAIJobCustomerEntity();
        if (null == jobCustomerInfo) {
            return;
        }
        // 查询出索引的名称
        String indexName = ccAiCallJobService.getIndexNameByVendorJobId(jobCustomerInfo.getVendorJobId(), CALL_TYPE_BYAI.getType());
        // 更新到ES数据库
        aiCallJobCustomerInfoService.bulkUpdate(indexName, List.of(jobCustomerInfo));
        // 异步下载录音
        if ("Y".equals(jobCustomerInfo.getHasRecordingDownLoad())) {
            Map<String, Object> data = new HashMap<>(4);
            data.put("channelType", CALL_TYPE_BYAI.getType());
            data.put("indexName", indexName);
            data.put("recordingDownLoadTimes", jobCustomerInfo.getRecordingDownLoadTimes());
            data.put("voiceSourceUrl", jobCustomerInfo.getRecordingUrl());
            data.put("id", jobCustomerInfo.getId());
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data));
        }
    }

    /**
     * 下载录音文件到OSS并将地址更新到ES内
     *
     * @param map 录音文件等地址
     **/
    @Override
    public void downLoadRecording(Map<String, Object> map) {
        String voiceSourceUrl = map.get("voiceSourceUrl").toString();
        if (StringUtils.isEmpty(voiceSourceUrl)) {
            return;
        }
        String indexName = map.get("indexName").toString();
        int recordingDownLoadTimes = Integer.parseInt(map.get("recordingDownLoadTimes").toString());
        String id = map.get("id").toString();
        String fileName = id + ".mp3";
        AICallJobCustomerInfo jobCustomerInfo = new AICallJobCustomerInfo();
        jobCustomerInfo.setId(id);
        jobCustomerInfo.setModifiedTime(new Date());
        jobCustomerInfo.setRecordingDownLoadTimes(recordingDownLoadTimes + 1);
        try {
            Map<String, Object> uploadResMap = HttpUtils.uploadVoiceToOSS(voiceSourceUrl, fileName);
            jobCustomerInfo.setServerFolder((String) uploadResMap.get("filePath"));
            jobCustomerInfo.setFileKey((String) uploadResMap.get("fileKey"));
            // 更新通话记录
        } catch (Exception e) {
            logger.warn(e, "百应通道下载通话录音id为:{}出现异常,异常信息:{}", id, e.getMessage());
        }
        aiCallJobCustomerInfoService.bulkUpdate(indexName, List.of(jobCustomerInfo));
    }

    /**
     * 更新外呼任务的信息到数据库
     *
     * @param jobIds 外呼任务主键id
     * @param ucUser 操作人信息
     * @return java.util.List<com.niceloo.cmc.ex.entity.CcAiCallJob>
     **/
    @Override
    public List<CcAiCallJob> batchUpdateAICallJob(List<String> jobIds, UcUser ucUser) throws IOException {
        if (CollectionUtils.isEmpty(jobIds)) {
            return new ArrayList<>();
        }
        // 根据id列表查询数据库
        List<CcAiCallJob> callJobList = ccAiCallJobService.listByIds(jobIds);
        // 过滤掉外呼任务为已删除的外呼任务
        List<CcAiCallJob> filteredCallJobList = callJobList.stream().
                filter(t -> 1 == t.getJobLevel()
                        && !t.getStatus().equals(AICallJobStatus.canceled.name()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredCallJobList)) {
            return new ArrayList<>();
        }
        // 调用百应接口更新外呼任务列表
        this.updateJobInfoList(filteredCallJobList);
        if (ucUser != null) {
            filteredCallJobList.forEach(t -> {
                t.setModifier(ucUser.getUserId());
                t.setModifyName(ucUser.getUserName());
            });
        }
        // 更新到数据库
        List<CcAiCallJob> updateCallJobList = filteredCallJobList
                .stream()
                .filter(t -> t.getVendorJobId() != 0)
                .collect(Collectors.toList());
        ccAiCallJobService.updateBatchById(updateCallJobList);
        return filteredCallJobList;
    }

    /**
     * 创建子任务并追加客户的信息到子任务
     *
     * @param parentCallJob 父任务信息
     * @param request       客户列表
     **/
    @Override
    public void createSubJobAndAddCustomer(CcAiCallJob parentCallJob, JDCallCustomerAddRequest request, NgExpand ngExpand) {
        String lotNo = request.getLotNo();
        // AI 外呼任务类型
        String callTaskType = parentCallJob.getCallTaskType();
        // 客户信息列表
        List<JDCallCustomerRequest> jdCallCustomerRequestList = request.getJdCallCustomerRequestList();
        // 根据分校id进行分组，但首先检查callTaskType的值
        Map<String, List<JDCallCustomerRequest>> jdCallCustomerRequestListGroup;
        if (AI_SCHOOL_TASK.equals(callTaskType)) { // 假设callTaskType是字符串类型
            // 如果是分校任务，则按分校ID分组
            jdCallCustomerRequestListGroup = jdCallCustomerRequestList.stream()
                    .collect(Collectors.groupingBy(JDCallCustomerRequest::getSchoolId));
        } else if (AI_ALL_TASK.equals(callTaskType)) { // 假设callTaskType是字符串类型
            // 如果是全国任务，则不分组，schoolId固定，schoolName固定为"全国"
            jdCallCustomerRequestListGroup = Collections.singletonMap(
                    "SCHOOL20190411010000000019", jdCallCustomerRequestList
            );
        } else {
            throw new IllegalArgumentException("Invalid callTaskType: " + callTaskType);
        }

        // 创建子任务到数据库
        jdyxService.globalCreateSubCallJob(parentCallJob, jdCallCustomerRequestListGroup, callTaskType);
        // 存储要添加到ES内的客户信息列表
        List<AICallJobCustomerInfo> callJobCustomerInfoListTotal = new ArrayList<>(jdCallCustomerRequestList.size());
        List<CcAiCallJob> callJobList = fetchChildJobList(parentCallJob.getJobId());
        Map<String, CcAiCallJob> callJobMap = JDYXServiceImpl.createSchoolIdMap(callJobList);
        for (Map.Entry<String, List<JDCallCustomerRequest>> listEntry : jdCallCustomerRequestListGroup.entrySet()) {
            String schoolId = listEntry.getKey();
            // 获取子任务的信息
            CcAiCallJob aiCallJob = callJobMap.get(schoolId);
            List<AICallJobCustomerInfo> callJobCustomerInfoList = aiCallJobCustomerInfoService.entityConverter(aiCallJob, listEntry.getValue(), lotNo);
            callJobCustomerInfoListTotal.addAll(callJobCustomerInfoList);
        }
        // 在百应平台创建外呼任务
        Map<String, String> firstCustomerPhoneMap = byaiService.createJobFormVendor(parentCallJob.getJobId(), jdCallCustomerRequestListGroup, ngExpand);
        // 将客户信息添加到ES
        aiCallJobCustomerInfoService.bulkIndex(parentCallJob.getCustomerIndex(), callJobCustomerInfoListTotal);
        // 发送MQ添加客户信息到子任务
        this.sendAddCustomerInfoMQ(parentCallJob.getJobId(), lotNo, request.getFinishFlag(), firstCustomerPhoneMap, ngExpand);
    }

    /**
     * 追加客户信息到百应
     *
     * @param indexName          索引名称
     * @param lotNo              批次号,为空则上传所有未上传的客户
     * @param jobId              任务id
     **/
    @Override
    public void addCustomerInfoToJob(String indexName, String lotNo, String jobId, String firstCustomerPhone, NgExpand ngExpand) {
        // 滚动查询es
        Pair<String, List<JDCallCustomerRequest>> pair = aiCallJobCustomerInfoService.startScrollByLotNoAndJob(indexName, lotNo, jobId);
        String scrollId = pair.getKey();
        if (StringUtils.isEmpty(scrollId)) {
            return;
        }
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(jobId);
        // 追加失败的客户信息
        Map<String, String> invalidMap = new HashMap<>();
        boolean flag = true;
        while (flag) {
            scrollId = pair.getKey();
            try (BYClient client = new DefaultBYClient(new Token(getBYAIAccessToken()))) {
                ByaiOpenapiCalljobCustomerImportParams byaiOpenapiCalljobCustomerImportParams = new ByaiOpenapiCalljobCustomerImportParams();
                byaiOpenapiCalljobCustomerImportParams.setCallJobId(aiCallJob.getVendorJobId().longValue());
                byaiOpenapiCalljobCustomerImportParams.setCompanyId(aiCallJob.getCompanyId());
                byaiOpenapiCalljobCustomerImportParams.setCustomerInfoVOList(changeToByCustomerInfos(pair.getValue(), aiCallJob.getParentJobId()));
//                byaiOpenapiCalljobCustomerImportParams.setEncryptionPhone(true);
                ByaiOpenapiCalljobCustomerImport byaiOpenapiCalljobCustomerImport = new ByaiOpenapiCalljobCustomerImport();
                logger.debug("调用百应接口追加客户信息请求参数: {}", JSONUtils.toJSONString(byaiOpenapiCalljobCustomerImportParams.toParams()));
                byaiOpenapiCalljobCustomerImport.setAPIParams(byaiOpenapiCalljobCustomerImportParams);
                ByaiOpenapiCalljobCustomerImportResult result = client.invoke(byaiOpenapiCalljobCustomerImport);
                // 存在导入失败的客户
                if (result.getPlaceFailNum() > 0) {
                    Map<String, String> invalidPhoneMap = JSONUtils.toList(result.getPlaceFailDetail(), Map.class).stream()
//                            .collect(Collectors.toMap(map -> EncryptUtils.aesDecrypt(map.get("phone").toString(), byAiProperty.getClientSecret()),
                            .collect(Collectors.toMap(map -> map.get("phone").toString(),
                                    map -> map.get("msg").toString(),
                                    (msg1, msg2) -> msg1 + ", " + msg2));
                    logger.warn("百应任务{}存在追加失败的客户:{}", aiCallJob.getVendorJobId(), invalidPhoneMap);
                    invalidMap.putAll(invalidPhoneMap);
                }
            } catch (Exception e) {
                logger.warn(e, "调用百应接口追加客户信息出现异常,异常信息:{}", e.getMessage());
                Map<String, String> invalidPhoneMap = new HashMap<>();
                pair.getValue().forEach(t -> invalidPhoneMap.put(t.getPhone(), e.getMessage()));
                invalidMap.putAll(invalidPhoneMap);
            }

            // 追加完成后修改到ES内
            List<AICallJobCustomerInfo> aiCallJobCustomerInfoList = JDCallCustomerRequest.bulkUpdateCustomerSyncStatus(aiCallJob.getVendorJobId(), aiCallJob.getJobName(), pair.getValue(), invalidMap);
            aiCallJobCustomerInfoService.bulkUpdate(indexName, aiCallJobCustomerInfoList);
            pair = aiCallJobCustomerInfoService.continueScroll(indexName, scrollId);
            invalidMap.clear();
            flag = StringUtils.isNotEmpty(pair.getKey());
        }
    }

    private String changeToByCustomerInfos(List<JDCallCustomerRequest> requests, String parentJobId) {
        List<Map<String, Object>> infos = requests.stream().map(request -> Map.of(
                "name", request.getName(),
//                "phone", EncryptUtils.aesEncrypt(request.getPhone(), byAiProperty.getClientSecret()),
                "phone", MD5Utils.MD5(request.getPhone()),
                "properties", Map.of(
                        "jobId", parentJobId,
                        "custId", request.getCustId(),
                        "uuid", request.getUuId()
                )
        )).collect(Collectors.toList());
        return JSONUtils.toJSONString(infos);
    }

    /**
     * 发送MQ添加客户信息到外呼任务内
     *
     * @param parentId              父任务id
     * @param lotNo                 批次号
     * @param finishFlag            是否已经追加完成的标志
     * @param firstCustomerPhoneMap 已经添加到外呼任务的客户Map
     **/
    private void sendAddCustomerInfoMQ(String parentId, String lotNo, String finishFlag, Map<String, String> firstCustomerPhoneMap, NgExpand ngExpand) {
        // 根据父任务id获取所有创建任务成功的子任务
        List<CcAiCallJob> subJobList = fetchChildJobList(parentId);
        for (CcAiCallJob callJob : subJobList) {
            // 发送MQ将客户信息追加到百应的外呼任务上
            Map<String, String> mqRequest = new HashMap<>();
            mqRequest.put("indexName", callJob.getCustomerIndex());
            mqRequest.put("lotNo", lotNo);
            mqRequest.put("jobId", callJob.getJobId());
            mqRequest.put("finishFlag", finishFlag);
            mqRequest.put("jobProgress", callJob.getJobProgress().toString());
            mqRequest.put("firstCustomerPhone", "");
            mqRequest.put("userId", ngExpand.getUcUser().getUserId());
            client.publish(MQConst.ADD_CUSTOMER_INFO_TO_BY_JOB_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(2));
        }
    }

    /**
     * 在百应平台创建外呼任务
     *
     * @param parentId                父任务的id
     * @param jdCallCustomerListGroup 根据schoolId分组后的客户信息
     * @return java.util.Map<java.lang.String, java.lang.String>
     **/
    @Klock(name = "GLOBAL_CREATE_CALL_JOB_FORM_BYAI_LOCK", waitTime = 60, leaseTime = 50, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public Map<String, String> createJobFormVendor(String parentId, Map<String, List<JDCallCustomerRequest>> jdCallCustomerListGroup, NgExpand ngExpand) {
        // 父任务查子任务列表
        List<CcAiCallJob> subJobList = fetchChildJobList(parentId);
        for (CcAiCallJob aiCallJob : subJobList) {
            if (aiCallJob.getJobProgress() == 1 && aiCallJob.getVendorJobId() == 0) {
                List<JDCallCustomerRequest> customerRequestList = jdCallCustomerListGroup.get(aiCallJob.getJobSchoolId());
                if (null == customerRequestList || customerRequestList.isEmpty()) {
                    continue;
                }
                // 调用百应的接口创建任务和更新数据库
                this.createJobFromVendor(aiCallJob, List.of(), ngExpand);
            }
        }
        return Map.of();
    }

    /**
     * 根据父级外呼任务ID获取子级外呼任务列表
     *
     * @param parentCallJobJobId 父级外呼任务ID
     * @return 子级外呼任务列表
     */
    private List<CcAiCallJob> fetchChildJobList(String parentCallJobJobId) {
        return ccAiCallJobService.selectJobListByParentId(parentCallJobJobId);
    }

    /**
     * 更新外呼任务列表
     *
     * @param filteredCallJobList 外呼任务列表
     **/
    private void updateJobInfoList(List<CcAiCallJob> filteredCallJobList) {
        // 任务id列表
        List<Integer> jobIdList = filteredCallJobList.stream().map(CcAiCallJob::getVendorJobId).collect(Collectors.toList());

        // 获取当前任务的话术模板
        List<BYAIContextListDTO> contextList = this.getContextList();
        Map<Integer, String> contextMap = contextList.stream().collect(Collectors.toMap(BYAIContextListDTO::getId, BYAIContextListDTO::getName));

        // 外呼任务信息
        List<BYAIJobReportDTO> jobReportList = this.getJobReportList(jobIdList);
        Map<Integer, BYAIJobReportDTO> jdJobReportDTOMap = jobReportList.stream()
                .collect(Collectors.toMap(BYAIJobReportDTO::getId, Function.identity(), (m1, m2) -> m1));

        // 更新任务信息
        for (CcAiCallJob ccAiCallJob : filteredCallJobList) {
            // 通过外呼任务ID进行比对(在百应后台被删除的不能更新)
            if (jdJobReportDTOMap.containsKey(ccAiCallJob.getVendorJobId())) {
                BYAIJobReportDTO jdJobReportDTO = jdJobReportDTOMap.get(ccAiCallJob.getVendorJobId());
                String contextName = contextMap.get(ccAiCallJob.getContextId());
                ccAiCallJob.setContextName(contextName);
                jdJobReportDTO.updateCallInfo(ccAiCallJob);
            }
        }
    }

    /**
     * 调用接口发送请求
     *
     **/
    private BYAIBaseDTO sendRequest(String url, Map<String, Object> params) {
        String response = okHttpUtil.postFrom(url, params);
        if (StringUtils.isEmpty(response)) {
            logger.warn("请求百应接口出现异常,请求参数:{}", params);
            throw new ApplicationException(ApiErrorCodes.execute_failed_handler, "请求百应接口出现异常");
        }
        BYAIBaseDTO baseDTO = JSONUtils.toObject(response, BYAIBaseDTO.class);
        Integer code = baseDTO.getCode();
        if (code != 200) {
            logger.warn("请求百应接口出现异常,请求参数:{}", params);
            throw new ApplicationException(ApiErrorCodes.execute_failed_handler, "BYAI:" + JSONUtils.toJSONString(baseDTO));
        }
        return baseDTO;
    }

}
