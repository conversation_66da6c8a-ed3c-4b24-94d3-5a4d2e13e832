package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.pojo.request.AuthBaseRequest;
import com.niceloo.cmc.ex.pojo.request.TrafficAnalysisRequest;
import com.niceloo.cmc.ex.pojo.request.TrafficTopRequest;
import com.niceloo.cmc.ex.pojo.vo.BasePageVO;
import com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO;
import com.niceloo.cmc.ex.pojo.vo.TrafficStatisticVO;
import com.niceloo.cmc.ex.pojo.vo.ValidCallAnalysisVO;

import java.util.List;
import java.util.Map;

public interface TrafficStatisticService {

    Map<String, String> getCountDataBySchool(String createdTimeStart, String createdTimeEnd);

    /**
     * 统计员工话务数据到日统计表
     * @param dayDate 天日期 (2022-03-21)
     * @param channelType 外呼类型
     */
    void addDayStatisticToDB(String dayDate, String channelType);

    /**
     * 统计员工话务数据到月统计表
     * @param date 月日期 (2022-03)
     * @param channelType 外呼类型
     */
    void addMonthStatisticToDB(String date, String channelType);

    /**
     * 根据具体参数进行分段话务统计查询
     *
     * @return com.niceloo.cmc.ex.pojo.vo.RecordDataPageVO<T>
     * @paramter callerUserId 员工的用户标识
     * @paramter dptIds 部门id列表
     * @paramter channelType 外呼通道
     * @paramter T 返回结果值类型
     * @paramter dates 话务统计的时间列表,如:["2021-12-10","2021-12-11","2021-12-12"]
     */
    <T> List<T> selectTrafficStatistic(String callerUserId, String dptIds, String channelType, List<String> dates, Class<T> T);

    /**
     * 补偿员工话务统计分校信息
     *
     * @param trafficStatistic 员工维度话务统计列表
     */
    void replenishEeSchoolInfo(List<RecordEeStatisticsVO> trafficStatistic);

    /**
     * 合并员工维度的话务统计
     *
     * @param eeStatisticsVOS 分段(多个日期段统计的)的话务统计列表
     * @return 员工维度的话务统计
     */
    List<RecordEeStatisticsVO> mergeTrafficStatisticGroupEe(List<RecordEeStatisticsVO> eeStatisticsVOS);


    /**
     * 根据条件查询当天的话务统计聚合
     *
     * @param request 请求参数
     * @return 话务统计聚合
     */
    TrafficStatisticVO instantStatistics(AuthBaseRequest request);

    /**
     * 根据条件聚合查询ES获取话务统计
     *
     * @param request 请求参数
     * @return 话务统计聚合
     */
    BasePageVO<TrafficStatisticVO> trafficAnalysis(TrafficTopRequest request);

    /**
     * 根据条件聚合查询ES获取有效通话数据
     *
     * @param request 请求参数
     * @return 有效通话话务统计聚合
     */
    BasePageVO<ValidCallAnalysisVO> validTrafficAnalysis(TrafficAnalysisRequest request);

    /**
     * 根据条件聚合查询ES获取话务统计并存储在EXCEL内
     *
     * @param request 请求参数
     * @param userId 请求人userId
     * @return excel下载Id
     */
    String asyncTrafficAnalysisExcel(TrafficTopRequest request, String userId);

    /**
     * 根据条件聚合查询ES获取话务统计并存储在EXCEL内
     *
     * @param request 请求参数
     * @param userId 请求人userId
     * @return excel下载Id
     */
    String trafficAnalysisExcel(TrafficAnalysisRequest request, String userId);

    /**
     * 获取EXCEL下载到阿里云的地址和fileKey
     *
     * @param excelId excel下载Id
     * @return 文件地址
     */
    Map<String, Object> trafficStatisticsExcel(String excelId);

    /**
     * 生成云外呼通道账号余额
     */
    void createSchoolBalance();
}
