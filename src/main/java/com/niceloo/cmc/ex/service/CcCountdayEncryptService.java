package com.niceloo.cmc.ex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niceloo.cmc.ex.entity.CcCountday;

/**
 * <p>
 * 库表数据加密
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21 17:58:46
 */
public interface CcCountdayEncryptService extends IService<CcCountday> {

    /**
     * 加密日统计表字段
     *
     * @param batch  每批次更新数
     */
    public void encryptTable(int batch) throws InterruptedException;
}
