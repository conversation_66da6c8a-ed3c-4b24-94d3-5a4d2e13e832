package com.niceloo.cmc.ex.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.CustomThreadFactory;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.config.CommentWriteHandler;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.cmc.ex.config.es.EmptyPageable;
import com.niceloo.cmc.ex.controller.web.CallRecordManageController;
import com.niceloo.cmc.ex.entity.CcCountday;
import com.niceloo.cmc.ex.entity.CcCountmonth;
import com.niceloo.cmc.ex.entity.CcSchoolBalance;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.mapper.CcSchoolBalanceMapper;
import com.niceloo.cmc.ex.pojo.dto.*;
import com.niceloo.cmc.ex.pojo.request.AuthBaseRequest;
import com.niceloo.cmc.ex.pojo.request.TrafficAnalysisRequest;
import com.niceloo.cmc.ex.pojo.request.TrafficTopRequest;
import com.niceloo.cmc.ex.pojo.vo.*;
import com.niceloo.cmc.ex.service.*;
import com.niceloo.cmc.ex.service.call.YXService;
import com.niceloo.cmc.ex.service.call.ZHZXService;
import com.niceloo.cmc.ex.utils.*;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.file.FileUploadResponse;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.plugin.sdk.lang.Pair;
import lombok.CustomLog;
import okhttp3.Headers;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.*;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.cardinality.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.cardinality.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.sum.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.bucketsort.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.aggregations.support.ValueType;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.BizConst.TRAFFIC_ANALYSIS_EXCEL;
import static com.niceloo.cmc.ex.common.BizConst.TRAFFIC_TOP_EXCEL;
import static com.niceloo.cmc.ex.utils.EncryptDealUtil.decryptFields;
import static com.niceloo.cmc.ex.utils.EncryptDealUtil.encryptFields;

/**
 * 话务统计服务
 *
 * <AUTHOR>
 * @since 2022-02-23 17:41
 */
@Service
@CustomLog
public class TrafficStatisticServiceImpl implements TrafficStatisticService {
    private static final Logger logger = LoggerFactory.getLogger(TrafficStatisticServiceImpl.class);

    @Resource
    private CcSchoolBalanceMapper ccSchoolBalanceMapper;

    @Resource
    private ZHZXService zhzxService;

    @Resource
    private YXService yxService;

    @Resource
    private BdCallaccountinfoService bdCallaccountinfoService;

    @Resource
    private CallRecordService callRecordService;

    @Resource
    private CcCountdayService ccCountdayService;

    @Resource
    private CcCountmonthService ccCountmonthService;
    @Resource
    private BdEeService bdEeService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private FileUtil fileUtil;

    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);

    private static final int THREAD_NUM = Runtime.getRuntime().availableProcessors() * 2;
    // 线程池: 用于子线程生成Excel和上传到阿里云
    public static final ThreadPoolExecutor GENERATE_EXCEL_EXECUTOR = new ThreadPoolExecutor(THREAD_NUM, THREAD_NUM, 30, TimeUnit.MINUTES, new LinkedBlockingQueue<>(), new CustomThreadFactory("generateTrafficStatisticExcelExecutor"));
    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;
    // 聚合查询时根据员工分组最多查询前多少条数量
    private static final Integer aggEeMaxNum = 10000;

    /**
     * 获取话务统计数据，按照分校分组
     *
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @paramter createdTimeStart  yyyy-MM-dd HH:mm:ss
     * @paramter createdTimeEnd  yyyy-MM-dd HH:mm:ss
     * <AUTHOR>
     * @Date 10:26 2022/3/21
     **/
    @Override
    public Map<String, String> getCountDataBySchool(String createdTimeStart, String createdTimeEnd) {
        // 获取要查询的字段
        List<String> sourceSearch = this.getRealTimeFindFiledNameList();
        // 查询ES获取通话记录
        List<CallRecord> trafficInfoList =
                callRecordService.getTrafficInfoList(createdTimeStart, createdTimeEnd, sourceSearch);
        // 获取各种统计数据
        Map<String, String> resMap = new LinkedHashMap<>();
        if (!trafficInfoList.isEmpty()) {
            // 对通话记录以分校分组
            Map<String, List<CallRecord>> dataMap =
                    trafficInfoList.stream()
                            .filter(trafficInfo -> StringUtils.isNotEmpty(trafficInfo.getSchoolId()))
                            .collect(Collectors.groupingBy(CallRecord::getSchoolId));
            for (Map.Entry<String, List<CallRecord>> entry : dataMap.entrySet()) {
                EeTrafficStatisticVO eeTrafficStatisticVo = new EeTrafficStatisticVO();
                // 根据员工的通话记录列表进行话务统计
                List<Map<String, Object>> mapList = new ArrayList<>(entry.getValue().size());
                entry.getValue().forEach(callRecord -> mapList.add(MapUtils.bean2Map(callRecord)));
                eeTrafficStatisticVo.trafficStatistic(mapList);
                Map<String, Object> value = eeTrafficStatisticVo.convertToMap();
                resMap.put(entry.getKey(), JSONUtils.toJSONString(value));
            }
        }
        return resMap;
    }

    /**
     * 统计员工话务数据到日统计表
     *
     * @paramter dayDate 要统计的年月日[yyyy-MM-dd]
     * @paramter channelType 外呼类型
     * <AUTHOR>
     * @Date 11:08 2021/12/8
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDayStatisticToDB(String dayDate, String channelType) {
        logger.info("开始--->话务统计_日统计表,进行统计时间为:" + dayDate + ",外呼类型为:" + channelType + " 的通话记录");
        String startTime = DateUtil.format(dayDate, DateUtil.YMD) + " 00:00:00";
        String endTime = DateUtil.format(dayDate, DateUtil.YMD) + " 23:59:59";
        // 根据时间范围和外呼类型查询ES获取通话记录
        List<EeTrafficStatisticVO> countData = this.getCountData(channelType, startTime, endTime);
        List<CcCountday> commuCounts = new LinkedList<>();
        if (!countData.isEmpty()) {
            for (EeTrafficStatisticVO eeTrafficStatisticVo : countData) {
                // 将 EeTrafficStatisticVo 转为 CcCountday
                CcCountday countDay = eeTrafficStatisticVo.convertToCountDay();
                countDay.setCountchannelType(channelType);
                countDay.setCountcreatedTime(startTime.substring(0, 10));

                // 数据加密
                encryptFields(countDay);
                commuCounts.add(countDay);
            }
            // 删除当天被统计过的数据
            ccCountdayService.deleteByDateAndChannelType(dayDate, channelType);
            // 存储日级别统计记录
            ccCountdayService.batchSave(commuCounts);
        }
        logger.info("完成--->话务统计_日统计表,完成统计时间为:" + dayDate + ",外呼类型为:" + channelType + " 的通话记录");
    }

    /**
     * 统计员工话务数据到月统计表
     *
     * @paramter date 要统计的年月[yyyy-MM]
     * @paramter channelType
     * <AUTHOR>
     * @Date 14:22 2021/12/7
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMonthStatisticToDB(String date, String channelType) {
        logger.info("开始--->话务统计_月统计表,进行统计时间为:" + date + ",外呼类型为:" + channelType + " 的通话记录");
        String[] dates = date.split("-");
        // 当月开始时间
        String start = DateUtils
                .toStr(DateUtils.getFirstDayOfMonth(Integer.parseInt(dates[0]), Integer.parseInt(dates[1])), DateUtil.YMD);
        // 当月结束时间
        String end = DateUtils
                .toStr(DateUtils.getLastDayOfMonth(Integer.parseInt(dates[0]), Integer.parseInt(dates[1])), DateUtil.YMD);
        // 从日统计表内查询出当月的所有统计数据
        List<CcCountday> ccCountDays = ccCountdayService.selectListByDate(start, end, channelType);
        if (CollectionUtils.isEmpty(ccCountDays)) {
            logger.info("完成--->话务统计_月统计表,完成统计时间为:" + date + ",外呼类型为:" + channelType + " 的通话记录");
            return;
        }
        // 添加从日统计表内查询出来的汇总数据到月统计表
        List<CcCountmonth> ccCountMonths = new LinkedList<>();
        // 将数据按员工分组
        Map<String, List<CcCountday>> eeMap = ccCountDays.stream()
                .collect(Collectors.groupingBy(day -> day.getCountcallAccount() + "_" + day.getCountcallerUserId()));
        for (Map.Entry<String, List<CcCountday>> entry : eeMap.entrySet()) {
            // 员工当月的日汇总话务列表
            List<CcCountday> countDays = entry.getValue();
            EeTrafficStatisticVO eeTrafficStatisticVo = new EeTrafficStatisticVO();
            // 月话务统计到EeTrafficStatisticVo内
            eeTrafficStatisticVo.monthTrafficStatistic(countDays);
            // 将 EeTrafficStatisticVo 转为 CcCountmonth
            CcCountmonth ccCountmonth = eeTrafficStatisticVo.convertToCountMonth();
            ccCountmonth.setCountcreatedTime(countDays.get(0).getCountcreatedTime().substring(0, 7));
            ccCountmonth.setCountchannelType(channelType);

            // 数据加密
            encryptFields(ccCountmonth);
            ccCountMonths.add(ccCountmonth);
        }
        if (!ccCountMonths.isEmpty()) {
            // 删除月统计表内的当月统计数据并赋值为新统计数据
            ccCountmonthService.deleteByDate(DateUtil.format(start, DateUtil.YM), channelType);
            ccCountmonthService.batchSave(ccCountMonths);
        }
        logger.info("完成--->话务统计_月统计表,完成统计时间为:" + date + ",外呼类型为:" + channelType + " 的通话记录");
    }

    /**
     * 根据具体参数进行分段话务统计查询
     *
     * @return java.util.List<T>
     * @paramter callerUserId 员工的用户标识
     * @paramter dptIds 部门id列表
     * @paramter channelType 外呼通道
     * @paramter T 返回结果值类型
     * @paramter dates 话务统计的时间列表,如:["2021-12-10","2021-12-11","2021-12-12"]
     * <AUTHOR>
     * @Date 17:23 2022/3/3
     **/
    @Override
    public <T> List<T> selectTrafficStatistic(String callerUserId, String dptIds, String channelType, List<String> dates, Class<T> T) {
        int size = CallRecordManageController.size;
        int page = (dates.size() + size - 1) / size;
        List<String> subDate = new ArrayList<>();
        if (T == RecordDayStatisticsVO.class) {
            List<RecordDayStatisticsVO> recordDayStatisticsVOList = new ArrayList<>();
            //进行分批次日期查询[消除因为随着查询日期增大响应时间过长]
            for (int i = 0; i < page; i++) {
                subDate.clear();
                int fromIndex = i * size;
                int toIndex = (i + 1) * size;
                subDate.addAll(dates.subList(fromIndex, Math.min(toIndex, dates.size())));
                //查询数据或获取根据日期分组统计的话务信息
                List<RecordDayStatisticsVO> statisticGroupDate = this.selectTrafficStatisticGroupDate(callerUserId, dptIds, subDate);
                recordDayStatisticsVOList.addAll(statisticGroupDate);
            }
            return (List<T>) recordDayStatisticsVOList;
        }
        if (T == RecordEeStatisticsVO.class) {
            List<RecordEeStatisticsVO> recordEeStatisticsVOList = new ArrayList<>();
            //进行分批次日期查询[消除因为随着查询日期增大响应时间过长]
            for (int i = 0; i < page; i++) {
                subDate.clear();
                int fromIndex = i * size;
                int toIndex = (i + 1) * size;
                subDate.addAll(dates.subList(fromIndex, Math.min(toIndex, dates.size())));
                //查询数据或获取根据员工分组统计的话务信息
                List<RecordEeStatisticsVO> statisticGroupEe = this.selectTrafficStatisticGroupEe(callerUserId, dptIds, channelType, subDate);
                recordEeStatisticsVOList.addAll(statisticGroupEe);
            }
            return (List<T>) recordEeStatisticsVOList;
        }
        throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法的话务统计类型");
    }

    /**
     * 补偿员工话务统计分校信息
     *
     * @param trafficStatistic 员工维度话务统计列表
     * <AUTHOR>
     * @Date 13:43 2022/3/4
     **/
    @Override
    public void replenishEeSchoolInfo(List<RecordEeStatisticsVO> trafficStatistic) {
        if (CollectionUtils.isEmpty(trafficStatistic)) {
            return;
        }
        List<String> schoolIds = trafficStatistic.stream().map(RecordEeStatisticsVO::getSchoolId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<EeSchoolInfoDTO> schoolInfoList = bdEeService.findSchoolNameByIds(schoolIds);
        if (CollectionUtils.isEmpty(schoolInfoList)) {
            return;
        }
        Map<String, String> schoolMap = schoolInfoList.stream().collect(Collectors.toMap(EeSchoolInfoDTO::getSchoolId, EeSchoolInfoDTO::getSchoolName));
        for (RecordEeStatisticsVO eeStatisticsVO : trafficStatistic) {
            String schoolId = eeStatisticsVO.getSchoolId();
            if (StringUtils.isEmpty(schoolId)) {
                eeStatisticsVO.setSchoolId("");
                eeStatisticsVO.setSchoolName("");
            } else {
                if (schoolMap.containsKey(schoolId)) {
                    eeStatisticsVO.setSchoolName(schoolMap.get(schoolId));
                }
            }
        }
    }

    /**
     * 合并员工维度的话务统计
     *
     * @param eeStatisticsVOS 分段(多个日期段统计的)的话务统计列表
     * @return 员工维度的话务统计
     * <AUTHOR>
     * @Date 14:11 2022/3/4
     **/
    @Override
    public List<RecordEeStatisticsVO> mergeTrafficStatisticGroupEe(List<RecordEeStatisticsVO> eeStatisticsVOS) {
        List<RecordEeStatisticsVO> trafficStatisticList = new ArrayList<>();
        // 进行员工分组
        Map<String, List<RecordEeStatisticsVO>> dataMap = eeStatisticsVOS.stream().collect(Collectors.groupingBy(t -> t.getCallAccount() + "_" + t.getCallerUserId()));
        for (Map.Entry<String, List<RecordEeStatisticsVO>> entry : dataMap.entrySet()) {
            List<RecordEeStatisticsVO> eeStatisticsVOList = entry.getValue();
            RecordEeStatisticsVO eeStatisticsVO = eeStatisticsVOList.get(0);
            //如果查询时间段内该员工只有一条分组过的统计数据不需要再进行合并统计
            if (eeStatisticsVOList.size() == 1) {
                trafficStatisticList.add(eeStatisticsVO);
            } else {
                // 话务统计
                RecordEeStatisticsVO sumTrafficStatisticInfo = this.sumTrafficStatisticGroupEe(eeStatisticsVOList);
                sumTrafficStatisticInfo.addEeBaeData(eeStatisticsVO);
                trafficStatisticList.add(sumTrafficStatisticInfo);
            }
        }

        // 数据解密
        for (RecordEeStatisticsVO recordEeStatisticsVO : trafficStatisticList) {
            decryptFields(recordEeStatisticsVO);
        }

        return trafficStatisticList;
    }

    /**
     * 将不同时间的员工维度话务统计的数据sum到一起
     *
     * @return com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO
     * @paramter eeStatisticsVOList
     * <AUTHOR>
     * @Date 14:57 2022/3/4
     **/
    private RecordEeStatisticsVO sumTrafficStatisticGroupEe(List<RecordEeStatisticsVO> eeStatisticsVOList) {
        RecordEeStatisticsVO recordEeStatisticsVO = new RecordEeStatisticsVO();
        Integer callinNum = eeStatisticsVOList.stream().mapToInt(RecordEeStatisticsVO::getCallinNum).sum();
        Integer inConnectNum = eeStatisticsVOList.stream().mapToInt(RecordEeStatisticsVO::getInConnectNum).sum();
        Integer calloutNum = eeStatisticsVOList.stream().mapToInt(RecordEeStatisticsVO::getCalloutNum).sum();
        Integer outSuccessNum = eeStatisticsVOList.stream().mapToInt(RecordEeStatisticsVO::getOutSuccessNum).sum();
        Integer outConnectNum = eeStatisticsVOList.stream().mapToInt(RecordEeStatisticsVO::getOutConnectNum).sum();
        Integer validCallNum = eeStatisticsVOList.stream().mapToInt(RecordEeStatisticsVO::getValidCallNum).sum();
        BigInteger durationTotal = eeStatisticsVOList.stream().map(t -> new BigInteger(t.getDurationTotal().toString())).reduce(BigInteger.ZERO, BigInteger::add);
        recordEeStatisticsVO.setCallinNum(callinNum);
        recordEeStatisticsVO.setInConnectNum(inConnectNum);
        recordEeStatisticsVO.setCalloutNum(calloutNum);
        recordEeStatisticsVO.setOutConnectNum(outConnectNum);
        recordEeStatisticsVO.setValidCallNum(validCallNum);
        recordEeStatisticsVO.setDurationTotal(durationTotal.intValue());
        recordEeStatisticsVO.setOutSuccessNum(outSuccessNum);
        //补充缺少的话务统计信息
        this.replenishStatisticalInfo(recordEeStatisticsVO);
        return recordEeStatisticsVO;
    }

    /**
     * 查询MySQL以员工[Ee]进行聚合的话务统计
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO>
     * @paramter callerUserId 员工的用户标识
     * @paramter dptIds 部门id列表
     * @paramter channelType 外呼通道
     * @paramter dates 话务统计的时间列表,如:["2021-12-10","2021-12-11","2021-12-12"]
     * <AUTHOR>
     * @Date 11:55 2022/3/4
     **/
    private List<RecordEeStatisticsVO> selectTrafficStatisticGroupEe(String callerUserId, String dptIds, String channelType, List<String> dates) {
        //日期校验
        if (CollectionUtils.isEmpty(dates)) {
            return new ArrayList<>();
        }
        //查询MySQL以员工[Ee]进行聚合的话务统计
        List<RecordEeStatisticsVO> trafficStatisticList = ccCountdayService.getTrafficStatisticGroupEeSql(callerUserId, dptIds, channelType, dates);
        for (RecordEeStatisticsVO trafficStatistic : trafficStatisticList) {
            //预设置员工部门和分校名称为空字符串
            trafficStatistic.setSchoolName("");

            // 数据解密
            decryptFields(trafficStatistic);

            //补充缺少的统计信息
            this.replenishStatisticalInfo(trafficStatistic);
        }
        return trafficStatisticList;
    }

    /**
     * 根据传递的参数查询ES
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.EeTrafficStatisticVO>
     * @paramter channelType 外呼类型
     * @paramter createdTimeStart 外呼时间-起始时间
     * @paramter createdTimeEnd 外呼时间-结束时间
     * <AUTHOR>
     * @Date 9:26 2022/3/21
     **/
    private List<EeTrafficStatisticVO> getCountData(String channelType, String createdTimeStart,
                                                    String createdTimeEnd) {
        // 要查询的字段
        List<String> sourceSearch = this.getFindFiledNameList();
        // 索引名称列表
        List<String> indices = RecordUtil.getIndiceList(createdTimeStart, createdTimeEnd);
        // 查询条件
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("callTime").gte(createdTimeStart).lte(createdTimeEnd);
        // 不统计通话时长小于0的,因为-1表示通话记录的通话数据还没有获取到.统计了会更加不准确
        RangeQueryBuilder durationRangeQuery = QueryBuilders.rangeQuery("duration").gt(-1);
        List<TermQueryBuilder> termQueryBuilders = this.checkChannelType(channelType);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(rangeQuery);
        boolQueryBuilder.must(durationRangeQuery);
        termQueryBuilders.forEach(boolQueryBuilder::must);
        // 排序
        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("callTime").order(SortOrder.ASC);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indices.toArray(new String[0]))
                .withSort(sortBuilder)
                .withPageable(PageRequest.of(0, 3500))
                .withFilter(boolQueryBuilder)
                .withTypes("_doc")
                .withSourceFilter(new FetchSourceFilter(sourceSearch.toArray(new String[0]), null))
                .build();
        // 查询ES
        List<CallRecord> callRecordList = callRecordService.scrollSelectList(searchQuery);
        if (callRecordList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> allData = new ArrayList<>(callRecordList.size());
        callRecordList.forEach(callRecord -> allData.add(MapUtils.bean2Map(callRecord)));
        // 进行话务汇总统计
        return this.getEeTrafficStatisticList(allData);
    }

    /**
     * 通过从ES内查询出的通话记录统计出员工的话务数据汇总
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.EeTrafficStatisticVO>
     * @paramter allData ES内查询出的员工通话记录列表
     * <AUTHOR>
     * @Date 9:52 2021/12/7
     **/
    private List<EeTrafficStatisticVO> getEeTrafficStatisticList(List<Map<String, Object>> allData) {
        // 获取一个以(外呼账号+外呼用户Id)进行分组的Map
        Map<String, List<Map<String, Object>>> dataMap = allData.stream()
                .collect(Collectors.groupingBy(map -> map.get("callAccount").toString() + "_" + map.get("callerUserId")));
        List<EeTrafficStatisticVO> eeTrafficStatisticVos = new ArrayList<>(dataMap.size());
        for (Map.Entry<String, List<Map<String, Object>>> entry : dataMap.entrySet()) {
            EeTrafficStatisticVO eeTrafficStatisticVo = new EeTrafficStatisticVO();
            // 获取某个员工的通话记录列表
            List<Map<String, Object>> value = entry.getValue();
            // 添加员工基础信息
            eeTrafficStatisticVo.addEeBaseData(value.get(0));
            // 对员工进行数据统计
            eeTrafficStatisticVo.trafficStatistic(value);

            eeTrafficStatisticVos.add(eeTrafficStatisticVo);
        }
        return eeTrafficStatisticVos;
    }

    /**
     * 获取话务实时统计 ES要查询的字段名称列表
     *
     * @return java.util.List<java.lang.String>
     * @paramter
     * <AUTHOR>
     * @Date 18:39 2021/12/6
     **/
    private List<String> getRealTimeFindFiledNameList() {
        List<String> sourceSearch = new ArrayList<>();
        sourceSearch.add("isValidCall");
        sourceSearch.add("isConnected");
        sourceSearch.add("callType");
        sourceSearch.add("duration");
        sourceSearch.add("isConnectSuccess");
        sourceSearch.add("schoolId");
        sourceSearch.add("createdTime");
        return sourceSearch;
    }

    /**
     * 获取日/月话务统计 ES要查询的字段名称列表
     *
     * @return java.util.List<java.lang.String>
     * @paramter
     * <AUTHOR>
     * @Date 18:39 2021/12/6
     **/
    private List<String> getFindFiledNameList() {
        List<String> sourceSearch = new ArrayList<>();
        sourceSearch.add("callerName");
        sourceSearch.add("callAccount");
        sourceSearch.add("callerUserId");
        sourceSearch.add("dptId");
        sourceSearch.add("dptName");
        sourceSearch.add("schoolId");
        sourceSearch.add("schoolName");
        sourceSearch.add("channelType");
        sourceSearch.add("isValidCall");
        sourceSearch.add("isConnected");
        sourceSearch.add("callType");
        sourceSearch.add("duration");
        sourceSearch.add("isConnectSuccess");
        sourceSearch.add("createdTime");
        return sourceSearch;
    }

    /**
     * 判断外呼类型，并返回相应的参数
     */
    private List<TermQueryBuilder> checkChannelType(String channelType) {
        CallChannelEnum callChannel = CallChannelEnum.getCallChannel(channelType);
        List<TermQueryBuilder> termQueryBuilders = new ArrayList<>();
        if (null == callChannel) {
            return termQueryBuilders;
        }
        // 1: 呼叫中心-固话 2: 工作手机
        switch (callChannel) {
            case CALL_TYPE_TQ_MOBILE: {
                channelType = CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType();
                TermQueryBuilder termQuery = QueryBuilders.termQuery("field3", 2);
                termQueryBuilders.add(termQuery);
                break;
            }
            case CALL_TYPE_TQ_FIX: {
                channelType = CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType();
                TermQueryBuilder termQuery = QueryBuilders.termQuery("field3", 1);
                termQueryBuilders.add(termQuery);
                break;
            }
            default:
                break;
        }
        TermQueryBuilder termQuery = QueryBuilders.termQuery("channelType", channelType);
        termQueryBuilders.add(termQuery);
        return termQueryBuilders;
    }

    /**
     * 查询以日期[天]进行聚合的话务统计
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.RecordDayStatisticsVO>
     * @paramter callerUserId 员工的用户标识
     * @paramter dptIds 部门id列表
     * @paramter dates 话务统计的时间列表,如:["2021-12-10","2021-12-11","2021-12-12"]
     * <AUTHOR>
     * @Date 11:26 2022/3/4
     **/
    private List<RecordDayStatisticsVO> selectTrafficStatisticGroupDate(String callerUserId, String dptIds, List<String> dates) {
        //日期校验
        if (CollectionUtils.isEmpty(dates)) {
            return new ArrayList<>();
        }
        List<RecordDayStatisticsVO> resultList = new ArrayList<>();
        //查询MySQL以日期[天]进行聚合的话务统计
        List<RecordDayStatisticsData> trafficStatisticList = ccCountdayService.getTrafficStatisticGroupDateSql(callerUserId, dptIds, dates);
        //存储有数据的日期,用来后面判断补充缺少的日期
        Set<String> useDate = new HashSet<>();
        for (RecordDayStatisticsData recordDayStatisticsData : trafficStatisticList) {
            //补充缺少的统计信息
            Map<String, Double> map = this.replenishStatisticalInfo(recordDayStatisticsData);
            recordDayStatisticsData.setOutConnectSuccess(map.get("outConnectSuccess"));
            recordDayStatisticsData.setInConnectSuccess(map.get("inConnectSuccess"));
            //添加到返回值
            String date = recordDayStatisticsData.getDate();
            RecordDayStatisticsVO recordDayStatisticsVO = new RecordDayStatisticsVO(date, recordDayStatisticsData);
            resultList.add(recordDayStatisticsVO);
            useDate.add(date);
        }
        //补充缺少的日期
        for (String date : dates) {
            if (!useDate.contains(date)) {
                RecordDayStatisticsVO recordDayStatisticsVO = new RecordDayStatisticsVO(date, new RecordDayStatisticsData());
                resultList.add(recordDayStatisticsVO);
            }
        }
        //以日期升序排列
        resultList.sort(Comparator.comparing(RecordDayStatisticsVO::getDate));
        return resultList;
    }

    /**
     * 补充缺少的话务统计信息
     *
     * @return java.util.Map<java.lang.String, java.lang.Double>
     * @paramter baseVO
     * <AUTHOR>
     * @Date 12:17 2021/12/11
     **/
    private Map<String, Double> replenishStatisticalInfo(RecordStatisticsBaseDTO baseVO) {
        Map<String, Object> trafficStatisticInfo = JSONUtils.toMap(JSONUtils.toJSONString(baseVO));
        String outConnectNum = trafficStatisticInfo.get("outConnectNum").toString();
        String calloutNum = trafficStatisticInfo.get("calloutNum").toString();
        String inConnectNum = trafficStatisticInfo.get("inConnectNum").toString();
        String callInNum = trafficStatisticInfo.get("callinNum").toString();
        String validCallNum = trafficStatisticInfo.get("validCallNum").toString();
        String outSuccessNum = trafficStatisticInfo.get("outSuccessNum").toString();
        String durationTotal = trafficStatisticInfo.get("durationTotal").toString();
        int callNum = Integer.parseInt(calloutNum) + Integer.parseInt(callInNum);
        int callConnectNum = Integer.parseInt(outConnectNum) + Integer.parseInt(inConnectNum);
        BigDecimal outConnectSuccess = new BigDecimal("0");
        BigDecimal inConnectSuccess = new BigDecimal("0");
        BigDecimal validCall = new BigDecimal("0");
        BigDecimal connectSuccess = new BigDecimal("0");
        BigDecimal calloutSuccess = new BigDecimal("0");
        BigDecimal durationAvg = new BigDecimal("0");
        if (!"0".equals(calloutNum)) {
            //呼出接通率
            outConnectSuccess = new BigDecimal(outConnectNum).divide(new BigDecimal(calloutNum), 2, RoundingMode.HALF_DOWN);
            //呼出成功率[接通率]
            calloutSuccess = new BigDecimal(outSuccessNum).divide(new BigDecimal(calloutNum), 2, RoundingMode.HALF_DOWN);
        }
        if (!"0".equals(callInNum)) {
            //呼入接通率
            inConnectSuccess = new BigDecimal(inConnectNum).divide(new BigDecimal(callInNum), 2, RoundingMode.HALF_DOWN);
        }
        if (0 < callNum) {
            //总有效沟通率
            validCall = new BigDecimal(validCallNum).divide(new BigDecimal(callNum), 2, RoundingMode.HALF_DOWN);
            //总接通率
            connectSuccess = new BigDecimal(callConnectNum).divide(new BigDecimal(callNum), 2, RoundingMode.HALF_DOWN);
        }
        if (0 < callConnectNum) {
            //总平均通话时长
            durationAvg = new BigDecimal(durationTotal).divide(new BigDecimal(callConnectNum), 2, RoundingMode.HALF_DOWN);
        }
        baseVO.setValidCall(validCall.doubleValue());
        baseVO.setConnectSuccess(connectSuccess.doubleValue());
        baseVO.setCalloutSuccess(calloutSuccess.doubleValue());
        baseVO.setDurationAvg(durationAvg.doubleValue());
        Map<String, Double> statisticInfo = new HashMap<>();
        statisticInfo.put("outConnectSuccess", outConnectSuccess.doubleValue());
        statisticInfo.put("inConnectSuccess", inConnectSuccess.doubleValue());
        return statisticInfo;
    }

    // ******************************************在ES聚合进行话务统计***********************************************

    /**
     * 根据条件查询当天的话务统计聚合
     *
     * @param request 请求参数
     * @return com.niceloo.cmc.ex.pojo.vo.TrafficStatisticVO
     * <AUTHOR>
     * @Date 17:27 2022/10/8
     **/
    @Override
    public TrafficStatisticVO instantStatistics(AuthBaseRequest request) {
        String indexName = RecordUtil.getCurrentMonthRecordIndexName();
        // 请求参数
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        this.getTrafficStatisticParam(boolQuery, request);
        // 呼叫时间
        String startTime = DateUtils.toStr(new Date(), DateUtil.YMD) + " 00:00:00";
        boolQuery.filter(QueryBuilders.rangeQuery("callTime").gte(startTime));
        NativeSearchQueryBuilder nativeSearch = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withQuery(boolQuery)
                .withTypes("_doc")
                .withPageable(EmptyPageable.empty())
                .addAggregation(callOutAggParam())
                .addAggregation(callInAggParam())
                .addAggregation(durationCountAggParam());
        AggregatedPage<CallRecord> callRecords = elasticsearchTemplate.queryForPage(nativeSearch.build(), CallRecord.class);
        Aggregations aggregations = callRecords.getAggregations();
        // 聚合集合转换为VO
        TrafficStatisticVO trafficStatisticVO = this.convertAggregationsToTrafficStatisticVO(aggregations);
        // 添加需要计算的属性值
        this.calculatePropertyValues(trafficStatisticVO);
        return trafficStatisticVO;
    }

    /**
     * 根据条件聚合查询ES获取话务统计
     * <br/>
     * <b>分页其实使用的是<a href="https://www.elastic.co/guide/en/elasticsearch/reference/6.8/search-aggregations-pipeline-bucket-sort-aggregation.html">bucket_sort</a>进行截断的,如果数据量很大会有占用较多的ElasticSearch服务器内存和响应时间变慢。不过现在来看根据员工进行分组数据量暂时不会太大
     *
     * @param request 请求参数
     * @return com.niceloo.cmc.ex.pojo.vo.BasePageVO<com.niceloo.cmc.ex.pojo.vo.TrafficStatisticVO>
     * <AUTHOR>
     * @Date 15:45 2022/10/9
     * @see <p>"This aggregation creates too many buckets (10004) and will throw an error in future versions.
     * You should update the [search.max_buckets] cluster setting or use the [<a href = "https://www.elastic.co/guide/en/elasticsearch/reference/6.8/search-aggregations-bucket-composite-aggregation.html#search-aggregations-bucket-composite-aggregation">composite</a>] aggregation to paginate all buckets in multiple requests."</p>
     **/
    @Override
    public BasePageVO<TrafficStatisticVO> trafficAnalysis(TrafficTopRequest request) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        // 拼接参数和获取根据条件获取所有名称列表
        String[] indexName = this.getIndexNameAndJoinParam(request, boolQuery);
        NativeSearchQueryBuilder nativeSearch = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withQuery(boolQuery)
                .withTypes("_doc")
                .withPageable(EmptyPageable.empty())
                .addAggregation(this.totalNumAgg())
                .addAggregation(trafficTopAggParam(request.getPageIndex(), request.getPageSize()));
        AggregatedPage<CallRecord> callRecords = elasticsearchTemplate.queryForPage(nativeSearch.build(), CallRecord.class);
        Aggregations aggregations = callRecords.getAggregations();
        ParsedStringTerms callerUserIdGroupAgg = aggregations.get("callerUserIdGroup");
        // 获取分页查询的buckets
        List<? extends Terms.Bucket> buckets = callerUserIdGroupAgg.getBuckets();
        List<TrafficStatisticVO> trafficStatisticVOList = new ArrayList<>(buckets.size());
        for (Terms.Bucket bucket : buckets) {
            String userId = bucket.getKeyAsString();
            // 聚合集合转换为VO
            TrafficStatisticVO trafficStatisticVO = this.convertAggregationsToTrafficStatisticVO(bucket.getAggregations());
            // 添加需要计算的属性值
            this.calculatePropertyValues(trafficStatisticVO);
            trafficStatisticVO.setUserId(userId);
            trafficStatisticVOList.add(trafficStatisticVO);
        }
        // 添加员工组织架构
        this.trafficStatisticAddOrganization(trafficStatisticVOList);
        // 总条数
        ParsedCardinality parsedCardinality = aggregations.get("totalNum");
        int count = (int) parsedCardinality.getValue();
        return new BasePageVO<>(count, trafficStatisticVOList);
    }

    /**
     * 根据条件聚合查询ES获取有效通话数据
     * <br/>
     * <b>分页其实使用的是<a href="https://www.elastic.co/guide/en/elasticsearch/reference/6.8/search-aggregations-pipeline-bucket-sort-aggregation.html">bucket_sort</a>进行截断的,如果数据量很大会有占用较多的ElasticSearch服务器内存和响应时间变慢。不过现在来看根据员工进行分组数据量暂时不会太大
     *
     * @param request 请求参数
     * @return com.niceloo.cmc.ex.pojo.vo.BasePageVO<com.niceloo.cmc.ex.pojo.vo.ValidCallAnalysisVO>
     * <AUTHOR>
     * @Date 11:32 2022/10/10
     * @see <p>"This aggregation creates too many buckets (10004) and will throw an error in future versions.
     * You should update the [search.max_buckets] cluster setting or use the [<a href = "https://www.elastic.co/guide/en/elasticsearch/reference/6.8/search-aggregations-bucket-composite-aggregation.html#search-aggregations-bucket-composite-aggregation">composite</a>] aggregation to paginate all buckets in multiple requests."</p>
     **/
    @Override
    public BasePageVO<ValidCallAnalysisVO> validTrafficAnalysis(TrafficAnalysisRequest request) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        // 拼接参数和获取根据条件获取所有名称列表
        String[] indexName = this.getIndexNameAndJoinParam(request, boolQuery);
        NativeSearchQueryBuilder nativeSearch = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withQuery(boolQuery)
                .withTypes("_doc")
                .withPageable(EmptyPageable.empty())
                .addAggregation(this.totalNumAgg()) // 获取总条数
                .addAggregation(validTrafficAggParam(request.getPageIndex(), request.getPageSize()));
        AggregatedPage<CallRecord> callRecords = elasticsearchTemplate.queryForPage(nativeSearch.build(), CallRecord.class);
        Aggregations aggregations = callRecords.getAggregations();
        ParsedStringTerms callerUserIdGroupAgg = aggregations.get("callerUserIdGroup");
        // 获取分页查询的buckets
        List<? extends Terms.Bucket> buckets = callerUserIdGroupAgg.getBuckets();
        List<ValidCallAnalysisVO> validCallAnalysisVOList = new ArrayList<>(buckets.size());
        for (Terms.Bucket bucket : buckets) {
            String userId = bucket.getKeyAsString();
            ParsedFilters parsedFilters = bucket.getAggregations().get("durationCount");
            ValidCallAnalysisVO validCallAnalysisVO = this.convertToValidCallAnalysisVO(parsedFilters);
            validCallAnalysisVO.setUserId(userId);
            validCallAnalysisVOList.add(validCallAnalysisVO);
        }
        // 添加员工组织架构
        this.trafficStatisticAddOrganization(validCallAnalysisVOList);
        ParsedCardinality parsedCardinality = aggregations.get("totalNum");
        int count = (int) parsedCardinality.getValue();
        return new BasePageVO<>(count, validCallAnalysisVOList);
    }

    /**
     * 根据条件聚合查询ES获取话务统计并存储在EXCEL内
     *
     * @param request 请求参数
     * @param userId  请求人userId
     * @return java.lang.String   excel下载Id
     * <AUTHOR>
     * @Date 15:45 2022/10/10
     **/
    @Override
    public String asyncTrafficAnalysisExcel(TrafficTopRequest request, String userId) {
        String excelId = RecordUtil.getUUID();
        // 1、重复请求下载校验
        String key = RedisConst.TRAFFIC_STATISTIC_APPLY_EXCEL_KEY + TRAFFIC_TOP_EXCEL + ":" + userId;
        Boolean flag = stringRedisTemplate.opsForValue().setIfAbsent(key, excelId, Duration.ofSeconds(30));
        if (Boolean.FALSE.equals(flag)) {
            return stringRedisTemplate.opsForValue().get(key);
        }
        GENERATE_EXCEL_EXECUTOR.execute(() -> {
            // 2、查询出满足条件的所有员工统计信息
            request.setPageSize(aggEeMaxNum);
            BasePageVO<TrafficStatisticVO> trafficAnalysis = this.trafficAnalysis(request);
            List<TrafficStatisticVO> statisticVOList = trafficAnalysis.getData();
            // 3、统计信息转换为ExcelDTO
            List<TrafficTopExcelDTO> trafficTopExcelDTOS = TrafficTopExcelDTO.trafficAnalysisExcelConverter(statisticVOList);
            // 4、将数据写入Excel
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            String sheetName = "实时话务统计";
            EasyExcelFactory.write(outputStream, TrafficTopExcelDTO.class)
                    .inMemory(Boolean.TRUE) //添加批注必须设置为true
                    .registerWriteHandler(new CommentWriteHandler())
                    .sheet(sheetName)
                    .doWrite(trafficTopExcelDTOS);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 5、上传到阿里云
            try {
                String fileName = "通话记录实时统计(" + DateUtils.getNowDString(DateUtil.YMD) + ").xls";
                FileUploadResponse response = fileUtil.uploadExcelFile(inputStream, fileName);
                //6、将结果放到Redis内
                Map<String, String> map = Map.of("type", TRAFFIC_TOP_EXCEL, "data", JSONUtils.toJSONString(response));
                stringRedisTemplate.opsForValue().set(RedisConst.TRAFFIC_STATISTIC_GENERATE_EXCEL_KEY + excelId, JSONUtils.toJSONString(map), Duration.ofDays(1));
            } catch (Exception e) {
                logger.error(e, "话务统计Excel上传到阿里云出现异常,异常信息；{}", e.getMessage());
                // 7、将结果放到Redis内
                Map<String, String> map = Map.of("type", TRAFFIC_TOP_EXCEL, "error", "Excel下载失败,请尝试重新下载");
                stringRedisTemplate.opsForValue().set(RedisConst.TRAFFIC_STATISTIC_GENERATE_EXCEL_KEY + excelId, JSONUtils.toJSONString(map), Duration.ofDays(1));
            }
        });
        return excelId;
    }

    /**
     * 根据条件聚合查询ES获取话务统计并存储在EXCEL内
     *
     * @param request 请求参数
     * @param userId  请求人userId
     * @return java.lang.String
     * <AUTHOR>
     * @Date 17:20 2022/10/10
     **/
    @Override
    public String trafficAnalysisExcel(TrafficAnalysisRequest request, String userId) {
        String excelId = RecordUtil.getUUID();
        // 1、重复请求下载校验
        String key = RedisConst.TRAFFIC_STATISTIC_APPLY_EXCEL_KEY + TRAFFIC_ANALYSIS_EXCEL + ":" + userId;
        Boolean flag = stringRedisTemplate.opsForValue().setIfAbsent(key, excelId, Duration.ofSeconds(30));
        if (Boolean.FALSE.equals(flag)) {
            return stringRedisTemplate.opsForValue().get(key);
        }
        // 2、创建子线程生成Excel并上传到阿里云服务器
        GENERATE_EXCEL_EXECUTOR.execute(() -> {
            // 3、查询出满足条件的所有员工统计信息
            request.setPageSize(aggEeMaxNum);
            // 3.1、通话分析
            BasePageVO<TrafficStatisticVO> trafficAnalysis = this.trafficAnalysis(request);
            // 3.2、有效通话分析
            BasePageVO<ValidCallAnalysisVO> validTrafficAnalysis = this.validTrafficAnalysis(request);
            List<TrafficStatisticVO> statisticVOList = trafficAnalysis.getData();
            List<ValidCallAnalysisVO> validCallAnalysisVOList = validTrafficAnalysis.getData();
            String startDate = DateUtil.format(request.getStartTime(), DateUtil.YMD);
            String endDate = DateUtil.format(request.getEndTime(), DateUtil.YMD);
            String date = startDate + "至" + endDate;
            // 3.3、通话分析数据转为ExcelDTO
            List<CallAnalysisExcelDTO> callAnalysisExcelDTOList = CallAnalysisExcelDTO.trafficStatisticVOConverter(statisticVOList, date);
            // 3.4、有效通话分析数据转为ExcelDTO
            List<ValidCallAnalysisExcelDTO> validCallAnalysisExcelDTOList = ValidCallAnalysisExcelDTO.trafficStatisticVOConverter(validCallAnalysisVOList, date);
            // 4、将数据写入Excel
            ByteArrayOutputStream outputStream = this.trafficAnalysisExcelWrite(callAnalysisExcelDTOList, validCallAnalysisExcelDTOList);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 5、上传到阿里云服务器
            try {
                String fileName = "话务分析(" + date + ").xls";
                FileUploadResponse response = fileUtil.uploadExcelFile(inputStream, fileName);
                Map<String, String> map = Map.of("type", TRAFFIC_ANALYSIS_EXCEL, "data", JSONUtils.toJSONString(response));
                // 6、将结果放到Redis内
                stringRedisTemplate.opsForValue().set(RedisConst.TRAFFIC_STATISTIC_GENERATE_EXCEL_KEY + excelId, JSONUtils.toJSONString(map), Duration.ofDays(1));
            } catch (Exception e) {
                logger.error(e, "话务统计Excel上传到阿里云出现异常,异常信息；{}", e.getMessage());
                // 7、将结果放到Redis内
                Map<String, String> map = Map.of("type", TRAFFIC_ANALYSIS_EXCEL, "error", "Excel下载失败,请尝试重新下载");
                stringRedisTemplate.opsForValue().set(RedisConst.TRAFFIC_STATISTIC_GENERATE_EXCEL_KEY + excelId, JSONUtils.toJSONString(map), Duration.ofDays(1));
            }
        });
        return excelId;
    }

    /**
     * 获取EXCEL下载到阿里云的地址和fileKey
     *
     * @param excelId excel下载Id
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Date 10:55 2022/10/11
     **/
    @Override
    public Map<String, Object> trafficStatisticsExcel(String excelId) {
        String response = stringRedisTemplate.opsForValue().get(RedisConst.TRAFFIC_STATISTIC_GENERATE_EXCEL_KEY + excelId);
        if (StringUtils.isEmpty(response)) {
            return Collections.emptyMap();
        }
        return JSONUtils.toMap(response);
    }

    /**
     * 生成云外呼通道账号余额
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSchoolBalance() {
        // 从配置文件获取要查询余额的通道
        String[] channelTypes = CallProperties.ZhzxProperty.BALANCE_CHANNEL_TYPES.split(",");
        // 根据当前时间生成******** 形式的统计日信息
        String countDay = DateUtils.toStr(new Date(), "yyyyMMdd");
        List<CcSchoolBalance> ccSchoolBalanceList = new ArrayList<>();

        // 分外呼通道获取账号余额，不同外呼通道获取账号余额的厂家接口不同
        for (String channelType : channelTypes) {
            // 获取有账号的分校及账号信息
            List<AccountDTO> accountDTOs = bdCallaccountinfoService.selectUniqueAccountsByType(channelType);
            for (AccountDTO accountDTO : accountDTOs) {
                // 检查账号信息是否为空
                if (ObjectUtils.isEmpty(accountDTO)) {
                    continue;
                }
                String accountId = accountDTO.getApiAccount();
                String apiSecret = accountDTO.getApiSecret();
                String resStr = null;
                String balanceValue = null;
                switch (Objects.requireNonNull(CallChannelEnum.getCallChannel(channelType))) {
                    case CALL_TYPE_ZHZX:
                        // 中弘智享账号余额查询接口
                        resStr = okHttpUtil.post(CallProperties.ZhzxProperty.HOST + CallProperties.ZhzxProperty.GET_BALANCE,
                                "111", Headers.of(zhzxService.getHeader(accountId, null, null)));
                        balanceValue = parseBalanceValue(resStr, "中弘智享");
                        break;
                    case CALL_TYPE_ZK:
                        // 亿迅账号余额查询接口
                        resStr = okHttpUtil.post(CallProperties.YXProperty.ZK_HOST + CallProperties.YXProperty.BALANCE,
                                yxService.getBalanceQueryRequestBody(accountId, apiSecret), Headers.of(YXService.HEADER));
                        balanceValue = parseBalanceValue(resStr, "亿迅");
                        break;
                    default:
                        // 不支持的外呼通道类型
                        break;
                }
                if (StringUtils.isNotEmpty(balanceValue) && StringUtils.isNotEmpty(accountDTO.getSchoolId())) {
                    // 将账号余额信息添加到余额列表中
                    CcSchoolBalance ccSchoolBalance = new CcSchoolBalance();
                    ccSchoolBalance.setSchoolId(accountDTO.getSchoolId());
                    ccSchoolBalance.setChannelType(channelType);
                    try {
                        ccSchoolBalance.setAccountBalance(Double.parseDouble(balanceValue));
                    } catch (NumberFormatException e) {
                        ccSchoolBalance.setAccountBalance((double) -9999);
                        ccSchoolBalance.setRemark(balanceValue);
                    }
                    ccSchoolBalance.setCountDay(countDay);
                    ccSchoolBalance.setCreator("system");
                    ccSchoolBalance.setCreatedDate(DateUtils.toStr(new Date()));
                    ccSchoolBalance.setModifier("system");
                    ccSchoolBalance.setModifiedDate(DateUtils.toStr(new Date()));
                    ccSchoolBalanceList.add(ccSchoolBalance);
                }
            }
        }

        batchSaveAfterDelete(countDay, ccSchoolBalanceList);
    }

    /**
     * 批量删除后保存余额信息。
     *
     * @param countDay 统计日期。
     * @param ccSchoolBalanceList 待保存的余额信息列表。
     */
    private void batchSaveAfterDelete(String countDay, List<CcSchoolBalance> ccSchoolBalanceList) {
        // 将余额信息进行入库操作
        // 先删除已存在的记录
        Map<String, Object> columnMap = new HashMap<>();
        for (CcSchoolBalance ccSchoolBalance : ccSchoolBalanceList) {
            String schoolId = ccSchoolBalance.getSchoolId();
            String channelType = ccSchoolBalance.getChannelType();

            if (StringUtils.isNotEmpty(schoolId) && StringUtils.isNotEmpty(channelType) && StringUtils.isNotEmpty(countDay)) {
                columnMap.put("schoolId", schoolId);
                columnMap.put("channelType", channelType);
                columnMap.put("countDay", countDay);

                // 执行删除操作
                ccSchoolBalanceMapper.deleteByMap(columnMap);
            }
        }

        // 在保证已存在数据删除的基础上，添加新的记录
        ccSchoolBalanceMapper.batchSave(ccSchoolBalanceList);
    }

    /**
     * 解析账号余额值
     *
     * @param resStr 接口返回的字符串
     * @param vendor 厂家名称
     * @return 账号余额值
     */
    private String parseBalanceValue(String resStr, String vendor) {
        if (StringUtils.isEmpty(resStr)) {
            throw new ApplicationException(ApiErrorCodes.execute_failed_handler, "查询" + vendor + "账号余额出现异常,返回结果:" +  resStr);
        }

        JSONObject jsonObject = JSON.parseObject(resStr);
        JSONObject dataObject = jsonObject.getJSONObject("data");
        if (dataObject == null) {
            LOGGER.error("查询 {} 账号余额出现异常，接口返回结果：{}", vendor, resStr);
            return "-9999";
        }

        String balance = dataObject.getString("balance");
        if (!StringUtils.isEmpty(balance)) {
            return balance;
        }

        JSONObject responseObject = dataObject.getJSONObject("response");
        if (responseObject == null) {
            LOGGER.error("查询 {} 账号余额出现异常，接口返回结果：{}", vendor, resStr);
            return "-9999";
        }

        JSONObject resultObject = responseObject.getJSONObject("result");
        if (resultObject == null) {
            LOGGER.error("查询 {} 账号余额出现异常，接口返回结果：{}", vendor, resStr);
            return "-9999";
        }

        String errorStr = resultObject.getString("error");
        if (Integer.parseInt(errorStr) != 0) {
            LOGGER.error("查询 {} 账号余额出现异常，接口返回结果：{}", vendor, resStr);
            JSONObject uplevelResultObject = jsonObject.getJSONObject("result");
            return uplevelResultObject.getString("msg");
        }

        JSONObject responseDataObject = responseObject.getJSONObject("data");
        if (responseDataObject == null) {
            LOGGER.error("查询 {} 账号余额出现异常，接口返回结果：{}", vendor, resStr);
            return "-9999";
        }

        balance = responseDataObject.getString("balance");
        if (!StringUtils.isEmpty(balance)) {
            return balance;
        }

        LOGGER.error("查询 {} 账号余额出现异常，接口返回结果：{}", vendor, resStr);
        return "-9999";
    }

    // 将数据写入Excel
    private ByteArrayOutputStream trafficAnalysisExcelWrite(List<CallAnalysisExcelDTO> callAnalysisExcelDTOArrayList, List<ValidCallAnalysisExcelDTO> validCallAnalysisExcelDTOList) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcelFactory
                .write(outputStream)
                .inMemory(true)
                .registerWriteHandler(new CommentWriteHandler())
                .build();
        WriteSheet writeSheet1 = EasyExcelFactory
                .writerSheet(0, "通话分析")
                .head(CallAnalysisExcelDTO.class)
                .build();
        WriteSheet writeSheet2 = EasyExcelFactory
                .writerSheet(1, "有效通话分析")
                .head(ValidCallAnalysisExcelDTO.class)
                .build();
        excelWriter.write(callAnalysisExcelDTOArrayList, writeSheet1);
        excelWriter.write(validCallAnalysisExcelDTOList, writeSheet2);
        excelWriter.finish();
        return outputStream;
    }

    /**
     * 有效话务分析聚合参数
     *
     * @param form 分页开始数
     * @param size 分页结束数
     * @return org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder
     * <AUTHOR>
     * @Date 11:52 2022/10/10
     **/
    private TermsAggregationBuilder validTrafficAggParam(Integer form, Integer size) {
        // 分组聚合
        TermsAggregationBuilder callerUserIdGroupAgg = new TermsAggregationBuilder("callerUserIdGroup", ValueType.STRING);
        callerUserIdGroupAgg.field("callerUserId").size(aggEeMaxNum).includeExclude(new IncludeExclude(null, ""));
        // 聚合排序(截断)
        FieldSortBuilder countSort = new FieldSortBuilder("_count").order(SortOrder.DESC);
        BucketSortPipelineAggregationBuilder sortAgg = new BucketSortPipelineAggregationBuilder("callNumSort", List.of(countSort)).from(form).size(size);
        // 分组后的子聚合
        FiltersAggregationBuilder durationCountAgg = new FiltersAggregationBuilder("durationCount", this.subValidTrafficAggParam("callInNumGe10", BizConst.CALL_IN, 10), this.subValidTrafficAggParam("callInNumGe20", BizConst.CALL_IN, 20), this.subValidTrafficAggParam("callInNumGe30", BizConst.CALL_IN, 30), this.subValidTrafficAggParam("callInNumGe60", BizConst.CALL_IN, 60), this.subValidTrafficAggParam("callOutNumGe10", BizConst.CALL_OUT, 10), this.subValidTrafficAggParam("callOutNumGe20", BizConst.CALL_OUT, 20), this.subValidTrafficAggParam("callOutNumGe30", BizConst.CALL_OUT, 30), this.subValidTrafficAggParam("callOutNumGe60", BizConst.CALL_OUT, 60));
        callerUserIdGroupAgg.subAggregation(durationCountAgg);
        callerUserIdGroupAgg.subAggregation(sortAgg);
        return callerUserIdGroupAgg;
    }

    /**
     * 获取索引名称和拼接查询参数
     *
     * @param request   接口参数
     * @param boolQuery 查询ES参数
     * @return java.lang.String[] 索引名称列表
     * <AUTHOR>
     * @Date 11:51 2022/10/10
     **/
    private String[] getIndexNameAndJoinParam(TrafficTopRequest request, BoolQueryBuilder boolQuery) {
        String[] indexName = {RecordUtil.getCurrentMonthRecordIndexName()};
        // 请求参数
        this.getTrafficStatisticParam(boolQuery, request);
        if (request instanceof TrafficAnalysisRequest) {
            TrafficAnalysisRequest trafficAnalysisRequest = (TrafficAnalysisRequest) request;
            boolQuery.filter(QueryBuilders.rangeQuery("callTime").gte(trafficAnalysisRequest.getStartTime()).lte(trafficAnalysisRequest.getEndTime()));
            indexName = RecordUtil.getIndiceList(trafficAnalysisRequest.getStartTime(), trafficAnalysisRequest.getEndTime()).toArray(new String[0]);
        } else {
            // 呼叫时间
            String startTime = DateUtils.toStr(new Date(), DateUtil.YMD) + " 00:00:00";
            boolQuery.filter(QueryBuilders.rangeQuery("callTime").gte(startTime));
        }
        return indexName;
    }

    /**
     * 为话务统计添加员工的组织架构
     *
     * @param trafficStatisticBdEeVOList userId不为空的话务统计
     * <AUTHOR>
     * @Date 10:40 2022/10/10
     **/
    private void trafficStatisticAddOrganization(List<? extends TrafficStatisticBdEeVO> trafficStatisticBdEeVOList) {
        List<String> userIds = trafficStatisticBdEeVOList.stream().map(TrafficStatisticBdEeVO::getUserId).collect(Collectors.toList());
        List<BdEeDptDTO> bdEeDptDTOList = bdEeService.findBdEeDptListByUserIds(userIds);
        Map<String, BdEeDptDTO> bdEeDptDTOMap = bdEeDptDTOList.stream().collect(Collectors.toMap(BdEeDptDTO::getUserId, Function.identity(), (m1, m2) -> m1));
        for (TrafficStatisticBdEeVO trafficStatisticVO : trafficStatisticBdEeVOList) {
            BdEeDptDTO bdEeDptDTO = bdEeDptDTOMap.get(trafficStatisticVO.getUserId());
            // 在员工表内没有找到将用户名称置为userId
            if (null != bdEeDptDTO) {
                trafficStatisticVO.fillValue(bdEeDptDTO);
            } else {
                trafficStatisticVO.setUserName(trafficStatisticVO.getUserId());
            }
        }
    }

    // 呼出数据聚合参数
    private FilterAggregationBuilder callOutAggParam() {
        TermQueryBuilder callOutAggQuery = QueryBuilders.termQuery("callType", 0);
        FilterAggregationBuilder callOutAgg = new FilterAggregationBuilder("callOut", callOutAggQuery);
        SumAggregationBuilder callOutDurationSum = new SumAggregationBuilder("callOutDurationSum").field("duration");
        callOutAgg.subAggregation(callOutDurationSum);
        return callOutAgg;
    }

    // 呼入数据聚合参数
    private FilterAggregationBuilder callInAggParam() {
        TermQueryBuilder callInAggQuery = QueryBuilders.termQuery("callType", 1);
        FilterAggregationBuilder callInAgg = new FilterAggregationBuilder("callIn", callInAggQuery);
        SumAggregationBuilder callInDurationSum = new SumAggregationBuilder("callInDurationSum").field("duration");
        callInAgg.subAggregation(callInDurationSum);
        return callInAgg;
    }

    // 接通量聚合参数
    private FiltersAggregationBuilder durationCountAggParam() {
        BoolQueryBuilder outConnectNumQuery = QueryBuilders.boolQuery();
        outConnectNumQuery.filter(QueryBuilders.termQuery("callType", 0));
        outConnectNumQuery.filter(QueryBuilders.rangeQuery("duration").gt(0));
        BoolQueryBuilder inConnectNumQuery = QueryBuilders.boolQuery();
        inConnectNumQuery.filter(QueryBuilders.termQuery("callType", 1));
        inConnectNumQuery.filter(QueryBuilders.rangeQuery("duration").gt(0));
        FiltersAggregator.KeyedFilter outConnectNumFilter = new FiltersAggregator.KeyedFilter("outConnectNumFilter", outConnectNumQuery);
        FiltersAggregator.KeyedFilter inConnectNumFilter = new FiltersAggregator.KeyedFilter("inConnectNumFilter", inConnectNumQuery);
        return new FiltersAggregationBuilder("durationCount", outConnectNumFilter, inConnectNumFilter);
    }

    // 呼入数据聚合参数
    private CardinalityAggregationBuilder totalNumAgg() {
        return new CardinalityAggregationBuilder("totalNum", ValueType.STRING).field("callerUserId");
    }

    // 呼入/呼出话务排名聚合参数
    private TermsAggregationBuilder trafficTopAggParam(Integer form, Integer size) {
        // 分组聚合
        TermsAggregationBuilder callerUserIdGroupAgg = new TermsAggregationBuilder("callerUserIdGroup", ValueType.STRING);
        callerUserIdGroupAgg.field("callerUserId").size(aggEeMaxNum).includeExclude(new IncludeExclude(null, ""));

        // 聚合排序(截断)
        FieldSortBuilder countSort = new FieldSortBuilder("_count").order(SortOrder.DESC);
        BucketSortPipelineAggregationBuilder sortAgg = new BucketSortPipelineAggregationBuilder("callNumSort", List.of(countSort)).from(form).size(size);

        // 分组后的子聚合
        callerUserIdGroupAgg.subAggregation(callOutAggParam());
        callerUserIdGroupAgg.subAggregation(callInAggParam());
        callerUserIdGroupAgg.subAggregation(durationCountAggParam());
        callerUserIdGroupAgg.subAggregation(sortAgg);
        return callerUserIdGroupAgg;
    }

    // 有效(通话时长)话务统计聚合参数
    private FiltersAggregator.KeyedFilter subValidTrafficAggParam(String aggName, Integer callType, Integer duration) {
        BoolQueryBuilder outConnectNumQuery = QueryBuilders.boolQuery();
        outConnectNumQuery.filter(QueryBuilders.termQuery("callType", callType));
        outConnectNumQuery.filter(QueryBuilders.rangeQuery("duration").gte(duration));
        return new FiltersAggregator.KeyedFilter(aggName, outConnectNumQuery);
    }

    /**
     * 获取公共的话务统计的参数
     *
     * @param boolQuery bool
     * @param request   请求接口参数
     * <AUTHOR>
     * @Date 15:02 2022/10/9
     **/
    private void getTrafficStatisticParam(BoolQueryBuilder boolQuery, AuthBaseRequest request) {
        if (StringUtils.isNotEmpty(request.getSchoolId())) {
            boolQuery.filter(QueryBuilders.termsQuery("schoolId", request.getSchoolId().split(",")));
        }
        if (StringUtils.isNotEmpty(request.getDptId())) {
            boolQuery.filter(QueryBuilders.termsQuery("dptId", request.getDptId().split(",")));
        }
        if (StringUtils.isNotEmpty(request.getEeUserId())) {
            boolQuery.filter(QueryBuilders.termQuery("callerUserId", request.getEeUserId()));
        }
        // 通话时长大于等于0
        boolQuery.filter(QueryBuilders.rangeQuery("duration").gte(0));
    }

    /**
     * 解析聚合返回值-> 通话记录数量count和通话时长sum
     *
     * @param parsedFilter 集合返回结果
     * @param subAggName   子聚合名称
     * @return com.niceloo.plugin.sdk.lang.Pair<java.lang.Integer, java.lang.Integer> key:count,value:sum
     * <AUTHOR>
     * @Date 14:55 2022/10/9
     **/
    private Pair<Integer, Integer> callNumAnalysis(ParsedFilter parsedFilter, String subAggName) {
        if (null == parsedFilter) {
            return null;
        }
        long docCount = parsedFilter.getDocCount();
        Aggregations subAggregations = parsedFilter.getAggregations();
        ParsedSum parsedSum = subAggregations.get(subAggName);
        double value = parsedSum.getValue();
        return new Pair<>((int) docCount, (int) value);
    }

    /**
     * 解析聚合返回值-> 呼出接通量和呼入接通量
     *
     * @param parsedFilters 集合返回结果
     * @return com.niceloo.plugin.sdk.lang.Pair<java.lang.Integer, java.lang.Integer> key:呼出接通量,value:呼入接通量
     * <AUTHOR>
     * @Date 14:57 2022/10/9
     **/
    private Pair<Integer, Integer> callConnectNumAnalysis(ParsedFilters parsedFilters) {
        if (null == parsedFilters) {
            return null;
        }
        ParsedFilters.ParsedBucket parsedBucket1 = parsedFilters.getBucketByKey("outConnectNumFilter");
        ParsedFilters.ParsedBucket parsedBucket2 = parsedFilters.getBucketByKey("inConnectNumFilter");
        long outConnectNum = parsedBucket1.getDocCount();
        long inConnectNum = parsedBucket2.getDocCount();
        return new Pair<>((int) outConnectNum, (int) inConnectNum);
    }

    /**
     * 将从ES查询出来的统计数据转换为VO
     *
     * @param aggregations 聚合集合
     * @return com.niceloo.cmc.ex.pojo.vo.TrafficStatisticVO
     * <AUTHOR>
     * @Date 17:23 2022/10/9
     **/
    private TrafficStatisticVO convertAggregationsToTrafficStatisticVO(Aggregations aggregations) {
        ParsedFilter callOutAggResult = aggregations.get("callOut");
        ParsedFilter callInAggResult = aggregations.get("callIn");
        ParsedFilters durationCountAggResult = aggregations.get("durationCount");
        Pair<Integer, Integer> callOutPair = this.callNumAnalysis(callOutAggResult, "callOutDurationSum");
        Pair<Integer, Integer> callInPair = this.callNumAnalysis(callInAggResult, "callInDurationSum");
        Pair<Integer, Integer> callConnectPair = this.callConnectNumAnalysis(durationCountAggResult);
        TrafficStatisticVO trafficStatisticVO = new TrafficStatisticVO();
        trafficStatisticVO.setCallOutNum(callOutPair.getKey());
        trafficStatisticVO.setCallOutConnectNum(callConnectPair.getKey());
        trafficStatisticVO.setCallOutDuration(callOutPair.getValue());
        trafficStatisticVO.setCallInNum(callInPair.getKey());
        trafficStatisticVO.setCallInConnectNum(callConnectPair.getValue());
        trafficStatisticVO.setCallInDuration(callInPair.getValue());
        return trafficStatisticVO;
    }

    /**
     * 将从ES查询出来的统计数据转换为VO
     *
     * @param parsedFilters 聚合集合
     * @return com.niceloo.cmc.ex.pojo.vo.TrafficStatisticVO
     * <AUTHOR>
     * @Date 17:23 2022/10/9
     **/
    private ValidCallAnalysisVO convertToValidCallAnalysisVO(ParsedFilters parsedFilters) {
        ValidCallAnalysisVO validCallAnalysisVO = new ValidCallAnalysisVO();
        List<? extends Filters.Bucket> subBuckets = parsedFilters.getBuckets();
        for (Filters.Bucket subBucket : subBuckets) {
            String key = subBucket.getKeyAsString();
            long docCount = subBucket.getDocCount();
            switch (key) {
                case "callOutNumGe10": {
                    validCallAnalysisVO.setCallOutNumGe10((int) docCount);
                    break;
                }
                case "callOutNumGe20": {
                    validCallAnalysisVO.setCallOutNumGe20((int) docCount);
                    break;
                }
                case "callOutNumGe30": {
                    validCallAnalysisVO.setCallOutNumGe30((int) docCount);
                    break;
                }
                case "callOutNumGe60": {
                    validCallAnalysisVO.setCallOutNumGe60((int) docCount);
                    break;
                }
                case "callInNumGe10": {
                    validCallAnalysisVO.setCallInNumGe10((int) docCount);
                    break;
                }
                case "callInNumGe20": {
                    validCallAnalysisVO.setCallInNumGe20((int) docCount);
                    break;
                }
                case "callInNumGe30": {
                    validCallAnalysisVO.setCallInNumGe30((int) docCount);
                    break;
                }
                case "callInNumGe60": {
                    validCallAnalysisVO.setCallInNumGe60((int) docCount);
                    break;
                }
                default:
                    break;
            }
        }
        return validCallAnalysisVO;
    }

    /**
     * 根据已经有的值计算其他的值
     *
     * @param trafficStatisticVO 已经有初始值的VO ,即已经调用过这个方法-> {@link TrafficStatisticServiceImpl#convertAggregationsToTrafficStatisticVO}
     * <AUTHOR>
     * @Date 17:36 2022/10/9
     **/
    private void calculatePropertyValues(TrafficStatisticVO trafficStatisticVO) {
        Integer callOutNum = trafficStatisticVO.getCallOutNum();
        Integer callInNum = trafficStatisticVO.getCallInNum();
        trafficStatisticVO.setCallNum(callOutNum + callInNum);
        Integer callOutDuration = trafficStatisticVO.getCallOutDuration();
        Integer callInDuration = trafficStatisticVO.getCallInDuration();
        trafficStatisticVO.setCallDuration(callOutDuration + callInDuration);
        Integer callOutConnectNum = trafficStatisticVO.getCallOutConnectNum();
        Integer callInConnectNum = trafficStatisticVO.getCallInConnectNum();
        trafficStatisticVO.setCallConnectNum(callOutConnectNum + callInConnectNum);
        // 接通率->总接通量(呼出接通量+呼入接通量)/通话总量。
        BigDecimal callConnectNumDecimal = new BigDecimal(trafficStatisticVO.getCallConnectNum());
        if (trafficStatisticVO.getCallNum() > 0) {
            BigDecimal callNumDecimal = new BigDecimal(trafficStatisticVO.getCallNum());
            trafficStatisticVO.setCallConnectRate(callConnectNumDecimal.divide(callNumDecimal, 2, RoundingMode.HALF_UP).doubleValue());
        }
        // 呼出接通率->呼出接通量/呼出总量
        BigDecimal callOutConnectNumDecimal = new BigDecimal(callOutConnectNum);
        if (callOutNum > 0) {
            BigDecimal callOutNumDecimal = new BigDecimal(callOutNum);
            trafficStatisticVO.setCallOutConnectRate(callOutConnectNumDecimal.divide(callOutNumDecimal, 2, RoundingMode.HALF_UP).doubleValue());
        }
        // 呼入接通率->呼入接通量/呼入总量
        BigDecimal callInConnectNumDecimal = new BigDecimal(callInConnectNum);
        if (callInNum > 0) {
            BigDecimal callInNumDecimal = new BigDecimal(callInNum);
            trafficStatisticVO.setCallInConnectRate(callInConnectNumDecimal.divide(callInNumDecimal, 2, RoundingMode.HALF_UP).doubleValue());
        }
        // 平均通话时长->通话总时长/总接通量(计算出来的是四舍五入的整秒)
        if (trafficStatisticVO.getCallConnectNum() > 0) {
            BigDecimal callDurationDecimal = new BigDecimal(trafficStatisticVO.getCallDuration());
            trafficStatisticVO.setCallDurationAvg(callDurationDecimal.divide(callConnectNumDecimal, 2, RoundingMode.HALF_UP).intValue());
        }
        // 呼出平均通话时长->拨出且接通的总通话时长/呼出接通量(计算出来的是四舍五入的整秒)
        if (callOutConnectNum > 0) {
            BigDecimal callOutDurationDecimal = new BigDecimal(callOutDuration);
            trafficStatisticVO.setCallOutDurationAvg(callOutDurationDecimal.divide(callOutConnectNumDecimal, 2, RoundingMode.HALF_UP).intValue());
        }
        // 呼入平均通话时长->来电且接通的总通话时长/呼入接通量(计算出来的是四舍五入的整秒)
        if (callInConnectNum > 0) {
            BigDecimal callInDurationDecimal = new BigDecimal(callInDuration);
            trafficStatisticVO.setCallInDurationAvg(callInDurationDecimal.divide(callInConnectNumDecimal, 2, RoundingMode.HALF_UP).intValue());
        }
    }
}
