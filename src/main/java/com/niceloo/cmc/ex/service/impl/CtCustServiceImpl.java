package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.entity.CtCust;
import com.niceloo.cmc.ex.mapper.CtCustMapper;
import com.niceloo.cmc.ex.service.CtCustService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 客户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Service
public class CtCustServiceImpl implements CtCustService {

    @Resource
    private CtCustMapper ctCustMapper;


    /**
     * 根据客户手机号查询客户信息
     *
     * @return java.util.List<com.niceloo.cmc.ex.entity.CtCust>
     * @paramter receiverPhone 加密后的客户手机号
     * <AUTHOR>
     * @Date 14:54 2022/2/23
     **/
    @Override
    public List<CtCust> findByMobile(String receiverPhone) {
        List<CtCust> ctCustList = new ArrayList<>();
        if (StringUtils.isEmpty(receiverPhone)) {
            return ctCustList;
        }
        // 解密客户手机号
        String sourcePhone = FieldCipherUtil.decrypt(receiverPhone);
        // 根据手机号查询
        if (sourcePhone.startsWith("1")) {
            ctCustList = ctCustMapper.selectByPhone(receiverPhone);
        } else { //根据客户固话查询
            ctCustList = ctCustMapper.selectByTel(receiverPhone);
            if (CollectionUtils.isEmpty(ctCustList)) {
                if (sourcePhone.contains("-")) {
                    receiverPhone = FieldCipherUtil.oneEncrypt(sourcePhone.replace("-", ""));
                } else {
                    if (sourcePhone.length() > 8) {
                        String prefix = sourcePhone.substring(0, sourcePhone.length() - 8);
                        String suffix = sourcePhone.substring(sourcePhone.length() - 8);
                        if (StringUtils.isEmpty(prefix)) {
                            sourcePhone = suffix;
                        } else {
                            sourcePhone = prefix + "-" + suffix;
                        }
                        receiverPhone = FieldCipherUtil.oneEncrypt(sourcePhone);
                    }
                }
                ctCustList = ctCustMapper.selectByTel(receiverPhone);
            }
            if (!CollectionUtils.isEmpty(ctCustList)) {
                ctCustList.get(0).setCustMobile(receiverPhone);
            }
        }
        return ctCustList;
    }

    @Override
    public List<CtCust> findListByMobileList(List<String> mobileList) {
        return ctCustMapper.getListByMobileList(mobileList);
    }

    @Override
    public List<String> findMobileListByPage(Integer pageNum, Integer pageSize) {
        return ctCustMapper.getMobileListByPage(pageNum, pageSize);
    }

    @Override
    public int replaceSaveBatch(List<CtCust> list) {
        return ctCustMapper.replaceInsertBatch(list);
    }

    @Override
    public int updateBatch(List<CtCust> list) {
        return ctCustMapper.batchUpdate(list);
    }

    /**
     * 通过使用主键范围代替分页查询客户手机(防止深分页的问题)
     *
     * @param minPrimaryValue 最小主键值 <span>(custId > #{minPrimaryValue})</span>
     * @param size            查询多少条数据 <span>( limit size)</span>
     * @return java.util.List<com.niceloo.cmc.ex.entity.CtCust>
     * <AUTHOR>
     * @Date 14:42 2022/5/20
     **/
    @Override
    public List<CtCust> selectMobileListUsePrimaryRange(String minPrimaryValue, Integer size) {
        if (null == size || size < 0) {
            return new ArrayList<>();
        }
        return ctCustMapper.selectMobileListUsePrimaryRange(minPrimaryValue, size);
    }
}
