package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.mapper.BdCallaccountinfoMapper;
import com.niceloo.cmc.ex.pojo.dto.AccountDTO;
import com.niceloo.cmc.ex.service.BdCallaccountinfoService;
import com.niceloo.framework.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 通讯厂商开放API对接密钥 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Service
public class BdCallaccountinfoServiceImpl implements BdCallaccountinfoService {

    @Resource
    private BdCallaccountinfoMapper bdCallaccountinfoMapper;

    @Override
    public List<String> selectSchoolIdByAccountType(String accountType) {
        return bdCallaccountinfoMapper.selectSchoolIdByAccountType(accountType);
    }

    /**
     * 根据账号和外呼类型查询账号信息
     *
     * @param account 账号
     * @param type    外呼类型
     * @return com.niceloo.cmc.ex.pojo.dto.AccountDTO
     * <AUTHOR>
     * @Date 16:52 2022/2/24
     **/
    @Override
    public AccountDTO selectAccount(String account, String type) {
        if (StringUtils.isEmpty(account) || StringUtils.isEmpty(type)) {
            return null;
        }
        return bdCallaccountinfoMapper.selectAccount(account, type);
    }

    /**
     * 查询指定账号类型下所有的对接密钥: 1去重后的 2未删除的
     *
     * @param channelType 外呼厂商类型
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.AccountDTO>
     * <AUTHOR>
     * @Date 17:13 2022/2/25
     **/
    @Override
    public List<AccountDTO> selectUniqueAccountsByType(String channelType) {
        return bdCallaccountinfoMapper.selectUniqueAccountsByType(channelType);
    }

    /**
     * 根据员工的id和外呼类型获取外呼账号
     *
     * @param eeId 员工的id
     * @param type 外呼类型
     * @return java.lang.String
     * <AUTHOR>
     * @Date 15:12 2022/3/2
     **/
    @Override
    public String selectAccountNameByBdEeId(String eeId, String type) {
        String accountName = bdCallaccountinfoMapper.selectAccountNameByBdEeId(eeId, type);
        return StringUtils.nullToEmpty(accountName);
    }
}
