package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.entity.CcSchoolBalance;
import com.niceloo.cmc.ex.mapper.CcSchoolBalanceMapper;
import com.niceloo.cmc.ex.service.CcSchoolBalanceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 话务统计表(月) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Service
public class CcSchoolBalanceServiceImpl implements CcSchoolBalanceService {

    @Resource
    private CcSchoolBalanceMapper ccSchoolBalanceMapper;

    /**
     * 批量添加
     *
     * @param ccSchoolBalance 分校账号余额列表
     * <AUTHOR>
     * @since 2023-07-31 15:09:24
     **/
    @Override
    public void batchSave(List<CcSchoolBalance> ccSchoolBalanceList) {
        ccSchoolBalanceMapper.batchSave(ccSchoolBalanceList);
    }
}
