package com.niceloo.cmc.ex.service.ai;

import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.dto.byai.*;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerAddRequest;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface BYAIService {

    /**
     * 查询某一个机器人对应的话术模板列表
     *
     * @return 话术模板列表
     */
    List<BYAIContextListDTO> getContextList();


    /**
     * 查询电话线路列表
     *
     * @return 电话线路列表
     */
    List<BYAILineListDTO> getLineList();

    Optional<String> getLineNameById(Long lineId);

    /**
     * 查询指定任务ID执行状态
     *
     * @param jobId   任务id
     * @return 任务状态信息
     */
    BYAIJobInfoDTO getJobInfo(Long jobId);

    /**
     * 批量任务结果查询接口
     *
     * @param jobIds  任务id列表
     * @return 电话线路列表
     */
    List<BYAIJobReportDTO> getJobReportList(List<Integer> jobIds);

    /**
     * 根据不同的操作类型操作外呼任务
     *
     * @param vendorJobId 第三方厂商-任务id
     * @param type        操作类型 (1->启动,2->暂停,3->继续,4->删除)
     */
    void operatingJob(Long vendorJobId, Integer type);

    /**
     * 从京东言犀创建外呼任务和追加客户
     *
     * @param aiCallJob           任务信息
     * @param customerRequestList 客户信息一条,如果有的话
     * @param ngExpand 登录用户信息
     */
    Integer createJobFromVendor(CcAiCallJob aiCallJob, List<JDCallCustomerRequest> customerRequestList, NgExpand ngExpand);


    /**
     * 从京东言犀创建外呼任务和追加客户
     *
     * @param aiJobId jobId
     * @param ngExpand 登录用户信息
     */
    void createJobFromVendor(String aiJobId, NgExpand ngExpand);

    /**
     * 根据京东言犀的外呼id更新任务的状态
     *
     * @param vendorJobId   京东言犀后台外呼id
     * @param companyId companyId
     */
    void updateJobStatusByJobId(Integer vendorJobId, Long companyId) throws IOException;

    /**
     * 接收百应回调外呼任务电话明细回调
     *
     * @param callRecordCallback 回调详情信息
     */
    void updateAIJobCustomerInfo(BYAICallRecordCallback callRecordCallback);

    /**
     * 下载录音文件到OSS并将地址更新到ES内
     *
     * @param map 录音文件等地址
     */
    void downLoadRecording(Map<String, Object> map);

    /**
     * 更新外呼任务的信息到数据库
     *
     * @param jobIds 外呼任务主键id
     * @param ucUser 操作录用户信息
     * @return 更新后的任务列表
     */
    List<CcAiCallJob> batchUpdateAICallJob(List<String> jobIds, UcUser ucUser) throws IOException;

    /**
     * 创建子任务并追加客户的信息到子任务
     *
     * @param parentCallJob 父任务信息
     * @param request       客户列表
     * @param ngExpand 登录用户信息
     */
    void createSubJobAndAddCustomer(CcAiCallJob parentCallJob, JDCallCustomerAddRequest request, NgExpand ngExpand);

    /**
     * 追加客户信息到京东言犀
     *
     * @param indexName       索引名称
     * @param lotNo           批次号,为空则上传所有未上传的客户
     * @param jobId           任务id
     * @param firstCustomerPhone 已经追加到任务的客户手机号
     */
    void addCustomerInfoToJob(String indexName, String lotNo, String jobId, String firstCustomerPhone, NgExpand ngExpand);

    /**
     * 获取天盾策略列表
     */
    List<BYAITiandunListDTO> getTiandunList();
}
