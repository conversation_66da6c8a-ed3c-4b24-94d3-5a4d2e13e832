package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.entity.CcCountday;
import com.niceloo.cmc.ex.mapper.CcCountdayMapper;
import com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData;
import com.niceloo.cmc.ex.pojo.vo.CallStatsVO;
import com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO;
import com.niceloo.cmc.ex.service.CcCountdayService;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 话务统计表(天) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Service
public class CcCountdayServiceImpl implements CcCountdayService {

    @Resource
    private CcCountdayMapper ccCountdayMapper;


    /**
     * 根据日期和外呼类型删除话务统计信息
     *
     * @param date        日期
     * @param channelType 外呼类型
     * <AUTHOR>
     * @Date 14:49 2022/2/24
     **/
    @Override
    public void deleteByDateAndChannelType(String date, String channelType) {
        if (StringUtils.isEmpty(date) || StringUtils.isEmpty(channelType)) {
            return;
        }
        ccCountdayMapper.deleteByDateAndChannelType(date, channelType);
    }

    /**
     * 批量添加
     *
     * @param commuCounts 添加列表
     * <AUTHOR>
     * @Date 14:49 2022/2/24
     **/
    @Override
    public void batchSave(List<CcCountday> commuCounts) {
        if (CollectionUtils.isEmpty(commuCounts)) {
            return;
        }
        ccCountdayMapper.batchSave(commuCounts);
    }

    /**
     * 根据日期范围和外呼类型查询列表
     *
     * @param start       开始时间
     * @param end         结束时间
     * @param channelType 外呼类型
     * @return java.util.List<com.niceloo.cmc.ex.entity.CcCountday>
     * <AUTHOR>
     * @Date 14:49 2022/2/24
     **/
    @Override
    public List<CcCountday> selectListByDate(String start, String end, String channelType) {
        if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || StringUtils.isEmpty(channelType)) {
            return new ArrayList<>();
        }
        return ccCountdayMapper.selectListByDate(start, end, channelType);
    }

    /**
     * 查询MySQL以日期[天]进行聚合的话务统计
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData>
     * @paramter callerUserId 员工的用户标识
     * @paramter dptIds 部门id列表
     * @paramter dates 话务统计的时间列表,如:["2021-12-10","2021-12-11","2021-12-12"]
     * <AUTHOR>
     * @Date 17:58 2022/3/3
     **/
    @Override
    public List<RecordDayStatisticsData> getTrafficStatisticGroupDateSql(String callerUserId, String dptIds, List<String> dates) {
        List<String> deptList = new ArrayList<>();
        if (!StringUtils.isEmpty(dptIds)) {
            deptList = List.of(dptIds.split(","));
        }
        if (dates == null || dates.isEmpty()) {
            return List.of();
        }
//        return ccCountdayMapper.getTrafficStatisticGroupDateSql(callerUserId, deptList, dates, null);
        Collections.sort(dates);
        String minDate = dates.get(0);
        String maxDate = dates.get(dates.size() - 1);
        return ccCountdayMapper.getTrafficStatisticGroupDateSql2(callerUserId, deptList, minDate, maxDate, null);
    }

    @Override
    public List<RecordEeStatisticsVO> getTrafficStatisticGroupEeSql(String callerUserId, String dptIds, String channelType, List<String> dates) {
        List<String> deptList = new ArrayList<>();
        if (!StringUtils.isEmpty(dptIds)) {
            deptList = List.of(dptIds.split(","));
        }
        if (dates == null || dates.isEmpty()) {
            return List.of();
        }
//        return ccCountdayMapper.getTrafficStatisticGroupEeSql(callerUserId, deptList, dates, channelType);
        Collections.sort(dates);
        String minDate = dates.get(0);
        String maxDate = dates.get(dates.size() - 1);
        return ccCountdayMapper.getTrafficStatisticGroupEeSql2(callerUserId, deptList, minDate, maxDate, channelType);
    }

    public CallStatsVO getCallStatsByUserId(String callerUserId, List<String> dates) {

        return ccCountdayMapper.getCallStatsByUserId(callerUserId, dates);
    }
}
