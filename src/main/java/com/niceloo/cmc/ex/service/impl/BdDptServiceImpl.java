package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.entity.BdDpt;
import com.niceloo.cmc.ex.mapper.BdDptMapper;
import com.niceloo.cmc.ex.service.BdDptService;
import com.niceloo.framework.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 部门 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Service
public class BdDptServiceImpl implements BdDptService {

    @Resource
    private BdDptMapper bdDptMapper;

    /**
     * 根据员工id获取部门信息
     * @paramter eeId
     * @return com.niceloo.cmc.ex.entity.BdDpt
     * <AUTHOR>
     * @Date 10:23 2022/2/26
     **/
    @Override
    public BdDpt findDptIdAndNameByEeId(String eeId) {
        if (StringUtils.isEmpty(eeId)){
            return null;
        }
        return bdDptMapper.findDptIdAndNameByEeId(eeId);
    }
}
