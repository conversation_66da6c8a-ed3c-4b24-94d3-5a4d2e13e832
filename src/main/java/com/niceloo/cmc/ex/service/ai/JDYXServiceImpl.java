package com.niceloo.cmc.ex.service.ai;

import com.niceloo.cmc.ex.common.AICallJobStatus;
import com.niceloo.cmc.ex.common.JDInterfaceTypeEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo;
import com.niceloo.cmc.ex.mapper.CcAiCallJobMapper;
import com.niceloo.cmc.ex.mapper.CcCallAccountConfigMapper;
import com.niceloo.cmc.ex.pojo.dto.jdyx.*;
import com.niceloo.cmc.ex.pojo.param.JDCallCustomerParam;
import com.niceloo.cmc.ex.pojo.param.JDCallPeriodParam;
import com.niceloo.cmc.ex.pojo.param.JDCreateJobParam;
import com.niceloo.cmc.ex.pojo.param.JDJobDetailParam;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerAddRequest;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.pojo.request.JDTagListRequest;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.utils.*;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.mq.client.Client;
import com.niceloo.plugin.sdk.lang.Pair;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import com.niceloo.segment.core.NicelooIdTemplate;
import lombok.CustomLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.BizConst.*;
import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_JDYX;

/**
 * 京东言犀AI外呼服务类
 *
 * <AUTHOR>
 * @since 2022-08-09 17:50
 */
@CustomLog
@Service
public class JDYXServiceImpl implements JDYXService {
    private static final Logger logger = LoggerFactory.getLogger(JDYXServiceImpl.class);

    private static final ExecutorService executor = Executors.newFixedThreadPool(10);

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Resource
    private OkHttpUtil okHttpUtil;

    @Resource
    private CcCallAccountConfigMapper ccCallAccountConfigMapper;

    @Resource
    private NicelooIdTemplate nicelooIdTemplate;

    @Resource
    private CcAiCallJobMapper ccAiCallJobMapper;

    @Resource
    private CcAiCallJobService ccAiCallJobService;

    @Resource
    private AICallJobCustomerInfoService aiCallJobCustomerInfoService;

    @Resource(name = "jobAddCustomerRabbitMqClient")
    private Client client;

    @Resource
    private final CallProperties.YanxiProperty yanxiProperty;

    @Autowired
    public JDYXServiceImpl(CallProperties.YanxiProperty yanxiProperty) {
        this.yanxiProperty = yanxiProperty;
    }

    /**
     * 查询某一个机器人对应的话术模板列表
     *
     * @param tenantId 租户ID
     * @return 返回JDContextFullDTO类型的列表，表示话术模板列表
     * <AUTHOR>
     * @since 2025-02-06 11:44:18
     */
    @Override
    public List<JDContextFullDTO> getContextFullList(String tenantId) {
        // 请求参数中的userPin失效，通过ngExpand动态获取
        // 为了避免业务调用方代码修改，这里暂时保留
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.CONTEXT_LIST.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByTenantId(tenantId);
        if (null == tenant) {
            return Collections.emptyList();
        } else {
            Map<String, Object> param = new HashMap<>();
            param.put("caller", tenant.getCaller());
            param.put("tenantId", tenant.getTenantId());
            Map<String, Object> businessParam = new HashMap<>();
            businessParam.put("botId", tenant.getBotId());
            businessParam.put("userPin", tenant.getUserPin());
            param.put("params", businessParam);
            param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));
            // 发起请求
            JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
            List<JDContextFullDTO> contextList = JSONUtils.toList(JSONUtils.toJSONString(baseDTO.getData()), JDContextFullDTO.class);

            // 格式化时间
            for (JDContextFullDTO context : contextList) {
                if (context.getCreatedTime() != null) {
                    context.setFormattedCreatedTime(DateUtils.toStr(new Date(context.getCreatedTime())));
                }
                if (context.getUpdatedTime() != null) {
                    context.setFormattedUpdatedTime(DateUtils.toStr(new Date(context.getUpdatedTime())));
                }
            }

            return contextList;
        }
    }

    /**
     * 查询某一个机器人对应的话术模板列表
     *
     * @param userPin  用户pin
     * @param ngExpand 登录用户信息
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.jdyx.JDYXContextListDTO>
     * <AUTHOR>
     * @since 2022-08-10 9:56
     **/
    @Override
    public List<JDContextListDTO> getContextList(String userPin, NgExpand ngExpand) {
        // 请求参数中的userPin失效，通过ngExpand动态获取
        // 为了避免业务调用方代码修改，这里暂时保留
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.CONTEXT_LIST.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);
        if (null == tenant) {
            return Collections.emptyList();
        } else {
            Map<String, Object> param = new HashMap<>();
            param.put("caller", tenant.getCaller());
            param.put("tenantId", tenant.getTenantId());
            Map<String, Object> businessParam = new HashMap<>();
            businessParam.put("botId", tenant.getBotId());
            businessParam.put("userPin", tenant.getUserPin());
            param.put("params", businessParam);
            param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));
            // 发起请求
            JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
            return JSONUtils.toList(JSONUtils.toJSONString(baseDTO.getData()), JDContextListDTO.class);
        }
    }

    /**
     * 根据租户标识获取话术列表
     *
     * @param tenantId 租户标识
     * @return 话术列表
     */
    @Override
    public List<JDContextListDTO> getContextListByTenantId(Integer tenantId) {
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.CONTEXT_LIST.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByTenantId(String.valueOf(tenantId));
        if (null == tenant) {
            return Collections.emptyList();
        } else {
            Map<String, Object> param = new HashMap<>();
            param.put("caller", tenant.getCaller());
            param.put("tenantId", tenant.getTenantId());
            Map<String, Object> businessParam = new HashMap<>();
            businessParam.put("botId", tenant.getBotId());
            businessParam.put("userPin", tenant.getUserPin());
            param.put("params", businessParam);
            param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));
            // 发起请求
            JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
            return JSONUtils.toList(JSONUtils.toJSONString(baseDTO.getData()), JDContextListDTO.class);
        }
    }

    /**
     * 获取标签列表
     *
     * @param jdTagListRequest 标签列表请求对象，包含请求参数如 cid, groupId, pageSize, currentPage
     * @param ngExpand         登录用户信息，用于获取租户信息
     * @return 标签列表DTO对象列表
     */
    @Override
    public List<JDTagListDTO.TagDTO> getTagList(JDTagListRequest jdTagListRequest, NgExpand ngExpand) {
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.TAG_LIST.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);

        if (tenant == null) {
            return Collections.emptyList();
        }

        Map<String, Object> param = new HashMap<>();
        param.put("caller", tenant.getCaller());
        param.put("tenantId", tenant.getTenantId());

        Map<String, Object> businessParam = new HashMap<>();
        businessParam.put("botId", tenant.getBotId());
        businessParam.put("cid", jdTagListRequest.getCid());
        businessParam.put("userPin", tenant.getUserPin());
        businessParam.put("tenantId", tenant.getTenantId());
        businessParam.put("pageSize", jdTagListRequest.getPageSize());
        businessParam.put("currentPage", jdTagListRequest.getCurrentPage());
        if (jdTagListRequest.getGroupId() != null) {
            businessParam.put("groupId", jdTagListRequest.getGroupId());
        }
        param.put("params", businessParam);

        param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));

        // 发起请求
        JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));

        // 反序列化为包装类
        JDTagListResponse response = JSONUtils.toObject(JSONUtils.toJSONString(baseDTO.getData()), JDTagListResponse.class);

        // 提取所有的 TagDTO
        List<JDTagListDTO.TagDTO> tagList = new ArrayList<>();
        if (response != null && response.getResult() != null) {
            for (JDTagListDTO dto : response.getResult()) {
                if (dto.getTagList() != null) {
                    tagList.addAll(dto.getTagList());
                }
            }
        }

        return tagList;
    }

    /**
     * 获取分组列表
     *
     * @param cid 客户ID，用于指定需要获取分组列表的客户
     * @param ngExpand 登录用户信息，用于获取租户信息和进行权限验证
     * @return JDGroupListDTO对象列表，包含指定客户的分组信息
     *
     * 方法首先根据传入的cid和ngExpand构建请求URL和参数。
     * 然后，通过ngExpand获取租户信息，如果租户信息不存在，则返回空列表。
     * 接下来，构建请求参数，包括调用者信息、租户ID、机器人ID、客户ID和用户Pin，
     * 并使用业务参数和租户令牌生成签名。
     * 最后，发送请求到指定的URL，并将返回的JSON字符串转换为JDGroupListDTO对象列表。
     */
    @Override
    public List<JDGroupListDTO> getGroupList(String cid, NgExpand ngExpand) {
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.GROUP_LIST.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);

        // 检查租户信息
        if (tenant == null) {
            return Collections.emptyList();
        }

        Map<String, Object> param = new HashMap<>();
        param.put("caller", tenant.getCaller());
        param.put("tenantId", tenant.getTenantId());

        Map<String, Object> businessParam = new HashMap<>();
        businessParam.put("botId", tenant.getBotId());
        businessParam.put("cid", cid); // 使用传入的cid
        businessParam.put("userPin", tenant.getUserPin());
        param.put("params", businessParam);

        // 生成签名
        param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));

        // 发起请求
        JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
        return JSONUtils.toList(JSONUtils.toJSONString(baseDTO.getData()), JDGroupListDTO.class);
    }

    /**
     * 查询电话线路列表
     *
     * @param ngExpand 登录用户信息
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.jdyx.JDYXLineListDTO>
     * <AUTHOR>
     * @since 10:42 2022/8/10
     **/
    @Override
    public List<JDLineListDTO> getLineList(NgExpand ngExpand) {
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.TEL_LINE_LIST.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);
        if (null == tenant) {
            return Collections.emptyList();
        } else {
            Map<String, Object> param = new HashMap<>();
            param.put("caller", tenant.getCaller());
            param.put("tenantId", tenant.getTenantId());
            Map<String, Object> businessParam = new HashMap<>();
            businessParam.put("userPin", tenant.getUserPin());
            businessParam.put("botId", tenant.getBotId());
            param.put("params", businessParam);
            param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));
            // 发起请求
            JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
            return JSONUtils.toList(JSONUtils.toJSONString(baseDTO.getData()), JDLineListDTO.class);
        }
    }

    /**
     * 查询指定任务ID执行状态
     *
     * @param jobId    任务id
     * @param userPin  用户pin
     * @param ngExpand 登录用户信息
     * @return com.niceloo.cmc.ex.pojo.dto.jdyx.JDYXJobInfoDTO
     * <AUTHOR>
     * @since 14:56 2022/8/10
     **/
    @Override
    public JDJobInfoDTO getJobInfo(Integer jobId, String userPin, NgExpand ngExpand) {
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.JOB_INFO.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);
        if (null == tenant) {
            return null;
        } else {
            Map<String, Object> param = new HashMap<>();
            param.put("caller", tenant.getCaller());
            param.put("tenantId", tenant.getTenantId());
            Map<String, Object> businessParam = new HashMap<>();
            businessParam.put("userPin", tenant.getUserPin());
            businessParam.put("botId", tenant.getBotId());
            businessParam.put("jobId", jobId);
            param.put("params", businessParam);
            param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));
            // 发起请求
            JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
            return JSONUtils.toObject(JSONUtils.toJSONString(baseDTO.getData()), JDJobInfoDTO.class);
        }
    }

    /**
     * 批量任务结果查询接口
     *
     * @param jobIds   任务id列表
     * @param ngExpand 登录用户信息
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.jdyx.JDYXJobReportDTO>
     * <AUTHOR>
     * @Date 15:21 2022/8/10
     **/
    @Override
    public List<JDJobReportDTO> getJobReportList(List<Integer> jobIds, NgExpand ngExpand) {
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.JOB_GET_REPORT_LIST.getUrl();
        List<JDJobReportDTO> mergedList = Collections.synchronizedList(new ArrayList<>()); // 线程安全的列表
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);

        if (tenant == null) {
            return mergedList; // tenant 为空，返回空的 mergedList
        }

        // 并发处理 jobIds
        final int batchSize = 20;
        List<Future<Void>> futures = new ArrayList<>();

        for (int i = 0; i < jobIds.size(); i += batchSize) {
            int end = Math.min(i + batchSize, jobIds.size());
            List<Integer> batchJobIds = jobIds.subList(i, end);

            futures.add(executor.submit(() -> {
                // 为每个任务创建独立的 param 和 businessParam
                Map<String, Object> param = new HashMap<>();
                param.put("caller", tenant.getCaller());
                param.put("tenantId", tenant.getTenantId());

                Map<String, Object> businessParam = new HashMap<>();
                businessParam.put("userPin", tenant.getUserPin());
                businessParam.put("botId", tenant.getBotId());
                businessParam.put("jobIds", batchJobIds); // 使用当前批次的 jobIds
                businessParam.put("pageSize", 1);

                // 计算签名
                param.put("params", businessParam);
                param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));

                // 发起请求
                JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
                if (baseDTO.getData() != null) {
                    mergedList.addAll(JSONUtils.toList(JSONUtils.toJSONString(baseDTO.getData()), JDJobReportDTO.class));
                }
                return null;
            }));
        }

        // 等待所有任务完成
        for (Future<Void> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                LOGGER.error("获取京东言犀任务状态时发生异常", e);
                throw new ApplicationException(ApiErrorCodes.execute_failed, "获取京东言犀任务状态时发生异常", e);
            }
        }

        return mergedList;
    }

    /**
     * 创建外呼任务
     *
     * @param request  创建参数
     * @param ngExpand 登录用户信息
     * @return java.lang.Integer
     * <AUTHOR>
     * @since 16:04 2022/8/10
     **/
    @Override
    public Integer createJob(JDCreateJobParam request, NgExpand ngExpand) {
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.CREATE_JOB.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);
        if (null == tenant) {
            return null;
        } else {
            Map<String, Object> param = new HashMap<>();
            param.put("caller", tenant.getCaller());
            param.put("tenantId", tenant.getTenantId());
            Map<String, Object> businessParam = JSONUtils.toMap(JSONUtils.toJSONString(request));
            param.put("params", businessParam);
            param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));
            // 发起请求
            JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
            Map<String, Object> responseMap = JSONUtils.toMap(JSONUtils.toJSONString(baseDTO.getData()));
            return Integer.parseInt(responseMap.get("id").toString());
        }
    }

    /**
     * 根据不同的操作类型操作外呼任务
     *
     * @param vendorJobId 第三方厂商-任务id
     * @param type        操作类型 (1->启动,2->暂停,3->继续,4->删除)
     * @param userPin     用户pin
     * @param ngExpand    登录用户信息
     * <AUTHOR>
     * @Date 17:44 2022/8/10
     **/
    @Override
    public void operatingJob(Integer vendorJobId, Integer type, String userPin, NgExpand ngExpand) {
        String url = CallProperties.JDYXProperty.url_pre;
        switch (type) {
            case 1: {
                url = url + JDInterfaceTypeEnum.START_JOB.getUrl();
                break;
            }
            case 2: {
                url = url + JDInterfaceTypeEnum.PAUSE_JOB.getUrl();
                break;
            }
            case 3: {
                url = url + JDInterfaceTypeEnum.CONTINUE_JOB.getUrl();
                break;
            }
            case 4: {
                url = url + JDInterfaceTypeEnum.DELETE_JOB.getUrl();
                break;
            }
            default: {
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "不支持此操作类型");
            }
        }
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);
        Map<String, Object> param = new HashMap<>();
        param.put("caller", tenant.getCaller());
        param.put("tenantId", tenant.getTenantId());
        Map<String, Object> businessParam = new HashMap<>();
        businessParam.put("userPin", tenant.getUserPin());
        businessParam.put("jobId", vendorJobId);
        param.put("params", businessParam);
        param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));
        // 发起请求
        JDBaseDTO jdBaseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
        if (!"10000".equals(jdBaseDTO.getCode())) {
            throw new ApplicationException(ApiErrorCodes.execute_failed_handler, JSONUtils.toJSONString(jdBaseDTO));
        }
    }

    /**
     * 外呼任务明细数据滚动查询
     *
     * @param request 包含查询参数的请求对象，包括任务ID、应用/机器人ID、查询起点、查询数量以及登录用户信息
     * @return JDJobDetailsDTO 返回查询到的外呼任务明细数据对象
     * @throws ApplicationException 如果任务不存在或查询过程中出现错误，则抛出此异常
     * <AUTHOR>
     * @since 2024-12-26 14:39:24
     *
     * 该方法通过发送HTTP请求到指定的URL，查询指定任务ID的外呼任务明细数据。
     * 首先，根据任务ID获取对应的租户ID，并校验任务是否存在。如果不存在，则抛出异常。
     * 然后，根据租户ID获取相应的租户信息，并构建查询参数。
     * 查询参数包括调用者、租户ID、应用/机器人ID、任务ID（由租户任务ID转换而来）、查询起点和查询数量。
     * 接着，对查询参数进行签名，并将所有参数转换为JSON字符串格式。
     * 发送HTTP请求到指定的URL，并将响应结果转换为JDBaseDTO对象。
     * 如果响应状态码不为"10000"，则抛出异常；否则，将响应数据转换为JDJobDetailsDTO对象并返回。
     */
    @Override
    public JDJobDetailsDTO getJobDetails(JDJobDetailParam request) {
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.ROLLIING_GET_JOB_DETAILS.getUrl();

        // 根据jobId获取租户ID
        CcAiCallJob ccAiCallJob = ccAiCallJobMapper.selectById(request.getJobId());
        if (ccAiCallJob == null) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "任务不存在");
        }
        CallProperties.YanxiProperty.Tenant tenant = getTenantByTenantId(ccAiCallJob.getTenantId());

        Map<String, Object> param = new HashMap<>();
        param.put("caller", tenant.getCaller());
        param.put("tenantId", tenant.getTenantId());

        Map<String, Object> businessParam = new HashMap<>();
        businessParam.put("botId", tenant.getBotId());
        businessParam.put("jobId", ccAiCallJob.getVendorJobId());
        businessParam.put("startIndex", request.getStartIndex() != null ? request.getStartIndex() : 0);
        businessParam.put("limit", request.getLimit() != null ? request.getLimit() : 100);

        param.put("params", JSONUtils.toJSONString(businessParam));
        param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));

        // 发起请求
        JDBaseDTO baseDTO = this.sendRequest(url, JSONUtils.toJSONString(param));
        if (!"10000".equals(baseDTO.getCode())) {
            throw new ApplicationException(ApiErrorCodes.execute_failed_handler, JSONUtils.toJSONString(baseDTO));
        }

        return JSONUtils.toObject(JSONUtils.toJSONString(baseDTO.getData()), JDJobDetailsDTO.class);
    }

    /**
     * 获取指定任务ID的所有任务详情。
     *
     * @param jobId 任务ID，用于标识要获取详情的任务。
     * @return 包含所有任务详情的列表。如果任务ID无效或没有任务详情，则返回空列表。
     * @throws IllegalArgumentException 如果jobId为null，则抛出此异常。
     */
    @Override
    public List<JDJobDetailsDTO.JobDetail> getAllJobDetails(String jobId) {
        List<JDJobDetailsDTO.JobDetail> jobDetailList = new ArrayList<>();
        JDJobDetailParam jdyxParam = new JDJobDetailParam();
        jdyxParam.setJobId(jobId);
        Long startIndex = 0L; // 初始化起始索引

        while (true) {
            jdyxParam.setStartIndex(startIndex); // 设置当前起始索引
            JDJobDetailsDTO jobDetails = getJobDetails(jdyxParam);

            Long nextStartIndex = jobDetails.getNextStartIndex();
            if (nextStartIndex == null || nextStartIndex <= startIndex) {
                break; // 没有更多数据或索引未增加，退出循环
            }
            startIndex = nextStartIndex; // 更新起始索引

            // 先查询下一次查询id起点，再添加集合，下一次查询id起点为null的话，说明已经没有数据了
            jobDetailList.addAll(jobDetails.getJobDetails());
        }

        return jobDetailList;
    }

    /**
     * 从京东言犀创建外呼任务和追加客户
     *
     * @param aiCallJob           任务信息
     * @param customerRequestList 一条客户信息,如果有的话
     * @param ngExpand            登录用户信息
     * <AUTHOR>
     * @Date 14:41 2022/8/23 assignment
     **/
    @Override
    public Integer createJobFromVendor(CcAiCallJob aiCallJob, List<JDCallCustomerRequest> customerRequestList, NgExpand ngExpand) {
        // 如果任务名称|客户信息列表校验失败,不进行创建
        // 已经有京东言犀任务ID的，也不进行创建
        if (StringUtils.isEmpty(aiCallJob.getCustomerIndex()) || 0 != aiCallJob.getVendorJobId()) {
            return null;
        }
        // 组织创建外呼任务的参数
        JDCreateJobParam jdCreateJobParam = JDCreateJobParam.defaultValue();
        paramAssign(jdCreateJobParam, aiCallJob, ngExpand);
        // 转换客户参数
        List<JDCallCustomerParam> customerParamList = JDCallCustomerParam.convertToParam(customerRequestList, aiCallJob.getParentJobId());
        jdCreateJobParam.setCustomers(customerParamList);
        // 调用接口创建外呼任务
        return this.createJobAndUpdateDB(jdCreateJobParam, aiCallJob.getJobId(), ngExpand);
    }

    /**
     * 从京东言犀创建外呼任务和追加客户
     *
     * @param aiJobId 外呼任务主键id
     * <AUTHOR>
     * @Date 16:29 2022/8/24
     **/
    @Override
    public void createJobFromVendor(String aiJobId, NgExpand ngExpand) {
        if (StringUtils.isEmpty(aiJobId)) {
            return;
        }
        // 得到外呼任务
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(aiJobId);
        // 得到客户的信息
        List<JDCallCustomerRequest> customerRequestList = aiCallJobCustomerInfoService.selectJobCustomerOne(aiCallJob.getCustomerIndex(), aiJobId);
        // 从京东言犀创建外呼任务
        Integer vendorId = this.createJobFromVendor(aiCallJob, customerRequestList, ngExpand);
        aiCallJob = ccAiCallJobService.getById(aiJobId);
        // 将客户追加到京东言犀
        if (vendorId != null) {
            // 发送MQ将客户信息追加到京东言犀的外呼任务上
            Map<String, String> mqRequest = new HashMap<>();
            mqRequest.put("indexName", aiCallJob.getCustomerIndex());
            mqRequest.put("lotNo", null);
            mqRequest.put("jobLevel", "1");
            mqRequest.put("jobId", aiCallJob.getJobId());
            mqRequest.put("jobProgress", aiCallJob.getJobProgress().toString());
            mqRequest.put("finishFlag", aiCallJob.getCustomerAddStatus());
            JDCallCustomerRequest customerRequest = customerRequestList.get(0);
            mqRequest.put("firstCustomerPhone", customerRequest.getPhone());
            mqRequest.put("userId", ngExpand.getUcUser().getUserId());
            client.publish(MQConst.ADD_CUSTOMER_INFO_TO_JOB_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest));
        }
    }

    /**
     * 根据京东言犀的外呼id更新任务的状态
     *
     * @param vendorJobId 京东言犀后台外呼id
     * @param jobName     任务名称
     * @param status      任务状态
     * <AUTHOR>
     * @Date 10:25 2022/8/25
     **/
    @Override
    public void updateJobStatusByJobId(Integer vendorJobId, String jobName, String status, Integer tenantId) {
        String jobId = ccAiCallJobService.getJobIdByVendorJobId(vendorJobId, CALL_TYPE_JDYX.getType());
        if (StringUtils.isEmpty(jobId)) {
            return;
        }
        // 更新外呼任务的信息到数据库
        this.batchUpdateAICallJob(List.of(jobId), null, tenantId);
    }

    /**
     * 接收京东回调外呼任务电话明细回调
     *
     * @param callRecordCallback 回调详情信息
     * <AUTHOR>
     * @Date 11:18 2022/8/25
     **/
    @Override
    public void updateAIJobCustomerInfo(JDCallRecordCallback callRecordCallback) {
        // 将回调参数转成实体
        AICallJobCustomerInfo jobCustomerInfo = callRecordCallback.convertToAIJobCustomerEntity();
        if (null == jobCustomerInfo) {
            return;
        }
        // 查询出索引的名称
        String indexName = ccAiCallJobService.getIndexNameByVendorJobId(jobCustomerInfo.getVendorJobId(), CALL_TYPE_JDYX.getType());
        // 更新到ES数据库
        aiCallJobCustomerInfoService.bulkUpdate(indexName, List.of(jobCustomerInfo));
        // 异步下载录音
        if ("Y".equals(jobCustomerInfo.getHasRecordingDownLoad())) {
            Map<String, Object> data = new HashMap<>(4);
            data.put("channelType", CALL_TYPE_JDYX.getType());
            data.put("indexName", indexName);
            data.put("recordingDownLoadTimes", jobCustomerInfo.getRecordingDownLoadTimes());
            data.put("voiceSourceUrl", jobCustomerInfo.getRecordingUrl());
            data.put("id", jobCustomerInfo.getId());
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data));
        }
    }

    /**
     * 下载录音文件到OSS并将地址更新到ES内
     *
     * @param map 录音文件等地址
     * <AUTHOR>
     * @since 14:38 2022/8/25
     **/
    @Override
    public void downLoadRecording(Map<String, Object> map) {
        String voiceSourceUrl = map.get("voiceSourceUrl").toString();
        if (StringUtils.isEmpty(voiceSourceUrl)) {
            return;
        }
        String indexName = map.get("indexName").toString();
        int recordingDownLoadTimes = Integer.parseInt(map.get("recordingDownLoadTimes").toString());
        String id = map.get("id").toString();
        String fileName = id + ".mp3";
        AICallJobCustomerInfo jobCustomerInfo = new AICallJobCustomerInfo();
        jobCustomerInfo.setId(id);
        jobCustomerInfo.setModifiedTime(new Date());
        jobCustomerInfo.setRecordingDownLoadTimes(recordingDownLoadTimes + 1);
        try {
            Map<String, Object> uploadResMap = HttpUtils.uploadVoiceToOSS(voiceSourceUrl, fileName);
            jobCustomerInfo.setServerFolder((String) uploadResMap.get("filePath"));
            jobCustomerInfo.setFileKey((String) uploadResMap.get("fileKey"));
            // 更新通话记录
        } catch (Exception e) {
            logger.warn(e, "京东言犀通道下载通话录音id为:{}出现异常,异常信息:{}", id, e.getMessage());
        }
        aiCallJobCustomerInfoService.bulkUpdate(indexName, List.of(jobCustomerInfo));
    }

    /**
     * 更新外呼任务的信息到数据库
     *
     * @param jobIds 外呼任务主键id
     * @param ucUser 操作人信息
     * @return java.util.List<com.niceloo.cmc.ex.entity.CcAiCallJob>
     * <AUTHOR>
     * @since 14:59 2022/8/26
     **/
    @Override
    public List<CcAiCallJob> batchUpdateAICallJob(List<String> jobIds, UcUser ucUser, Integer tenantId) {
        NgExpand ngExpand = new NgExpand();
        ngExpand.setUcUser(ucUser);
        if (CollectionUtils.isEmpty(jobIds)) {
            return new ArrayList<>();
        }
        // 根据id列表查询数据库
        List<CcAiCallJob> callJobList = ccAiCallJobService.listByIds(jobIds);

        // 创建一个列表用于保存被过滤掉的任务
        List<CcAiCallJob> filteredOutJobList = new ArrayList<>();

        // 过滤掉已删除的外呼任务和 vendorJobId 为 0 的外呼任务
        List<CcAiCallJob> filteredCallJobList = callJobList.stream()
                .filter(t -> {
                    if (t.getStatus().equals(AICallJobStatus.canceled.name()) || t.getVendorJobId() == 0) {
                        filteredOutJobList.add(t); // 保存被过滤的任务
                        return false; // 过滤掉
                    }
                    return true; // 保留
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredCallJobList)) {
            return filteredOutJobList; // 如果没有有效任务，返回被过滤的任务
        }
        // 调用京东言犀接口更新外呼任务列表
        this.updateJobInfoList(filteredCallJobList, ngExpand, tenantId);

        // 重新从数据库查询以确保状态的准确性
        List<CcAiCallJob> updatedCallJobList = ccAiCallJobService.listByIds(jobIds);

        // 更新 filteredCallJobList 中的 jobProgress
        for (CcAiCallJob updatedJob : updatedCallJobList) {
            filteredCallJobList.stream()
                    .filter(job -> job.getJobId().equals(updatedJob.getJobId()))
                    .findFirst()
                    .ifPresent(job -> job.setJobProgress(updatedJob.getJobProgress()));
        }

        // 更新其他的字段
        if (ucUser != null) {
            filteredCallJobList.forEach(t -> {
                t.setModifier(ucUser.getUserId());
                t.setModifyName(ucUser.getUserName());
            });
        }
        // 更新到数据库
        ccAiCallJobService.updateBatchById(filteredCallJobList);

        // 返回更新后的任务列表和被过滤的任务
        filteredCallJobList.addAll(filteredOutJobList);
        return filteredCallJobList;
    }

    /**
     * 创建子任务并追加客户的信息到子任务。
     *
     * 根据父任务类型（分校任务、全国任务或学服任务），将客户列表分组并创建相应的子任务。
     * 如果是分校任务或全国任务，则按学校ID分组客户列表；如果是学服任务，则不分组。
     *
     * @param parentCallJob 父任务信息，包含任务类型等信息
     * @param request       客户列表请求，包含批次号和客户请求列表
     * @param ngExpand      扩展信息，用于创建任务时的额外参数
     * @throws ApplicationException 如果任务类型无效，则抛出此异常
     * <AUTHOR>
     * @since 10:57 2022/9/30
     */
    @Override
    public void createSubJobAndAddCustomer(CcAiCallJob parentCallJob, JDCallCustomerAddRequest request, NgExpand ngExpand) {
        String lotNo = request.getLotNo();
        // AI 外呼任务类型
        String callTaskType = parentCallJob.getCallTaskType();
        // 客户信息列表
        List<JDCallCustomerRequest> jdCallCustomerRequestList = request.getJdCallCustomerRequestList();

        JDYXServiceImpl jdyxService = SpringUtils.getBean(JDYXServiceImpl.class);

        // 存储按学校ID分组的客户请求列表
        Map<String, List<JDCallCustomerRequest>> jdCallCustomerRequestListGroup = null;

        // 针对不同类型的处理逻辑
        switch (callTaskType) {
            case AI_SCHOOL_TASK: // 分校任务
                // 按分校ID分组
                if (jdCallCustomerRequestList != null) {
                    jdCallCustomerRequestListGroup = jdCallCustomerRequestList.stream()
                            .collect(Collectors.groupingBy(JDCallCustomerRequest::getSchoolId));
                }
                jdyxService.globalCreateSubCallJob(parentCallJob, jdCallCustomerRequestListGroup, callTaskType);
                break;

            case AI_ALL_TASK: // 全国任务
                // 不分组，固定schoolId
                jdCallCustomerRequestListGroup = Collections.singletonMap(
                        TOP_SCHOOL_ID, jdCallCustomerRequestList != null ? jdCallCustomerRequestList : Collections.emptyList());
                jdyxService.globalCreateSubCallJob(parentCallJob, jdCallCustomerRequestListGroup, callTaskType);
                break;

            case AI_SS_TASK: // 学服任务
                // 创建一个包含一个键值对的 Map
                jdCallCustomerRequestListGroup = Collections.singletonMap(TOP_SCHOOL_ID, jdCallCustomerRequestList != null ? jdCallCustomerRequestList : Collections.emptyList());
                break;

            default:
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "Invalid callTaskType: " + callTaskType);
        }

        // 存储要添加到ES内的客户信息列表
        List<AICallJobCustomerInfo> callJobCustomerInfoListTotal = new ArrayList<>();
        if (jdCallCustomerRequestList != null) {
            callJobCustomerInfoListTotal = new ArrayList<>(jdCallCustomerRequestList.size());
        }

        List<CcAiCallJob> callJobList = fetchChildJobList(parentCallJob.getJobId());
        Map<String, CcAiCallJob> callJobMap = createSchoolIdMap(callJobList);

        // 如果不是学服任务，获取客户信息并添加到列表
        if (!AI_SS_TASK.equals(callTaskType)) {
            if (!MapUtils.isEmpty(jdCallCustomerRequestListGroup)) {
                for (Map.Entry<String, List<JDCallCustomerRequest>> listEntry : jdCallCustomerRequestListGroup.entrySet()) {
                    String schoolId = listEntry.getKey();
                    // 获取子任务的信息
                    CcAiCallJob aiCallJob = callJobMap.get(schoolId);
                    List<AICallJobCustomerInfo> callJobCustomerInfoList = aiCallJobCustomerInfoService.entityConverter(aiCallJob, listEntry.getValue(), lotNo);
                    callJobCustomerInfoListTotal.addAll(callJobCustomerInfoList);
                }
            }
        } else {
            // 对于学服任务，直接将客户信息挂到父任务下
            if (!CollectionUtils.isEmpty(jdCallCustomerRequestList)) {
                List<AICallJobCustomerInfo> callJobCustomerInfoList = aiCallJobCustomerInfoService.entityConverter(parentCallJob, jdCallCustomerRequestList, lotNo);
                callJobCustomerInfoListTotal.addAll(callJobCustomerInfoList);
            }
        }
        // 3、在京东言犀平台创建外呼任务
        Map<String, String> firstCustomerPhoneMap = jdyxService.createJobFormVendor(parentCallJob, jdCallCustomerRequestListGroup, ngExpand, callTaskType);

        // 如果是学服任务，且父任务创建失败，则不继续后续操作
        if (AI_SS_TASK.equals(callTaskType)) {
            String jobId = parentCallJob.getJobId();
            CcAiCallJob ccAiCallJob = ccAiCallJobService.getById(jobId);
            Integer jobProgress = ccAiCallJob.getJobProgress();
            if (jobProgress == 3) {
                return;
            }
        }

        // 4、将客户信息添加到ES
        if (!CollectionUtils.isEmpty(callJobCustomerInfoListTotal)) {
            aiCallJobCustomerInfoService.bulkIndex(parentCallJob.getCustomerIndex(), callJobCustomerInfoListTotal);
        }
        // 5、发送MQ添加客户信息到子任务
        this.sendAddCustomerInfoMQ(parentCallJob, lotNo, request.getFinishFlag(), firstCustomerPhoneMap, ngExpand);
    }

    /**
     * 追加客户信息到京东言犀
     *
     * @param indexName          索引名称
     * @param lotNo              批次号,为空则上传所有未上传的客户
     * @param jobId              任务id
     * @param firstCustomerPhone 已经追加到任务的客户手机号
     * <AUTHOR>
     * @since 13:39 2022/9/30
     **/
    @Override
    public void addCustomerInfoToJob(String indexName, String lotNo, String jobId, String firstCustomerPhone, NgExpand ngExpand) {
        // 滚动查询es
        Pair<String, List<JDCallCustomerRequest>> pair = aiCallJobCustomerInfoService.startScrollByLotNoAndJob(indexName, lotNo, jobId);
        String scrollId = pair.getKey();
        if (StringUtils.isEmpty(scrollId)) {
            return;
        }
        // 根据id查询任务信息
        CcAiCallJob aiCallJob = ccAiCallJobService.getById(jobId);
        String url = CallProperties.JDYXProperty.url_pre + JDInterfaceTypeEnum.APPEND_CUSTOMER.getUrl();
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);
        Map<String, Object> param = new HashMap<>();
        param.put("caller", tenant.getCaller());
        param.put("tenantId", tenant.getTenantId());
        Map<String, Object> businessParam = new HashMap<>();
        businessParam.put("userPin", tenant.getUserPin());
        businessParam.put("jobId", aiCallJob.getVendorJobId());
        // 追加失败的客户信息
        Map<String, String> invalidMap = new HashMap<>();
        boolean flag = true;
        while (flag) {
            scrollId = pair.getKey();
            param.put("params", businessParam);
            businessParam.put("customers", JDCallCustomerParam.convertToParam(pair.getValue(), aiCallJob.getParentJobId()));
            param.put("sign", JDHmacMD5Util.genSign(businessParam, tenant.getToken()));
            try {
                // 追加客户信息
                JDBaseDTO baseDTO = sendRequest(url, JSONUtils.toJSONString(param));
                Map<String, String> invalidPhoneMap = this.getInvalidPhoneList(baseDTO);
                invalidMap.putAll(invalidPhoneMap);
            } catch (Exception e) {
                logger.error(e, "调用京东言犀接口追加客户信息出现异常,异常信息:{}", e.getMessage());
                Map<String, String> invalidPhoneMap = new HashMap<>();
                pair.getValue().forEach(t -> invalidPhoneMap.put(t.getPhone(), e.getMessage()));
                invalidMap.putAll(invalidPhoneMap);
            }
            // 追加完成后修改到ES内
            invalidMap.remove(firstCustomerPhone);
            List<AICallJobCustomerInfo> aiCallJobCustomerInfoList = JDCallCustomerRequest.bulkUpdateCustomerSyncStatus(aiCallJob.getVendorJobId(), aiCallJob.getJobName(), pair.getValue(), invalidMap);
            aiCallJobCustomerInfoService.bulkUpdate(indexName, aiCallJobCustomerInfoList);
            pair = aiCallJobCustomerInfoService.continueScroll(indexName, scrollId);
            invalidMap.clear();
            flag = StringUtils.isNotEmpty(pair.getKey());
        }
    }

    /**
     * 发送添加客户信息到消息队列的请求
     *
     * @param parentJob          父任务对象
     * @param lotNo              批次号
     * @param finishFlag           完成标志
     * @param firstCustomerPhoneMap 首个客户电话号码映射
     * @param ngExpand             扩展信息对象
     * @throws ApplicationException 当任务类型无效时抛出此异常
     */
    private void sendAddCustomerInfoMQ(CcAiCallJob parentJob, String lotNo, String finishFlag, Map<String, String> firstCustomerPhoneMap, NgExpand ngExpand) {
        String callTaskType = parentJob.getCallTaskType();
        List<CcAiCallJob> subJobList;

        switch (callTaskType) {
            case AI_SCHOOL_TASK: // 分校任务
            case AI_ALL_TASK: // 全国任务
                String parentId = parentJob.getJobId();
                // 根据父任务id获取所有创建任务成功的子任务
                subJobList = fetchChildJobList(parentId);
                break;

            case AI_SS_TASK: // 学服任务
                subJobList = Collections.singletonList(parentJob);
                break;

            default:
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "Invalid callTaskType: " + callTaskType);
        }

        // 发送MQ将客户信息追加到京东言犀的外呼任务上
        for (CcAiCallJob callJob : subJobList) {
            Map<String, String> mqRequest = createMqRequest(callJob, lotNo, finishFlag, firstCustomerPhoneMap, ngExpand);
            client.publish(MQConst.ADD_CUSTOMER_INFO_TO_JOB_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(2));
        }
    }

    /**
     * 创建消息队列请求
     *
     * @param callJob          呼叫作业对象
     * @param lotNo            批次号
     * @param finishFlag       完成标志
     * @param firstCustomerPhoneMap 首个客户电话号码映射
     * @param ngExpand         扩展信息对象
     * @return 包含消息队列请求参数的Map对象
     */
    private Map<String, String> createMqRequest(CcAiCallJob callJob, String lotNo, String finishFlag, Map<String, String> firstCustomerPhoneMap, NgExpand ngExpand) {
        Map<String, String> mqRequest = new HashMap<>();
        mqRequest.put("indexName", callJob.getCustomerIndex());
        mqRequest.put("lotNo", lotNo);
        mqRequest.put("jobId", callJob.getJobId());
        mqRequest.put("finishFlag", finishFlag);
        mqRequest.put("jobProgress", callJob.getJobProgress().toString());
        mqRequest.put("firstCustomerPhone", firstCustomerPhoneMap.get(callJob.getJobId()));
        mqRequest.put("userId", ngExpand.getUcUser().getUserId());
        return mqRequest;
    }

    /**
     * 在京东言犀平台创建外呼任务
     *
     * @param parentId                父任务的id
     * @param jdCallCustomerListGroup 根据schoolId分组后的客户信息
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Date 9:32 2022/10/6
     **/
    @Klock(name = "GLOBAL_CREATE_CALL_JOB_FORM_JDYX_LOCK", waitTime = 60, leaseTime = 50, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public Map<String, String> createJobFormVendor(CcAiCallJob parentJob, Map<String, List<JDCallCustomerRequest>> jdCallCustomerListGroup, NgExpand ngExpand, String callTaskType) {
        Map<String, String> firstCustomerPhoneMap = new HashMap<>();

        // 根据 callTaskType 进行分支处理
        switch (callTaskType) {
            case AI_ALL_TASK:
            case AI_SCHOOL_TASK:
                // 父任务查子任务列表
                String parentId = parentJob.getJobId();
                List<CcAiCallJob> subJobList = fetchChildJobList(parentId);

                // 处理子任务
                for (CcAiCallJob aiCallJob : subJobList) {
                    if (aiCallJob.getJobProgress() == 1 && aiCallJob.getVendorJobId() == 0) {
                        List<JDCallCustomerRequest> customerRequestList = jdCallCustomerListGroup.get(aiCallJob.getJobSchoolId());
                        if (customerRequestList == null || customerRequestList.isEmpty()) {
                            continue;
                        }

                        // 创建的时候加一条客户信息
                        JDCallCustomerRequest customerRequest = customerRequestList.get(0);
                        firstCustomerPhoneMap.put(aiCallJob.getJobId(), customerRequest.getPhone());
                        // 调用京东言犀的接口创建任务和更新数据库
                        this.createJobFromVendor(aiCallJob, new ArrayList<>(List.of(customerRequest)), ngExpand);
                    }
                }
                break;

            case AI_SS_TASK:
                // 直接使用入参中的父任务 parentJob
                if (!MapUtils.isEmpty(jdCallCustomerListGroup)) {
                    List<JDCallCustomerRequest> customerRequestList = jdCallCustomerListGroup.get(TOP_SCHOOL_ID);
                    if (customerRequestList != null && !customerRequestList.isEmpty()) {
                        JDCallCustomerRequest customerRequest = customerRequestList.get(0);
                        firstCustomerPhoneMap.put(parentJob.getJobId(), customerRequest.getPhone());
                        // 调用京东言犀的接口创建任务和更新数据库
                        this.createJobFromVendor(parentJob, new ArrayList<>(List.of(customerRequest)), ngExpand);
                    }
                }
                break;

            default:
                LOGGER.warn("创建任务并追加客户时，出现未知的callTaskType: {}", callTaskType);
                break;
        }

        return firstCustomerPhoneMap;
    }

    /**
     * 获取追加失败的客户手机号
     *
     * @param baseDTO 响应返回值
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Date 16:55 2022/8/23
     **/
    private Map<String, String> getInvalidPhoneList(JDBaseDTO baseDTO) {
        Map<String, String> invalidPhoneMap = new HashMap<>();
        if ("D3300".equals(baseDTO.getCode()) && null != baseDTO.getData()) {
            // 追加失败后,记录失败的客户信息
            Map<String, Object> responseMap = JSONUtils.toMap(JSONUtils.toJSONString(baseDTO.getData()));
            List<Map> invalidList = JSONUtils.toList(JSONUtils.toJSONString(responseMap.get("invalidList")), Map.class);
            if (CollectionUtils.isEmpty(invalidList)) {
                return invalidPhoneMap;
            }
            for (Map map : invalidList) {
                String phone = JDAESUtil.decrypt(map.get("phone").toString());
                invalidPhoneMap.put(phone, map.get("errorMsg").toString());
            }
        }
        return invalidPhoneMap;
    }

    /**
     * 分布式锁创建子任务
     *
     * @param parentCallJob                  父任务信息
     * @param jdCallCustomerRequestListGroup 分组后客户列表
     * <AUTHOR>
     * @since 11:05 2022/9/30
     **/
    @Klock(name = "GLOBAL_CREATE_SUB_CALL_JOB_LOCK", waitTime = 60, leaseTime = 50, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public void globalCreateSubCallJob(CcAiCallJob parentCallJob, Map<String, List<JDCallCustomerRequest>> jdCallCustomerRequestListGroup, String callTaskType) {
        String parentCallJobJobId = parentCallJob.getJobId();
        List<CcAiCallJob> callJobList = fetchChildJobList(parentCallJobJobId);
        Map<String, CcAiCallJob> schoolIdMap = createSchoolIdMap(callJobList);

        List<CcAiCallJob> saveCallJobList = new ArrayList<>(schoolIdMap.size());
        List<CcAiCallJob> updateCallJobList = new ArrayList<>(schoolIdMap.size());

        if (AI_SCHOOL_TASK.equals(callTaskType)) {
            processSchoolTasks(parentCallJob, jdCallCustomerRequestListGroup, schoolIdMap, parentCallJobJobId, saveCallJobList, updateCallJobList);
        } else if (AI_ALL_TASK.equals(callTaskType)) {
            processAllTasks(parentCallJob, jdCallCustomerRequestListGroup, callJobList, parentCallJobJobId, saveCallJobList, schoolIdMap, updateCallJobList);
        } else {
            throw new IllegalArgumentException("Invalid callTaskType: " + callTaskType);
        }

        saveOrUpdateJobs(updateCallJobList, saveCallJobList);
    }

    /**
     * 根据给定的外呼任务列表创建学校ID和外呼任务的映射关系。
     *
     * @param callJobList 外呼任务列表
     * @return 返回一个Map，其中键为学校ID，值为对应的外呼任务对象
     */
    public static Map<String, CcAiCallJob> createSchoolIdMap(List<CcAiCallJob> callJobList) {
        return callJobList.stream().collect(Collectors.toMap(CcAiCallJob::getJobSchoolId, Function.identity()));
    }

    /**
     * 根据父级外呼任务ID获取子级外呼任务列表
     *
     * @param parentCallJobJobId 父级外呼任务ID
     * @return 子级外呼任务列表
     */
    private List<CcAiCallJob> fetchChildJobList(String parentCallJobJobId) {
        return ccAiCallJobService.selectJobListByParentId(parentCallJobJobId);
    }

    /**
     * 保存或更新任务列表
     *
     * @param updateCallJobList 需要更新的任务列表
     * @param saveCallJobList   需要保存的任务列表
     */
    private void saveOrUpdateJobs(List<CcAiCallJob> updateCallJobList, List<CcAiCallJob> saveCallJobList) {
        // 更新客户数量
        ccAiCallJobService.updateBatchById(updateCallJobList);
        // 将任务信息添加到数据库
        ccAiCallJobService.saveBatch(saveCallJobList);
    }

    /**
     * 处理全国任务
     *
     * @param parentCallJob         父级外呼任务对象
     * @param jdCallCustomerRequestListGroup 包含学校ID和对应客户请求列表的映射
     * @param callJobList           待处理的外呼任务列表（此处未使用，可能用于其他逻辑）
     * @param parentCallJobJobId    父级外呼任务的Job ID
     * @param saveCallJobList       用于保存新创建的外呼任务对象的列表
     * @param schoolIdMap           包含学校ID和外呼任务对象的映射
     * @param updateCallJobList     用于保存需要更新的外呼任务对象的列表
     */
    private void processAllTasks(CcAiCallJob parentCallJob, Map<String, List<JDCallCustomerRequest>> jdCallCustomerRequestListGroup, List<CcAiCallJob> callJobList, String parentCallJobJobId, List<CcAiCallJob> saveCallJobList, Map<String, CcAiCallJob> schoolIdMap, List<CcAiCallJob> updateCallJobList) {
        String schoolId = TOP_SCHOOL_ID;
        if (CollectionUtils.isEmpty(callJobList)) {
            CcAiCallJob subAiCallJob = BeanUtils.copyFromObjToClass(CcAiCallJob.class, parentCallJob);
            subAiCallJob.setJobLevel(1);
            subAiCallJob.setStatus(AICallJobStatus.waiting.name());
            subAiCallJob.setParentJobId(parentCallJobJobId);
            // 获取Map的第一个元素（注意：Map没有固定的顺序，但这里我们假设是第一个被放入的元素）
            Map.Entry<String, List<JDCallCustomerRequest>> firstEntry = jdCallCustomerRequestListGroup.entrySet().iterator().next();
            // 获取第一个列表的size
            int firstListSize = firstEntry.getValue().size();
            subAiCallJob.setTotalTaskNum(firstListSize);
            String subSiJobId = nicelooIdTemplate.get("AiCallJob");
            subAiCallJob.setJobId(subSiJobId);
            // 获取第一个列表的key
            String firstKey = firstEntry.getKey();
            subAiCallJob.setJobSchoolId(firstKey);
            subAiCallJob.setJobSchoolName("全国");
            subAiCallJob.setJobName(subAiCallJob.getJobName() + "-[" + subAiCallJob.getJobSchoolName() + "]");
            saveCallJobList.add(subAiCallJob);
        } else {
            // 创建过的更新客户数量
            CcAiCallJob subAiCallJob = schoolIdMap.get(schoolId);
            // 获取Map的第一个元素（注意：Map没有固定的顺序，但这里我们假设是第一个被放入的元素）
            Map.Entry<String, List<JDCallCustomerRequest>> firstEntry = jdCallCustomerRequestListGroup.entrySet().iterator().next();
            // 获取第一个列表的size
            int firstListSize = firstEntry.getValue().size();
            subAiCallJob.setTotalTaskNum(firstListSize + subAiCallJob.getTotalTaskNum());
            subAiCallJob.setModifyDate(DateUtils.getNowDString());
            updateCallJobList.add(subAiCallJob);
        }
    }

    /**
     * 处理分校任务
     *
     * @param parentCallJob 父级外呼任务对象
     * @param jdCallCustomerRequestListGroup 包含学校ID和对应客户请求列表的映射
     * @param schoolIdMap 包含学校ID和外呼任务对象的映射
     * @param parentCallJobJobId 父级外呼任务的Job ID
     * @param saveCallJobList 用于保存新创建的外呼任务对象的列表
     * @param updateCallJobList 用于保存需要更新的外呼任务对象的列表
     */
    private void processSchoolTasks(CcAiCallJob parentCallJob, Map<String, List<JDCallCustomerRequest>> jdCallCustomerRequestListGroup, Map<String, CcAiCallJob> schoolIdMap, String parentCallJobJobId, List<CcAiCallJob> saveCallJobList, List<CcAiCallJob> updateCallJobList) {
        for (Map.Entry<String, List<JDCallCustomerRequest>> listEntry : jdCallCustomerRequestListGroup.entrySet()) {
            String schoolId = listEntry.getKey();
            List<JDCallCustomerRequest> customerRequestList = listEntry.getValue();
            // 添加还没有创建的外呼任务
            if (!schoolIdMap.containsKey(schoolId)) {
                CcAiCallJob subAiCallJob = BeanUtils.copyFromObjToClass(CcAiCallJob.class, parentCallJob);
                subAiCallJob.setJobLevel(1);
                subAiCallJob.setStatus(AICallJobStatus.waiting.name());
                subAiCallJob.setParentJobId(parentCallJobJobId);
                subAiCallJob.setTotalTaskNum(customerRequestList.size());
                String subSiJobId = nicelooIdTemplate.get("AiCallJob");
                subAiCallJob.setJobId(subSiJobId);
                JDCallCustomerRequest customerRequest = customerRequestList.get(0);
                subAiCallJob.setJobSchoolId(customerRequest.getSchoolId());
                subAiCallJob.setJobSchoolName(customerRequest.getSchoolName());
                subAiCallJob.setJobName(subAiCallJob.getJobName() + "-[" + subAiCallJob.getJobSchoolName() + "]");
                saveCallJobList.add(subAiCallJob);
            } else {
                // 创建过的更新客户数量
                CcAiCallJob subAiCallJob = schoolIdMap.get(schoolId);
                subAiCallJob.setTotalTaskNum(customerRequestList.size() + subAiCallJob.getTotalTaskNum());
                subAiCallJob.setModifyDate(DateUtils.getNowDString());
                updateCallJobList.add(subAiCallJob);
            }
        }
    }

    /**
     * 创建AI外呼任务并且更新到数据库
     * 报错时的message（创建京东言犀任务失败, 失败原因:后面的日志）
     * {"code":"D0001","data":null,"message":"{外呼线路}_线路500010500不存在，请联系言犀产品经理分配","state":0}
     * {"code":"D1000","data":null,"message":"正式任务不可选择测试线路","state":0}
     *
     * @param jdCreateJobParam 创建AI外呼任务参数
     * @param dbId             数据库主键id
     * @return java.lang.Integer
     * <AUTHOR>
     * @Date 15:47 2022/8/23
     **/
    private Integer createJobAndUpdateDB(JDCreateJobParam jdCreateJobParam, String dbId, NgExpand ngExpand) {
        CcAiCallJob aiCallJob = new CcAiCallJob();
        aiCallJob.setJobId(dbId);
        Integer vendorJobId = null;
        try {
            // 发送请求
            vendorJobId = this.createJob(jdCreateJobParam, ngExpand);
            aiCallJob.setVendorJobId(vendorJobId);
            aiCallJob.setJobProgress(2);
        } catch (Exception exception) {
            String message = exception.getMessage();
            String jsonErrorLog = ErrorLogUtil.formatErrorResponse(message);
            aiCallJob.setErrorLog(jsonErrorLog);
            aiCallJob.setJobProgress(3);
        }
        if (!aiCallJob.getJobProgress().equals(3)) {
            aiCallJob.setStatus(AICallJobStatus.waiting.name());
        }
        aiCallJob.setModifyDate(DateUtils.getNowDString());
        // 更新数据库外呼任务
        ccAiCallJobService.updateById(aiCallJob);

        CcAiCallJob ccAiCallJob = ccAiCallJobService.getById(dbId);
        // 给业务发任务状态变更MQ通知，学服主要用到jobProgress、errorLog字段
        String aiTaskStatusChange = JSONUtils.toJSONString(ccAiCallJob);
        LOGGER.info("给业务发任务状态变更MQ通知，通知内容是：{}", aiTaskStatusChange);
        client.publish(MQConst.AI_TASK_STATUS_CHANGE_TOPIC, UUID.randomUUID().toString(), aiTaskStatusChange);
        return vendorJobId;
    }

    /**
     * 更新外呼任务列表
     *
     * @param filteredCallJobList 外呼任务列表
     * @param ngExpand            登录用户信息
     * <AUTHOR>
     * @since 17:35 2022/8/13
     **/
    private void updateJobInfoList(List<CcAiCallJob> filteredCallJobList, NgExpand ngExpand, Integer tenantId) {
        // 任务id列表
        List<Integer> jobIdList = filteredCallJobList.stream().map(CcAiCallJob::getVendorJobId).collect(Collectors.toList());

        // 获取当前任务的话术模板
        List<JDContextListDTO> contextList = (tenantId == null) ? this.getContextList(null, ngExpand) : this.getContextListByTenantId(tenantId);
        Map<Integer, String> contextMap = contextList.stream().collect(Collectors.toMap(JDContextListDTO::getId, JDContextListDTO::getName));

        // 获取外呼任务信息，使用缓存
        List<JDJobReportDTO> jobReportList = getJobReportListFromCache(jobIdList, ngExpand);

        Map<Integer, JDJobReportDTO> jdJobReportDTOMap = jobReportList.stream()
                .collect(Collectors.toMap(JDJobReportDTO::getId, Function.identity(), (m1, m2) -> m1));

        // 更新任务信息
        for (CcAiCallJob ccAiCallJob : filteredCallJobList) {
            // 通过外呼任务ID进行比对(在京东言犀后台被删除的不能更新)
            if (jdJobReportDTOMap.containsKey(ccAiCallJob.getVendorJobId())) {
                JDJobReportDTO jdJobReportDTO = jdJobReportDTOMap.get(ccAiCallJob.getVendorJobId());
                String contextName = contextMap.get(ccAiCallJob.getContextId());
                ccAiCallJob.setContextName(contextName);
                jdJobReportDTO.updateCallInfo(ccAiCallJob);
            }
        }
    }

    /**
     * 从缓存中获取任务列表
     *
     * @param jobIdList    任务ID列表
     * @param ngExpand     扩展参数
     * @return 任务列表
     */
    private List<JDJobReportDTO> getJobReportListFromCache(List<Integer> jobIdList, NgExpand ngExpand) {
        List<JDJobReportDTO> jobReportList = new ArrayList<>();
        ValueOperations<String, String> valueOps = redisTemplate.opsForValue();

        // 缓存中存在的 jobId
        List<Integer> cachedJobIds = new ArrayList<>();

        // 检查缓存
        for (Integer jobId : jobIdList) {
            String cacheKey = RedisConst.AI_JOB_REPORT_LIST + jobId; // 每个 jobId 单独的缓存键
            String cachedData = valueOps.get(cacheKey);

            if (cachedData != null) {
                // 如果缓存存在，反序列化为对象并添加到结果列表
                jobReportList.add(JSONUtils.toObject(cachedData, JDJobReportDTO.class));
                cachedJobIds.add(jobId); // 记录已缓存的 jobId
            }
        }

        // 获取未缓存的 jobId
        List<Integer> uncachedJobIds = jobIdList.stream()
                .filter(jobId -> !cachedJobIds.contains(jobId))
                .collect(Collectors.toList());

        // 如果有未缓存的 jobId，从接口获取数据
        if (!uncachedJobIds.isEmpty()) {
            List<JDJobReportDTO> uncachedReports = this.getJobReportList(uncachedJobIds, ngExpand);
            for (JDJobReportDTO jobReportDTO : uncachedReports) {
                jobReportList.add(jobReportDTO);
                // 将结果存储到 Redis，设置缓存时间为 1 分钟
                String cacheKey = RedisConst.AI_JOB_REPORT_LIST + jobReportDTO.getId();
                valueOps.set(cacheKey, JSONUtils.toJSONString(jobReportDTO), 1, TimeUnit.MINUTES);
            }
        }

        return jobReportList;
    }

    /**
     * 调用接口发送请求
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    private JDBaseDTO sendRequest(String url, String params) {
        String response = okHttpUtil.post(url, params);
        if (StringUtils.isEmpty(response)) {
            logger.error("请求京东言犀接口出现异常,请求参数:{}", params);
            throw new ApplicationException(ApiErrorCodes.execute_failed_handler, "请求京东言犀接口出现异常");
        }
        JDBaseDTO baseDTO = JSONUtils.toObject(response, JDBaseDTO.class);
        String code = baseDTO.getCode();
        // 任务追加客户名单（名单列表形式）需要特殊处理的错误
        if ("D3300".equals(code) && null != baseDTO.getData()) {
            return baseDTO;
        }
        if (!"10000".equals(code)) {
            logger.error("请求京东言犀接口出现异常,请求参数:{}", params);
            throw new ApplicationException(ApiErrorCodes.execute_failed_handler, JSONUtils.toJSONString(baseDTO));
        }
        return baseDTO;
    }

    /**
     * 京东言犀
     * 根据NgExpand获取租户信息
     *
     * @param ngExpand 登录用户信息
     * @return tenantObject
     */
    public CallProperties.YanxiProperty.Tenant getTenantByToken(NgExpand ngExpand) {
        if (ngExpand == null || ngExpand.getUcUser() == null) {
            return null;
        }
        String userId = ngExpand.getUcUser().getUserId();
        CcCallAccountConfig callAccountConfig = ccCallAccountConfigMapper.searchCallAccountInfoByUserId(userId, CALL_TYPE_JDYX.getType());
        if (callAccountConfig == null) {
            return null;
        }
        logger.info("登录用户的账号信息:{}", callAccountConfig.toString());
        String tenantId = callAccountConfig.getTenantId();
        if (tenantId == null) {
            return null;
        }
        return yanxiProperty.getTenants()
                .stream().filter(e -> tenantId.equals(e.getTenantId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据租户标识获取租户信息
     *
     * @param tenantId 租户标识
     * @return tenant 租户配置信息
     */
    @Override
    public CallProperties.YanxiProperty.Tenant getTenantByTenantId(String tenantId) {
        return yanxiProperty.getTenants()
                .stream().filter(e -> tenantId.equals(e.getTenantId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 参数赋值
     *
     * @param jdCreateJobParam 京东接口调用参数
     * @param aiCallJob        任务实体
     * @param ngExpand         登录用户信息
     * <AUTHOR>
     * @Date 15:14 2022/8/23
     **/
    public void paramAssign(JDCreateJobParam jdCreateJobParam, CcAiCallJob aiCallJob, NgExpand ngExpand) {
        CallProperties.YanxiProperty.Tenant tenant = getTenantByToken(ngExpand);

        // defaultValue方法 处理过，这里覆盖更新，为了根据登录用户更新默认的botId
        jdCreateJobParam.setBotId(tenant.getBotId());
        jdCreateJobParam.setUserPin(tenant.getUserPin());
        jdCreateJobParam.setName(aiCallJob.getJobName());
        jdCreateJobParam.setDescription(aiCallJob.getDescription());
        jdCreateJobParam.setStartupMode("Manual");
        jdCreateJobParam.setDialTimeStyle("Workday");
        jdCreateJobParam.setJobOfflineTimes(JDCallPeriodParam.getPeriodList(aiCallJob.getJobOfflineWeek(), aiCallJob.getTimeBegin(), aiCallJob.getTimeEnd()));
        jdCreateJobParam.setLineId(aiCallJob.getLineId());
        jdCreateJobParam.setConcurrentNumber(aiCallJob.getConcurrency());
        jdCreateJobParam.setContextId(aiCallJob.getContextId());
        jdCreateJobParam.setRedial((1 == aiCallJob.getIsRedial()));
        if (jdCreateJobParam.getIsRedial()) {
            jdCreateJobParam.setRedialTimes(aiCallJob.getRedialTimes());
            jdCreateJobParam.setRedialInterval(aiCallJob.getRedialInterval());
            jdCreateJobParam.setRedialReason(aiCallJob.getRedialReason());
        }
        Map<String, String> jobPhoneCallMap = new HashMap<>();
        jobPhoneCallMap.put("innerPhoneNumber", aiCallJob.getDisplayNumber());
        jdCreateJobParam.setJobPhoneCalls(List.of(jobPhoneCallMap));
    }
}

