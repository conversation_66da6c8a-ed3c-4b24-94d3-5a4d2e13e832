package com.niceloo.cmc.ex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niceloo.cmc.ex.entity.CcCountday;
import com.niceloo.cmc.ex.mapper.CcCountdayMapper;
import com.niceloo.cmc.ex.service.CcCountdayEncryptService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.db.dynamic.core.DS;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 加密日统计表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-11-21 18:01:24
 */
@Service
@DS("cmc")
public class CcCountdayEncryptServiceImpl extends ServiceImpl<CcCountdayMapper, CcCountday> implements CcCountdayEncryptService {
    private static final Logger logger = LoggerFactory.getLogger(CcCountdayEncryptServiceImpl.class);

    @Resource
    private CcCountdayMapper ccCountdayMapper;

    /**
     * 对表中字段进行加密
     * 对输入的MySQL中的tableName的encryptedFlag为空记录的countcallerName字段进行加密，
     * 并将加密后的结果存入到入参tableName表的countcallerName字段中。
     * 加密方法采用 {@link com.niceloo.cmc.ex.utils.FieldCipherUtil#encrypt(String)} 。
     * 加密完成，将加密记录的encryptedFlag字段修改为1，每批次处理batch条记录，批次间sleep 2000ms
     * 采用异步方式处理，加密开始后即返回“已开始加密"的提示，无需等待加密完成
     * 另外，目前CcCountmonth有219384条记录，CcCountday表有3809167条记录，处理中打印处理日志，便于进度监控
     *
     * @param batch 批次号
     * @return 返回加密后的字符串，如果返回null则表示加密失败
     */
    @Override
    public void encryptTable(int batch) {
        logger.info("加密开始，表名：CcCountday, 开始时间：{}", DateUtils.getNowDString());

        processBatch(batch);

        logger.info("加密完成，表名：CcCountday, 完成时间：{}", DateUtils.getNowDString());
    }

    /**
     * 处理批次中的记录。
     *
     * @param batch     每批次处理数量
     */
    private void processBatch(int batch) {
        // 从tableName表中获取encryptedFlag为空的记录
        // 将加密后的结果更新到countcallerName字段中
        List<CcCountday> entitiesToUpdate = new ArrayList<>();
        String encryptedFlagValue = "1";
        LambdaQueryWrapper<CcCountday> wrapperCcCountday = new LambdaQueryWrapper<>();
        wrapperCcCountday.isNull(CcCountday::getEncryptedFlag).
                last("LIMIT 0," + batch);
        List<CcCountday> ccCountdays = ccCountdayMapper.selectList(wrapperCcCountday);

        // 更新表CcCountday的encryptedFlag字段为1
        for (CcCountday ccCountday : ccCountdays) {
            String countcallerName = ccCountday.getCountcallerName();
            if (!FieldCipherUtil.checkEncrypt(countcallerName)) {
                String encryptedCountcallerName = FieldCipherUtil.encrypt(ccCountday.getCountcallerName());
                ccCountday.setCountcallerName(encryptedCountcallerName);
            }
            ccCountday.setEncryptedFlag(encryptedFlagValue);
            entitiesToUpdate.add(ccCountday);
        }

        // 批量更新数据库
        this.updateBatchById(entitiesToUpdate);
    }
}
