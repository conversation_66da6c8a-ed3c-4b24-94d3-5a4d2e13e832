package com.niceloo.cmc.ex.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.niceloo.cmc.ex.service.WechatGroupRobotService;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * 企业微信机器人推送配置类
 * <AUTHOR>
 * @since 2024-07-27 16:20:07
 */
@CustomLog
@Component
public class WechatGroupRobotServiceImpl implements WechatGroupRobotService {

    /**  是否启用企业微信预警  */
    @Value("${wechat.enabled}")
    public boolean wechatEnabled;

    /**  企业微信机器人请求地址  */
    @Value("${wechat.bot_url}")
    public String webHook;

    /**  手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人  */
    @Value("${wechat.mentioned_mobile_list}")
    public String mentionedMobileList;


    /**  消息类型  */
    public class MsgType {

        /** 文本类型 */
        public static final String TEXT = "text";

        /** markdown类型 */
        public static final String MARKDOWN = "markdown";

        /** 图片类型 */
        public static final String IMAGE = "image";

        /** 图文类型 */
        public static final String NEWS = "news";

    }

    /**
     * 向指定webhook发送消息
     *
     * @param message 要发送的消息内容
     */
    @Override
    public void sendMessage(String message) {
        LOGGER.info(wechatEnabled + "  " + webHook);
        if (!wechatEnabled) {
            LOGGER.warn("企业微信预警未启用，不进行推送！");
            return;
        }
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String data = generateTextMessageJSON(message, mentionedMobileList);

        HttpEntity<String> entity = new HttpEntity<>(data, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(webHook, entity, String.class);

        if (response.getStatusCodeValue() == 200) {
            // 解析返回值 JSON 内容
            JSONObject responseBody = JSON.parseObject(response.getBody());
            int errcode = responseBody.getInteger("errcode");
            if (errcode == 0) {
                LOGGER.debug("Message sent successfully");
            } else {
                String errmsg = responseBody.getString("errmsg");
                LOGGER.warn("Failed to send message, errcode: {}, errmsg: {}", errcode, errmsg);
            }
        } else {
            LOGGER.warn("HTTP request failed with status code: {}", response.getStatusCodeValue());
        }
    }

    /**
     * 生成消息JSON
     *
     * @param content 消息内容
     * @param mentionedMobileList 被提及的用户列表
     * @return 返回生成的JSON字符串
     * @throws RuntimeException 当生成JSON出现异常时抛出
     */
    public static String generateTextMessageJSON(String content, String mentionedMobileList) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode dataNode = mapper.createObjectNode();
        ObjectNode textNode = mapper.createObjectNode();

        textNode.put("content", content.replace("\\n", "\n")); // 内部处理换行符
        if (StringUtils.isNotEmpty(mentionedMobileList)) {
            String[] mentionedUsers = mentionedMobileList.split(",");
            ArrayNode mentionedArray = mapper.createArrayNode();
            for (String user : mentionedUsers) {
                mentionedArray.add(user.trim());
            }
            textNode.set("mentioned_mobile_list", mentionedArray);
        }

        dataNode.put("msgtype", MsgType.TEXT.toString());
        dataNode.set("text", textNode);

        try {
            return mapper.writeValueAsString(dataNode);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error generating JSON", e);
        }
    }

}
