package com.niceloo.cmc.ex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niceloo.cmc.ex.entity.CcCallAccountModifyLog;
import com.niceloo.cmc.ex.entity.CcCountmonth;
import com.niceloo.cmc.ex.mapper.CcCallAccountModifyLogMapper;
import com.niceloo.cmc.ex.mapper.CcCountmonthMapper;
import com.niceloo.cmc.ex.service.CcCountmonthEncryptService;
import com.niceloo.cmc.ex.service.ModifyLogEncryptService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.db.dynamic.core.DS;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 加密表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21 18:01:24
 */
@Service
@DS("cmc")
public class ModifyLogEncryptServiceImpl extends ServiceImpl<CcCallAccountModifyLogMapper, CcCallAccountModifyLog> implements ModifyLogEncryptService {
    private static final Logger logger = LoggerFactory.getLogger(ModifyLogEncryptServiceImpl.class);

    @Resource
    private CcCallAccountModifyLogMapper ccCallAccountModifyLogMapper;

    @Override
    public void encryptCcCallAccountModifyLog(int batch) {
        // 从表中获取记录
        // 将加密后的结果更新到字段中
        List<CcCallAccountModifyLog> entitiesToUpdate = new ArrayList<>();

        LambdaQueryWrapper<CcCallAccountModifyLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.last("LIMIT 0," + batch);
        List<CcCallAccountModifyLog> ccCallAccountModifyLogs = ccCallAccountModifyLogMapper.selectList(wrapper);

        // 更新表
        for (CcCallAccountModifyLog ccCallAccountModifyLog : ccCallAccountModifyLogs) {
            String beforeEeUserName = ccCallAccountModifyLog.getBeforeEeUserName();
            if (!FieldCipherUtil.checkEncrypt(beforeEeUserName)) {
                String encryptedBeforeEeUserName = FieldCipherUtil.encrypt(ccCallAccountModifyLog.getBeforeEeUserName());
                ccCallAccountModifyLog.setBeforeEeUserName(encryptedBeforeEeUserName);
            }
            String afterEeUserName = ccCallAccountModifyLog.getAfterEeUserName();
            if (!FieldCipherUtil.checkEncrypt(afterEeUserName)) {
                String encryptedAfterEeUserName = FieldCipherUtil.encrypt(ccCallAccountModifyLog.getAfterEeUserName());
                ccCallAccountModifyLog.setAfterEeUserName(encryptedAfterEeUserName);
            }
            String accountLogCreatorName = ccCallAccountModifyLog.getAccountLogCreatorName();
            if (!FieldCipherUtil.checkEncrypt(accountLogCreatorName)) {
                String encryptedAccountLogCreatorName = FieldCipherUtil.encrypt(ccCallAccountModifyLog.getAccountLogCreatorName());
                ccCallAccountModifyLog.setAccountLogCreatorName(encryptedAccountLogCreatorName);
            }

            entitiesToUpdate.add(ccCallAccountModifyLog);
        }

        // 批量更新数据库
        if (!entitiesToUpdate.isEmpty()) {
            this.updateBatchById(entitiesToUpdate);
        }
    }
}
