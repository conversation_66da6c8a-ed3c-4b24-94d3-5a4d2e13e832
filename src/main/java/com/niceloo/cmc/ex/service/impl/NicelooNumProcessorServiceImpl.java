package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.common.UCInterfaceTypeEnum;
import com.niceloo.cmc.ex.feign.UserFeignClient;
import com.niceloo.cmc.ex.pojo.vo.CallRecordsListVO;
import com.niceloo.cmc.ex.pojo.vo.YouLooVO;
import com.niceloo.cmc.ex.service.NicelooNumProcessorService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 优路号处理服务类
 * @author: <PERSON><PERSON><PERSON>yu
 * @create: 2022-03-07 15:53
 */
@Service
public class NicelooNumProcessorServiceImpl implements NicelooNumProcessorService {
    private static final Logger logger = LoggerFactory.getLogger(NicelooNumProcessorServiceImpl.class);

    @Resource
    private UserFeignClient userFeignClient;

    /**
     * 获取客户的手机号根据优路号
     *
     * @return java.lang.String
     * @paramter youluNum 优路号
     * <AUTHOR>
     * @Date 16:00 2022/3/7
     **/
    @Override
    public String getUserMobileByYouluNum(String youluNum) {
        //根据优路号调用用户中心接口获取用户ID
        String userId = this.getUserIdByYouluNum(youluNum);
        if (null == userId || "".equals(userId)) {
            return null;
        }
        //调用接口获取用户手机号
        return this.getMobileByUserIds(userId);
    }

    /**
     * 根据优路号调用用户中心接口获取用户ID
     *
     * @return java.lang.String
     * @paramter youluNum
     * <AUTHOR>
     * @Date 16:06 2022/3/7
     **/
    public String getUserIdByYouluNum(String youluNum) {
        //调用接口获取用户ID
        List<YouLooVO> youLooVos;
        try {
            youLooVos = this.callUserCenterBase(youluNum, UCInterfaceTypeEnum.YOULOO_GET_USER_ID);
        } catch (Exception e) {
            logger.error(e, e.toString());
            throw new RuntimeException("通讯中心话务查询失败: 调用用户中心接口-->根据优路号列表获取用户ID列表", e);
        }
        if (null != youLooVos && youLooVos.size() > 0) {
            return youLooVos.get(0).getUserId();
        }
        return null;
    }

    /**
     * 调用接口获取用户手机号
     *
     * @return java.lang.String
     * @paramter userId
     * <AUTHOR>
     * @Date 16:06 2022/3/7
     **/
    public String getMobileByUserIds(String userId) {
        //调用接口获取用户手机号
        List<YouLooVO> youLooVos;
        try {
            youLooVos = this.callUserCenterBase(userId, UCInterfaceTypeEnum.USER_ID_GET_MOBILE);
        } catch (Exception e) {
            logger.error(e, e.toString());
            throw new RuntimeException("通讯中心话务查询失败: 调用用户中心接口-->根据用户ID列表获取用户信息列表", e);
        }
        if (null != youLooVos && youLooVos.size() > 0) {
            return youLooVos.get(0).getUserMobile();
        }
        return null;
    }

    /**
     * 调用接口获取用户优路号列表
     *
     * @param mobiles 手机号列表
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.YouLooVO> 用户优路信息Vo
     * <AUTHOR>
     * @Date 14:27 2021/9/16
     **/
    public List<YouLooVO> getYouluNumByMobiles(String mobiles) {
        //调用接口获取用户优路号列表
        List<YouLooVO> youLooVos;
        try {
            youLooVos = this.callUserCenterBase(mobiles, UCInterfaceTypeEnum.MOBILE_GET_YOULOO);
        } catch (Exception e) {
            logger.error(e, e.toString());
            throw new RuntimeException("通讯中心话务查询失败: 调用用户中心接口-->根据用户ID列表获取用户信息列表", e);
        }
        return youLooVos;
    }

    /**
     * 添加优路号到列表
     *
     * @paramter callRecordsListVOS
     * <AUTHOR>
     * @Date 17:43 2022/3/7
     **/
    @Override
    public void superadditionYouluNum(List<CallRecordsListVO> callRecordsListVOS) {
        if (CollectionUtils.isEmpty(callRecordsListVOS)) {
            return;
        }
        //拼接根据手机号列表批量查询优路号的参数,同时解密客户姓名和客户手机号
        StringBuilder receiverPhones = this.getYouluNumByMobilesParamJoint(callRecordsListVOS);
        if (receiverPhones.length() <= 0) {
            return;
        }
        receiverPhones.deleteCharAt(receiverPhones.length() - 1);
        //查询优路号
        List<YouLooVO> youLooVos = this.getYouluNumByMobiles(receiverPhones.toString());
        if (CollectionUtils.isEmpty(youLooVos)) {
            return;
        }
        //追加优路号具体细节执行
        this.superadditionYouluNumDetail(youLooVos, callRecordsListVOS);
    }

    /**
     * 调用用户中心接口基本执行方法
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.YouLooVO>
     * @paramter params
     * @paramter typeEnum
     * <AUTHOR>
     * @Date 16:26 2022/3/7
     **/
    private List<YouLooVO> callUserCenterBase(String params, UCInterfaceTypeEnum typeEnum) {
        //拼接参数
        String requestParam = this.getCallRequestParam(params, typeEnum);
        //调用用户中心接口
        Object response = null;
        switch (typeEnum) {
            case YOULOO_GET_USER_ID: {
                response = userFeignClient.getUserIdByYouluNum(requestParam);
                break;
            }
            case USER_ID_GET_MOBILE: {
                response = userFeignClient.getMobileByUserIds(requestParam);
                break;
            }
            case MOBILE_GET_YOULOO: {
                response = userFeignClient.getYouluNumByMobiles(requestParam);
                break;
            }
        }
        //处理接口返回值并返回
        return this.objectToYouLooVoConverter(response, typeEnum);
    }

    /**
     * 拼接优路号json字符串
     *
     * @return java.lang.String
     * @paramter params
     * @paramter typeEnum
     * <AUTHOR>
     * @Date 16:25 2022/3/7
     **/
    private String getCallRequestParam(String params, UCInterfaceTypeEnum typeEnum) {
        if (null == params || "".equals(params)) {
            return null;
        }
        StringBuilder str = new StringBuilder();
        //拼接JSON前半部分
        switch (typeEnum) {
            case YOULOO_GET_USER_ID:
                str.append("{\"youluNums\":[");
                break;
            case USER_ID_GET_MOBILE:
                str.append("{\"userIds\":[");
                break;
            case MOBILE_GET_YOULOO:
                str.append("{\"userMobiles\":[");
                break;
        }
        String[] split = params.split(",");
        //拼接参数数值
        for (String s : split) {
            str.append("\"").append(s).append("\"").append(",");
        }
        str.deleteCharAt(str.length() - 1);
        //拼接JSON后半部分
        switch (typeEnum) {
            case YOULOO_GET_USER_ID:
            case USER_ID_GET_MOBILE:
                str.append("]}");
                break;
            case MOBILE_GET_YOULOO:
                str.append("],\"userFlag\":\"S\"}");
                break;
        }
        return str.toString();
    }

    /**
     * 解析返回值
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.YouLooVO>
     * @paramter object
     * @paramter typeEnum
     * <AUTHOR>
     * @Date 16:24 2022/3/7
     **/
    private List<YouLooVO> objectToYouLooVoConverter(Object object, UCInterfaceTypeEnum typeEnum) {
        //需要根据调用不同的接口分别解析返回值
        switch (typeEnum) {
            case YOULOO_GET_USER_ID: {
                return this.manageCallUserCenterResult1(object);
            }
            case USER_ID_GET_MOBILE:
            case MOBILE_GET_YOULOO: {
                return this.manageCallUserCenterResult2(object);
            }
        }
        return new ArrayList<>();
    }

    /**
     * 处理调用用户中心接口返回值1
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.YouLooVO>
     * <AUTHOR>
     * @Date 9:55 2021/9/17
     * @see UCInterfaceTypeEnum#YOULOO_GET_USER_ID
     **/
    private List<YouLooVO> manageCallUserCenterResult1(Object object) {
        List<YouLooVO> youLooVos = new ArrayList<>();
        if (object instanceof Collection) {
            List<LinkedHashMap> result = (List) object;
            for (LinkedHashMap o : result) {
                String userId = null == o.get("userId") ? null : o.get("userId").toString();
                String userYoulunum = null == o.get("userYoulunum") ? null : o.get("userYoulunum").toString();
                YouLooVO youLooVo = new YouLooVO(userId, userYoulunum);
                youLooVos.add(youLooVo);
            }
        }
        return youLooVos;
    }


    /**
     * 处理调用用户中心接口返回值2
     *
     * @param object 用户中心返回的结果
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.YouLooVO>
     * <AUTHOR>
     * @Date 10:02 2021/9/17
     * @see UCInterfaceTypeEnum#USER_ID_GET_MOBILE
     * @see UCInterfaceTypeEnum#MOBILE_GET_YOULOO
     **/
    private List<YouLooVO> manageCallUserCenterResult2(Object object) {
        List<YouLooVO> youLooVos = new ArrayList<>();
        if (object instanceof Map) {
            object = ((Map<?, ?>) object).get("data");
            if (object instanceof Collection) {
                List<LinkedHashMap> result = (List) object;
                for (LinkedHashMap o : result) {
                    String userId = null == o.get("userId") ? null : o.get("userId").toString();
                    String userYoulunum = null == o.get("userYoulunum") ? null : o.get("userYoulunum").toString();
                    String userMobile = null == o.get("userMobile") ? null : o.get("userMobile").toString();
                    YouLooVO youLooVo = new YouLooVO(userId, userYoulunum, userMobile);
                    youLooVos.add(youLooVo);
                }
            }
        }
        return youLooVos;
    }

    /**
     * 拼接根据手机号列表批量查询优路号的参数
     * 同时解密客户姓名和客户手机号
     *
     * @param callRecordsListVOS 缺少优路号的通话记录列表
     * @return java.lang.StringBuilder
     * <AUTHOR>
     * @Date 14:10 2021/11/13
     **/
    private StringBuilder getYouluNumByMobilesParamJoint(List<CallRecordsListVO> callRecordsListVOS) {
        //拼接参数
        StringBuilder receiverPhones = new StringBuilder();
        for (CallRecordsListVO recordsListVO : callRecordsListVOS) {
            recordsListVO.setReciverName(FieldCipherUtil.decrypt(recordsListVO.getReciverName()));
            recordsListVO.setYouluNum("");
            String receiverPhone = recordsListVO.getReciverPhone();
            if (StringUtils.isEmpty(receiverPhone)) {
                continue;
            }
            receiverPhone = FieldCipherUtil.decrypt(receiverPhone);
            recordsListVO.setReciverPhone(receiverPhone);
            receiverPhones.append(receiverPhone).append(",");
        }
        return receiverPhones;
    }

    /**
     * 追加优路号具体细节
     *
     * @param youLooVos          优路号信息列表
     * @param callRecordsListVOS 缺少优路号的通话记录列表
     * <AUTHOR>
     * @Date 14:24 2021/11/13
     **/
    private void superadditionYouluNumDetail(List<YouLooVO> youLooVos, List<CallRecordsListVO> callRecordsListVOS) {
        //将list转为key为手机号的map方便进行赋值,手机号如果相同进行覆盖
        Map<String, YouLooVO> map = youLooVos.stream().collect(Collectors.toMap(YouLooVO::getUserMobile, Function.identity(), (key1, key2) -> key2));
        for (CallRecordsListVO recordsListVO : callRecordsListVOS) {
            if (StringUtils.isNotEmpty(recordsListVO.getReciverPhone())) {
                YouLooVO youLooVO = map.get(recordsListVO.getReciverPhone());
                if (null != youLooVO) {
                    recordsListVO.setYouluNum(youLooVO.getUserYoulunum());
                }
            }
        }
    }
}
