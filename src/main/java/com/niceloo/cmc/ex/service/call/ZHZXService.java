package com.niceloo.cmc.ex.service.call;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.entity.es.CallRecording;
import com.niceloo.cmc.ex.pojo.dto.AccountDTO;
import com.niceloo.cmc.ex.pojo.dto.YKCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.dto.ZHZXCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.request.GenerateCallRecordRequest;
import com.niceloo.cmc.ex.pojo.request.ZHZXSendSmsRequest;
import com.niceloo.cmc.ex.pojo.request.ZHZXSetupWhiteListRequest;
import com.niceloo.cmc.ex.pojo.response.ZhongHongApiResponse;
import com.niceloo.cmc.ex.service.BdCallaccountinfoService;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.CallRecordingService;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.service.impl.CallRecordingServiceImpl;
import com.niceloo.cmc.ex.utils.*;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.mq.client.Client;
import okhttp3.Headers;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.*;

import static com.niceloo.cmc.ex.common.BizConst.CALL_RECORD_ES_PREFIX;
import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_ZHZX;

/**
 * 中弘智享厂商服务
 *
 * <AUTHOR>
 * @since 2023-07-22 14:14:40
 */
@Service
public class ZHZXService extends CallRecordsBaseService {
    private static final Logger logger = LoggerFactory.getLogger(ZHZXService.class);

    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);
    private final StringRedisTemplate redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
    private final BdCallaccountinfoService bdCallaccountinfoService = SpringUtils.getBean(BdCallaccountinfoService.class);
    private final CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);
    private final CallRecordingService callRecordingService = SpringUtils.getBean(CallRecordingServiceImpl.class);
    private final Client client = SpringUtils.getBean(Client.class);
    public static final Map<String, String> HEADER = Map.of("Content-Type", "application/json;charset=utf-8", "Accept", "application/json");
    public static final Map<String, String> PARAM = Map.of("debug", "true", "lang", "zh_CN");

    /**
     * 返回一个包含 HTTP 请求头的映射。
     *
     * @param apiKey 要包含在请求头中的 API key（可以为 null 或空字符串）
     *
     * @param apiToken 要包含在请求头中的 API token（可以为 null 或空字符串）
     *
     * @param msgdst 要包含在请求头中的消息目的地（可以为 null 或空字符串）
     *
     * @return 包含 HTTP 请求头的映射。
     * <AUTHOR>
     * @since 2023-07-22 15:19:25
     **/
    public Map<String, String> getHeader(String apiKey, String apiToken, String msgdst) {
        Map<String, String> headers = new HashMap<>();
        if (StringUtils.isNotEmpty(apiKey)) {
            headers.put("api-key", apiKey);
        }
        if (StringUtils.isNotEmpty(apiToken)) {
            headers.put("api-token", apiToken);
        }
        if (StringUtils.isNotEmpty(msgdst)) {
            headers.put("msgdst", msgdst);
        }
        return headers;
    }

    /**
     * 接收智能外呼MQ外呼成功后返回的主叫被叫信息生成缺少通话信息的通话记录
     *
     * @param request 主被叫信息
     * <AUTHOR>
     * @since 2023-08-09 10:29:57
     **/
    public void generateCallRecord(GenerateCallRecordRequest request) {
        // 生成通话记录保存到ElasticSearch
        Map<String, Object> mqRequest = super.commonGenerateCallRecord(request);
        if (mqRequest != null) {
            String[] s = request.getCallAccount().split("_");
            mqRequest.put("accountId", s[0]);
            // 推送到延时队列30分钟后去查询此通通话记录是否被补充完整，如果还没有补充完整，则调用查询接口去查询更新
            client.publish(MQConst.ACTIVE_QUERY_RECORD_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(30));
        }
    }

    /**
     * 回调：补充通话记录信息
     *
     * @param zhzxCallRecordsDTO 通话记录实体
     * <AUTHOR>
     * @since 2023-08-10 18:22:45
     **/
    public void supplementaryCallRecordInfoToES(ZHZXCallRecordsDTO zhzxCallRecordsDTO) {
        Date callTime = DateUtil.getFirstNonNullDate(
                zhzxCallRecordsDTO.getCallerAlertTime(),
                zhzxCallRecordsDTO.getCallerAnswerTime(),
                zhzxCallRecordsDTO.getCalleeStartTime(),
                zhzxCallRecordsDTO.getCalleeAlertTime(),
                zhzxCallRecordsDTO.getCalleeAnswerTime(),
                zhzxCallRecordsDTO.getHangupTime()
        );
        String index = RecordUtil.getRecordIndexName(DateUtils.toStr(callTime), DateUtil.YMD_HMS);
        String uniqueId = zhzxCallRecordsDTO.getSessionId();
        // 查询ES验证是否已经生成此条通话记录(学员回拨呼入没有通话记录，需要去创建)
        CallRecord callRecord1 = callRecordService.querySupplementaryFieldByUniqueId(index,
                CALL_TYPE_ZHZX.getType(), uniqueId);
        if (null == callRecord1) {
            // 生成通话记录
            this.generateCallInCallRecord(zhzxCallRecordsDTO);
            return;
        }
        // 表示已经补充过通话信息了
        if (callRecord1.getDuration() != -1) {
            return;
        }
        CallRecord callRecord = zhzxCallRecordsDTO.callRecordsConverter();
        String callId = callRecord1.getCallId();
        Date callTimeFromCallback = callRecord.getCallTime();
        Date callTimeFromRecord = callRecord1.getCallTime();
        // 如果通话回调中的呼叫时间为null，则使用通话记录中的呼叫时间
        if (callTimeFromCallback == null) {
            callRecord.setCallTime(callTimeFromRecord);
        }
        callRecord.setCallAccount(null);
        callRecord.setAgentId(null);
        callRecord.setCreatedTime(null);
        // 补充外呼通话信息到ES
        Map<String, Object> recordingData = super.updateCallRecord(callRecord, callId);
        if (null != recordingData) {
            // 发送下载录音MQ
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordingData));
        }
    }

    /**
     * 生成呼入的通话记录
     *
     * @param zhzxCallRecordsDTO 通话记录
     * <AUTHOR>
     * @since 2023-08-11 14:57:16
     **/
    private void generateCallInCallRecord(ZHZXCallRecordsDTO zhzxCallRecordsDTO) {
        CallChannelEnum callChannel = CALL_TYPE_ZHZX;
        String userData = zhzxCallRecordsDTO.getSessionId();
        String uniqueKey = zhzxCallRecordsDTO.getCalleeANum() + ":" + zhzxCallRecordsDTO.getSessionId();
        // 校验此条通话记录是否已经生成
        boolean existRecord = super.isNotExistRecord(userData, uniqueKey, callChannel);
        if (!existRecord) {
            return;
        }
        CallRecord callRecord = new CallRecord();
        Date callTime = DateUtil.getFirstNonNullDate(
                zhzxCallRecordsDTO.getCallerAlertTime(),
                zhzxCallRecordsDTO.getCallerAnswerTime(),
                zhzxCallRecordsDTO.getCalleeStartTime(),
                zhzxCallRecordsDTO.getCalleeAlertTime(),
                zhzxCallRecordsDTO.getCalleeAnswerTime(),
                zhzxCallRecordsDTO.getHangupTime()
        );
        String index = CALL_RECORD_ES_PREFIX + DateUtils.toStr(callTime, "yyyyMM");
        try {
            callRecord = zhzxCallRecordsDTO.callRecordsConverter();
            //补充空值
            new YKCallRecordsDTO().replenishEmpty(callRecord);
            //补充员工信息
            super.setEE(zhzxCallRecordsDTO.getCallerAlertTime(), callChannel.getType(), callRecord);
            //补充客户信息
            super.addCustomerInfoByPhone(callRecord);
            // 校验是否有此索引，保存到ES内
            this.generateCallRecordToES(index, callRecord);
        } catch (Exception e) {
            logger.error(e, "生成客户呼入中弘智享通话记录失败,异常信息:{},呼入回调参数:{}", e.getMessage(), JSONUtils.toJSONString(zhzxCallRecordsDTO));
            // 失败后删除去重ID
            String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + callChannel.getType() + ":" + uniqueKey;
            redisTemplate.delete(redisKeyPrefix);
            return;
        }
        // 创建成功后发送MQ下载录音
        if (callRecord.getDuration() > 0 && StringUtils.isNotEmpty(callRecord.getVoiceSourceUrl())) {
            Map<String, Object> data = new HashMap<>(5);
            data.put("channelType", callChannel.getType());
            data.put("indexName", index);
            data.put("voiceSourceUrl", callRecord.getVoiceSourceUrl());
            data.put("callId", callRecord.getCallId());
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data));
        }
    }

    /**
     * 接收MQ->下载通话录音并更新ES
     *
     * @param map 通话记录信息
     * <AUTHOR>
     * @since 11:42 2022/7/1
     **/
    @Override
    public boolean downLoadRecording(Map<String, Object> map) {
        boolean flag = super.downLoadRecording(map);
        // 下载失败后放入ES CallRecording索引内
        if (!flag) {
            String indexName = map.get("indexName").toString();
            String callId = map.get("callId").toString();
            String voiceSourceUrl = map.get("voiceSourceUrl").toString();
            CallRecording callRecording = CallRecording.initialize();
            callRecording.setCallId(callId);
            String date = RecordUtil.getDateByIndexName(indexName);
            callRecording.setDate(date);
            callRecording.setVoiceSourceUrl(voiceSourceUrl);
            callRecording.setChannelType("ZHZX");
            callRecording.setDuration(20);
            try {
                callRecordingService.save(callRecording);
            } catch (Exception ex) {
                logger.error(ex, "保存中弘智享通话录音到索引callRecording出现异常,callId:{},异常信息:{}", callId, ex.getMessage());
            }
        }
        return flag;
    }

    /**
     * 根据员工标识获取中弘智享通讯账号。
     *
     * @param eeId 员工标识
     * @return 中弘智享通讯账号信息
     */
    private AccountDTO getZhzxAccountByEeId(String eeId) {
        // 从通讯账号配置表中获取当前用户归属分校的API-KEY和API-TOKEN
        String accountName = bdCallaccountinfoService.selectAccountNameByBdEeId(eeId, "ZHZX");
        List<AccountDTO> zhzxAccounts = bdCallaccountinfoService.selectUniqueAccountsByType("ZHZX");

        // 返回当前用户对应的中弘智享通讯账号
        return zhzxAccounts.stream()
                .filter(accountDTO -> accountName.equals(accountDTO.getAccountName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 向中弘智享平台请求发送短信验证码。
     *
     * @param zhzxSendSmsRequest 包含短信信息的请求对象
     * @param eeId 员工标识
     *
     * @return 中弘智享平台返回的响应
     */
    public String sendSms(ZHZXSendSmsRequest zhzxSendSmsRequest, String eeId) throws ApplicationException {
        // 中弘智享平台的请求地址
        String zhzxRequestUrl = CallProperties.ZhzxProperty.HOST + CallProperties.ZhzxProperty.SEND_SMS;
        AccountDTO currAccount = this.getZhzxAccountByEeId(eeId);

        if (ObjectUtils.isEmpty(currAccount)) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided,
                    "未查询到当前用户的中弘智享通讯账号，请在通讯中心-通讯设置-中弘智享账号设置 页面 配置中弘智享通讯账号");
        }

        // 发送请求并返回响应
        return sendRequest(zhzxSendSmsRequest, currAccount, zhzxRequestUrl);
    }

    /**
     * 发送请求并返回响应。
     *
     * @param zhzxSendSmsRequest 包含短信信息的请求对象
     * @param currAccount 中弘智享通讯账号信息
     * @param zhzxRequestUrl 中弘智享请求地址
     *
     * @return 中弘智享平台返回的响应
     */
    private String sendRequest(ZHZXSendSmsRequest zhzxSendSmsRequest, AccountDTO currAccount, String zhzxRequestUrl) {
        Map<String, String> header = this.getHeader(currAccount.getApiAccount(), currAccount.getApiSecret(), null);
        Headers headers = Headers.of(header);
        String vid = "";
        String resultStr = okHttpUtil.post(zhzxRequestUrl, JSONUtils.toJSONString(zhzxSendSmsRequest), headers);

        // 如果resultStr不为空，且返回值中，status_code=200，则获取接口返回值中的vid
        if (StringUtils.isNotEmpty(resultStr)) {
            Map<String, Object> resultMap = JSONUtils.toMap(resultStr);
            int statusCode = ObjectUtils.isEmpty(resultMap.get("status_code")) ? 0 : Integer.parseInt(resultMap.get("status_code").toString());
            if (statusCode == 200) {
                String msg = resultMap.get("message").toString();
                if( !"发送成功".equals(msg)) {
                    throw new ApplicationException(ApiErrorCodes.execute_failed_handler, msg);
                } else {
                    Object dataObj = resultMap.get("data");
                    if (!ObjectUtils.isEmpty(dataObj)) {
                        // 直接将 dataObj 转换为 Map
                        Map<String, Object> data = (Map<String, Object>) dataObj;
                        vid = ObjectUtils.isEmpty(data.get("vid")) ? "" : data.get("vid").toString();
                    }
                }

                // 输出 vid 的值
                logger.info("短信验证码发送成功，中弘智享返回的vid：{}", vid);

                // 将 vid 的值存入Redis缓存
                String key = RedisConst.ZHZX_SMS_VID + ":" + zhzxSendSmsRequest.getPhone();

                // 中弘智享验证码有效期5分钟，设置识别码vid为5分钟多5秒，
                // 加白时的入参vid即可使用缓存中的vid，也可据此判断验证码是否因为已失效而不可用
                redisTemplate.opsForValue().set(key, vid, Duration.ofSeconds(Long.parseLong(CallProperties.ZhzxProperty.SMS_VID_TIMEOUT)));
            }
        }

        return CommonUtils.decodeUnicodeInJson(resultStr);
    }

    /**
     * 从缓存中获取 vid 的值
     *
     * @param phone 手机号码
     * @return vid 的值，如果缓存中不存在或已过期，则返回 null
     */
    public String getVidFromCache(String phone) {
        String key = RedisConst.ZHZX_SMS_VID + ":" + phone;
        String vid = redisTemplate.opsForValue().get(key);

        if (!ObjectUtils.isEmpty(vid)) {
            Long expire = redisTemplate.getExpire(key);

            Duration ttl = Duration.ofSeconds(expire);
            Duration threshold = Duration.ofSeconds(5);

            if (ttl.compareTo(threshold) < 0) {
                // 如果剩余 TTL 时间小于 5 秒钟，则提示验证码已过期
                throw new ApplicationException(ApiErrorCodes.verifyCode_invalided, "验证码已过期");
            }
        }

        return vid;
    }

    /**
     * 中弘智享平台设置白名单
     *
     * @param zhzxSetupWhiteListRequest 包含短信信息的请求对象
     * @param eeId 员工标识
     *
     * @return 中弘智享平台返回的响应
     */
    public String setupWhiteList(ZHZXSetupWhiteListRequest zhzxSetupWhiteListRequest, String eeId) {
        // 中弘智享平台的请求地址
        String zhzxRequestUrl = CallProperties.ZhzxProperty.HOST + CallProperties.ZhzxProperty.SETUP_WHITE_LIST;
        AccountDTO currAccount;

        // 从通讯账号配置表中获取当前用户归属分校的API-KEY和API-TOKEN
        currAccount = this.getZhzxAccountByEeId(eeId);

        // 发起接口请求
        if(!ObjectUtils.isEmpty(currAccount)) {
            Map<String, String> header = this.getHeader(currAccount.getApiAccount(), currAccount.getApiSecret(), null);
            Headers headers = Headers.of(header);
            String responseStr = okHttpUtil.post(zhzxRequestUrl, JSONUtils.toJSONString(zhzxSetupWhiteListRequest), headers);
            ZhongHongApiResponse zhongHongApiResponse = ZhongHongApiResponse.fromJson(responseStr);
            if (200 != zhongHongApiResponse.getStatusCode()) {
               throw new ApplicationException(ApiErrorCodes.verifyCode_invalided, zhongHongApiResponse.getMessage());
            }
            return CommonUtils.decodeUnicodeInJson(responseStr);
        } else {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "未查询到当前用户的中弘智享通讯账户");
        }
    }

    /**
     * 根据传入的主叫账号判断是否已经加入中弘智享白名单
     *
     * @param phone 主叫账号
     * @param eeId 员工标识
     *
     * @return 是否已经加入中弘智享白名单
     */
    public Boolean isZhzxWhiteUser(String phone, String eeId) {
        String zhzxRequestUrl = CallProperties.ZhzxProperty.HOST + CallProperties.ZhzxProperty.GET_WHITE_LIST;
        AccountDTO currAccount;

        // 从通讯账号配置表中获取当前用户归属分校的API-KEY和API-TOKEN
        currAccount = this.getZhzxAccountByEeId(eeId);

        // 发起接口请求
        if (!ObjectUtils.isEmpty(currAccount)) {
            Map<String, String> header = this.getHeader(currAccount.getApiAccount(), null, null);
            Headers headers = Headers.of(header);
            // jsonBody随便传个值，不传会报错，这里传入111
            String responseStr = okHttpUtil.post(zhzxRequestUrl, "111", headers);
            return isPhoneInResponse(responseStr, phone);
        } else {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "未查询到当前用户的中弘智享通讯账户");
        }
    }

    /**
     * 判断指定电话号码是否在接口返回的电话号码列表中。
     *
     * @param responseStr 接口返回的 JSON 字符串
     * @param phone 待判断的电话号码
     * @return 如果电话号码在列表中，返回 true；否则返回 false。
     */
    private static boolean isPhoneInResponse(String responseStr, String phone) {
        JSONObject jsonObject = JSON.parseObject(responseStr);
        JSONArray phoneList = jsonObject.getJSONArray("data");
        for (int i = 0; i < phoneList.size(); i++) {
            String phoneInList = phoneList.getString(i);
            if (phoneInList.equals(phone)) {
                return true;
            }
        }
        return false;
    }
}
