package com.niceloo.cmc.ex.service.ai;

import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.dto.jdyx.*;
import com.niceloo.cmc.ex.pojo.param.JDCreateJobParam;
import com.niceloo.cmc.ex.pojo.param.JDJobDetailParam;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerAddRequest;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.pojo.request.JDTagListRequest;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;

import java.util.List;
import java.util.Map;

/**
 * 京东言犀AI外呼
 * *
 * * <AUTHOR>
 * * @Date 2022-08-09 17:50
 */
public interface JDYXService {

    /**
     * 查询某一个机器人对应的话术模板列表（言犀话术模板全量信息）
     *
     * @param tenantId 租户ID
     * @return 话术模板列表
     */
    List<JDContextFullDTO> getContextFullList(String tenantId);

    /**
     * 查询某一个机器人对应的话术模板列表
     *
     * @param userPin 用户pin
     * @param ngExpand 登录用户信息
     * @return 话术模板列表
     */
    List<JDContextListDTO> getContextList(String userPin, NgExpand ngExpand);

    /**
     * 根据租户标识获取话术列表
     *
     * @param tenantId 租户标识
     * @return
     */
    List<JDContextListDTO> getContextListByTenantId(Integer tenantId);

    /**
     * 获取标签列表
     *
     * @param jdTagListRequest 标签列表请求对象，包含请求参数
     * @param ngExpand 扩展参数，用于指定返回哪些字段
     * @return 包含标签列表的DTO对象列表
     */
    List<JDTagListDTO.TagDTO> getTagList(JDTagListRequest jdTagListRequest, NgExpand ngExpand);

    /**
     * 获取分组列表
     *
     * @param cid 客户ID，用于指定需要获取分组列表的客户
     * @param ngExpand 登录用户信息，用于验证用户权限和获取租户相关信息
     * @return JDGroupListDTO对象列表，包含客户的分组信息
     */
    List<JDGroupListDTO> getGroupList(String cid, NgExpand ngExpand);

    /**
     * 查询电话线路列表
     *
     * @param ngExpand 登录用户信息
     * @return 电话线路列表
     */
    List<JDLineListDTO> getLineList(NgExpand ngExpand);

    /**
     * 查询指定任务ID执行状态
     *
     * @param userPin 用户pin
     * @param jobId   任务id
     * @param ngExpand 登录用户信息
     * @return 任务状态信息
     */
    JDJobInfoDTO getJobInfo(Integer jobId, String userPin, NgExpand ngExpand);

    /**
     * 批量任务结果查询接口
     *
     * @param ngExpand 登录用户信息
     * @param jobIds  任务id列表
     * @return 电话线路列表
     */
    List<JDJobReportDTO> getJobReportList(List<Integer> jobIds, NgExpand ngExpand);

    /**
     * 创建外呼任务
     *
     * @param request 创建参数
     * @return 创建成功的外呼任务id
     */
    Integer createJob(JDCreateJobParam request,NgExpand ngExpand);


    /**
     * 根据不同的操作类型操作外呼任务
     *
     * @param vendorJobId 第三方厂商-任务id
     * @param type        操作类型 (1->启动,2->暂停,3->继续,4->删除)
     * @param userPin     用户pin
     */
    void operatingJob(Integer vendorJobId, Integer type, String userPin, NgExpand ngExpand);

    /**
     * 外呼任务明细数据滚动查询
     *
     * @param request 包含请求参数的对象，例如任务ID、查询条件等
     * @return 返回包含任务信息详情的JDJobDetailsDTO对象
     * @throws Exception 如果在获取详情过程中发生任何异常，将抛出此异常
     */
    JDJobDetailsDTO getJobDetails(JDJobDetailParam request);


    /**
     * 获取指定任务ID的所有任务详情。
     *
     * @param jobId 任务ID
     * @return 包含所有任务详情的列表，使用 JDJobDetailsDTO.JobDetail 类型表示。
     */
    List<JDJobDetailsDTO.JobDetail> getAllJobDetails(String jobId);

    /**
     * 从京东言犀创建外呼任务和追加客户
     *
     * @param aiCallJob           任务信息
     * @param customerRequestList 客户信息一条,如果有的话
     * @param ngExpand 登录用户信息
     */
    Integer createJobFromVendor(CcAiCallJob aiCallJob, List<JDCallCustomerRequest> customerRequestList, NgExpand ngExpand);


    /**
     * 从京东言犀创建外呼任务和追加客户
     *
     * @param aiJobId jobId
     * @param ngExpand 登录用户信息
     */
    void createJobFromVendor(String aiJobId, NgExpand ngExpand);

    /**
     * 根据京东言犀的外呼id更新任务的状态
     *
     * @param vendorJobId   京东言犀后台外呼id
     * @param jobName 任务名称
     * @param status  任务状态
     */
    void updateJobStatusByJobId(Integer vendorJobId, String jobName, String status, Integer tenantId);

    /**
     * 接收京东回调外呼任务电话明细回调
     *
     * @param callRecordCallback 回调详情信息
     */
    void updateAIJobCustomerInfo(JDCallRecordCallback callRecordCallback);

    /**
     * 下载录音文件到OSS并将地址更新到ES内
     *
     * @param map 录音文件等地址
     */
    void downLoadRecording(Map<String, Object> map);

    /**
     * 更新外呼任务的信息到数据库
     *
     * @param jobIds 外呼任务主键id
     * @param ucUser 操作录用户信息
     * @return 更新后的任务列表
     */
    List<CcAiCallJob> batchUpdateAICallJob(List<String> jobIds, UcUser ucUser, Integer tenantId);

    /**
     * 创建子任务并追加客户的信息到子任务
     *
     * @param parentCallJob 父任务信息
     * @param request       客户列表
     * @param ngExpand 登录用户信息
     */
    void createSubJobAndAddCustomer(CcAiCallJob parentCallJob, JDCallCustomerAddRequest request, NgExpand ngExpand);

    /**
     * 追加客户信息到京东言犀
     *
     * @param indexName       索引名称
     * @param lotNo           批次号,为空则上传所有未上传的客户
     * @param jobId           任务id
     * @param firstCustomerPhone 已经追加到任务的客户手机号
     */
    void addCustomerInfoToJob(String indexName, String lotNo, String jobId, String firstCustomerPhone, NgExpand ngExpand);

    /**
     * 京东言犀：根据NgExpand获取租户信息
     *
     * @param ngExpand 登录用户信息
     * @return tenantObject 租户对象
     */
    CallProperties.YanxiProperty.Tenant getTenantByToken(NgExpand ngExpand);

    /**
     * 根据租户标识获取租户配置信息
     *
     * @param tenantId 租户标识
     * @return tenant 租户配置信息
     */
    CallProperties.YanxiProperty.Tenant getTenantByTenantId(String tenantId);
}
