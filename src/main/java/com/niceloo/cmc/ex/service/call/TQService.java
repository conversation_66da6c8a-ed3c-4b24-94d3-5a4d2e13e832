package com.niceloo.cmc.ex.service.call;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.request.GenerateCallRecordRequest;
import com.niceloo.cmc.ex.pojo.request.PullTQRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.TQCallRecordsVO;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.utils.OkHttpUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.utils.encrypt.MD5Utils;
import com.niceloo.mq.client.Client;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_TQ_FIX;
import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_TQ_MOBILE;

/**
 * @description: TQ厂商服务
 * @author: WangChenyu
 * @create: 2022-02-28 11:50
 */
@Service
public class TQService extends CallRecordsBaseService {
    private static final Logger logger = LoggerFactory.getLogger(TQService.class);
    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);
    private final StringRedisTemplate redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
    private final Client client = SpringUtils.getBean(Client.class);
    private final CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);

    public TQService() {
        
    }

    public TQService(String actionType) {
        super.ACTION_TYPE = actionType;
    }

    /**
     * 拉取通话记录数据(入口)
     *
     * @param from 开始时间
     * @param to   结束时间
     * <AUTHOR>
     * @Date 11:09 2022/2/26
     **/
    @Override
    protected void downFile(Date from, Date to) {
        int page = 1;
        int totalPage = -1;
        String start = from.getTime() / 1000 + "";
        String end = to.getTime() / 1000 + "";
        PullTQRecordRequest tqRecordRequest = new PullTQRecordRequest(page, start, end);
        do {
            tqRecordRequest.setPageNum(page);
            // 查询厂商获取通话记录
            String response = this.selectRecordsFromVendor(tqRecordRequest);
            List<TQCallRecordsVO> tqCallRecordsVOS = this.recordsResultConverter(response);
            if (null == tqCallRecordsVOS) {
                // 失败重试
                tqCallRecordsVOS = this.retrySelectRecordsFromVendor(tqRecordRequest);
                if (null == tqCallRecordsVOS) {
                    return;
                }
            }
            if (totalPage == -1) {
                Map<String, Object> map = JSONUtils.toMap(response);
                int total = Integer.parseInt(JSONUtils.toMap(JSONUtils.toJSONString(map.get("data"))).get("total").toString());
                totalPage = (total + tqRecordRequest.getPageSize() - 1) / tqRecordRequest.getPageSize();
            }
            // 保存通话记录
            this.saveOrUpdateRecordsToEs(tqCallRecordsVOS);
        } while (totalPage > page++);
    }

    /**
     * C:保存通话记录和上传录音，U:更新通话记录和发送MQ上传录音
     *
     * @paramter tqCallRecordsVOS
     * <AUTHOR>
     * @Date 11:22 2022/3/1
     **/
    private void saveOrUpdateRecordsToEs(List<TQCallRecordsVO> tqCallRecordsVOS) {
        if (BizConst.CREATE.equals(super.ACTION_TYPE)) {
            this.saveRecordsToEs(tqCallRecordsVOS);
        } else {
            this.updateRecordsToEs(tqCallRecordsVOS);
        }
    }

    /**
     * 更新通话记录到ES
     *
     * @param tqCallRecordsVOS TQ厂商返回的通话记录列表
     * <AUTHOR>
     * @Date 17:10 2022/7/4
     **/
    public void updateRecordsToEs(List<TQCallRecordsVO> tqCallRecordsVOS) {
        List<String> uniqueIds = tqCallRecordsVOS.stream().map(TQCallRecordsVO::getClient_id).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        // 根据field2字段查询是否已经有了该条通话记录,如果没有还需要去创建
        List<CallRecord> recordList = callRecordService.querySupplementaryFieldByUniqueIds(super.ES_INDEX, CALL_TYPE_TQ_MOBILE.getType(), uniqueIds);
        Map<String, CallRecord> recordMap = recordList.stream().collect(Collectors.toMap(CallRecord::getField2, Function.identity(), (m1, m2) -> m2));
        // 需要更新的通话记录列表
        List<CallRecord> updateRecordList = new ArrayList<>(recordList.size());
        List<TQCallRecordsVO> saveCallRecordsVOS = new ArrayList<>(tqCallRecordsVOS.size() - recordList.size());
        for (TQCallRecordsVO tqCallRecordsVO : tqCallRecordsVOS) {
            String client_id = tqCallRecordsVO.getClient_id();
            if (recordMap.containsKey(tqCallRecordsVO.getClient_id())) {
                CallRecord record = recordMap.get(client_id);
                // 表示已经补充过通话信息了
                if (record.getDuration() != -1) {
                    continue;
                }
                CallRecord callRecord = tqCallRecordsVO.callRecordsConverter();
                callRecord.setCallId(record.getCallId());
                callRecord.setCreatedTime(null);
                updateRecordList.add(callRecord);
            } else {
                saveCallRecordsVOS.add(tqCallRecordsVO);
            }
        }
        logger.info("TQ外呼厂商定时任务拉取通话记录单次查询的总通话记录数量为:{},需要更新的通话记录数量为:{},需要生成的通话记录数量为:{}", tqCallRecordsVOS.size(), updateRecordList.size(), saveCallRecordsVOS.size());
        if (updateRecordList.size() > 0) {
            // 更新通话记录
            List<Map<String, Object>> recordingData = batchUpdateCallRecord(super.ES_INDEX, updateRecordList);
            // 发送下载录音MQ
            recordingData.forEach(data -> client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data)));
        }
        if (saveCallRecordsVOS.size() > 0) {
            this.saveRecordsToEs(saveCallRecordsVOS);
        }
    }


    /**
     * 保存通话记录到ES
     *
     * @param tqCallRecordsVOS TQ厂商返回的通话信息列表
     * <AUTHOR>
     * @Date 14:40 2022/7/4
     **/
    private void saveRecordsToEs(List<TQCallRecordsVO> tqCallRecordsVOS) {
        List<CallRecord> callRecordList = new ArrayList<>();
        for (TQCallRecordsVO tqCallRecordsVO : tqCallRecordsVOS) {
            // 添加数据到实体
            CallRecord callRecord = this.saveEntity(tqCallRecordsVO);
            if (null == callRecord) {
                continue;
            }
            if (!StringUtils.isEmpty(callRecord.getVoiceSourceUrl())) {
                this.totalSize++;
            }
            callRecordList.add(callRecord);
        }
        if (!CollectionUtils.isEmpty(callRecordList)) {
            try {
                //添加客户信息
                super.batchAddCustomerInfoByPhone(callRecordList);
                //批量添加
                callRecordService.bulkSave(ES_INDEX, callRecordList);
            } catch (Exception e) {
                logger.error(e, "TQ插入通话记录到ES异常:" + e.getMessage());
                //失败后删除创建的唯一值KEY
                String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_TQ_MOBILE.getType() + ":";
                redisTemplate.delete(callRecordList.stream().map(callRecord -> (redisKeyPrefix + callRecord.getVoiceSourceId())).collect(Collectors.toList()));
                redisTemplate.delete(callRecordList.stream().map(callRecord -> (redisKeyPrefix + callRecord.getField2())).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 添加通话记录到实体内,包括通话信息/客户信息/员工信息/上传录音
     *
     * @return com.niceloo.cmc.ex.entity.es.CallRecord
     * @paramter tqCallRecordsVO
     * <AUTHOR>
     * @Date 11:39 2022/3/1
     **/
    private CallRecord saveEntity(TQCallRecordsVO tqCallRecordsVO) {
        //校验是否重复[一个是外呼返回的id(client_id), 一个通话记录的唯一id(fsunique_id)]
        String id = tqCallRecordsVO.getFsunique_id();
        if (super.isNotExistRecord(tqCallRecordsVO.getClient_id(), id, CALL_TYPE_TQ_MOBILE)) {
            //补充通话信息
            CallRecord callRecord = tqCallRecordsVO.callRecordsConverter();
            tqCallRecordsVO.replenishEmpty(callRecord);
            //补充员工信息
            String type = callRecord.getField3();
            String channel = "1".equals(type) ? CALL_TYPE_TQ_FIX.getType() : CALL_TYPE_TQ_MOBILE.getType();
            super.setEE(DateUtils.toStr(callRecord.getCallTime()), channel, callRecord);
            // 上传录音
            super.uploadVoiceToOSS(callRecord);
            return callRecord;
        }
        return null;
    }

    /**
     * 从厂商查询通话记录列表
     *
     * @return java.lang.String
     * @paramter startTime 通话开始时间
     * @paramter endTime   通话结束时间
     * @paramter page      当前页数
     * <AUTHOR>
     * @Date 14:13 2021/12/22
     **/
    public String selectRecordsFromVendor(PullTQRecordRequest request) {
        this.checkRequestTime("TQ_ACCOUNT", 5000);
        String url = CallProperties.TQProperty.url + "/webservice/phoneRecord/list";
        Map<String, String> queries = new HashMap<>(2);
        queries.put("admin_uin", CallProperties.TQProperty.managerAccount);
        queries.put("access_token", this.getTQTokenInfo());
        return okHttpUtil.postFrom(url, JSONUtils.toMap(JSONUtils.toJSONString(request)), queries);
    }

    /**
     * 调用接口查询通话记录前要先调用该方法获取token信息
     *
     * @return java.lang.String
     * <AUTHOR>
     * @Date 10:25 2022/3/1
     **/
    public String getTQTokenInfo() {
        // 获取 TQ token
        String managerAccount = CallProperties.TQProperty.managerAccount;
        if (StringUtils.isEmpty(managerAccount)) {
            logger.error("callRecord--sync--云客厂商优路管理员账号为空");
            throw new RuntimeException("云客厂商优路管理员账号为空");
        }
        String accountKey = "TQ_" + managerAccount;
        if (null != REQ_CACHE.get(accountKey)) {
            // 判断缓存中没有保存当前账号 account，就去获取 token，并保存到缓存中
            return this.getTQTokenInfoFromCache(accountKey);
        } else {
            // 从TQ厂商查询
            String tokenInfo = this.selectTQTokenInfoFormVendor();
            if (StringUtils.isEmpty(tokenInfo)) {
                throw new RuntimeException("TQ获取token信息出现异常...");
            }
            REQ_CACHE.put(accountKey, Arrays.asList(tokenInfo, String.valueOf(System.currentTimeMillis())));
            return tokenInfo;
        }
    }

    /**
     * 从缓存内获取token信息,如果过期从TQ厂商查询并重新放入缓存
     *
     * @return java.lang.String
     * @paramter accountKey
     * <AUTHOR>
     * @Date 10:22 2022/3/1
     **/
    private String getTQTokenInfoFromCache(String accountKey) {
        // 如果缓存中已经保存了当前的账号 account，则需要先判断其 token 的有效期
        long validTime = CallProperties.TQProperty.validTime;
        long cacheTime = Long.parseLong(REQ_CACHE.get(accountKey).get(1));
        // 如果超过了有效期就要重新获取 token
        if ((System.currentTimeMillis() - cacheTime) >= validTime) {
            String tokenInfo = this.selectTQTokenInfoFormVendor();
            if (!StringUtils.isEmpty(tokenInfo)) {
                REQ_CACHE.put(accountKey, Arrays.asList(tokenInfo, String.valueOf(System.currentTimeMillis())));
            }
            return tokenInfo;
        } else {
            return REQ_CACHE.get(accountKey).get(0);
        }
    }

    /**
     * 查询TQToken[频繁获取返回同一个token]
     *
     * @return java.lang.String
     * <AUTHOR>
     * @Date 10:19 2022/3/1
     **/
    private String selectTQTokenInfoFormVendor() {
        String time = System.currentTimeMillis() / 1000 + "";
        String url = CallProperties.TQProperty.url + "/webservice/getAccessToken";
        Map<String, Object> map = new HashMap<>(3);
        map.put("admin_uin", CallProperties.TQProperty.managerAccount);
        map.put("ctime", time);
        map.put("sign", MD5Utils.md5(CallProperties.TQProperty.managerAccount + "$" + CallProperties.TQProperty.appKey + "$" + time).toUpperCase());
        String response = okHttpUtil.postFrom(url, map);
        if (StringUtils.isNotEmpty(response)) {
            Map<String, Object> result = JSONUtils.toMap(response);
            if (null == result.get("access_token") && null != result.get("errorCode")) {
                throw new RuntimeException("TQ获取token信息出现异常...返回结果:" + response);
            }
            return JSONUtils.toMap(response).get("access_token").toString();
        }
        return null;
    }


    /**
     * 结果转换器
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.TQCallRecordsVO>
     * @paramter response 返回结果字符串
     * <AUTHOR>
     * @Date 14:14 2021/12/22
     **/
    public List<TQCallRecordsVO> recordsResultConverter(String response) {
        if (StringUtils.isEmpty(response)) {
            return null;
        }
        Map<String, Object> map = JSONUtils.toMap(response);
        Map<String, Object> data = JSONUtils.toMap(JSONUtils.toJSONString(map.get("data")));
        long total = Long.parseLong(data.get("total").toString());
        if (0 == total) {
            return new ArrayList<>();
        }
        Object list = data.get("list");
        if (!(list instanceof Collection<?>)) {
            return new ArrayList<>();
        }
        return JSONUtils.toList(JSONUtils.toJSONString(list), TQCallRecordsVO.class);
    }

    /**
     * 查询通话记录失败后重试处理[重试十次]
     *
     * @param request
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.TQCallRecordsVO>
     * @paramter startTime 通话开始时间
     * @paramter endTime   通话结束时间
     * @paramter page      当前页数
     * <AUTHOR>
     * @Date 14:41 2021/12/22
     */
    private List<TQCallRecordsVO> retrySelectRecordsFromVendor(PullTQRecordRequest request) {
        for (int i = 0; i < 10; i++) {
            String response = this.selectRecordsFromVendor(request);
            List<TQCallRecordsVO> tqCallRecordsVOS = this.recordsResultConverter(response);
            if (null != tqCallRecordsVOS) {
                return tqCallRecordsVOS;
            }
        }
        // [重试十次,失败放入异常数组内]
        Date startDate = new Date(Long.parseLong(request.getStart_time()) * 1000);
        Date endDate = new Date(Long.parseLong(request.getEnd_time()) * 1000);
        recordList.add(startDate + "_" + endDate + "_" + super.ACTION_TYPE);
        return null;
    }

    /**
     * 切换TQ服务器域名
     * <AUTHOR>
     * @Date 2022/2/10 15:24
     */
    public static String switchHostName(String voiceSourceUrl) {
        if (voiceSourceUrl.contains(CallProperties.TQProperty.oldHost)) {
            return voiceSourceUrl.replace(CallProperties.TQProperty.oldHost, CallProperties.TQProperty.newHost);
        } else if (voiceSourceUrl.contains(CallProperties.TQProperty.newHost)) {
            return voiceSourceUrl.replace(CallProperties.TQProperty.newHost, CallProperties.TQProperty.oldHost);
        }
        return voiceSourceUrl;
    }

    @Override
    public void checkRequestTime(String key, int time) {
        synchronized (TQService.class) {
            super.checkRequestTime(key, time);
        }
    }

    /**
     * 接收智能外呼MQ外呼成功后返回的主叫被叫信息生成缺少通话信息的通话记录
     *
     * @param request 主被叫信息
     * <AUTHOR>
     * @Date 14:39 2022/5/28
     **/
    public void generateCallRecord(GenerateCallRecordRequest request) {
        // 生成通话记录保存到ElasticSearch 
        super.commonGenerateCallRecord(request);
    }
}
