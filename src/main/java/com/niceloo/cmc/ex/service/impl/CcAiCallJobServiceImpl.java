package com.niceloo.cmc.ex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.mapper.CcAiCallJobMapper;
import com.niceloo.cmc.ex.pojo.request.AICallJobListRequest;
import com.niceloo.cmc.ex.pojo.vo.AiCallJobVO;
import com.niceloo.cmc.ex.pojo.vo.BasePageVO;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.service.ai.JDYXService;
import com.niceloo.framework.db.dynamic.core.DS;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import com.niceloo.segment.core.NicelooIdTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * AI外呼任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Service
@DS("cmc")
public class CcAiCallJobServiceImpl extends ServiceImpl<CcAiCallJobMapper, CcAiCallJob> implements CcAiCallJobService {
    private static final Logger logger = LoggerFactory.getLogger(CcAiCallJobServiceImpl.class);
    @Autowired
    private CcAiCallJobMapper ccAiCallJobMapper;
    @Resource
    private NicelooIdTemplate nicelooIdTemplate;
    @Resource
    private JDYXService jdyxService;
    @Resource
    private AICallJobCustomerInfoService aiCallJobCustomerInfoService;

    /**
     * 分页查询AI外呼任务列表
     *
     * @param request 请求参数
     * @return com.niceloo.cmc.ex.pojo.vo.BasePageVO<com.niceloo.cmc.ex.pojo.vo.AiCallJobVO>
     * <AUTHOR>
     * @Date 14:55 2022/8/13
     **/
    @Override
    public BasePageVO<AiCallJobVO> selectJobPage(AICallJobListRequest request) {
        if (StringUtils.isNotEmpty(request.getName())) {
            request.setName("%" + request.getName() + "%");
        }
        //得到总页数
        int total = ccAiCallJobMapper.selectParentJobCount(request);
        if (total <= 0) {
            return new BasePageVO<>(0, new ArrayList<>());
        }
        // 分页查询父任务的列表
        List<CcAiCallJob> callJobList = ccAiCallJobMapper.selectParentJobPage(request);
        // 类型转换
        List<AiCallJobVO> callJobVOList = BeanUtils.copyFromMultiObjToClass(AiCallJobVO.class, callJobList);
        // 得到父任务的id
        List<String> aiJobId = callJobList.stream().map(CcAiCallJob::getJobId).collect(Collectors.toList());
        // 根据父任务的id列表获取到子任务的列表
        List<CcAiCallJob> childCallJobList = ccAiCallJobMapper.selectChildrenJobPageParentIds(aiJobId);
        List<AiCallJobVO> childCallJobVOList = BeanUtils.copyFromMultiObjToClass(AiCallJobVO.class, childCallJobList);
        // 子任务列表根据父任务id分组
        Map<String, List<AiCallJobVO>> childCallJobGroupMap = childCallJobVOList.stream().collect(Collectors.groupingBy(AiCallJobVO::getParentJobId));
        // 循环父任务,放入子任务
        for (AiCallJobVO callJobVO : callJobVOList) {
            List<AiCallJobVO> childCallJob = childCallJobGroupMap.get(callJobVO.getJobId());
            callJobVO.setChildren(childCallJob);
        }
        //得到总页数和数据列表
        return new BasePageVO<>(total, callJobVOList);
    }

    /**
     * 根据厂商的外呼id查询索引名称
     *
     * @param vendorJobId 厂商外呼id
     * @return java.lang.String  索引名称
     * <AUTHOR>
     * @Date 11:38 2022/8/25
     **/
    @Override
    public String getIndexNameByVendorJobId(Integer vendorJobId, String aiJobType) {
        LambdaQueryWrapper<CcAiCallJob> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CcAiCallJob::getVendorJobId, vendorJobId)
                .eq(CcAiCallJob::getAiJobType, aiJobType)
                .select(CcAiCallJob::getJobId)
                .select(CcAiCallJob::getCustomerIndex);
        List<CcAiCallJob> aiCallJobList = super.list(wrapper);
        if (null != aiCallJobList) {
            return aiCallJobList.get(0).getCustomerIndex();
        }
        return null;
    }

    /**
     * 根据厂商的外呼id查询主键id
     *
     * @param vendorJobId 厂商外呼id
     * @param aiJobType 外呼通道类型
     * @return java.lang.String 主键id
     * <AUTHOR>
     * @Date 14:56 2022/8/26
     **/
    @Override
    public String getJobIdByVendorJobId(Integer vendorJobId, String aiJobType) {
        LambdaQueryWrapper<CcAiCallJob> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CcAiCallJob::getVendorJobId, vendorJobId)
                .eq(CcAiCallJob::getAiJobType, aiJobType)
                .select(CcAiCallJob::getJobId);
        List<CcAiCallJob> aiCallJobList = super.list(wrapper);
        if (null != aiCallJobList && !aiCallJobList.isEmpty()) {
            return aiCallJobList.get(0).getJobId();
        }
        return null;
    }

    @Override
    public String getJobIdByVendorJobIdAndCompanyId(Integer vendorJobId, String aiJobType, Long companyId) {
        LambdaQueryWrapper<CcAiCallJob> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CcAiCallJob::getVendorJobId, vendorJobId)
                .eq(CcAiCallJob::getAiJobType, aiJobType)
                .eq(CcAiCallJob::getCompanyId, companyId)
                .select(CcAiCallJob::getJobId);
        List<CcAiCallJob> aiCallJobList = super.list(wrapper);
        if (aiCallJobList != null && !aiCallJobList.isEmpty()) {
            return aiCallJobList.get(0).getJobId();
        }
        return null;
    }

    /**
     * 校验任务名称是否存在
     *
     * @param name 任务名称
     * <AUTHOR>
     * @Date 14:04 2022/9/2
     **/
    @Override
    public boolean existJobName(String name) {
        if (StringUtils.isEmpty(name)) {
            return false;
        }
        LambdaQueryWrapper<CcAiCallJob> queryWrapper = new LambdaQueryWrapper<CcAiCallJob>()
                .eq(CcAiCallJob::getJobName, name);
        int count = this.count(queryWrapper);
        return count > 0;
    }

    /**
     * 根据任务id获取子任务列表
     *
     * @param parentId 父任务ID
     * @return java.util.List<com.niceloo.cmc.ex.entity.CcAiCallJob> 子任务列表
     * <AUTHOR>
     * @Date 11:11 2022/9/30
     **/
    @Override
    public List<CcAiCallJob> selectJobListByParentId(String parentId) {
        if (StringUtils.isEmpty(parentId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CcAiCallJob> queryWrapper = new LambdaQueryWrapper<CcAiCallJob>()
                .eq(CcAiCallJob::getParentJobId, parentId);
        return this.list(queryWrapper);
    }

    /**
     * 根据任务id和分校id获取子任务
     *
     * @param parentId 父任务ID
     * @param schoolId 子任务的分校id
     * @return com.niceloo.cmc.ex.entity.CcAiCallJob
     * <AUTHOR>
     * @Date 12:44 2022/9/30
     **/
    @Override
    public CcAiCallJob selectJobByParentIdAndSchoolId(String parentId, String schoolId) {
        if (StringUtils.isEmpty(parentId) || StringUtils.isEmpty(schoolId)) {
            return null;
        }
        LambdaQueryWrapper<CcAiCallJob> queryWrapper = new LambdaQueryWrapper<CcAiCallJob>()
                .eq(CcAiCallJob::getParentJobId, parentId)
                .eq(CcAiCallJob::getJobSchoolId, schoolId);
        return this.getOne(queryWrapper);
    }

    /**
     * 一、更新任务进度为已完成(同一个父id的都更新为已完成,创建失败的除外)<br/>
     * 二、更新客户追加状态为已完成
     *
     * @param jobId 任务id
     * <AUTHOR>
     * @Date 15:24 2022/9/30
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateJobProgressToFinish(String jobId, String jobLevel) {
        CcAiCallJob aiCallJob = this.getById(jobId);
        String callTaskType = aiCallJob.getCallTaskType();
        switch (callTaskType) {
            case BizConst.AI_SCHOOL_TASK:
            case BizConst.AI_ALL_TASK:
                if (StringUtils.isNotEmpty(jobLevel) && "1".equals(jobLevel)) {
                    CcAiCallJob callJob = new CcAiCallJob();
                    callJob.setJobId(jobId);
                    callJob.setJobProgress(4);
                    callJob.setModifyDate(DateUtils.getNowDString());
                    this.updateById(callJob);
                } else {
                    ccAiCallJobMapper.updateJobProgressToFinish(aiCallJob.getParentJobId(), DateUtils.getNowDString());
                    ccAiCallJobMapper.updateCustomerAddStatusToFinish(aiCallJob.getParentJobId(), DateUtils.getNowDString());
                }
                break;
            case BizConst.AI_SS_TASK:
                aiCallJob.setJobProgress(4);
                aiCallJob.setModifyDate(DateUtils.getNowDString());
                aiCallJob.setCustomerAddStatus("Y");
                this.updateById(aiCallJob);
                break;
            default:
                logger.error("任务类型错误,callTaskType:{}", callTaskType);
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "Invalid callTaskType: " + callTaskType);
        }
    }
}
