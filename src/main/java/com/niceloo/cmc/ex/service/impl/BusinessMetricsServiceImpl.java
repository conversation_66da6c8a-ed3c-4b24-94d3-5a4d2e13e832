package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.service.BusinessMetricsService;
import com.niceloo.cmc.ex.service.WechatGroupRobotService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.utils.DateUtils;
import lombok.CustomLog;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

import static org.elasticsearch.index.query.QueryBuilders.*;

/**
 * 监控关键业务指标
 * <AUTHOR>
 * @since 2024/7/29 15:17
 */
@CustomLog
@Service
public class BusinessMetricsServiceImpl implements BusinessMetricsService {
    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Resource
    private WechatGroupRobotService wechatGroupRobotService;

    @Value("${wechat.channeltypes}")
    private String channelTypesKey;

    @Value("${wechat.warningThreshold.recording}")
    private int recordingWaringThreshold = 100;

    @Value("${wechat.warningThreshold.record}")
    private int recordWaringThreshold = 10;

    /**
     * 监控业务指标
     */
    @Override
    public void monitorBusinessMetrics() {
        // 监控录音缺失业务指标
        monitorRecordingMetrics();
        // 监控通话记录缺失业务指标
        monitorRecordMetrics();

    }

    /**
     * 监控录音缺失业务指标
     * 根据外呼通道，监控当前月应该有录音但无录音的通话记录条数，超过预警阈值时发送报警消息
     */
    private void monitorRecordingMetrics() {
        // 外呼通道列表
        String[] channelTypes = channelTypesKey.split(",");
        StringBuilder warningMessage = new StringBuilder();

        for (String channelType : channelTypes) {
            long noRecordingCount = getNoRecordingCountByChannelType(channelType);
            LOGGER.info("{}外呼通道, 当月无录音的通话记录条数: {}", channelType, noRecordingCount);

            if (noRecordingCount > recordingWaringThreshold) {
                warningMessage.append("【业务监控】")
                        .append(channelType)
                        .append("外呼通道, 当月无录音的通话记录条数: ")
                        .append(noRecordingCount)
                        .append("超过预警阈值(")
                        .append(recordingWaringThreshold)
                        .append("), 请关注!\n");
            }
        }

        if (warningMessage.length() > 0) {
            wechatGroupRobotService.sendMessage(warningMessage.toString());
        }
    }

    /**
     * 监控通话记录缺失业务指标
     * 根据外呼通道列表，获取当月应该有录音但无录音的记录条数，
     * 如果记录数超过阈值条数，则发送微信消息报警。
     */
    private void monitorRecordMetrics() {
        // 外呼通道列表
        String[] channelTypes = channelTypesKey.split(",");
        StringBuilder warningMessage = new StringBuilder();

        for (String channelType : channelTypes) {
            long noRecordCount = getNoRecordCountByChannelType(channelType);
            long totalRecordCount = getRecordTotalCountByChannelType(channelType);
            double percent = Math.round((double) noRecordCount / totalRecordCount * 10000.0) / 100.0;

            LOGGER.debug("{}外呼通道, 当月缺失通话结果条数: {}, 当月总通话记录条数: {}, 当月通话结果缺失占比: {}", channelType, noRecordCount, totalRecordCount, percent);

            if (percent > recordWaringThreshold) {
                warningMessage.append("【业务监控】")
                        .append(channelType)
                        .append("外呼通道, 当月无通话结果的通话记录占比: ")
                        .append(percent)
                        .append("%, 超过预警阈值(")
                        .append(recordWaringThreshold)
                        .append("%),请关注!\n");
            }
        }

        if (warningMessage.length() > 0) {
            wechatGroupRobotService.sendMessage(warningMessage.toString());
        }
    }

    /**
     * 根据索引名和外呼通道类型获取当月无录音的通话数量
     *
     * @param channelType 外呼通道类型
     * @return 返回无录音的通话数量
     */
    public long getNoRecordingCountByChannelType(String channelType) {
        // 获取当前日期
        Date today = new Date();
        // 昨天
        Date yesterday = DateUtils.addDay(today, -1);
        String indexName = "call_record_" + DateUtils.toStr(yesterday, "yyyyMM");
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withTypes("_doc")
                .withQuery(boolQuery()
                        .must(termQuery("channelType", channelType))
                        .must(rangeQuery("callTime")
                                .gte(DateUtils.toStr(DateUtil.getFirstDayOfMonth(yesterday)))
                                .lt(DateUtils.toStr(DateUtil.getYesterdayEndDate())))
                        .must(termQuery("serverFolder", ""))
                        .must(rangeQuery("duration").gte(10))
                        .mustNot(termQuery("voiceSourceUrl", "")))
                .build();

        return elasticsearchTemplate.count(searchQuery, CallRecord.class);
    }


    /**
     * 根据索引名和外呼通道类型获取当月缺失通话记录的通话数量
     *
     * @param channelType 外呼通道类型
     * @return 返回缺失通话记录的通话数量
     */
    public long getNoRecordCountByChannelType(String channelType) {
        // 获取当前日期
        Date today = new Date();
        // 昨天
        Date yesterday = DateUtils.addDay(today, -1);
        String indexName = "call_record_" + DateUtils.toStr(today, "yyyyMM");
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withTypes("_doc")
                .withQuery(boolQuery()
                        .must(termQuery("channelType", channelType))
                        .must(rangeQuery("callTime")
                                .gte(DateUtils.toStr(DateUtil.getFirstDayOfMonth(yesterday)))
                                )
                        .must(termQuery("duration", "-1")))
                .build();

        return elasticsearchTemplate.count(searchQuery, CallRecord.class);
    }


    /**
     * 根据索引名和外呼通道类型获取当月通话记录总数
     *
     * @param channelType 外呼通道类型
     * @return 返回通话记录总数
     */
    public long getRecordTotalCountByChannelType(String channelType) {
        // 获取当前日期
        Date today = new Date();
        // 昨天
        Date yesterday = DateUtils.addDay(today, -1);
        String indexName = "call_record_" + DateUtils.toStr(yesterday, "yyyyMM");
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withTypes("_doc")
                .withQuery(boolQuery()
                        .must(termQuery("channelType", channelType))
                        .must(rangeQuery("callTime")
                                .gte(DateUtils.toStr(DateUtil.getFirstDayOfMonth(yesterday)))
                        ))
                .build();

        return elasticsearchTemplate.count(searchQuery, CallRecord.class);
    }
}
