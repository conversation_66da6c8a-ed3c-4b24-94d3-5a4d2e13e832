package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.pojo.dto.BdEeDTO;
import com.niceloo.cmc.ex.pojo.dto.BdEeDptDTO;
import com.niceloo.cmc.ex.pojo.dto.EeSchoolInfoDTO;

import java.util.List;

/**
 * <p>
 * 员工 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
public interface BdEeService {

    /**
     * 根据分校id查询分校名称
     *
     * @param schoolId 分校ID
     * @return 分校名称
     */
    String findSchoolNameById(String schoolId);

    /**
     * 根据外呼账号,外呼通道,呼叫时间匹配员工
     *
     * @param callAccount 外呼账号
     * @param callTime    外呼时间,为空时取当前时间,会查询当前绑定callAccount的员工
     * @param callType    外呼通道
     */
    BdEeDTO queryBdEeDTOByCallAccountTypeAndTimeUseCache(String callAccount, String callTime, String callType);

    /**
     * 根据用户id获取用户信息
     * @paramter userId 用户ID
     * @return com.niceloo.cmc.ex.pojo.dto.BdEeDTO
     */
    BdEeDTO findBdEeDTOByUserId(String userId);

    /**
     * 根据分校id列表查询分校名称列表
     *
     * @param schoolIds 分校ID列表
     * @return 分校名称
     */
    List<EeSchoolInfoDTO> findSchoolNameByIds(List<String> schoolIds);

    /**
     * 查询员工信息，包括分校信息
     *
     * @param userId 员工id
     * @return 员工的基础信息
     */
    BdEeDTO findBdEeDTOAndSchoolNameByUserId(String userId);

    /**
     * 批量查询员工的组织架构信息
     *
     * @param userIds 员工的userId列表
     * @return 员工组织架构列表
     */
    List<BdEeDptDTO> findBdEeDptListByUserIds(List<String> userIds);
}
