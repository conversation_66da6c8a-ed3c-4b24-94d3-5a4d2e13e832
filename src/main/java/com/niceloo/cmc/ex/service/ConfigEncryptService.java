package com.niceloo.cmc.ex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;

/**
 * <p>
 * 库表数据加密
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21 17:58:46
 */
public interface ConfigEncryptService extends IService<CcCallAccountConfig> {

    /**
     * 加密表字段
     *
     * @param batch  每批次更新数
     */
    public void encryptCcCallAccountConfig(int batch) throws InterruptedException;
}
