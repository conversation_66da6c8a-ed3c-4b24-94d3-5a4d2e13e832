package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.entity.CcCountmonth;

import java.util.List;

/**
 * <p>
 * 话务统计表(月) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
public interface CcCountmonthService {

    /**
     * 根据日期和外呼类型删除,参数不能为空
     * 
     * @param date
     *            日期
     * @param channelType
     *            外呼类型
     */
    void deleteByDate(String date, String channelType);

    /**
     * 批量添加
     * 
     * @param ccCountMonths
     *            统计列表
     */
    void batchSave(List<CcCountmonth> ccCountMonths);
}
