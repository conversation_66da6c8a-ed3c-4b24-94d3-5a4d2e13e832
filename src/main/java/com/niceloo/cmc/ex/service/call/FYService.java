package com.niceloo.cmc.ex.service.call;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.request.GenerateCallRecordRequest;
import com.niceloo.cmc.ex.pojo.request.PullFYRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.FYCallRecordsVO;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.utils.HttpUtils;
import com.niceloo.cmc.ex.utils.OkHttpUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.utils.encrypt.MD5Utils;
import com.niceloo.mq.client.Client;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import lombok.NoArgsConstructor;
import okhttp3.Headers;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_FY;

/**
 * @description: 风云厂商服务
 * @author: WangChenyu
 * @create: 2022-02-26 11:41
 */
@NoArgsConstructor
@Service
public class FYService extends CallRecordsBaseService {
    private static final Logger logger = LoggerFactory.getLogger(FYService.class);
    private final Client client = SpringUtils.getBean(Client.class);
    private final String fyUrl = SpringUtils.getProperty("fy.url");
    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);
    private final StringRedisTemplate redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
    private final CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);

    public FYService(String actionType) {
        super.ACTION_TYPE = actionType;
    }

    /**
     * 拉取通话记录数据(入口)
     *
     * @param from 开始时间
     * @param to   结束时间
     * <AUTHOR>
     * @Date 11:42 2022/2/26
     **/
    @Override
    protected void downFile(Date from, Date to) {
        String start = DateUtils.toStr(from);
        String end = DateUtils.toStr(to);
        // 得到配置文件内的风云外呼的所有账户信息
        List<Map<String, String>> accountList = this.selectFYAccountInfoList();
        for (Map<String, String> secretMap : accountList) {
            String accountId = secretMap.get("AccountId");
            String apiSecret = secretMap.get("ApiSecret");
            String nameSuffix = secretMap.get("NameSuffix");
            PullFYRecordRequest fyRecordRequest = new PullFYRecordRequest(accountId, apiSecret, start, end);
            // 根据账号拉取通话记录并保存到ES
            this.pullRecordsOfAccountInfo(fyRecordRequest, nameSuffix);
        }
    }

    /**
     * 根据账号拉取通话记录并保存到ES
     *
     * @paramter fyRecordRequest 请求参数
     * <AUTHOR>
     * @Date 16:53 2022/2/26
     **/
    private void pullRecordsOfAccountInfo(PullFYRecordRequest fyRecordRequest, String nameSuffix) {
        boolean flag = true;
        int page = 1;
        do {
            // 拉取通话记录
            fyRecordRequest.setPage(page);
            List<FYCallRecordsVO> fyCallRecordsVOList = this.selectRecordsFromVendor(fyRecordRequest);
            if (null == fyCallRecordsVOList) {
                //失败后重试
                fyCallRecordsVOList = this.retrySelectRecordsFromVendor(fyRecordRequest);
                if (null == fyCallRecordsVOList) {
                    flag = false;
                }
            }
            // 如果调用接口成功,但拉取已完成直接return
            if (flag && fyCallRecordsVOList.size() == 0) {
                return;
            }
            page++;
            this.saveOrUpdateRecordsToEs(fyCallRecordsVOList, nameSuffix);
        } while (flag);
    }

    /**
     * C:保存通话记录和上传录音，U:更新通话记录和发送MQ上传录音
     *
     * @paramter tqCallRecordsVOS
     * <AUTHOR>
     * @Date 11:22 2022/3/1
     **/
    private void saveOrUpdateRecordsToEs(List<FYCallRecordsVO> fyCallRecordsVOList, String nameSuffix) {
        if (BizConst.CREATE.equals(super.ACTION_TYPE)) {
            this.saveRecordsToEs(fyCallRecordsVOList, nameSuffix);
        } else {
            this.updateRecordsToEs(fyCallRecordsVOList, nameSuffix);
        }
    }


    /**
     * 添加通话记录到ES
     *
     * @paramter fyCallRecordsVOList 厂商返回的通话记录信息
     * @paramter nameSuffix 账号信息
     * <AUTHOR>
     * @Date 16:49 2022/2/26
     **/
    private void saveRecordsToEs(List<FYCallRecordsVO> fyCallRecordsVOList, String nameSuffix) {
        if (CollectionUtils.isEmpty(fyCallRecordsVOList)) {
            return;
        }
        List<CallRecord> callRecordList = new ArrayList<>(fyCallRecordsVOList.size());
        for (FYCallRecordsVO fyCallRecordsVO : fyCallRecordsVOList) {
            // 赋值通话记录数据到实体类
            CallRecord callRecord = this.saveEntity(fyCallRecordsVO, nameSuffix);
            if (null == callRecord) {
                continue;
            }
            if (!StringUtils.isEmpty(callRecord.getVoiceSourceUrl())) {
                this.totalSize++;
            }
            callRecordList.add(callRecord);
        }
        if (!CollectionUtils.isEmpty(callRecordList)) {
            try {
                // 添加客户信息
                super.batchAddCustomerInfoByPhone(callRecordList);
                //批量添加
                callRecordService.bulkSave(ES_INDEX, callRecordList);
            } catch (Exception e) {
                logger.error(e, "风云插入通话记录到ES异常,异常信息:{}", e.getMessage());
                //失败后删除创建的唯一值KEY
                String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_FY.getType() + ":";
                redisTemplate.delete(callRecordList.stream().map(callRecord -> (redisKeyPrefix + callRecord.getVoiceSourceId())).collect(Collectors.toList()));
                redisTemplate.delete(callRecordList.stream().map(callRecord -> (redisKeyPrefix + callRecord.getField2())).collect(Collectors.toList()));
            }
        }
    }


    /**
     * 更新通话记录到ES
     *
     * @param fyCallRecordsVOList 风云厂商返回的通话记录列表
     * @param nameSuffix          名称前缀
     * <AUTHOR>
     * @Date 17:10 2022/7/4
     **/
    public void updateRecordsToEs(List<FYCallRecordsVO> fyCallRecordsVOList, String nameSuffix) {
        List<String> uniqueIds = fyCallRecordsVOList.stream().map(FYCallRecordsVO::getACTION_ID).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        // 根据field2字段查询是否已经有了该条通话记录,如果没有还需要去创建
        List<CallRecord> recordList = callRecordService.querySupplementaryFieldByUniqueIds(super.ES_INDEX, CALL_TYPE_FY.getType(), uniqueIds);
        Map<String, CallRecord> recordMap = recordList.stream().collect(Collectors.toMap(CallRecord::getField2, Function.identity(), (m1, m2) -> m2));
        // 需要更新的通话记录列表
        List<CallRecord> updateRecordList = new ArrayList<>(recordList.size());
        // 需要添加的通话记录列表
        List<FYCallRecordsVO> saveCallRecordsVOS = new ArrayList<>(fyCallRecordsVOList.size() - recordList.size());
        for (FYCallRecordsVO fyCallRecordsVO : fyCallRecordsVOList) {
            String action_id = fyCallRecordsVO.getACTION_ID();
            if (recordMap.containsKey(action_id)) {
                CallRecord record = recordMap.get(action_id);
                // 表示已经补充过通话信息了
                if (record.getDuration() != -1) {
                    continue;
                }
                CallRecord callRecord = fyCallRecordsVO.callRecordsConverter();
                callRecord.setCallId(record.getCallId());
                callRecord.setCreatedTime(null);
                updateRecordList.add(callRecord);
            } else {
                saveCallRecordsVOS.add(fyCallRecordsVO);
            }
        }
        logger.info("风云外呼厂商定时任务拉取通话记录单次查询的总通话记录数量为:{},需要更新的通话记录数量为:{},需要生成的通话记录数量为:{}", fyCallRecordsVOList.size(), updateRecordList.size(), saveCallRecordsVOS.size());
        if (updateRecordList.size() > 0) {
            // 更新通话记录
            List<Map<String, Object>> recordingData = batchUpdateCallRecord(super.ES_INDEX, updateRecordList);
            // 发送下载录音MQ
            recordingData.forEach(data -> client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data)));
        }
        if (saveCallRecordsVOS.size() > 0) {
            this.saveRecordsToEs(saveCallRecordsVOS, nameSuffix);
        }
    }

    /**
     * 查询通话记录失败后重试处理[重试十次]
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.FYCallRecordsVO>
     * @paramter fyRecordRequest 请求参数
     * <AUTHOR>
     * @Date 16:42 2022/2/26
     **/
    private List<FYCallRecordsVO> retrySelectRecordsFromVendor(PullFYRecordRequest fyRecordRequest) {
        for (int i = 0; i < 10; i++) {
            List<FYCallRecordsVO> fyCallRecordsVOList = this.selectRecordsFromVendor(fyRecordRequest);
            if (null != fyCallRecordsVOList) {
                return fyCallRecordsVOList;
            }
        }
        // [重试十次,失败放入异常数组内]
        // TODO 应该将当前错误的风云账号存进去,不用重复浪费调用查询
        recordList.add(fyRecordRequest.getBeginTime() + "_" + fyRecordRequest.getEndTime() + "_" + super.ACTION_TYPE);
        return null;
    }

    /**
     * 调用厂商接口回去通话记录,调用接口失败返回 null,成功但数据为空返回空数组
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.FYCallRecordsVO>
     * @paramter fyRecordRequest 请求参数
     * <AUTHOR>
     * @Date 14:19 2022/2/26
     **/
    public List<FYCallRecordsVO> selectRecordsFromVendor(PullFYRecordRequest fyRecordRequest) {
        // 限流
        this.checkRequestTime("FS_ACCOUNT", 5000);
        String accountId = fyRecordRequest.getAccountId();
        String time = DateUtils.toStr(new Date(), "yyyyMMddHHmmss");
        String sign = MD5Utils.md5(accountId + fyRecordRequest.getApiSecret() + time).toUpperCase();
        String url = fyUrl + "/" + accountId + "?sig=" + sign;
        Map<String, String> headers = createFyHeader(accountId, time);
        //发起请求
        String response = okHttpUtil.post(url, JSONUtils.toJSONString(fyRecordRequest), Headers.of(headers));
        if (StringUtils.isEmpty(response)) {
            return null;
        }
        //得到结果
        Map<String, Object> resultMap = JSONUtils.toMap(response);
        Object success = resultMap.get("success");
        if (Boolean.TRUE.equals(success)) {
            Object data = resultMap.get("data");
            if (data == null) {
                return null;
            }
            // 结果转换
            if (data instanceof Collection<?>) {
                List<Map<String, Object>> fyResult = (List<Map<String, Object>>) data;
                List<FYCallRecordsVO> fyCallRecordsVOS = BeanUtils.copyFromMultiObjToClass(FYCallRecordsVO.class, fyResult);
                return CollectionUtils.isEmpty(fyCallRecordsVOS) ? new ArrayList<>() : fyCallRecordsVOS;
            }
        }
        return null;
    }

    /**
     * 拼装通话记录实体信息
     *
     * @return com.niceloo.cmc.ex.entity.es.CallRecord
     * @paramter fyCallRecordsVO 厂商返回信息
     * @paramter nameSuffix 风云账号
     * <AUTHOR>
     * @Date 10:56 2022/3/16
     **/
    private CallRecord saveEntity(FYCallRecordsVO fyCallRecordsVO, String nameSuffix) {
        //校验是否重复
        if (super.isNotExistRecord(fyCallRecordsVO.getACTION_ID(), fyCallRecordsVO.get_id(), CALL_TYPE_FY)) {
            //补充通话信息
            CallRecord callRecord = fyCallRecordsVO.callRecordsConverter();
            fyCallRecordsVO.replenishEmpty(callRecord);
            callRecord.setCallAccount(callRecord.getAgentId() + "@" + nameSuffix);
            //补充员工信息
            super.setEE(fyCallRecordsVO.getOFFERING_TIME(), CALL_TYPE_FY.getType(), callRecord);
            // 上传录音
            super.uploadVoiceToOSS(callRecord);
            return callRecord;
        }
        return null;
    }

    /**
     * 获取请求头
     *
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @paramter accountId 账户Id
     * @paramter time 时间戳
     * <AUTHOR>
     * @Date 14:27 2022/2/26
     **/
    private Map<String, String> createFyHeader(String accountId, String time) {
        String auth = HttpUtils.encode(accountId + ":" + time);
        Map<String, String> header = new HashMap<>(3);
        header.put("Authorization", auth);
        header.put("Content-Type", "application/json;charset=utf-8");
        header.put("Accept", "application/json");
        return header;
    }

    /**
     * 查询风云账号信息列表
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @paramter
     * <AUTHOR>
     * @Date 11:45 2022/2/26
     **/
    public List<Map<String, String>> selectFYAccountInfoList() {
        InputStream inputStream = this.getClass().getResourceAsStream("/fyCallSecret.json");
        if (null == inputStream) {
            return new ArrayList<>();
        }
        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
        StringBuilder builder = new StringBuilder();
        String s = "";
        while (true) {
            try {
                if ((s = br.readLine()) == null) break;
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
            builder.append(s);
        }
        Map<String, Object> map = JSONUtils.toMap(builder.toString());
        return (List<Map<String, String>>) map.get("Accounts");
    }

    /**
     * 校验该通话记录是否存在[不存在返回true]
     *
     * @return boolean
     * @paramter uniqueKey 该条通话记录的唯一id
     * <AUTHOR>
     * @Date 10:11 2022/2/23
     **/
    private boolean isNotExistRecord(String uniqueKey) {
        String key = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_FY.getType() + ":" + uniqueKey;
        Boolean flag = redisTemplate.opsForValue().setIfAbsent(key, "1", Duration.ofDays(92));
        return null != flag && flag;
    }

    @Override
    public void checkRequestTime(String key, int time) {
        synchronized (FYService.class) {
            super.checkRequestTime(key, time);
        }
    }

    /**
     * 接收智能外呼MQ外呼成功后返回的主叫被叫信息生成缺少通话信息的通话记录
     *
     * @param request 主被叫信息
     * <AUTHOR>
     * @Date 14:39 2022/5/28
     **/
    public void generateCallRecord(GenerateCallRecordRequest request) {
        // 生成通话记录保存到ElasticSearch 
        super.commonGenerateCallRecord(request);
    }
}
