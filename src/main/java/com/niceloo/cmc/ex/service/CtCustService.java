package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.entity.CtCust;

import java.util.List;

/**
 * <p>
 * 客户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
public interface CtCustService {


    /**
     * 根据客户手机号查询客户信息
     *
     * @param receiverPhone 加密后的客户手机号
     */
    List<CtCust> findByMobile(String receiverPhone);

    /**
     * @desc: 根据手机号集合查询客户信息
     * @author: song
     * @date: 2022/2/25
     */
    List<CtCust> findListByMobileList(List<String> mobileList);

    /**
     * @desc: 分页查询客户手机号
     * @author: song
     * @date: 2022/2/25
     */
    List<String> findMobileListByPage(Integer pageNum, Integer pageSize);

    /**
     * 批量保存客户信息[存在则覆盖,使用主键校验是否存在]
     *
     * @param list 要添加的客户数据列表
     * <AUTHOR> WangChenyu
     * @Date 2022/2/26
     */
    int replaceSaveBatch(List<CtCust> list);

    /**
     * @desc: 批量修改客户信息
     * @author: song
     * @date: 2022/2/26
     */
    int updateBatch(List<CtCust> list);

    /**
     * 通过使用主键范围代替分页查询客户手机(防止深分页的问题)
     * @param minPrimaryValue 最小主键值 <span>(custId > #{minPrimaryValue})</span>
     * @param size 查询多少条数据 <span>( limit size)</span>
     * @return 只包含custMobile和custId的数组
     */
    List<CtCust> selectMobileListUsePrimaryRange(String minPrimaryValue, Integer size);
}
