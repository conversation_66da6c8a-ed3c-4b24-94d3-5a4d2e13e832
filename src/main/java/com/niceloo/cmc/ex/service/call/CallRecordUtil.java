package com.niceloo.cmc.ex.service.call;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.CustomThreadFactory;
import com.niceloo.cmc.ex.entity.CtCust;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.exception.HttpResponseException;
import com.niceloo.cmc.ex.service.CtCustService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.cmc.ex.utils.FileUtil;
import com.niceloo.cmc.ex.utils.HttpUtils;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @desc 重构通话记录信息补偿工具
 * @date 2022/3/4
 */
@Component
public class CallRecordUtil {

    private static final Logger logger = LoggerFactory.getLogger(CallRecordUtil.class);

    private static final SecureRandom RANDOM = new SecureRandom();

    private static final ExecutorService addEmployeeInfoExecutorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(), new CustomThreadFactory("add_employee_info"));
    private static final ExecutorService addCustomerInfoExecutorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(), new CustomThreadFactory("add_employee_info"));

    private static FileUtil fileUtil;
    private static ElasticsearchOperations elasticsearchTemplate;

    @Resource
    public void setFileUtil(FileUtil fileUtil) {
        CallRecordUtil.fileUtil = fileUtil;
    }

    private static String yunKeUrl;

    @Value("${yunke.ossUrl}")
    public void setYunKeUrl(String yunKeUrl) {
        CallRecordUtil.yunKeUrl = yunKeUrl;
    }

    @Resource
    public void setElasticsearchTemplate(ElasticsearchOperations elasticsearchTemplate) {
        CallRecordUtil.elasticsearchTemplate = elasticsearchTemplate;
    }

    private static CtCustService ctCustService;

    @Resource
    public void setCtCustService(CtCustService ctCustService) {
        CallRecordUtil.ctCustService = ctCustService;
    }

    /**
     * 补充员工信息使用到的字段
     */
    private static final String[] SOURCE_FOR_ADD_EMPLOYEE_INFO = new String[]{"callId", "callAccount", "callTime", "channelType", "field3"};

    /**
     * 补充客户信息使用到的字段
     */
    private static final String[] SOURCE_FOR_ADD_CUSTOMER_INFO = new String[]{"callId", "reciverPhone"};

    /**
     * 从厂商同步录音文件使用到的字段
     */
    private static final String[] SOURCE_FOR_SYNC_VOICE = new String[]{"callId", "voiceSourceUrl", "channelType", "callTime", "reciverPhone", "callId", "voiceSyncStatus"};


    /**
     * @param indexes                   非空
     * @param callTimeStart,callTimeEnd 可空
     * @param channelType               可空
     * @return 任务执行情况
     * @Description: 添加员工信息
     * <AUTHOR>
     * @Date 2021/12/02 10:05
     */
    public static Future<BulkUpdateByQueryResult> addEmployeeInfo(String indexes, String callTimeStart, String callTimeEnd, String channelType) {
        String taskName = String.format("补充员工信息(%s--%s--%s--%s)", indexes, callTimeStart, callTimeEnd, channelType);

        //入参是实体类，出参是返回更新参数对象
        Function<ScrolledPage<CallRecord>, List<UpdateQuery>> hit2UpdateDoc = hitList -> {
            List<UpdateQuery> updateQuerys = new LinkedList<>();
            for (CallRecord hit : hitList.getContent()) {
                String recordChannelType = hit.getChannelType();
                if (CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType().equals(recordChannelType) && CallRecord.CALL_TYPE_TQ_FIX_FIELD3.equals(hit.getField3())) {
                    recordChannelType = CallChannelEnum.CALL_TYPE_TQ_FIX.getType();
                } else if (CallChannelEnum.CALL_TYPE_ZK.getType().equals(recordChannelType) && CallRecord.CALL_TYPE_YH_FIELD3.equals(hit.getField3())) {
                    recordChannelType = CallChannelEnum.CALL_TYPE_YH.getType();
                }

                CallRecord voiceVo = new CallRecord();
                voiceVo.setModifiedTime(new Date());
                voiceVo.setCallAccount(hit.getCallAccount());
                boolean added = CallRecordsBaseService.addEmployeeInfo(DateUtils.toStr(hit.getCallTime()), recordChannelType, voiceVo);
                if (added) {
                    //涉及批量更新参数返回
                    UpdateRequest updateRequest = new UpdateRequest();
                    updateRequest.retryOnConflict(3);
                    updateRequest.doc(JSONUtils.toMap(JSONUtils.toJSONString(voiceVo)));
                    UpdateQuery updateQuery = new UpdateQueryBuilder()
                            .withIndexName(RecordUtil.getRecordIndexName(hit.getCallTime()))
                            .withId(hit.getCallId())
                            .withDoUpsert(false)
                            .withUpdateRequest(updateRequest)
                            .withType("_doc").build();
                    updateQuerys.add(updateQuery);
                }
            }
            return updateQuerys;
        };

        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("_doc");
        SourceFilter sourceFilter = new FetchSourceFilter(SOURCE_FOR_ADD_EMPLOYEE_INFO, null);
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTypes("_doc");
        builder.withIndices(indexes);
        builder.withSort(sortBuilder);

        BoolQueryBuilder bqb = new BoolQueryBuilder();
        bqb.should(QueryBuilders.termQuery("callerUserId", ""));
        bqb.should(QueryBuilders.termQuery("schoolId", ""));
        bqb.should(QueryBuilders.termQuery("dptId", ""));

        if (StringUtils.isNotBlank(callTimeStart) && StringUtils.isNotBlank(callTimeEnd)) {
            bqb.filter(QueryBuilders.rangeQuery("callTime").gte(callTimeStart).lte(callTimeEnd));
        }
        if (StringUtils.isNotBlank(channelType)) {
            buildFilterByChannelType(bqb, channelType);
        }

        builder.withFilter(bqb);
        builder.withSourceFilter(sourceFilter);
        builder.withPageable(PageRequest.of(0, 1000));//从0页开始查，每页1000个结果
        return addEmployeeInfoExecutorService.submit(new BulkUpdateByQuery.Builder(taskName, 3 * 60 * 1000, builder.build(), hit2UpdateDoc).build());
    }

    /**
     * @param indexes                   非空
     * @param callTimeStart,callTimeEnd 可空
     * @param channelType               可空
     * @return 任务执行情况
     * @Description: 添加客户信息, 执行性能依赖CtCustService#findByMobile(String)
     * <AUTHOR>
     * @Date 2021/12/03 10:05
     * @version 1.0.0
     */
    public static Future<BulkUpdateByQueryResult> addCustomerInfo(String indexes, String callTimeStart, String callTimeEnd, String channelType) {
        String taskName = String.format("补充客户信息(%s--%s--%s--%s)", indexes, callTimeStart, callTimeEnd, channelType);

        Function<ScrolledPage<CallRecord>, List<UpdateQuery>> hit2UpdateDoc = hitList -> {
            List<UpdateQuery> updateQuerys = new LinkedList<>();
            for (CallRecord hit : hitList.getContent()) {
                String receiverPhone = hit.getReciverPhone();
                try {
                    // fix性能:需要批量查询接口
                    List<CtCust> custs = ctCustService.findByMobile(receiverPhone);
                    if (!custs.isEmpty()) {
                        CtCust cust = custs.get(0);
                        CallRecord voiceVo = new CallRecord();
                        voiceVo.setModifiedTime(new Date());
                        voiceVo.setReciverUserId(cust.getCustId());
                        voiceVo.setReciverbdUserId(cust.getUserId());
                        voiceVo.setReciverName(StringUtils.isEmpty(cust.getCustName()) ? "" : FieldCipherUtil.oneEncrypt(cust.getCustName()));
                        voiceVo.setAreaCode(cust.getCustMobileareacode());
                        //返回执行要执行的es条件
                        UpdateRequest updateRequest = new UpdateRequest();
                        updateRequest.retryOnConflict(3);
                        updateRequest.doc(JSONUtils.toMap(JSONUtils.toJSONString(voiceVo)));
                        UpdateQuery updateQuery = new UpdateQueryBuilder()
                                .withIndexName(RecordUtil.getRecordIndexName(hit.getCallTime()))
                                .withId(hit.getCallId())
                                .withDoUpsert(false)
                                .withUpdateRequest(updateRequest)
                                .withType("_doc").build();
                        updateQuerys.add(updateQuery);
                    }
                } catch (Exception e) {
                    logger.error(e, taskName + ":查询客户失败-->" + receiverPhone);
                }
            }
            return updateQuerys;
        };

        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("_doc");
        SourceFilter sourceFilter = new FetchSourceFilter(SOURCE_FOR_ADD_CUSTOMER_INFO, null);
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTypes("_doc");
        builder.withIndices(indexes);
        builder.withSort(sortBuilder);

        BoolQueryBuilder bqb = new BoolQueryBuilder();
        if (StringUtils.isNotBlank(callTimeStart) && StringUtils.isNotBlank(callTimeEnd)) {
            bqb.filter(QueryBuilders.rangeQuery("callTime").gte(callTimeStart).lte(callTimeEnd));
        }
        if (StringUtils.isNotBlank(channelType)) {
            buildFilterByChannelType(bqb, channelType);
        }
        bqb.filter(QueryBuilders.termQuery("reciverUserId", ""));
        builder.withQuery(bqb);
        builder.withSourceFilter(sourceFilter);
        builder.withPageable(PageRequest.of(0, 500));//从0页开始查，每页1000个结果
        return addCustomerInfoExecutorService.submit(new BulkUpdateByQuery.Builder(taskName, 5 * 60 * 1000, builder.build(), hit2UpdateDoc).build());
    }

    /**
     * 使用BulkUpdateByQuery分批同步录音:
     * 检索到一批录音未上传到OSS的通话记录-->从厂商下载录音并上传到OSS-->批量更新上传成功的录音的存储路径,继续处理下一批数据
     * 注意:1录音同步任务会提交给executorService异步执行,待处理数据越多任务执行时间越长
     * 2该方法未处理多任务并发场景.如果多任务并发执行且任务待处理数据有交叉,交叉的数据会被重复处理(即同一录音多次上传并更新存储路径),
     * 结果是同一录音被多次上传到OSS,但是只有一个存储路径会被更新到通话记录,浪费OSS储存空间.多任务并发执行时,方法调用方需保证
     * 各任务处理的通话记录数据不会交叉.
     *
     * @param indexes              要处理的索引,多个逗号拼接,非空
     * @param callTimeStart        起始时间,可为空
     * @param callTimeEnd          截止时间,可为空
     * @param channelType          厂商类型,可为空
     * @param voiceSyncStatusStart 录音已同步次数,可空,大于等于
     * @param voiceSyncStatusEnd   录音已同步次数,可空,小于等于
     * @return 任务执行情况
     * <AUTHOR>
     * @Date 2022/1/17 14:45
     */
    public static BulkUpdateByQuery syncVoice(String indexes, String callTimeStart, String callTimeEnd,
                                              Integer voiceSyncStatusStart, Integer voiceSyncStatusEnd, String channelType) {
        String taskName = String.format("从厂商同步录音文件(%s--%s--%s--%s--%s--%s)",
                indexes, callTimeStart, callTimeEnd, voiceSyncStatusStart, voiceSyncStatusEnd, channelType);

        Function<ScrolledPage<CallRecord>, List<UpdateQuery>> hit2UpdateDoc = hitList -> {
            List<UpdateQuery> updateQuerys = new LinkedList<>();
            for (CallRecord hit : hitList.getContent()) {
                String recordChannelType = hit.getChannelType();
                String voiceSourceUrl = hit.getVoiceSourceUrl();
                String defaultFileName = hit.getCallId() + ".mp3";

                Map<String, Object> uploadResultMap = null;
                if (CallChannelEnum.CALL_TYPE_YPHONE.getType().equals(recordChannelType)) {
                    // 手机号解密
                    String receiverPhone = FieldCipherUtil.decrypt(hit.getReciverPhone());
                    boolean correct = YKService.voiceSourceUrlIsCorrect(voiceSourceUrl, hit.getCallTime(), receiverPhone);
                    if (correct) {
                        try {
                            uploadResultMap = fileUtil.ossCopy2(voiceSourceUrl, defaultFileName, yunKeUrl);
                        } catch (Exception e) {
                            logger.warn(e, "任务名称:{},异常信息:{}", taskName, e.getMessage());
                        }
                    } else {
                        logger.info(taskName + ":识别到云客返回错误的录音文件url");
                        uploadResultMap = new HashMap<>();
                        uploadResultMap.put("filePath", "404");
                        uploadResultMap.put("fileKey", "");
                    }
                } else if (CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType().equals(recordChannelType)) {
                    try {
                        uploadResultMap = HttpUtils.uploadVoiceToOSS(voiceSourceUrl, defaultFileName);
                    } catch (HttpResponseException e) {
                        logger.info(taskName + "TQ录音地址响应异常,重试其他host", e);
                        try {
                            uploadResultMap = HttpUtils.uploadVoiceToOSS(TQService.switchHostName(voiceSourceUrl), defaultFileName);
                        } catch (Exception ex) {
                            logger.info(ex, taskName);
                        }
                    } catch (Exception e) {
                        logger.warn(e, taskName);
                    }
                } else if (CallChannelEnum.CALL_TYPE_ZK.getType().equals(recordChannelType)|| CallChannelEnum.CALL_TYPE_YH.getType().equals(recordChannelType)|| CallChannelEnum.CALL_TYPE_YX_SIP.getType().equals(recordChannelType)) {
                    try {
                        uploadResultMap = HttpUtils.uploadVoiceToOSS(voiceSourceUrl, defaultFileName);
                    } catch (HttpResponseException e) {
                        logger.info(taskName + "亿讯录音地址响应异常,重试其他host", e);
                        try {
                            uploadResultMap = HttpUtils.uploadVoiceToOSS(YXService.switchHostName(voiceSourceUrl), defaultFileName);
                        } catch (Exception ex) {
                            logger.info(ex, taskName);
                        }
                    } catch (Exception e) {
                        logger.warn(e, taskName);
                    }
                } else {
                    try {
                        uploadResultMap = HttpUtils.uploadVoiceToOSS(voiceSourceUrl, defaultFileName);
                    } catch (Exception e) {
                        logger.warn(e, taskName);
                    }
                }

                CallRecord voiceVo = new CallRecord();
                voiceVo.setModifiedTime(new Date());
                Integer voiceSyncStatus = hit.getVoiceSyncStatus();
                voiceVo.setVoiceSyncStatus(voiceSyncStatus == null ? 2 : (voiceSyncStatus + 1));
                if (!CollectionUtils.isEmpty(uploadResultMap)) {
                    voiceVo.setDataCompleteStatus("Y");
                    voiceVo.setServerFolder((String) uploadResultMap.get("filePath"));
                    voiceVo.setField1((String) uploadResultMap.get("fileKey"));
                }

                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.retryOnConflict(3);
                updateRequest.doc(JSONUtils.toMap(JSONUtils.toJSONString(voiceVo)));
                UpdateQuery updateQuery = new UpdateQueryBuilder()
                        .withIndexName(RecordUtil.getRecordIndexName(hit.getCallTime()))
                        .withId(hit.getCallId())
                        .withDoUpsert(false)
                        .withUpdateRequest(updateRequest)
                        .withType("_doc").build();
                updateQuerys.add(updateQuery);
            }
            return updateQuerys;
        };

        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("_doc");
        SourceFilter sourceFilter = new FetchSourceFilter(SOURCE_FOR_SYNC_VOICE, null);
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTypes("_doc");
        builder.withIndices(indexes);
        builder.withSort(sortBuilder);

        BoolQueryBuilder bqb = new BoolQueryBuilder();
        bqb.filter(QueryBuilders.termQuery("serverFolder", ""));
        if (null != voiceSyncStatusStart && null != voiceSyncStatusEnd) {
            bqb.filter(QueryBuilders.rangeQuery("voiceSyncStatus").gte(voiceSyncStatusStart).lte(voiceSyncStatusEnd));
        }
        bqb.mustNot(QueryBuilders.termQuery("voiceSourceUrl", ""));

        if (StringUtils.isNotBlank(callTimeStart) && StringUtils.isNotBlank(callTimeEnd)) {
            bqb.filter(QueryBuilders.rangeQuery("callTime").gte(callTimeStart).lte(callTimeEnd));
        }
        if (StringUtils.isNotBlank(channelType)) {
            buildFilterByChannelType(bqb, channelType);
        }
        builder.withQuery(bqb);
        builder.withSourceFilter(sourceFilter);
        builder.withPageable(PageRequest.of(0, 100));//从0页开始查，每页100个结果。过多可能会导致滚动查询时间超时
        NativeSearchQuery nsq = builder.build();
        return new BulkUpdateByQuery.Builder(taskName, 60 * 60 * 1000, nsq, hit2UpdateDoc).build();
    }

    private static void buildFilterByChannelType(BoolQueryBuilder builder, String channelType) {
        if (CallChannelEnum.CALL_TYPE_YH.getType().equals(channelType)) {
            builder.filter(QueryBuilders.termQuery("channelType", CallChannelEnum.CALL_TYPE_ZK.getType()));
            builder.filter(QueryBuilders.termQuery("field3", CallRecord.CALL_TYPE_YH_FIELD3));
        } else if (CallChannelEnum.CALL_TYPE_ZK.getType().equals(channelType)) {
            BoolQueryBuilder bqb = new BoolQueryBuilder();
            bqb.filter(QueryBuilders.termQuery("channelType", CallChannelEnum.CALL_TYPE_ZK.getType()));
            bqb.mustNot(QueryBuilders.termQuery("field3", CallRecord.CALL_TYPE_YH_FIELD3));
            builder.filter(bqb);
        } else if (CallChannelEnum.CALL_TYPE_TQ_FIX.getType().equals(channelType)) {
            builder.filter(QueryBuilders.termQuery("channelType", CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType()));
            builder.filter(QueryBuilders.termQuery("field3", CallRecord.CALL_TYPE_TQ_FIX_FIELD3));
        } else if (CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType().equals(channelType)) {
            BoolQueryBuilder bqb = new BoolQueryBuilder();
            bqb.filter(QueryBuilders.termQuery("channelType", CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType()));
            bqb.mustNot(QueryBuilders.termQuery("field3", CallRecord.CALL_TYPE_TQ_FIX_FIELD3));
            builder.filter(bqb);
        } else {
            builder.filter(QueryBuilders.termQuery("channelType", channelType));
        }
    }

    @FunctionalInterface
    public interface EsHitsFunction<R> extends Function<ScrolledPage<CallRecord>, R> {
        @Override
        R apply(ScrolledPage<CallRecord> scrolledPage);
    }

    public static class BulkUpdateByQuery implements Callable<BulkUpdateByQueryResult> {
        private final String taskName;
        private final long scrollTimelnMillis;
        private final NativeSearchQuery nativeSearchQuery;
        private final Function<ScrolledPage<CallRecord>, List<UpdateQuery>> hit2UpdateDoc;

        private BulkUpdateByQuery(Builder builder) {
            this.taskName = builder.taskName;
            this.scrollTimelnMillis = builder.scrollTimelnMillis;
            this.nativeSearchQuery = builder.nativeSearchQuery;
            this.hit2UpdateDoc = builder.hit2UpdateDoc;
        }

        @Override
        public BulkUpdateByQueryResult call() throws Exception {
            EsHitsFunction<BulkUpdateByQueryResult> esHitsFunction = hits -> {
//                List<CallRecord> callRecords = hits.getContent();
                BulkUpdateByQueryResult result = new BulkUpdateByQueryResult(taskName, (int) hits.getTotalElements(), hits.getSize());
                List<UpdateQuery> updateQuerys = hit2UpdateDoc.apply(hits);
                if (!updateQuerys.isEmpty()) {
                    try {
                        elasticsearchTemplate.bulkUpdate(updateQuerys);
                        result.setUpdated(updateQuerys.size());
                        updateQuerys.clear();
                    } catch (Exception e) {
                        logger.error(e, taskName + ":批量更新失败");
                        result.setUpdateFailed(updateQuerys.size());
                    }
                }
                return result;
            };

            try {
                logger.info(taskName + "开始");
                long beginTime = System.currentTimeMillis();
                BulkUpdateByQueryResult result = recordScrolledPage(scrollTimelnMillis, nativeSearchQuery, esHitsFunction,
                        BulkUpdateByQueryResult.getBinaryOperator(), new BulkUpdateByQueryResult(taskName));

                double elapsedTime = ((double) (System.currentTimeMillis() - beginTime)) / 1000;
                logger.info(String.format(taskName + "结束: 共查询到%d条异常数据,更新成功%d条,更新失败%d条,耗时%.2f秒",
                        result.getRetrieved(),
                        result.getUpdated(),
                        result.getUpdateFailed(),
                        elapsedTime));
                return result;
            } catch (Exception e) {
                logger.info(taskName + "因异常中断", e);
                throw new Exception(taskName + "因异常中断", e);
            }
        }

        static class Builder {
            private final String taskName;
            private final long scrollTimelnMillis;
            private final NativeSearchQuery nativeSearchQuery;
            private final Function<ScrolledPage<CallRecord>, List<UpdateQuery>> hit2UpdateDoc;

            Builder(String taskName, long scrollTimelnMillis, NativeSearchQuery nativeSearchQuery, Function<ScrolledPage<CallRecord>, List<UpdateQuery>> hit2UpdateDoc) {
                this.taskName = taskName;
                this.scrollTimelnMillis = scrollTimelnMillis;
                this.nativeSearchQuery = nativeSearchQuery;
                this.hit2UpdateDoc = hit2UpdateDoc;
            }

            BulkUpdateByQuery build() {
                return new BulkUpdateByQuery(this);
            }
        }
    }

    private static <R> R recordScrolledPage(long scrollTimeInMillis, SearchQuery query, EsHitsFunction<R> esHitsFunction, BinaryOperator<R> binaryOperator, R init) {

        R r = init;
        ScrolledPage<CallRecord> scroll = elasticsearchTemplate.startScroll(scrollTimeInMillis, query, CallRecord.class);
        while (scroll.hasContent()) {
            r = binaryOperator.apply(r, esHitsFunction.apply(scroll));
            scroll = elasticsearchTemplate.continueScroll(scroll.getScrollId(), scrollTimeInMillis, CallRecord.class);
        }
        elasticsearchTemplate.clearScroll(scroll.getScrollId());
        return r;
    }

    public static class BulkUpdateByQueryResult {
        private final String taskName;
        private int total = -1;
        private int retrieved;
        private int updated;
        private int updateFailed;

        public BulkUpdateByQueryResult(String taskName) {
            this.taskName = taskName;
        }

        public BulkUpdateByQueryResult(String taskName, int total, int retrieved) {
            this.taskName = taskName;
            this.total = total;
            this.retrieved = retrieved;
        }

        public int getTotal() {
            return total;
        }

        public int getRetrieved() {
            return retrieved;
        }

        public int getUpdated() {
            return updated;
        }

        public int getUpdateFailed() {
            return updateFailed;
        }

        public void setUpdated(int updated) {
            this.updated = updated;
        }

        public void setUpdateFailed(int updateFailed) {
            this.updateFailed = updateFailed;
        }

        /**
         * @return XX.XX%
         * @Description: 返回进度百分比
         * <AUTHOR>
         * @Date 2021/12/10 17:58
         */
        public String progressRate() {
            if (this.total < 0) {
                return "0.00%";
            }

            if (this.total == 0) {
                return "100.00%";
            }
            double progress = (double) this.retrieved / (double) this.total;
            return String.format("%.2f%%", progress * 100);
        }

        /**
         * @return XX.XX%
         * @Description: 返回更新失败百分比
         * <AUTHOR>
         * @Date 2021/12/10 17:58
         */
        public String updateFailureRate() {
            if (this.updateFailed + this.updated <= 0) {
                return "0.00%";
            }
            double updateFailureRate = (double) this.updateFailed / (double) (this.updateFailed + this.updated);
            return String.format("%.2f%%", updateFailureRate * 100);
        }


        /**
         * @return BinaryOperator
         * @Description: 合并两个BulkUpdateByQueryResult的结果
         * <AUTHOR>
         * @Date 2021/12/3 16:27
         */
        public static BinaryOperator<BulkUpdateByQueryResult> getBinaryOperator() {
            return (allBeforeResult, currentResult) -> {
                allBeforeResult.total = currentResult.total;
                allBeforeResult.retrieved = allBeforeResult.retrieved + currentResult.retrieved;
                allBeforeResult.updated = allBeforeResult.updated + currentResult.updated;
                allBeforeResult.updateFailed = allBeforeResult.updateFailed + currentResult.updateFailed;

                double logProbability = 0.1;
                if (RANDOM.nextDouble() < logProbability) {
                    logger.info(String.format("%s:进度%s, 更新失败率%s",
                            allBeforeResult.taskName, allBeforeResult.progressRate(), allBeforeResult.updateFailureRate()));
                }
                return allBeforeResult;
            };
        }
    }
}
