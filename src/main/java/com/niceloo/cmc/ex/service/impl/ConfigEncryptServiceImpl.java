package com.niceloo.cmc.ex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.entity.CcCountmonth;
import com.niceloo.cmc.ex.mapper.CcCallAccountConfigMapper;
import com.niceloo.cmc.ex.mapper.CcCountmonthMapper;
import com.niceloo.cmc.ex.service.CcCountmonthEncryptService;
import com.niceloo.cmc.ex.service.ConfigEncryptService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.db.dynamic.core.DS;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 加密月统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21 18:01:24
 */
@Service
@DS("cmc")
public class ConfigEncryptServiceImpl extends ServiceImpl<CcCallAccountConfigMapper, CcCallAccountConfig> implements ConfigEncryptService {
    private static final Logger logger = LoggerFactory.getLogger(ConfigEncryptServiceImpl.class);

    @Resource
    private CcCallAccountConfigMapper ccCallAccountConfigMapper;

    @Override
    public void encryptCcCallAccountConfig(int batch) {
        // 从表中获取记录
        // 将加密后的结果更新到字段中
        List<CcCallAccountConfig> entitiesToUpdate = new ArrayList<>();
        String encryptedFlagValue = "1";

        LambdaQueryWrapper<CcCallAccountConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.last("LIMIT 0," + batch);
        List<CcCallAccountConfig> ccCallAccountConfigs = ccCallAccountConfigMapper.selectList(wrapper);

        // 更新表
        for (CcCallAccountConfig ccCallAccountConfig : ccCallAccountConfigs) {
            String eeUserName = ccCallAccountConfig.getEeUserName();
            if (!FieldCipherUtil.checkEncrypt(eeUserName)) {
                String encryptedEeUserName = FieldCipherUtil.encrypt(eeUserName);
                ccCallAccountConfig.setEeUserName(encryptedEeUserName);
            }
            String account = ccCallAccountConfig.getAccount();
            if (!FieldCipherUtil.checkEncrypt(account)) {
                String encryptedAccount = FieldCipherUtil.encrypt(account);
                ccCallAccountConfig.setAccount(encryptedAccount);
            }
            String accountCreatorName = ccCallAccountConfig.getAccountCreatorName();
            if (!FieldCipherUtil.checkEncrypt(accountCreatorName)) {
                String encryptedAccountCreatorName = FieldCipherUtil.encrypt(accountCreatorName);
                ccCallAccountConfig.setAccountCreatorName(encryptedAccountCreatorName);
            }
            String accountModifierName = ccCallAccountConfig.getAccountModifierName();
            if (!FieldCipherUtil.checkEncrypt(accountModifierName)) {
                String encryptedAccountModifierName = FieldCipherUtil.encrypt(accountModifierName);
                ccCallAccountConfig.setAccountModifierName(encryptedAccountModifierName);
            }
            entitiesToUpdate.add(ccCallAccountConfig);
        }

        // 批量更新数据库
        if (!entitiesToUpdate.isEmpty()) {
            this.updateBatchById(entitiesToUpdate);
        }
    }
}
