package com.niceloo.cmc.ex.service;


import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.pojo.request.*;
import com.niceloo.cmc.ex.pojo.vo.BasePageVO;
import com.niceloo.cmc.ex.pojo.vo.CallAccountOperationListVO;
import com.niceloo.cmc.ex.pojo.vo.CallAccountVO;
import com.niceloo.cmc.ex.pojo.vo.EeCallAccountChannelVO;

import java.util.List;


/**
 * <p>
 * 外呼账号配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
public interface CcCallAccountConfigService {

    /**
     * 根据外呼账号表id查询账号信息
     *
     * @param id 外呼账号表id
     * @return 外呼账号信息
     */
    CcCallAccountConfig searchCallAccountInfoById(String id);

    /**
     * 根据主叫账号和外呼通道查询账号信息(一个外呼通道里,账号和员工一对一)
     *
     * @param account     外呼账号
     * @param channelEnum 通道枚举
     * @return 外呼账号信息
     */
    CcCallAccountConfig searchCallAccountInfoByAccount(String account, CallChannelEnum channelEnum);

    /**
     * 分页查询
     *
     * @param request 查询条件
     * @return 外呼账号信息列表
     */
    BasePageVO<CallAccountVO> searchPage(CallAccountSelectRequest request);

    /**
     * 根据外呼账号id查询操作记录列表
     *
     * @param callAccountId 外呼账号id
     * @return 操作记录列表
     */
    List<CallAccountOperationListVO> searchAccountOperationLogList(String callAccountId);

    /**
     * 外呼账号是否存在(未绑定到员工也为存在,删除状态为'Y'时为不存在)
     *
     * @param account     外呼账号
     * @param channelEnum 通道枚举
     * @return true->存在,false->不存在
     */
    boolean hasCallAccount(String account, CallChannelEnum channelEnum);

    /**
     * 该员工在该通道下是否存在账号(未绑定到员工为不存在,删除状态为'Y'时为不存在)
     *
     * @param userId      员工id
     * @param channelEnum 通道枚举
     */
    boolean hasCallAccountByUserId(String userId, CallChannelEnum channelEnum);

    /**
     * 添加外呼账号(包括生成记录到记录表)
     * <p>***没有做是否重复校验,请在调用该方法之前校验,可以使用 {@link #hasCallAccount}方法进行校验</p>
     *
     * @param request 添加请求参数
     */
    void insertCallAccount(CallAccountAddRequest request);

    /**
     * 修改操作外呼账号(解绑、换绑、绑定)[包括生成记录到记录表]
     *
     * @param request 操作请求参数
     */
    void accountOperation(CallAccountOperationRequest request);

    /**
     * 批量查询员工拥有的外呼通道
     *
     * @param eeUserIds 员工外呼id
     * @return 外呼通道列表
     */
    List<EeCallAccountChannelVO> batchQueryEeCallChannel(List<String> eeUserIds);

    /**
     * 根据员工id和外呼通道查询账号信息(一个外呼通道里,账号和员工一对一)
     *
     * @param userId      员工id
     * @param channelType 外呼通道
     */
    CcCallAccountConfig searchCallAccountInfoByUserId(String userId, String channelType);

    /**
     * 根据id列表查询出绑定着员工的外呼账号配置列表(绑定状态为'Y')
     *
     * @param ids id列表 [不能为空]
     */
    List<CcCallAccountConfig> searchBandingAccountListByIds(List<String> ids);

    /**
     * 批量解绑
     *
     * @param request           包含解绑人的信息
     * @param accountConfigList 需要解绑的账号列表
     */
    void batchUnbind(CallAccountBatchUnbindRequest request, List<CcCallAccountConfig> accountConfigList);

    /**
     * 获取当前账号创建者的组织架构相关id (schoolId,dptId,userId)
     *
     * @param id 主键id
     * @return 组织架构相关id
     */
    AuthBaseRequest getOrgStructureOfCreator(String id);

}
