package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.UpdateRecordingDTO;
import com.niceloo.cmc.ex.pojo.request.CallRecordsSelectRequest;
import com.niceloo.cmc.ex.pojo.request.CustRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.BasePageVO;
import com.niceloo.cmc.ex.pojo.vo.BizRecordVO;
import com.niceloo.cmc.ex.pojo.vo.CallRecordsListVO;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.CtCustService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.plugin.sdk.lang.Pair;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.IdsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.ElasticsearchException;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_TQ_MOBILE;
import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_ZK;
import static com.niceloo.cmc.ex.entity.es.CallRecord.CALL_TYPE_TQ_FIX_FIELD3;
import static com.niceloo.cmc.ex.entity.es.CallRecord.CALL_TYPE_YH_FIELD3;

/**
 * @description: 通话记录service
 * @author: WangChenyu
 * @create: 2022-02-17 15:05
 */
@Service
public class CallRecordServiceImpl implements CallRecordService {
    private static final Logger logger = LoggerFactory.getLogger(CallRecordServiceImpl.class);
    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Resource
    private CtCustService ctCustService;


    /**
     * 批量更新通话记录
     *
     * @paramter updateRecordingDTOS 需要修改的属性列表
     * <AUTHOR>
     * @Date 9:44 2022/2/18
     **/
    @Override
    public void bulkUpdate(List<UpdateRecordingDTO> updateRecordingDTOS) {
        if (CollectionUtils.isEmpty(updateRecordingDTOS)) {
            return;
        }
        Map<String, List<UpdateRecordingDTO>> listMap = updateRecordingDTOS.stream().collect(Collectors.groupingBy(UpdateRecordingDTO::getIndex));
        for (Map.Entry<String, List<UpdateRecordingDTO>> entry : listMap.entrySet()) {
            List<CallRecord> callRecords = BeanUtils.copyFromMultiObjToClass(CallRecord.class, entry.getValue());
            this.bulkUpdate(entry.getKey(), callRecords);
        }
    }

    /**
     * 批量更新通话记录
     *
     * @paramter indexName 索引名称
     * @paramter callRecords 更新列表
     * <AUTHOR>
     * @Date 9:46 2022/2/18
     **/
    @Override
    public void bulkUpdate(String indexName, List<CallRecord> callRecords) {
        if (!CollectionUtils.isEmpty(callRecords)) {
            List<UpdateQuery> updateQueryList = new ArrayList<>(callRecords.size());
            for (CallRecord callRecord : callRecords) {
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.doc(JSONUtils.toMap(JSONUtils.toJSONString(callRecord)));
                UpdateQuery updateQuery = new UpdateQueryBuilder()
                        .withIndexName(indexName)
                        .withId(callRecord.getCallId())
                        .withType("_doc")
                        .withDoUpsert(false)
                        .withUpdateRequest(updateRequest)
                        .build();
                updateQueryList.add(updateQuery);
            }
            elasticsearchTemplate.bulkUpdate(updateQueryList);
        }
    }

    /**
     * 从通话记录里获取被叫手机号和员工的userID信息列表
     *
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.CallRecord>
     * @paramter indexName 索引名称
     * @paramter ids id列表
     * <AUTHOR>
     * @Date 10:58 2022/2/18
     **/
    @Override
    public List<CallRecord> getPhoneAndUserIdList(String indexName, List<String> ids) {
        IdsQueryBuilder idsQueryBuilder = QueryBuilders.idsQuery().addIds(ids.toArray(new String[0]));
        SourceFilter sourceFilter = new FetchSourceFilter(new String[]{"callId", "callerUserId", "reciverPhone", "voiceSourceId"}, null);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(idsQueryBuilder)
                .withTypes("_doc")
                .withPageable(PageRequest.of(0, ids.size()))
                .withSourceFilter(sourceFilter)
                .build();
        List<CallRecord> callRecords = elasticsearchTemplate.queryForList(searchQuery, CallRecord.class);
        return CollectionUtils.isEmpty(callRecords) ? new ArrayList<>() : new ArrayList<>(callRecords);
    }

    /**
     * 批量添加通话记录
     *
     * @param index          索引名称
     * @param callRecordList 通话记录列表
     * <AUTHOR>
     * @Date 11:01 2022/2/23
     **/
    @Override
    public void bulkSave(String index, List<CallRecord> callRecordList) {
        if (CollectionUtils.isEmpty(callRecordList)) {
            return;
        }
        List<IndexQuery> indexQueries = new ArrayList<>(callRecordList.size());
        for (CallRecord callRecord : callRecordList) {
            IndexQuery indexQuery = new IndexQueryBuilder()
                    .withIndexName(index)
                    .withType("_doc")
                    .withId(callRecord.getCallId())
                    .withObject(callRecord)
                    .build();
            indexQueries.add(indexQuery);
        }
        try {
            elasticsearchTemplate.bulkIndex(indexQueries);
        } catch (ElasticsearchException e) {
            String message = e.getMessage();
            logger.error(e, "批量添加通话记录到ES出现异常,索引名称:{},异常信息:{}", index, message);
            // 因为索引不存在报错，创建索引后重试
            if (message.contains("type=index_not_found_exception")) {
                this.retryBulkSave(index, callRecordList);
            }
        }
    }

    /**
     * 重试批量添加，配合 {@link CallRecordService#bulkSave}失败后重试
     *
     * @param index          索引名称
     * @param callRecordList 通话记录列表
     * <AUTHOR>
     * @Date 14:44 2022/9/1
     **/
    private void retryBulkSave(String index, List<CallRecord> callRecordList) {
        if (CollectionUtils.isEmpty(callRecordList)) {
            return;
        }
        // 创建索引
        this.createIndex(index);
        List<IndexQuery> indexQueries = new ArrayList<>(callRecordList.size());
        for (CallRecord callRecord : callRecordList) {
            IndexQuery indexQuery = new IndexQueryBuilder()
                    .withIndexName(index)
                    .withType("_doc")
                    .withId(callRecord.getCallId())
                    .withObject(callRecord)
                    .build();
            indexQueries.add(indexQuery);
        }
        elasticsearchTemplate.bulkIndex(indexQueries);
    }


    /**
     * 创建索引,创建之前先校验是否存在,若存在直接返回true,否则创建索引返回false
     *
     * @return boolean
     * @paramter index 索引名称
     * <AUTHOR>
     * @Date 11:17 2022/2/23
     **/
    @Override
    public boolean createIndex(String index) {
        boolean exists = elasticsearchTemplate.indexExists(index);
        if (!exists) {
            synchronized (this) {
                exists = elasticsearchTemplate.indexExists(index);
                if (!exists) {
                    Map<String, String> setting = new HashMap<>();
                    setting.put("number_of_replicas", "1");
                    setting.put("max_result_window", String.valueOf(BizConst.ES_SEARCH_SIZE));
                    setting.put("number_of_shards", "5");
                    elasticsearchTemplate.createIndex(index, setting);
                    elasticsearchTemplate.putMapping(index, "_doc", CallRecord.getCallRecordMapping());
                }
            }
        }
        return exists;
    }

    /**
     * 获取通话记录话务相关信息列表(滚动查询ES)
     *
     * @param createdTimeStart 查询开始时间 yyyy-MM-dd HH:mm:ss
     * @param createdTimeEnd   查询结束时间 yyyy-MM-dd HH:mm:ss
     * @param sourceSearch     需要查询的字段列表
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.CallRecord>
     * <AUTHOR>
     * @Date 17:58 2022/2/23
     **/
    @Override
    public List<CallRecord> getTrafficInfoList(String createdTimeStart, String createdTimeEnd, List<String> sourceSearch) {
        //索引名称
        List<String> indices = RecordUtil.getIndiceList(createdTimeStart, createdTimeEnd);
        //查询条件
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("callTime").gte(createdTimeStart).lte(createdTimeEnd);
        // 不统计通话时长小于0的,因为-1表示通话记录的通话数据还没有获取到.统计了会更加不准确
        RangeQueryBuilder durationRangeQuery = QueryBuilders.rangeQuery("duration").gt(-1);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(rangeQueryBuilder);
        boolQueryBuilder.must(durationRangeQuery);
        SourceFilter sourceFilter = new FetchSourceFilter(sourceSearch.toArray(new String[0]), null);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indices.toArray(new String[0]))
                .withFilter(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 3500))
                .withTypes("_doc")
                .withSourceFilter(sourceFilter)
                .build();
        //滚动查询
        List<CallRecord> callRecordList = this.scrollSelectList(searchQuery);
        return CollectionUtils.isEmpty(callRecordList) ? new ArrayList<>() : new ArrayList<>(callRecordList);
    }


    /**
     * 根据条件滚动查询列表
     *
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.CallRecord>
     * @paramter searchQuery 查询条件
     * <AUTHOR>
     * @Date 11:42 2022/2/24
     **/
    @Override
    public List<CallRecord> scrollSelectList(SearchQuery searchQuery) {
        //滚动查询
        ScrolledPage<CallRecord> recordScrolledPage = elasticsearchTemplate.startScroll(5 * 60 * 1000, searchQuery, CallRecord.class);
        String scrollId = recordScrolledPage.getScrollId();
        List<CallRecord> callRecordList = new ArrayList<>(recordScrolledPage.getContent());
        while (recordScrolledPage.hasContent()) {
            recordScrolledPage = elasticsearchTemplate.continueScroll(scrollId, 5 * 60 * 1000, CallRecord.class);
            scrollId = recordScrolledPage.getScrollId();
            List<CallRecord> content = recordScrolledPage.getContent();
            if (CollectionUtils.isEmpty(content)) {
                break;
            }
            callRecordList.addAll(content);
        }
        elasticsearchTemplate.clearScroll(scrollId);
        return callRecordList;
    }

    /**
     * 查询通话记录根据参数(接口 话务查询调用)
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.CallRecordsListVO>
     * @paramter request 接口请求参数
     * <AUTHOR>
     * @Date 16:34 2022/3/7
     **/
    @Override
    public BasePageVO<CallRecordsListVO> searchRecordsByCriteria(CallRecordsSelectRequest request) {
        String timeStart = request.getCreatedTimeStart();
        String timeEnd = request.getCreatedTimeEnd();
        List<String> indices = RecordUtil.getIndiceList(timeStart, timeEnd);
        Integer pageIndex = request.getPageIndex();//offset
        Integer pageSize = request.getPageSize();
        int page = (pageIndex + pageSize - 1) / pageSize;
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "callTime"));
        SourceFilter sourceFilter = new FetchSourceFilter(this.searchRecordsByCriteriaSourceFilter(), null);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indices.toArray(new String[0]))
                .withFilter(this.stitchParams(request))
                .withTypes("_doc")
                .withPageable(pageable)
                .withSourceFilter(sourceFilter)
                .build();
        AggregatedPage<CallRecordsListVO> callRecordsListResult = elasticsearchTemplate.queryForPage(searchQuery, CallRecordsListVO.class);
        long totalElements = callRecordsListResult.getTotalElements();
        List<CallRecordsListVO> callRecordsListVOS = callRecordsListResult.getContent();
        return new BasePageVO<>((int) totalElements, new ArrayList<>(callRecordsListVOS));
    }

    /**
     * 检索ES索引库, 根据业务标识获取通话记录, 按照呼叫时间(callTime)倒序
     *
     * @param indexes         待检索的索引库
     * @param bizIdSet        业务标识集合, 业务标识即VoiceVo.field2字段
     * @param selectFieldList 要查询字段的列表
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.BizRecordVO>
     * <AUTHOR>
     * @Date 11:01 2022/3/8
     **/
    @Override
    public List<BizRecordVO> queryRecordsByBizIds(String indexes, List<String> bizIdSet, List<String> selectFieldList) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termsQuery("field2", bizIdSet));
        SourceFilter sourceFilter = new FetchSourceFilter(selectFieldList.toArray(new String[0]), null);
        PageRequest pageRequest = PageRequest.of(0, 1000, Sort.by(Sort.Direction.DESC, "callTime"));
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexes.split(","))
                .withFilter(boolQuery)
                .withTypes("_doc")
                .withPageable(pageRequest)
                .withSourceFilter(sourceFilter)
                .build();
        List<BizRecordVO> bizRecordVOS = elasticsearchTemplate.queryForList(searchQuery, BizRecordVO.class);
        return null == bizRecordVOS ? new ArrayList<>() : new ArrayList<>(bizRecordVOS);
    }

    /**
     * 根据外呼唯一id查询补充的字段信息,例如:field1,field2,field5,field6
     *
     * @param indexName   索引名称
     * @param channelType 外呼类型
     * @param uniqueId    厂商外呼ID(field2)
     * @return com.niceloo.cmc.ex.entity.es.CallRecord 只包含补充的字段
     * <AUTHOR>
     * @Date 11:19 2022/5/30
     **/
    @Override
    public CallRecord querySupplementaryFieldByUniqueId(String indexName, String channelType, String uniqueId) {
        if (StringUtils.isEmpty(uniqueId)) {
            return null;
        }
        List<CallRecord> callRecords = this.querySupplementaryFieldByUniqueIds(indexName, channelType, new ArrayList<>(List.of(uniqueId)));
        if (CollectionUtils.isEmpty(callRecords)) {
            return null;
        }
        return callRecords.get(0);
    }

    /**
     * 通过第三方的厂商id校验是否此条通话记录
     *
     * @param indexName     索引名称
     * @param voiceSourceId 厂商的通话记录id
     * @param channelType   外呼类型
     * @return boolean
     * <AUTHOR>
     * @Date 13:42 2022/8/22
     **/
    @Override
    public boolean checkCallRecordByVoiceSourceId(String indexName, String voiceSourceId, String channelType) {
        if (StringUtils.isEmpty(voiceSourceId)) {
            return false;
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("channelType", channelType));
        boolQuery.filter(QueryBuilders.termQuery("voiceSourceId", voiceSourceId));
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(boolQuery)
                .withTypes("_doc")
                .withSort(SortBuilders.fieldSort("_doc"))
                .build();
        long count = elasticsearchTemplate.count(searchQuery);
        return count > 0;
    }

    @Override
    public List<CallRecord> querySupplementaryFieldByUniqueIds(String indexName, String channelType, List<String> uniqueIds) {
        if (CollectionUtils.isEmpty(uniqueIds)) {
            return new ArrayList<>();
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("channelType", channelType));
        boolQuery.filter(QueryBuilders.termsQuery("field2", uniqueIds));
        SourceFilter sourceFilter = new FetchSourceFilter(new String[]{"callId", "field2", "field5", "field6", "duration", "reciverPhone", "callAccount"}, null);
        Pageable pageable = PageRequest.of(0, uniqueIds.size(), Sort.by(Sort.Direction.ASC, "createdTime"));
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(boolQuery)
                .withTypes("_doc")
                .withSourceFilter(sourceFilter)
                .withPageable(pageable)
                .build();
        List<CallRecord> callRecords = elasticsearchTemplate.queryForList(searchQuery, CallRecord.class);
        if (CollectionUtils.isEmpty(callRecords)) {
            return new ArrayList<>();
        }
        return callRecords;
    }

    @Override
    public void batchDel(List<String> indexNames) {
        if (CollectionUtils.isEmpty(indexNames)) {
            throw new RuntimeException("索引名称不能为空");
        }
        for (String indexName : indexNames) {
            elasticsearchTemplate.deleteIndex(indexName);
        }
    }


    /**
     * 根据查询条件滚动查询
     *
     * @param searchQuery 查询条件
     * @return com.niceloo.plugin.sdk.lang.Pair<java.lang.String, java.util.List < com.niceloo.cmc.ex.entity.es.CallRecord>>
     * <AUTHOR>
     * @Date 18:02 2022/8/18
     **/
    public Pair<String, List<CallRecord>> startScroll(SearchQuery searchQuery) {
        //滚动查询
        Pair<String, List<CallRecord>> pair = new Pair<>();
        ScrolledPage<CallRecord> recordScrolledPage = elasticsearchTemplate.startScroll(5 * 60 * 1000, searchQuery, CallRecord.class);
        if (recordScrolledPage.hasContent()) {
            List<CallRecord> callRecordList = recordScrolledPage.getContent();
            if (!CollectionUtils.isEmpty(callRecordList)) {
                String scrollId = recordScrolledPage.getScrollId();
                pair.setKey(scrollId);
                pair.setValue(callRecordList);
                return pair;
            }
        }
        return pair;
    }

    /**
     * 根据滚动id继续滚动查询
     *
     * @param scrollId 滚动id
     * @return com.niceloo.plugin.sdk.lang.Pair<java.lang.String, java.util.List < com.niceloo.cmc.ex.entity.es.CallRecord>>
     * <AUTHOR>
     * @Date 18:02 2022/8/18
     **/
    public Pair<String, List<CallRecord>> continueScroll(String scrollId) {
        Pair<String, List<CallRecord>> pair = new Pair<>();
        ScrolledPage<CallRecord> recordScrolledPage = elasticsearchTemplate.continueScroll(scrollId, 5 * 60 * 1000, CallRecord.class);
        scrollId = recordScrolledPage.getScrollId();
        if (recordScrolledPage.hasContent()) {
            List<CallRecord> callRecordList = recordScrolledPage.getContent();
            if (!CollectionUtils.isEmpty(callRecordList)) {
                pair.setKey(scrollId);
                pair.setValue(callRecordList);
                return pair;
            }
        }
        elasticsearchTemplate.clearScroll(scrollId);
        return pair;
    }

    /**
     * 根据id删除文档
     *
     * @param indexName 索引名称
     * @param callId    外呼id
     * @return java.lang.String
     * <AUTHOR>
     * @Date 16:14 2022/8/19
     **/
    public String deleteDoc(String indexName, String callId) {
        if (StringUtils.isEmpty(indexName) || StringUtils.isEmpty(callId)) {
            return null;
        }
        return elasticsearchTemplate.delete(indexName, "_doc", callId);
    }


    /**
     * 根据客户id查询通话记录
     *
     * @param request 请求参数
     * @return com.niceloo.cmc.ex.pojo.vo.BasePageVO<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @Date 13:41 2022/8/27
     **/
    @Override
    public BasePageVO<Map<String, Object>> queryRecordsByCustId(CustRecordRequest request) {
        String timeStart = request.getCreatedTimeStart();
        String timeEnd = request.getCreatedTimeEnd();
        // 索引名称
        List<String> indices = RecordUtil.getIndiceList(timeStart, timeEnd);
        // 要查询的字段
        SourceFilter sourceFilter = new FetchSourceFilter(request.getFields().split(","), null);
        // 查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("reciverUserId", request.getCustId()));
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("callTime");
        rangeQuery.gte(timeStart).lte(timeEnd);
        boolQuery.filter(rangeQuery);
        if (StringUtils.isNotEmpty(request.getEeUserId())) {
            boolQuery.filter(QueryBuilders.termQuery("callerUserId", request.getEeUserId()));
        }
        if (StringUtils.isNotEmpty(request.getDptId())) {
            boolQuery.filter(QueryBuilders.termQuery("dptId", request.getDptId()));
        }
        if (StringUtils.isNotEmpty(request.getSchoolId())) {
            boolQuery.filter(QueryBuilders.termQuery("schoolId", request.getSchoolId()));
        }
        if (StringUtils.isNotEmpty(request.getChannelType())) {
            boolQuery.filter(QueryBuilders.termQuery("channelType", request.getChannelType()));
        }
        if (StringUtils.isNotEmpty(request.getHasRecording()) && "Y".equals(request.getHasRecording())) {
            boolQuery.mustNot(QueryBuilders.termQuery("serverFolder", ""));
        }
        Integer pageSize = request.getPageSize();
        Integer pageIndex = request.getPageIndex();//offset
        int page = (pageIndex + pageSize - 1) / pageSize;
        // 是否分页(如果不分页最多返回最近100条)
        if (pageIndex == -1) {
            page = 0;
            pageSize = 100;
        }
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "callTime"));
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withIndices(indices.toArray(new String[0]))
                .withFilter(boolQuery)
                .withTypes("_doc")
                .withPageable(pageable)
                .withSourceFilter(sourceFilter);
        // 查询ES
        AggregatedPage<CallRecord> callRecordsListResult = elasticsearchTemplate.queryForPage(nativeSearchQueryBuilder.build(), CallRecord.class);
        long totalElements = callRecordsListResult.getTotalElements();
        List<CallRecord> callRecordList = callRecordsListResult.getContent();
        List<Map<String, Object>> callRecordMapList = new ArrayList<>(callRecordList.size());
        callRecordList.forEach(t -> callRecordMapList.add(JSONUtils.toMap(JSONUtils.toJSONString(t))));
        return new BasePageVO<>((int) totalElements, new ArrayList<>(callRecordMapList));
    }

    /**
     * 请求参数拼接(调用接口查询)
     *
     * @return org.elasticsearch.index.query.BoolQueryBuilder
     * @paramter request
     * <AUTHOR>
     * @Date 17:35 2022/3/7
     **/
    private BoolQueryBuilder stitchParams(CallRecordsSelectRequest request) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(request.getEeUserId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("callerUserId", request.getEeUserId()));
        }
        if (StringUtils.isNotEmpty(request.getCustId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("reciverUserId", request.getCustId()));
        }
        if (StringUtils.isNotEmpty(request.getCallAccount())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("callAccount", request.getCallAccount()));
        }
        if (StringUtils.isNotEmpty(request.getCallPhone())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("callPhone", request.getCallPhone()));
        }
        if (StringUtils.isNotEmpty(request.getVoiceStatus())) {
            // R->未响应
            if ("R".equals(request.getVoiceStatus())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("duration", -1));
            } else {
                boolQueryBuilder.filter(QueryBuilders.termQuery("voiceStatus", request.getVoiceStatus()));
            }
        }
        // 录音状态
        if (StringUtils.isNotEmpty(request.getRecordingStatus())) {
            if ("Y".equals(request.getRecordingStatus())) {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("serverFolder", ""));
            } else {
                boolQueryBuilder.filter(QueryBuilders.termQuery("serverFolder", ""));
            }
        }
        if (StringUtils.isNotEmpty(request.getCallType())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("callType", request.getCallType()));
        }
        if (StringUtils.isNotEmpty(request.getSchoolId())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("schoolId", request.getSchoolId().split(",")));
        }
        if (StringUtils.isNotEmpty(request.getDptId())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("dptId", request.getDptId().split(",")));
        }
        if (StringUtils.isNotEmpty(request.getCreatedTimeStart()) && StringUtils.isNotEmpty(request.getCreatedTimeEnd())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("callTime").gte(request.getCreatedTimeStart()).lte(request.getCreatedTimeEnd()));
        }
        boolean flag = false;
        RangeQueryBuilder duration = QueryBuilders.rangeQuery("duration");
        if (null != request.getDurationMax() && request.getDurationMax() > 0) {
            duration.lte(request.getDurationMax());
            flag = true;
        }
        if (null != request.getDurationMin() && request.getDurationMin() > 0) {
            duration.gte(request.getDurationMin());
            flag = true;
        }
        if (flag) {
            boolQueryBuilder.filter(duration);
        }
        String channelType = request.getChannelType();
        CallChannelEnum callChannel = CallChannelEnum.getCallChannel(channelType);
        if (null != callChannel) {
            switch (callChannel) {
                case CALL_TYPE_TQ_MOBILE: {
                    BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
                    boolQueryBuilder2.filter(QueryBuilders.termQuery("channelType", CALL_TYPE_TQ_MOBILE.getType()));
                    boolQueryBuilder2.mustNot(QueryBuilders.termQuery("field3", CALL_TYPE_TQ_FIX_FIELD3));
                    boolQueryBuilder.filter(boolQueryBuilder2);
                    break;
                }
                case CALL_TYPE_TQ_FIX: {
                    boolQueryBuilder.filter(QueryBuilders.termQuery("channelType", CALL_TYPE_TQ_MOBILE.getType()));
                    boolQueryBuilder.filter(QueryBuilders.termQuery("field3", CALL_TYPE_TQ_FIX_FIELD3));
                    break;
                }
                case CALL_TYPE_YH: {
                    boolQueryBuilder.filter(QueryBuilders.termQuery("channelType", CALL_TYPE_ZK.getType()));
                    boolQueryBuilder.filter(QueryBuilders.termQuery("field3", CALL_TYPE_YH_FIELD3));
                    break;
                }
                case CALL_TYPE_ZK: {
                    BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
                    boolQueryBuilder2.filter(QueryBuilders.termQuery("channelType", CALL_TYPE_ZK.getType()));
                    boolQueryBuilder2.mustNot(QueryBuilders.termQuery("field3", CALL_TYPE_YH_FIELD3));
                    boolQueryBuilder.filter(boolQueryBuilder2);
                    break;
                }
                default: {
                    boolQueryBuilder.filter(QueryBuilders.termsQuery("channelType", channelType));
                    break;
                }
            }
        }
        if (StringUtils.isNotEmpty(request.getReciverPhone())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("reciverPhone", FieldCipherUtil.oneEncrypt(request.getReciverPhone())));
        }
        if (StringUtils.isNotEmpty(request.getReciverName())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("reciverName", FieldCipherUtil.oneEncrypt(request.getReciverName())));
        }
        return boolQueryBuilder;
    }

    /**
     * 查询通话记录返回属性
     *
     * @return java.lang.String[]
     * @paramter
     * <AUTHOR>
     * @Date 16:51 2022/3/7
     **/
    private String[] searchRecordsByCriteriaSourceFilter() {
        List<String> source = new ArrayList<>();
        source.add("reciverUserId");
        source.add("reciverName");
        source.add("reciverPhone");
        source.add("callerName");
        source.add("callAccount");
        source.add("callPhone");
        source.add("schoolId");
        source.add("schoolName");
        source.add("dptId");
        source.add("dptName");
        source.add("channelType");
        source.add("callType");
        source.add("voiceStatus");
        source.add("callTime");
        source.add("duration");
        source.add("serverFolder");
        source.add("field1");
        source.add("field3");
        return source.toArray(new String[0]);
    }
}
