package com.niceloo.cmc.ex.service.call;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.entity.es.CallRecording;
import com.niceloo.cmc.ex.pojo.dto.AccountDTO;
import com.niceloo.cmc.ex.pojo.dto.YKCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.dto.YXCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.dto.YXSipCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.request.GenerateCallRecordRequest;
import com.niceloo.cmc.ex.service.BdCallaccountinfoService;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.CallRecordingService;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.service.impl.CallRecordingServiceImpl;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.cmc.ex.utils.OkHttpUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.utils.encrypt.MD5Utils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.mq.client.Client;
import okhttp3.Headers;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.BizConst.*;
import static com.niceloo.cmc.ex.common.CallChannelEnum.*;

/**
 * 亿迅厂商服务
 *
 * @author: WangChenyu
 * @create: 2022-02-23 11:29
 */
@Service
public class YXService extends CallRecordsBaseService {
    private static final Logger logger = LoggerFactory.getLogger(YXService.class);

    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);
    private final StringRedisTemplate redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
    private final BdCallaccountinfoService bdCallaccountinfoService = SpringUtils.getBean(BdCallaccountinfoService.class);
    private final CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);
    private final CallRecordingService callRecordingService = SpringUtils.getBean(CallRecordingServiceImpl.class);
    private final Client client = SpringUtils.getBean(Client.class);
    public static final Map<String, String> HEADER = Map.of("Content-Type", "application/json;charset=utf-8", "Accept", "application/json");
    public static final Map<String, String> PARAM = Map.of("debug", "true", "lang", "zh_CN");

    /**
     * 亿讯厂商提供的不同外呼方式(ZK/YX)
     */
    private String zkChannelType = CallChannelEnum.CALL_TYPE_ZK.getType();
    /**
     * 指定拉取通话数据使用的账号
     * 如果不指定,查询属于zkChannelType类型的未删除账号
     */
    private AccountDTO account;
    /**
     * 查询起始Id,null为第一条记录开始
     */
    private String startKey;
    /**
     * 是否需要下载通话记录对应的录音文件,默认不需要下载
     */
    private boolean downloadVoice = false;
    // 存储->添加通话记录到此列表
    private final List<CallRecord> callRecordList = new LinkedList<>();

    public YXService() {
    }

    public YXService(String zkChannelType, AccountDTO account, boolean downloadVoice) {
        this.zkChannelType = zkChannelType;
        this.account = account;
        this.downloadVoice = downloadVoice;
    }

    public YXService(String channelType, AccountDTO account, String startKey, String actionType) {
        this.zkChannelType = channelType;
        this.account = account;
        this.startKey = startKey;
        super.ACTION_TYPE = actionType;
    }

    /**
     * 切换亿讯服务器域名。
     * 亿讯后台可对录音地址进行加密，存在明文地址和暗文地址，调用路径方式不同。
     * 详细信息请参考亿讯后台文档：
     * <a href="http://*************:81/web/#/9/867">亿讯后台文档</a>
     *
     * @param voiceSourceUrl 录音地址
     * @return 切换后的域名
     * <AUTHOR>
     * @since 2023-08-31
     */
    public static String switchHostName(String voiceSourceUrl) {
        if (voiceSourceUrl.contains(CallProperties.YXProperty.RECORD_DOWNLOAD_API)) {
            return voiceSourceUrl.replace(CallProperties.YXProperty.RECORD_DOWNLOAD_API, CallProperties.YXProperty.RECORD_DOWNLOAD_API_CIPHERTEXT);
        } else if (voiceSourceUrl.contains(CallProperties.YXProperty.RECORD_DOWNLOAD_API_CIPHERTEXT)) {
            return voiceSourceUrl.replace(CallProperties.YXProperty.RECORD_DOWNLOAD_API_CIPHERTEXT, CallProperties.YXProperty.RECORD_DOWNLOAD_API);
        }
        return voiceSourceUrl;
    }

    /**
     * 拉取通话记录数据(入口)
     *
     * @param from 开始时间
     * @param to   结束时间
     * <AUTHOR>
     * @Date 17:08 2022/2/25
     **/
    @Override
    protected void downFile(Date from, Date to) {
        if (super.ACTION_TYPE.equals(BizConst.UPDATE)) {
            logger.error("亿迅厂商不支持通过定时任务补充通话记录");
            return;
        }
        String start = DateUtils.toStr(from);
        String end = DateUtils.toStr(to);
        if (this.account == null) {
            List<AccountDTO> accounts = this.selectAccountInfoByChannelType();
            for (AccountDTO account : accounts) {
                syncRecordsOfAccount(account, start, end, startKey);
            }
        } else {
            syncRecordsOfAccount(account, start, end, startKey);
        }
    }

    /**
     * 接收智能外呼MQ外呼成功后返回的主叫被叫信息生成缺少通话信息的通话记录
     *
     * @param request 主被叫信息
     * <AUTHOR>
     * @Date 14:39 2022/5/28
     **/
    public void generateCallRecord(GenerateCallRecordRequest request) {
        // 生成通话记录保存到ElasticSearch 
        Map<String, Object> mqRequest = super.commonGenerateCallRecord(request);
        if (mqRequest != null) {
            String[] s = request.getCallAccount().split("_");
            mqRequest.put("accountId", s[0]);
            // 推送到延时队列30分钟后去查询此通通话记录是否被补充完整，如果还没有补充完整，则调用查询接口去查询更新
            client.publish(MQConst.ACTIVE_QUERY_RECORD_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(30));
        }
    }

    /**
     * 回调：补充通话记录信息
     *
     * @paramter yxCallRecordsDTO
     * <AUTHOR>
     * @Date 11:33 2022/2/23
     **/
    public void supplementaryCallRecordInfoToES(YXCallRecordsDTO yxCallRecordsDTO) {
        String index = RecordUtil.getRecordIndexName(yxCallRecordsDTO.getCallerInviteTime(), DateUtil.YMD_HMS);
        String uniqueId = yxCallRecordsDTO.getUserData();
        // 查询ES验证是否已经生成此条通话记录(学员回拨呼入没有通话记录，需要去创建)
        CallRecord record = callRecordService.querySupplementaryFieldByUniqueId(index,
                yxCallRecordsDTO.isSign() ? CALL_TYPE_ZK.getType() : CALL_TYPE_YH.getType(), uniqueId);
        if (null == record) {
            // 生成通话记录
            this.generateCallInCallRecord(yxCallRecordsDTO);
            return;
        }
        // 表示已经补充过通话信息了
        if (record.getDuration() != -1) {
            return;
        }
        CallRecord callRecord = yxCallRecordsDTO.callRecordsConverter();
        String callId = record.getCallId();
        callRecord.setCallAccount(null);
        callRecord.setAgentId(null);
        callRecord.setCreatedTime(null);
        // 补充外呼通话信息到ES
        Map<String, Object> recordingData = super.updateCallRecord(callRecord, callId);
        if (null != recordingData) {
            // 发送下载录音MQ
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordingData));
        }
    }

    /**
     * 回调：补充SIP通话记录信息
     *
     * @param yxSipCallRecordsDTO SIP通话记录DTO对象
     * <AUTHOR>
     * @since 2024-08-22 13:40:03
     */
    public void supplementaryCallRecordInfoToES(YXSipCallRecordsDTO yxSipCallRecordsDTO) {
        String index = RecordUtil.getRecordIndexName(yxSipCallRecordsDTO.getStartTime(), DateUtil.YMD_HMS);
        String uniqueId = yxSipCallRecordsDTO.getUserData();
        // 查询ES验证是否已经生成此条通话记录(学员回拨呼入没有通话记录，需要去创建)
        CallRecord record = callRecordService.querySupplementaryFieldByUniqueId(index,CALL_TYPE_YX_SIP.getType(), uniqueId);
        if (null == record) {
            // 生成通话记录
            this.generateCallInCallRecord(yxSipCallRecordsDTO);
            return;
        }
        // 表示已经补充过通话信息了
        if (record.getDuration() != -1) {
            return;
        }
        CallRecord callRecord = yxSipCallRecordsDTO.callRecordsConverter();
        String callId = record.getCallId();
        callRecord.setCallAccount(null);
        callRecord.setAgentId(null);
        callRecord.setCreatedTime(null);
        // 补充外呼通话信息到ES
        Map<String, Object> recordingData = super.updateCallRecord(callRecord, callId);
        if (null != recordingData) {
            // 发送下载录音MQ
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordingData));
        }
    }

    /**
     * 通过智能运营平台拨打电话后，三十分钟内还没有接收到回调，就主动查询来补充通话记录,查询不到此条通话记录重复查询,最多重复查询5次
     *
     * @param mqRequest 查询通话记录的参数
     * <AUTHOR>
     * @since 10:51 2022/7/1
     **/
    public void activeQueryIncompleteRecord(Map<String, Object> mqRequest) {
        String index = mqRequest.get("index").toString();
        String contactId = mqRequest.get("contactId").toString();
        String channelType = mqRequest.get("channelType").toString();
        String accountId = mqRequest.get("accountId").toString();
        String createDate = mqRequest.get("createDate").toString();
        // 校验此条通话记录是否已经补充完整了
        CallRecord record = callRecordService.querySupplementaryFieldByUniqueId(index, channelType, contactId);
        if (record == null || record.getDuration() != -1) {
            return;
        }
        // 查询厂商获取通话记录信息
        String startDate = DateUtil.addDay(createDate, -2, DateUtils.DEFAULT_FORMAT);
        logger.info("主动查询亿迅厂商外呼后三十分钟没有回调的通话记录,mq接收参数:{}", JSONUtils.toJSONString(mqRequest));
        Map<String, String> map = this.getOneFromVendorByUserData(channelType, accountId, contactId, startDate, createDate);
        YXCallRecordsDTO yxCallRecordsDTO = new YXCallRecordsDTO().oneCdrResultConverter(map, contactId);
        if (null != yxCallRecordsDTO) {
            // 补充亿迅外呼信息到ES
            CallRecord callRecord = yxCallRecordsDTO.callRecordsConverter();
            callRecord.setCallId(record.getCallId());
            // 这里设为null是因为生成通话记录的时候已经有callAccount和AgentId,避免json序列化的时候覆盖
            callRecord.setCallAccount(null);
            callRecord.setAgentId(null);
            callRecord.setCreatedTime(null);
            // 补充外呼通话信息到ES
            Map<String, Object> recordingData = super.updateCallRecord(callRecord, record.getCallId());
            if (null != recordingData) {
                // 发送下载录音MQ
                client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordingData));
            }
        } else { // 没有查到重新投到MQ内,两次还不成功则打日志,忽略
            int times = Integer.parseInt(mqRequest.get("times").toString()) + 1;
            if (times >= 2) {
                logger.warn("亿迅主动查询还没有补充完整的通话记录信息超过两次没有成功,参数:" + JSONUtils.toJSONString(mqRequest));
                return;
            }
            mqRequest.put("times", times);
            client.publish(MQConst.ACTIVE_QUERY_RECORD_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(60));
        }
    }

    /**
     * 校验该通话记录是否存在[不存在返回true]
     * 过段时间可以删除：2022年7月1日14:17:29
     *
     * @return boolean  true->中科云(78),false->回拨(175)
     * @paramter uniqueKey
     * <AUTHOR>
     * @Date 16:31 2021/12/21
     **/
    private boolean isNotExistRecord(String uniqueKey, String channelType) {
        String key = RedisConst.DUPLICATE_RECORD_CHECK + channelType + ":" + uniqueKey;
        Boolean flag = redisTemplate.opsForValue().setIfAbsent(key, "1", Duration.ofDays(92));
        return null != flag && flag;
    }

    /**
     * @return List<Account>
     * @Description: 通过zkChannelType查询所有在用的对接密钥
     * <AUTHOR>
     * @Date 2021/9/16 18:20
     */
    private List<AccountDTO> selectAccountInfoByChannelType() {
        return bdCallaccountinfoService.selectUniqueAccountsByType(this.zkChannelType);
    }

    /**
     * 同步指定账号指定时间段的通话记录
     *
     * @param account  外呼账号信息
     * @param start    yyyy-MM-dd HH:mm:ss
     * @param end      yyyy-MM-dd HH:mm:ss
     * @param startKey 查询起始Id
     * <AUTHOR>
     * @Date 2021/11/3 11:06
     */
    private void syncRecordsOfAccount(AccountDTO account, String start, String end, String startKey) {
        String accountId = account.getApiAccount();
        String apiSecret = account.getApiSecret();
        int recordCountPerReq;
        do {
            List<Map<String, Object>> records = getRecordsFromVendor(start, end, startKey, accountId, apiSecret);
            // 请求厂商服务器异常后进行重试:请求失败后马上会检查到,所以retryMap中只有一个失败请求
            if (records == null) {
                records = retryGetRecordsFromVendorByRetryMap();
            }
            recordCountPerReq = records.size();
            if (recordCountPerReq > 0) {
                startKey = Long.toString(records.stream().mapToLong(record -> Long.parseLong((String) record.get("key"))).max().getAsLong());
                saveRecordsToEs(records, account);
            }
        } while (recordCountPerReq > 0);
    }

    /**
     * @return List<Map < String, Object>> 不会返回null:重试成功后返回通话记录列表,重试10次不成功返回空list,将失败的请求参数缓存到recordList,持久化后人工处理
     * @Description: 重试GetRecordsFromVendor: 从retryMap中获取请求参数,retryMap中只有一个失败请求
     * <AUTHOR>
     */
    private List<Map<String, Object>> retryGetRecordsFromVendorByRetryMap() {
        while (!retryMap.isEmpty()) {
            for (String reqParams : retryMap.keySet()) {
                String[] keys = reqParams.split("_");
                String startTime = keys[0];
                String endTime = keys[1];
                String startKey = keys[2];
                String accountId = keys[3];
                String apiSecret = keys[4];

                Integer failCount = retryMap.get(reqParams);
                if (failCount > 10) {
                    // 重试10次后, 将请求参数存入recordList, 持久化后人工处理
                    recordList.add(startTime + "_" + endTime + "_" + super.ACTION_TYPE + "_" + startKey + "_" + accountId);
                    retryMap.remove(reqParams);
                    continue;
                }

                List<Map<String, Object>> records = getRecordsFromVendor(startTime, endTime, startKey, accountId, apiSecret);
                if (records != null) {
                    return records;
                }
            }
        }
        return new ArrayList<>(0);
    }

    /**
     * 请求亿讯(中科云)服务器，查询通话记录数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param startKey  查询起始Id getCdrList接口最多返回500条通话记录,startKey用于获取全部数据
     * @param accountId 对接账号
     * @param apiSecret 对接密钥
     * @return List<Map < String, Object>> 通话记录
     * 请求亿讯服务器异常时返回null, 请求参数放到retryMap里并记录失败次数
     * 可通过检查是否为null,结合retryMap进行重试
     * <AUTHOR>
     */
    private List<Map<String, Object>> getRecordsFromVendor(String startTime, String endTime, String startKey,
                                                           String accountId, String apiSecret) {
        if (zkServiceIsBusy()) {
            this.checkRequestTime("ZK_ACCOUNT", CallProperties.YXProperty.GET_CDR_LIST_BUSY_SLEEP_MILLIS);
        } else {
            this.checkRequestTime("ZK_ACCOUNT", CallProperties.YXProperty.GET_CDR_LIST_SLEEP_MILLIS);
        }
        String retryKey = startTime + "_" + endTime + "_" + startKey + "_" + accountId + "_" + apiSecret;
        String reqStr = getRequestBody(startTime, endTime, startKey, accountId, apiSecret);
        String url = getZKChannelHost() + CallProperties.YXProperty.GET_CDR_LIST_API;
        String resStr = okHttpUtil.post(url, reqStr, Headers.of(HEADER));
        if (StringUtils.isEmpty(resStr)) {
            logger.info("查询亿迅通话记录出现异常,请求参数:{}", reqStr);
            super.putRetryMap(retryKey);
            return null;
        }
        logger.debug("查询亿迅厂商HTTP响应:" + url + "," + resStr);
        Map<String, Object> resMap;
        try {
            resMap = JSONUtils.toMap(resStr);
        } catch (Exception e) {
            logger.error(e, "拉取亿迅通话记录失败,异常信息:{},请求参数:{}", e.getMessage(), reqStr);
            putRetryMap(retryKey);
            return null;
        }
        Map<String, Object> result = (Map<String, Object>) resMap.get("result");
        if (!"0".equals(result.get("error").toString())) {
            logger.error("拉取亿迅通话记录失败,返回信息:{},请求参数:{}", result.get("msg").toString(), reqStr);
            putRetryMap(retryKey);
            return null;
        }
        retryMap.remove(retryKey);
        Map<String, Object> data = (Map<String, Object>) resMap.get("data");
        Map<String, Object> response = (Map<String, Object>) data.get("response");
        List<Map<String, Object>> records = (List<Map<String, Object>>) response.get("cdr");
        if (records == null) {
            records = new ArrayList<>(0);
        }
        return records;
    }

    /**
     * @param records 厂商返回的通话记录列表
     * @param account 账号信息
     * @Description: 保存通话记录到ES:
     * 1下载通话录音,并通过文件服务上传到阿里OSS上存储
     * 2根据外呼账号补充主叫员工信息
     * 3根据被叫手机号补充被叫客户信息
     * <AUTHOR>
     */
    private void saveRecordsToEs(List<Map<String, Object>> records, AccountDTO account) {
        for (Map<String, Object> record : records) {
            // 5 点拨呼叫,只过滤点拨呼叫的通话记录,目前回拨通话记录的serviceType=5
            if ("5".equals(record.get("serviceType").toString())) {
                String callPhone = (String) record.get("caller");
                // 回拨线路会产生两条通话记录(录音相同),平台固话打给销售侧的通话记录无用(浪费存储空间),需过滤掉
                // 目前回拨平台打给销售侧的主叫是(2或8或9)开头的10位固话: 亿讯回拨平台的固话变动时,此部分代码需相应变动,否则会存储无用数据
                // (0)28xxxxxxxx/(0)830xxxxxxx/(0)832xxxxxxx/(0)971xxxxxxx: 亿讯服务器返回的固话主叫省略了区号的0
                // 区号:3位或4位 亿讯:省略区号的0或不省略区号的0 固话:7位或8位 --> 亿讯回拨平台固话可能的位数:9-12位
                // 过滤原则:无用的通话记录可以不过滤(浪费存储空间),要避免将有用的通话记录错误的过滤掉
                // 程序需要尽可能的过滤掉无用数据以节省存储空间,因此需要过滤条件尽可能的精准
                if (!ObjectUtils.isEmpty(callPhone) && callPhone.length() == 10
                        && (callPhone.startsWith("2") || callPhone.startsWith("8") || callPhone.startsWith("9"))) {
                    continue;
                }
                // 回拨产生两条通话记录过滤
                String session = record.get("session").toString();
                if (!this.zkChannelType.equals(CALL_TYPE_YX_SIP.getType()) &&!session.contains("other")) {
                    continue;
                }
            }
            String userData = record.get("userData").toString();
            String uniqueKey = account.getApiAccount() + ":" + record.get("session").toString().replace("other", "");
            boolean flag = false;
            if (StringUtils.isNotEmpty(userData)) {
                flag = !super.isNotExistRecord(userData, CallChannelEnum.getCallChannel(this.zkChannelType));
            }
            // 检查该条通话记录之前是否已经处理过
            if (flag || !this.isNotExistRecord(uniqueKey, this.zkChannelType)) {
                continue;
            }
            ZKVoiceVoBuilder zkVoiceVoBuilder = new ZKVoiceVoBuilder(record, account);
            if (!StringUtils.isEmpty(zkVoiceVoBuilder.callRecord.getVoiceSourceUrl())) {
                // 有录音的通话记录数加1
                YXService.this.totalSize++;
            }
            if (downloadVoice) {
                zkVoiceVoBuilder.downloadAndUploadVoice();
            }
            zkVoiceVoBuilder.addCallerEmployeeInfo();
            zkVoiceVoBuilder.addCalleeCustomerInfo();
            zkVoiceVoBuilder.userInfoEncrypt();
            callRecordList.add(zkVoiceVoBuilder.callRecord);
        }

        if (callRecordList.size() > 0) {
            try {
                if (!downloadVoice) {
                    // 批量添加通话录音信息到ES[call_recording]
                    this.batchSaveRecording(callRecordList);
                }
                this.batchSaveRecords(callRecordList);
            } catch (Exception e) {
                logger.error(e, "ES插入数据异常:" + e.getMessage());
            }
        }
        logger.info("定时任务亿迅通话记录数量去重过滤后补充数量:" + callRecordList.size() + ",账号:" + account);
        callRecordList.clear();
    }

    /**
     * @return JSON格式
     * @Description: 组装请求参数
     * <AUTHOR>
     * @Date 2021/11/3 16:38
     */
    public String getRequestBody(String startTime, String endTime, String startKey, String accountId, String apiSecret) {
        Map<String, String> request = new HashMap<>(2);
        request.put("startTime", startTime);
        request.put("endTime", endTime);
        request.put("startKey", startKey);

        Map<String, Object> requestBody = new HashMap<>(3);
        requestBody.put("authentication", getAuthentication(accountId, apiSecret));
        requestBody.put("param", PARAM);
        requestBody.put("request", request);
        return JSONUtils.toJSONString(requestBody);
    }

    /**
     * 组装企业余额查询的请求参数
     *
     * @return JSON格式
     * <AUTHOR>
     * @since 2023-07-31 11:34:09
     */
    public String getBalanceQueryRequestBody(String accountId, String apiSecret) {
        Map<String, String> request = new HashMap<>(2);

        Map<String, Object> requestBody = new HashMap<>(3);
        requestBody.put("authentication", getAuthentication(accountId, apiSecret));
        requestBody.put("param", PARAM);
        requestBody.put("request", request);
        return JSONUtils.toJSONString(requestBody);
    }

    /**
     * @Description: 07:00:00-21:00:00 亿讯服务器繁忙时段
     * <AUTHOR>
     * @Date 2021/10/30 10:58
     */
    private static boolean zkServiceIsBusy() {
        int hour = LocalTime.now().getHour();
        return hour >= 7 && hour <= 21;
    }

    /**
     * @return 亿讯(中科云)的服务器地址
     * @Description: 根据this.zkChannelType获取亿讯(中科云)的服务器地址
     * <AUTHOR>
     * @Date 2021/9/17 9:25
     */
    private String getZKChannelHost() {
        if (CALL_TYPE_YH.getType().equals(this.zkChannelType)) {
            return CallProperties.YXProperty.YH_HOST;
        } else if (CALL_TYPE_YX_SIP.getType().equals(this.zkChannelType)){
            return CallProperties.YXProperty.YX_SIP_HOST;
        } else {
            return CallProperties.YXProperty.ZK_HOST;
        }
    }

    /**
     * @param accountId 对接账号
     * @param apiSecret 对接密钥
     * @return authentication
     * @Description: 获取authentication
     * <AUTHOR>
     */
    public static Map<String, String> getAuthentication(String accountId, String apiSecret) {
        Map<String, String> authentication = new HashMap<>(4);
        String timestamp = System.currentTimeMillis() + "";
        String seq = UUID.randomUUID().toString().replace("-", "");
        authentication.put("digest", MD5Utils.md5(accountId + "@" + timestamp + "@" + seq + "@" + apiSecret));
        authentication.put("customer", accountId);
        authentication.put("timestamp", timestamp);
        authentication.put("seq", seq);
        return authentication;
    }

    /**
     * 批量保存通话录音
     *
     * @paramter callRecordList
     * <AUTHOR>
     * @Date 14:40 2022/3/21
     **/
    private void batchSaveRecording(List<CallRecord> callRecordList) {
        try {
            callRecordingService.bulkSave(callRecordList);
        } catch (Exception e) {
            logger.error(e, "亿迅通话记录->批量添加通话录音信息失败,异常信息:{}", e.getMessage());
            this.deleteSaveFailingKey(callRecordList);
            throw new RuntimeException("亿迅通话记录->批量添加通话录音信息失败");
        }
    }

    /**
     * 保存通话记录到ES
     *
     * @paramter callRecordList
     * <AUTHOR>
     * @Date 14:49 2022/3/21
     **/
    private void batchSaveRecords(List<CallRecord> callRecordList) {
        try {
            callRecordService.bulkSave(super.ES_INDEX, callRecordList);
        } catch (Exception e) {
            logger.error(e, "亿迅通话记录->批量添加通话记录信息失败,索引:{},异常信息:{}", super.ES_INDEX, e.getMessage());
            this.deleteSaveFailingKey(callRecordList);
            if (!this.downloadVoice) {
                List<String> ids = callRecordList.stream().map(CallRecord::getCallId).collect(Collectors.toList());
                callRecordingService.batchDeleteByIds(ids);
            }
            throw new RuntimeException("亿迅通话记录->批量添加通话记录信息失败");
        }
    }


    /**
     * 删除保存通话记录失败的KEY列表
     *
     * @paramter callRecordList
     * <AUTHOR>
     * @Date 14:49 2022/3/21
     **/
    private void deleteSaveFailingKey(List<CallRecord> callRecordList) {
        // 失败后删除去重ID
        String redisKeyYXPrefix = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_YH.getType() + ":";
        String redisKeyZKPrefix = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_ZK.getType() + ":";
        List<String> yxIds = callRecordList.stream()
                .filter(callRecord -> StringUtils.isNotEmpty(callRecord.getField3()) && "21".equals(callRecord.getField3()))
                .map(callRecord -> (redisKeyYXPrefix + callRecord.getField4() + ":" + callRecord.getField6())).collect(Collectors.toList());
        List<String> zkIds = callRecordList.stream()
                .filter(callRecord -> StringUtils.isEmpty(callRecord.getField3()) || !"21".equals(callRecord.getField3()))
                .map(callRecord -> (redisKeyZKPrefix + callRecord.getField4() + ":" + callRecord.getField6())).collect(Collectors.toList());
        redisTemplate.delete(zkIds);
        redisTemplate.delete(yxIds);
    }

    /**
     * 生成呼入的通话记录(还有少数的不是通过智能运营平台打的)
     *
     * @param yxCallRecordsDTO 通话记录
     * <AUTHOR>
     * @Date 13:51 2022/7/1
     **/
    private void generateCallInCallRecord(YXCallRecordsDTO yxCallRecordsDTO) {
        CallChannelEnum callChannel = yxCallRecordsDTO.isSign() ? CALL_TYPE_ZK : CALL_TYPE_YH;
        String userData = yxCallRecordsDTO.getUserData();
        String uniqueKey = yxCallRecordsDTO.getAccount() + ":" + yxCallRecordsDTO.getSession();
        // 校验此条通话记录是否已经生成
        boolean existRecord = super.isNotExistRecord(userData, uniqueKey, callChannel);
        if (!existRecord) {
            return;
        }
        CallRecord callRecord = new CallRecord();
        Date callTime = DateUtils.toDate(yxCallRecordsDTO.getCallerInviteTime());
        String index = CALL_RECORD_ES_PREFIX + DateUtils.toStr(callTime, "yyyyMM");
        try {
            callRecord = yxCallRecordsDTO.callRecordsConverter();
            //补充空值
            new YKCallRecordsDTO().replenishEmpty(callRecord);
            //补充员工信息
            super.setEE(yxCallRecordsDTO.getCallerInviteTime(), callChannel.getType(), callRecord);
            //补充客户信息
            super.addCustomerInfoByPhone(callRecord);
            // 校验是否有此索引，保存到ES内
            this.generateCallRecordToES(index, callRecord);
        } catch (Exception e) {
            logger.error(e, "生成客户呼入亿迅通话记录失败,异常信息:{},呼入回调参数:{}", e.getMessage(), JSONUtils.toJSONString(yxCallRecordsDTO));
            // 失败后删除去重ID
            String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + callChannel.getType() + ":" + uniqueKey;
            redisTemplate.delete(redisKeyPrefix);
            return;
        }
        // 创建成功后发送MQ下载录音
        if (callRecord.getDuration() > 0 && StringUtils.isNotEmpty(callRecord.getVoiceSourceUrl())) {
            Map<String, Object> data = new HashMap<>(5);
            data.put("channelType", callChannel.getType());
            data.put("indexName", index);
            data.put("voiceSourceUrl", callRecord.getVoiceSourceUrl());
            data.put("callId", callRecord.getCallId());
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data));
        }
    }

    /**
     * 生成呼入的通话记录（包括少数非智能运营平台拨打的通话记录）
     *
     * @param yxSipCallRecordsDTO SIP通话记录DTO对象
     * <AUTHOR>
     * @since 2024-08-22 13:46:34
     */
    private void generateCallInCallRecord(YXSipCallRecordsDTO yxSipCallRecordsDTO) {
        CallChannelEnum callChannel = CALL_TYPE_YX_SIP;
        String userData = yxSipCallRecordsDTO.getUserData();
        String uniqueKey = yxSipCallRecordsDTO.getAccount() + ":" + yxSipCallRecordsDTO.getSession();
        // 校验此条通话记录是否已经生成
        boolean existRecord = super.isNotExistRecord(userData, uniqueKey, callChannel);
        if (!existRecord) {
            return;
        }
        CallRecord callRecord = new CallRecord();
        Date callTime = DateUtil.getFirstNonNullDate(yxSipCallRecordsDTO.getStartTime(), yxSipCallRecordsDTO.getRingTime(), yxSipCallRecordsDTO.getAnswerTime(),yxSipCallRecordsDTO.getByeTime());

        String index = CALL_RECORD_ES_PREFIX + DateUtils.toStr(callTime, "yyyyMM");
        try {
            callRecord = yxSipCallRecordsDTO.callRecordsConverter();
            //补充空值
            new YKCallRecordsDTO().replenishEmpty(callRecord);
            //补充员工信息
            String callTimeStr = DateUtils.toStr(callTime);
            super.setEE(callTimeStr, callChannel.getType(), callRecord);
            //补充客户信息
            super.addCustomerInfoByPhone(callRecord);
            // 校验是否有此索引，保存到ES内
            this.generateCallRecordToES(index, callRecord);
        } catch (Exception e) {
            logger.error(e, "生成客户呼入亿迅通话记录失败,异常信息:{},呼入回调参数:{}", e.getMessage(), JSONUtils.toJSONString(yxSipCallRecordsDTO));
            // 失败后删除去重ID
            String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + callChannel.getType() + ":" + uniqueKey;
            redisTemplate.delete(redisKeyPrefix);
            return;
        }
        // 创建成功后发送MQ下载录音
        if (callRecord.getDuration() > 0 && StringUtils.isNotEmpty(callRecord.getVoiceSourceUrl())) {
            Map<String, Object> data = new HashMap<>(5);
            data.put("channelType", callChannel.getType());
            data.put("indexName", index);
            data.put("voiceSourceUrl", callRecord.getVoiceSourceUrl());
            data.put("callId", callRecord.getCallId());
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data));
        }
    }

    /**
     * 根据外呼传入的自定义UUID查询唯一通话记录
     *
     * @param accountId 账户号
     * @param userData  唯一id
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Date 17:17 2022/7/1
     **/
    public Map<String, String> getOneFromVendorByUserData(String channelType, String accountId, String userData, String startTime, String endTime) {
        // 查询亿迅账号秘钥
        AccountDTO account = bdCallaccountinfoService.selectAccount(accountId, channelType);
        if (null == account || StringUtils.isEmpty(account.getApiSecret())) {
            logger.error("MQ中科云通过通话记录session补充agent失败(坐席工号),不存在的账号:{}", accountId);
            return null;
        }
        Map<String, String> request = new HashMap<>(2);
        request.put("startTime", startTime);
        request.put("endTime", endTime);
        request.put("userData", userData);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("authentication", YXService.getAuthentication(accountId, account.getApiSecret()));
        requestBody.put("request", request);
        String url = getYxApiHost(channelType);
        logger.info("亿迅获取指定话单请求参数:{}", JSONUtils.toJSONString(requestBody));
        String response = okHttpUtil.post(url, JSONUtils.toJSONString(requestBody));
        logger.info("亿迅获取指定话单返回值:{}", response);
        if (StringUtils.isEmpty(response)) {
            return null;
        }
        try {
            List<Map<String, String>> cdr = (List<Map<String, String>>) ((Map<String, Object>) ((Map<String, Object>)
                    JSONUtils.toMap(response).get("data")).get("response")).get("cdr");
            for (Map<String, String> map : cdr) {
                String voiceSign = map.get("voiceSign");
                if (voiceSign.contains("other")) {
                    map.put("session", voiceSign.replace("other", ""));
                    return map;
                }
            }
        } catch (Exception e) {
            logger.error(e, "调用亿迅厂商接口返回值解析出现异常,返回值:{},异常信息:{}", response, e.getMessage());
        }
        return null;
    }

    /**
     * 根据channelType获取亿迅API主机地址
     *
     * @param channelType 通道类型
     * @return 返回亿迅API主机地址
     * @throws ApplicationException 当channelType不是合法值时，抛出ApplicationException异常
     */
    private static String getYxApiHost(String channelType) {
        String zkType = CALL_TYPE_ZK.getType();
        String yxSipType = CALL_TYPE_YX_SIP.getType();
        String yhType = CALL_TYPE_YH.getType();

        String url;
        if (zkType.equals(channelType)) {
            url = CallProperties.YXProperty.ZK_HOST + CallProperties.YXProperty.ONE_RECORDS;
        } else if (yxSipType.equals(channelType)) {
            url = CallProperties.YXProperty.YX_SIP_HOST + CallProperties.YXProperty.ONE_RECORDS;
        } else if (yhType.equals(channelType)) {
            url = CallProperties.YXProperty.YH_HOST + CallProperties.YXProperty.ONE_RECORDS;
        } else {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "查询亿迅厂商接口类型错误,channelType:" + channelType);
        }
        return url;
    }

    /**
     * 接收MQ->下载通话录音并更新ES
     *
     * @param map 通话记录信息
     * <AUTHOR>
     * @Date 11:42 2022/7/1
     **/
    @Override
    public boolean downLoadRecording(Map<String, Object> map) {
        boolean flag = super.downLoadRecording(map);
        // 下载失败后放入ES CallRecording索引内
        if (!flag) {
            String indexName = map.get("indexName").toString();
            String callId = map.get("callId").toString();
            String voiceSourceUrl = map.get("voiceSourceUrl").toString();
            String channelType = map.get("channelType").toString();
            // 安全获取 duration 值
            Integer duration = null;
            if (map.containsKey("duration") && map.get("duration") != null) {
                duration = Integer.parseInt(map.get("duration").toString());
            } else {
                logger.warn("Duration is missing for callId: {}", callId);
                // 这里可以设置一个默认值
                duration = 0;
            }

            CallRecording callRecording = CallRecording.initialize();
            callRecording.setCallId(callId);
            String date = RecordUtil.getDateByIndexName(indexName);
            callRecording.setDate(date);
            callRecording.setVoiceSourceUrl(voiceSourceUrl);
            callRecording.setChannelType(channelType);
            callRecording.setDuration(duration);
            try {
                callRecordingService.save(callRecording);
            } catch (Exception ex) {
                logger.error(ex, "保存亿迅通话录音到索引callRecording出现异常,callId:{},异常信息:{}", callId, ex.getMessage());
            }
        }
        return flag;
    }

    /**
     * <AUTHOR>
     * @Description: 构建ZKVoiceVo
     * @Date 2021/9/26 14:12
     */
    private class ZKVoiceVoBuilder {
        final CallRecord callRecord = new CallRecord();
        String callTimeStr = DateUtils.getNowDString();

        ZKVoiceVoBuilder(Map<String, Object> zkRecord, AccountDTO account) {
            callRecord.setCallId(UUID.randomUUID().toString());
            // 回拨外呼专用服务产生的通话记录也统一归类成亿讯(中科云)
            callRecord.setChannelType(YXService.this.zkChannelType);
            // 2021.11.09新增: 存储API对接账号
            callRecord.setField4(account.getApiAccount());
            // 主叫数据--------------------开始-------------------
            callRecord.setDptId("");
            callRecord.setDptName("");
            callRecord.setSchoolId("");
            callRecord.setSchoolName("");
            callRecord.setCallerUserId("");
            callRecord.setCallerName("");
            callRecord.setCallerPhone("");
            callRecord.setCallPhone("");
            if (!ObjectUtils.isEmpty(zkRecord.get("caller"))) {
                callRecord.setCallPhone(zkRecord.get("caller").toString());
            }
            callRecord.setAgentPhone("");
            callRecord.setAgentId("");
            if (!ObjectUtils.isEmpty(zkRecord.get("agent"))) {
                callRecord.setAgentId(zkRecord.get("agent").toString());
            }
            callRecord.setCallAccount("");
            if (CALL_TYPE_YH.getType().equals(YXService.this.zkChannelType)) {
                // 亿讯回拨外呼专用服务,员工绑定的账号是主叫手机号
                callRecord.setCallAccount(callRecord.getCallPhone());
                // 21:标识亿讯回拨外呼专用服务产生的通话记录
                callRecord.setField3(CallRecord.CALL_TYPE_YH_FIELD3);
            } else {
                // 其他亿讯外呼,员工绑定的账号是坐席号,支持两种形式:93379或93379@ceshi,es中存储的是93379@ceshi这种格式
                if (!ObjectUtils.isEmpty(callRecord.getAgentId())) {
                    callRecord.setCallAccount(callRecord.getAgentId() + "@" + account.getAccountName());
                }
            }
            // 主叫数据--------------------结束-------------------
            // 被叫数据--------------------开始-------------------
            callRecord.setAreaCode("");
            callRecord.setAreaName("");
            callRecord.setReciverPhone("");
            if (!ObjectUtils.isEmpty(zkRecord.get("callee"))) {
                callRecord.setReciverPhone(zkRecord.get("callee").toString());
            }
            // 被叫数据--------------------结束-------------------
            // 通话数据--------------------开始-------------------
            callRecord.setCallType(null);
            if (!ObjectUtils.isEmpty(zkRecord.get("serviceType"))) {
                // 2 呼入呼叫
                // 1 手拨呼叫 3 内部呼叫 4 自动外呼 5 点拨呼叫
                // 7 录音业务 9 转接业务 17 租金业务 18 结算业务 19 语音验证码业务 21 语音通知
                String serviceType = zkRecord.get("serviceType").toString();
                if ("1".equals(serviceType) || "3".equals(serviceType) || "4".equals(serviceType) || "5".equals(serviceType)) {
                    callRecord.setCallType(0);
                } else if ("2".equals(serviceType)) {
                    callRecord.setCallType(1);
                } else {
                    callRecord.setCallType(Integer.valueOf(serviceType));
                }
            }
            callRecord.setCreatedTime(new Date());
            callRecord.setCallTime(new Date());
            callRecord.setDataSyncTime(new Date());
            if (!ObjectUtils.isEmpty(zkRecord.get("startTime"))) {
                this.callTimeStr = zkRecord.get("startTime").toString();
                callRecord.setCallTime(DateUtils.toDate(this.callTimeStr));
            }
            callRecord.setIsConnectSuccess(CONNECT_SUCCESS_N);
            callRecord.setVoiceStatus(VOICE_NO_DEAL);
            callRecord.setIsValidCall(VALID_CALL_N);
            callRecord.setDuration(0);
            if (!ObjectUtils.isEmpty(zkRecord.get("timeLength"))) {
                int duration = Integer.parseInt(zkRecord.get("timeLength").toString());
                callRecord.setDuration(duration);
                if (duration > 0) {
                    callRecord.setIsConnectSuccess(CONNECT_SUCCESS_Y);
                    callRecord.setVoiceStatus(VOICE_DEALING);
                }
                if (duration >= BizConst.EFFECTIVE_TIME) {
                    callRecord.setIsValidCall(VALID_CALL_Y);
                }
            }
            callRecord.setIsConnected(CONNECTED_FAIL);
            if (!ObjectUtils.isEmpty(zkRecord.get("result"))) {
                String callResult = zkRecord.get("result").toString();
                // 1 接通 2 未接通 3 空号
                if ("1".equals(callResult)) {
                    this.callRecord.setIsConnected(CONNECTED_SUCCESS);
                }
                if ("3".equals(callResult)) {
                    this.callRecord.setVoiceStatus(VOICE_BLANK);
                }
            }
            // 通话数据--------------------结束-------------------
            // 录音--------------------开始-------------------
            callRecord.setVoiceSourceUrl("");
            callRecord.setVoiceUrl("");
            callRecord.setServerFolder("");
            callRecord.setField1("");
            callRecord.setVoiceSyncStatus(0);
            callRecord.setDataCompleteStatus("Y");
            if (!ObjectUtils.isEmpty(zkRecord.get("filename"))) {
                callRecord.setVoiceSourceUrl(getZKChannelHost() + CallProperties.YXProperty.RECORD_DOWNLOAD_API + zkRecord.get("filename"));
                callRecord.setVoiceSyncStatus(1);
                // 需要下载录音上传到OSS
                callRecord.setDataCompleteStatus("N");
            }
            // 录音--------------------结束-------------------
            // 是使用接口外呼时返回的业务id，用来临时标记一次外呼，用来挂断电话或者与通话记录关联
            callRecord.setField2("");
            if (!ObjectUtils.isEmpty(zkRecord.get("userData"))) {
                callRecord.setField2(zkRecord.get("userData").toString());
            }
            // 厂商通话记录id
            callRecord.setVoiceSourceId("");
            if (!ObjectUtils.isEmpty(zkRecord.get("key"))) {
                callRecord.setVoiceSourceId(zkRecord.get("key").toString());
            }
            // session: 亿迅话单唯一标识
            callRecord.setField6("");
            if (!ObjectUtils.isEmpty(zkRecord.get("session"))) {
                callRecord.setField6(zkRecord.get("session").toString().replace("other", ""));
            }
            // 其他--------------------开始-------------------
            callRecord.setReciverType("");
            callRecord.setSatisfaction("");
            callRecord.setRemarks("");
            callRecord.setCommuIntention("");
            callRecord.setIntentionMsg("");
            callRecord.setHangUp(null);
            callRecord.setProjectId("");
            callRecord.setProjectName("");
            callRecord.setModifier("SYSTEM");
            callRecord.setModifiedTime(new Date());
            // 其他--------------------结束-------------------
        }

        /**
         * @Description: 下载录音并上传到OSS, 下载/上传失败时将处理失败的数据存入redis
         * <AUTHOR>
         * @Date 2021/9/26 15:44
         */
        void downloadAndUploadVoice() {
            if (!StringUtils.isEmpty(this.callRecord.getVoiceSourceUrl())) {
                YXService.this.uploadVoiceToOSS(this.callRecord);
            }
        }

        /**
         * @Description: 根据外呼账号补充主叫员工信息
         * <AUTHOR>
         * @Date 2021/9/26 16:33
         */
        void addCallerEmployeeInfo() {
            YXService.this.setEE(this.callTimeStr, YXService.this.zkChannelType, this.callRecord);
        }

        /**
         * @Description: 根据被叫手机号补充被叫客户信息
         * <AUTHOR>
         * @Date 2021/9/26 16:33
         */
        void addCalleeCustomerInfo() {
            YXService.super.addCustomerInfoByPhone(this.callRecord);
        }

        /**
         * 客户信息加密后保存到ES
         *
         * <AUTHOR>
         * @Date 10:09 2021/9/30
         **/
        void userInfoEncrypt() {
            this.callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.callRecord.getReciverPhone()));
            this.callRecord.setReciverName(FieldCipherUtil.oneEncrypt(this.callRecord.getReciverName()));
        }

    }

    @Override
    public void checkRequestTime(String key, int time) {
        synchronized (YXService.class) {
            super.checkRequestTime(key, time);
        }
    }
}
