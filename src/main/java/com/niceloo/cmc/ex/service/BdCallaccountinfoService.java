package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.pojo.dto.AccountDTO;

import java.util.List;

/**
 * <p>
 * 通讯厂商开放API对接密钥 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
public interface BdCallaccountinfoService{

    /**
     * 查询accountType下的所有分校ID
     *
     * @param accountType return 部门列表
     */
    List<String> selectSchoolIdByAccountType(String accountType );

    /**
     * 根据账号和外呼类型查询账号信息
     * @param account 账号
     * @param type 外呼类型
     */
    AccountDTO selectAccount(String account, String type);

    /**
     * 查询指定账号类型下所有的对接密钥: 1去重后的 2未删除的
     * @param channelType 外呼厂商类型
     * @return 账号信息列表
     */
    List<AccountDTO> selectUniqueAccountsByType(String channelType);

    /**
     * 根据员工的id和外呼类型获取外呼账号
     * @param eeId 员工的id
     * @param type 外呼类型
     */
    String selectAccountNameByBdEeId(String eeId, String type);
}
