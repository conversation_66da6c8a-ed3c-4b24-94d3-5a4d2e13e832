package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.entity.es.CallRecording;

import java.util.List;

/**
 * @description: 通话录音service
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-17 15:04
 */
public interface CallRecordingService {

    /**
     * 根据修改时间升序查询 查询操作次数大于或小于10次的前三千五百条数据
     *
     * @param operator 大于或小于等于 [0 小于等于,1 大于]
     * @param page     当前页数
     * @return 通话录音列表
     */
    List<CallRecording> selectCallRecording(int operator, int page);

    /**
     * 根据ID列表删除通话录音信息
     *
     * @param ids id列表
     */
    boolean batchDeleteByIds(List<String> ids);

    /**
     * 批量修改
     *
     * @param callRecordings 需要修改的通话录音信息列表
     */
    void bulkUpdate(List<CallRecording> callRecordings);

    /**
     * 查询没有通话录音地址,但是通话时长大于20秒的前2000条数据
     */
    List<CallRecording> selectNonRecordingList();

    /**
     * 批量添加通话录音
     *
     * @param callRecordList 通话记录列表
     * @see CallChannelEnum
     */
    void bulkSave(List<CallRecord> callRecordList);

    /**
     * 添加通话录音信息
     * @param callRecording 通话录音实体
     */
    void save(CallRecording callRecording);

    /**
     * 创建录音索引
     */
    void createIndex();

    /**
     * 删除昨天之前的未下载录音记录
     * operationSign=1 未下载，这类记录后面也无法下载，多了会影响正常录音下载
     */
    void deleteDataBeforeYesterday();
}
