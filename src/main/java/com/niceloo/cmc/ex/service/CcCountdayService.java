package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.entity.CcCountday;
import com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData;
import com.niceloo.cmc.ex.pojo.vo.CallStatsVO;
import com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO;

import java.util.List;

/**
 * <p>
 * 话务统计表(天) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
public interface CcCountdayService {


    /**
     * 根据日期和外呼类型删除话务统计信息
     *
     * @param date        日期
     * @param channelType 外呼类型
     */
    void deleteByDateAndChannelType(String date, String channelType);

    /**
     * 批量添加
     *
     * @param commuCounts 添加列表
     */
    void batchSave(List<CcCountday> commuCounts);

    /**
     * 根据日期范围和外呼类型查询列表
     *
     * @param start       开始时间
     * @param end         结束时间
     * @param channelType 外呼类型
     */
    List<CcCountday> selectListByDate(String start, String end, String channelType);

    /**
     * 查询MySQL以日期[天]进行聚合的话务统计
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData>
     * @paramter callerUserId 员工的用户标识
     * @paramter dptIds 部门id列表
     * @paramter dates 话务统计的时间列表,如:["2021-12-10","2021-12-11","2021-12-12"]
     */
    List<RecordDayStatisticsData> getTrafficStatisticGroupDateSql(String callerUserId, String dptIds, List<String> dates);

    /**
     * 查询MySQL以员工[Ee]进行聚合的话务统计
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData>
     * @paramter callerUserId 员工的用户标识
     * @paramter dptIds 部门id列表
     * @paramter dates 话务统计的时间列表,如:["2021-12-10","2021-12-11","2021-12-12"]
     */
    List<RecordEeStatisticsVO> getTrafficStatisticGroupEeSql(String callerUserId, String dptIds, String channelType, List<String> dates);

    /**
     * 根据员工id查询话务统计
     * @param callerUserId 主叫用户标识
     * @param dates 话务统计的时间列表,如:["2023-12-15","2023-12-15","2023-12-15"]
     * @return 通话量和通话时长VO
     */
    CallStatsVO getCallStatsByUserId(String callerUserId, List<String> dates);
}
