package com.niceloo.cmc.ex.service.base;

import com.niceloo.cmc.ex.common.RecordProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.FileUtil;
import com.niceloo.framework.json.JSONUtils;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 录音预检服务的基类，定义了一些通用的方法和属性。
 * <AUTHOR>
 * @since 2024-10-26 11:18:29
 */
@CustomLog
public abstract class BaseCallRecordingPrecheckService {
    @Resource
    protected ElasticsearchRestTemplate elasticsearchTemplate;

    @Resource
    protected RestHighLevelClient client;

    @Resource
    protected FileUtil fileUtil;

    protected static final String UNACCESSIBLE_INDEX = "call_recording_unaccessible";
    protected static final String RECORDING_INDEX = "call_recording";

    protected static final int BATCH_SIZE = 100;
    protected static final int MINIMUM_FILE_SIZE = 2048;

    @Resource
    protected RecordProperties recordProperties;

    /**
     * 执行批量请求
     *
     * @param moveBulkRequest 移动数据的批量请求
     * @param deleteBulkRequest 删除数据的批量请求
     *
     * 执行批量移动和删除数据的请求。
     * 首先，如果移动数据的批量请求中有动作，则执行移动操作，并记录失败的操作。
     * 然后，如果删除数据的批量请求中有动作，则执行删除操作，并记录失败的操作。
     * 如果在执行过程中发生异常，则记录异常信息。
     */
    protected void executeBulkRequests(BulkRequest moveBulkRequest, BulkRequest deleteBulkRequest) {
        try {
            if (moveBulkRequest.numberOfActions() > 0) {
                BulkResponse moveResponse = client.bulk(moveBulkRequest, RequestOptions.DEFAULT);
                handleBulkResponse(moveResponse); // 处理响应并检查是否有错误
            }
            if (deleteBulkRequest.numberOfActions() > 0) {
                BulkResponse deleteResponse = client.bulk(deleteBulkRequest, RequestOptions.DEFAULT);
                handleBulkResponse(deleteResponse); // 处理响应并检查是否有错误
            }
        } catch (ElasticsearchException e) {
            // 这里可以捕获Elasticsearch特定的异常，并对其进行处理
            LOGGER.error("Elasticsearch批量处理时发生异常: ", e);
            // 可以根据需要进行进一步的处理，例如重试、记录详细信息等
        } catch (Exception e) {
            // 捕获其他类型的异常
            LOGGER.error("批量处理时发生未知异常: ", e);
        }
    }

    /**
     * 处理批量操作的响应，并处理其中的失败项。
     *
     * @param response 批量操作的响应对象，包含了所有操作的结果。
     */
    private void handleBulkResponse(BulkResponse response) {
        /**
         * 检查批量响应中是否有失败的操作。
         */
        if (response.hasFailures()) {
            // 遍历批量响应中的每一项操作响应
            for (BulkItemResponse itemResponse : response) {
                /**
                 * 检查当前操作响应是否表示操作失败。
                 */
                if (itemResponse.isFailed()) {
                    // 获取失败操作的详细信息
                    BulkItemResponse.Failure failure = itemResponse.getFailure();
                    // 记录失败操作的错误信息
                    LOGGER.error("批量操作中的单个操作失败: {}", JSONUtils.toJSONString(failure.toString()));
                    // 可以在此处根据失败的类型进行更具体的处理，例如重试、记录等
                }
            }
        }
    }

    /**
     * 清除指定的滚动ID对应的滚动上下文。
     *
     * @param scrollId 需要清除的滚动ID
     * @throws IOException 如果清除过程中发生IO异常，则抛出IOException异常
     */
    protected void clearScroll(String scrollId) throws IOException {
        if (scrollId != null) {
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        }
    }

    /**
     * 判断指定索引中给定 callId 的录音是否已下载完成。
     * 下载完成的判断条件是 serverFolder 字段有值且不为 404，
     * 或者 dataCompleteStatus 字段值为 Y。
     *
     * @param indexName 索引名称
     * @param callId    录音的 callId
     * @return 如果录音已下载完成，返回 true；否则返回 false
     */
    protected boolean isRecordingDownloadComplete(String indexName, String callId) {
        // 构建查询以检查 serverFolder 和 dataCompleteStatus 字段
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withTypes("_doc")
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("callId", callId))) // 按 callId 查询
                .withFields("serverFolder", "dataCompleteStatus")
                .build();

        // 执行查询
        List<CallRecord> records = elasticsearchTemplate.queryForList(searchQuery, CallRecord.class);

        // 检查查询结果
        if (records.isEmpty()) {
            // 如果没有记录，认为录音已下载完成，不予处理
            return true;
        }

        // 获取记录并检查 download 状态
        CallRecord callRecord = records.get(0);
        String serverFolder = callRecord.getServerFolder();
        String dataCompleteStatus = callRecord.getDataCompleteStatus();

        // 判断下载是否完成
        return (!StringUtils.isEmpty(serverFolder) && !serverFolder.equals("404")) || "Y".equals(dataCompleteStatus);
    }
}