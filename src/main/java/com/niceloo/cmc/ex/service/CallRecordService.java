package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.UpdateRecordingDTO;
import com.niceloo.cmc.ex.pojo.request.CallRecordsSelectRequest;
import com.niceloo.cmc.ex.pojo.request.CustRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.BasePageVO;
import com.niceloo.cmc.ex.pojo.vo.BizRecordVO;
import com.niceloo.cmc.ex.pojo.vo.CallRecordsListVO;
import com.niceloo.plugin.sdk.lang.Pair;
import org.springframework.data.elasticsearch.core.query.SearchQuery;

import java.util.List;
import java.util.Map;

public interface CallRecordService {
    /**
     * 批量更新通话记录
     *
     * @param updateRecordingDTOS 需要修改的属性列表
     */
    void bulkUpdate(List<UpdateRecordingDTO> updateRecordingDTOS);

    /**
     * 批量更新通话记录
     *
     * @param indexName   索引名称
     * @param callRecords 更新列表
     */
    void bulkUpdate(String indexName, List<CallRecord> callRecords);

    /**
     * 从通话记录里获取被叫手机号和员工的userID信息列表
     *
     * @param indexName 索引名称
     * @param ids       id列表
     */
    List<CallRecord> getPhoneAndUserIdList(String indexName, List<String> ids);

    /**
     * 批量添加通话记录
     *
     * @param index          索引名称
     * @param callRecordList 通话记录列表
     */
    void bulkSave(String index, List<CallRecord> callRecordList);

    /**
     * 创建索引
     *
     * @param index 索引名称
     */
    boolean createIndex(String index);

    /**
     * 获取通话记录话务相关信息列表
     *
     * @param createdTimeStart 查询开始时间
     * @param createdTimeEnd   查询结束时间
     * @param sourceSearch     需要查询的字段列表
     * @return 通话记录话务相关信息列表
     */
    List<CallRecord> getTrafficInfoList(String createdTimeStart, String createdTimeEnd, List<String> sourceSearch);


    /**
     * 根据条件滚动查询列表
     *
     * @param searchQuery 查询条件
     * @return 通话记录列表
     */
    List<CallRecord> scrollSelectList(SearchQuery searchQuery);

    /**
     * 查询通话记录根据参数(接口 话务查询调用)
     *
     * @param request 接口请求参数
     */
    BasePageVO<CallRecordsListVO> searchRecordsByCriteria(CallRecordsSelectRequest request);

    /**
     * 检索ES索引库, 根据业务标识获取通话记录, 按照呼叫时间(callTime)倒序
     *
     * @param indexes         待检索的索引库
     * @param bizIdSet        业务标识集合, 业务标识即VoiceVo.field2字段
     * @param selectFieldList 要查询字段的列表
     */
    List<BizRecordVO> queryRecordsByBizIds(String indexes, List<String> bizIdSet, List<String> selectFieldList);

    /**
     * 根据外呼唯一id查询补充的字段信息,例如:field1,field2,field5,field6
     *
     * @param indexName   索引名称
     * @param channelType 外呼类型
     * @param uniqueId    厂商外呼ID(field2)
     * @return 只包含补充的字段
     */
    CallRecord querySupplementaryFieldByUniqueId(String indexName, String channelType, String uniqueId);

    List<CallRecord> querySupplementaryFieldByUniqueIds(String indexName, String channelType, List<String> uniqueIds);

    /**
     * 批量删除索引
     *
     * @param indexNames 索引名称列表
     */
    void batchDel(List<String> indexNames);

    /**
     * 根据查询条件滚动查询
     *
     * @param searchQuery 查询条件
     * @return 滚动id和数据列表
     */
    Pair<String, List<CallRecord>> startScroll(SearchQuery searchQuery);

    /**
     * 根据滚动id继续滚动查询
     *
     * @param scrollId 滚动id
     * @return 滚动id和数据列表
     */
    Pair<String, List<CallRecord>> continueScroll(String scrollId);

    /**
     * 根据id删除文档
     *
     * @param indexName 索引名称
     * @param callId    外呼id
     * @return 响应结果 外呼id 索引名称和外呼id为空则返回null
     */
    String deleteDoc(String indexName, String callId);

    /**
     * 通过第三方的厂商id校验是否此条通话记录
     *
     * @param indexName     索引名称
     * @param voiceSourceId 厂商的通话记录id
     * @param channelType   外呼类型
     * @return 是否存在此条通话记录(true : 存在)
     */
    boolean checkCallRecordByVoiceSourceId(String indexName, String voiceSourceId, String channelType);

    /**
     * 根据客户id查询通话记录
     *
     * @param request 请求参数
     * @return 分页的结果
     */
    BasePageVO<Map<String, Object>> queryRecordsByCustId(CustRecordRequest request);
}
