package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.mapper.BdEeMapper;
import com.niceloo.cmc.ex.pojo.dto.BdEeDTO;
import com.niceloo.cmc.ex.pojo.dto.BdEeDptDTO;
import com.niceloo.cmc.ex.pojo.dto.EeSchoolInfoDTO;
import com.niceloo.cmc.ex.service.BdCallaccountinfoService;
import com.niceloo.cmc.ex.service.BdEeService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.utils.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.CallChannelEnum.*;

/**
 * <p>
 * 员工 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Service
public class BdEeServiceImpl implements BdEeService {
    private static final Logger logger = LoggerFactory.getLogger(BdEeServiceImpl.class);
    @Resource
    private BdEeMapper bdEeMapper;
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Resource
    private BdCallaccountinfoService bdCallaccountinfoService;
    private final ThreadLocal<Double> queryCount = ThreadLocal.withInitial(() -> 0.0);
    private final ThreadLocal<Double> useCacheCount = ThreadLocal.withInitial(() -> 0.0);
    private final SecureRandom random = new SecureRandom();

    /**
     * 根据分校id查询分校名称
     *
     * @param schoolId 分校ID
     * @return java.lang.String
     * <AUTHOR>
     * @Date 10:15 2022/2/26
     **/
    @Override
    public String findSchoolNameById(String schoolId) {
        if (StringUtils.isEmpty(schoolId)) {
            return "";
        }
        String schoolName = bdEeMapper.findSchoolNameById(schoolId);
        return StringUtils.nullToEmpty(schoolName);
    }

    /**
     * 根据外呼账号,外呼通道,呼叫时间匹配员工
     *
     * @param callAccount 外呼账号
     * @param callTime    外呼时间,为空时取当前时间,会查询当前绑定callAccount的员工
     * @param callType    外呼通道
     * @return com.niceloo.cmc.ex.pojo.dto.BdEeDTO
     * <AUTHOR>
     * @Date 14:40 2022/3/2
     **/
    @Override
    public BdEeDTO queryBdEeDTOByCallAccountTypeAndTimeUseCache(String callAccount, String callTime, String callType) {
        callTime = StringUtils.isEmpty(callTime) ? DateUtils.getNowDString() : callTime;

        try {
            queryCount.set(queryCount.get() + 1);
            BdEeDTO bdEeDTO;
            String cacheKey = RedisConst.EE_CALL_ACCOUNT + callType + ":" + callAccount;

            try {
                String cacheValue = redisTemplate.opsForValue().get(cacheKey);
                if (StringUtils.isNotEmpty(cacheValue)) {
                    Map<String, Object> cacheMap = JSONUtils.toMap(cacheValue);
                    if (cacheMap != null) {
                        String userId = cacheMap.get("userId").toString();
                        String eeModifieddateWhenCache = cacheMap.get("eeModifieddateWhenCache").toString();
                        String infoModifieddateWhenCache = cacheMap.get("infoModifieddateWhenCache").toString();
                        String callTimeWhenCache = cacheMap.get("callTimeWhenCache").toString();

                        // 如果员工的数据在缓存之后没有被修改过(BdEe,BdPersonalinfo表数据未被修改过,注意不是每个员工都在BdPersonalinfo表中有数据),
                        // 即员工与外呼账号的绑定关系在缓存后没有被修改过,
                        // 那么可以认为时间点callTimeWhenCache之后由外呼账户callAccount产生的通话记录还属于该员工
                        if (DateUtils.compare(DateUtils.toDate(callTime), DateUtils.toDate(callTimeWhenCache)) >= 0) {
                            bdEeDTO = this.findBdEeDTOByUserId(userId);
                            if (bdEeDTO != null && BizConst.WORK_ON.equals(bdEeDTO.getEeWorkstatus())) {
                                String infoModifieddate = StringUtils.isEmpty(bdEeDTO.getInfoModifieddate()) ? "" : bdEeDTO.getInfoModifieddate();
                                if (bdEeDTO.getEeModifieddate().equals(eeModifieddateWhenCache) && infoModifieddate.equals(infoModifieddateWhenCache)) {
                                    useCacheCount.set(useCacheCount.get() + 1);
                                    return bdEeDTO;
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error(e, "查缓存失败:外呼账号与在职员工的绑定关系");
            }

            // 缓存未命中
            bdEeDTO = queryBdEeDTOByCallAccountTypeAndTime(callAccount, callTime, callType);

            // 缓存外呼账号与在职员工的绑定关系,90天后自动失效
            if (bdEeDTO != null && BizConst.WORK_ON.equals(bdEeDTO.getEeWorkstatus())) {
                if (!StringUtils.isEmpty(bdEeDTO.getUserId()) && !StringUtils.isEmpty(bdEeDTO.getEeModifieddate())) {
                    try {
                        Map<String, String> map = MapUtils.toMap(new String[][]{
                                {"userId", bdEeDTO.getUserId()},
                                {"eeModifieddateWhenCache", bdEeDTO.getEeModifieddate()},
                                {"infoModifieddateWhenCache", StringUtils.nullToEmpty(bdEeDTO.getInfoModifieddate())},
                                {"callTimeWhenCache", callTime}
                        });
                        redisTemplate.opsForValue().set(cacheKey, JSONUtils.toJSONString(map), Duration.ofDays(90));
                    } catch (Exception e) {
                        logger.error(e, "更新缓存失败:外呼账号与在职员工的绑定关系");
                    }
                }
            }
            return bdEeDTO;
        } finally {
            if (logCacheHitRate()) {
                logger.info(String.format("queryBdEeDTOByCallAccountTypeAndTimeUseCache缓存命中率: %.2f%%", (useCacheCount.get() / queryCount.get()) * 100));
            }
        }
    }

    /**
     * 根据用户id获取用户信息
     *
     * @return com.niceloo.cmc.ex.pojo.dto.BdEeDTO
     * @paramter userId 用户ID
     * <AUTHOR>
     * @Date 14:52 2022/3/2
     **/
    public BdEeDTO findBdEeDTOByUserId(String userId) {
        return bdEeMapper.findBdEeDTOByUserId(userId);
    }

    /**
     * 根据分校id列表查询分校名称列表
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.EeSchoolInfoDTO>
     * @paramter schoolIds
     * <AUTHOR>
     * @Date 13:52 2022/3/4
     **/
    @Override
    public List<EeSchoolInfoDTO> findSchoolNameByIds(List<String> schoolIds) {
        if (CollectionUtils.isEmpty(schoolIds)) {
            return new ArrayList<>();
        }
        List<EeSchoolInfoDTO> eeSchoolInfoDTOS = bdEeMapper.findSchoolNameByIds(schoolIds);
        return null == eeSchoolInfoDTOS ? new ArrayList<>() : eeSchoolInfoDTOS;
    }

    /**
     * 查询员工信息，包括分校信息
     *
     * @param userId 客户id
     * @return 员工的基础信息
     * <AUTHOR>
     * @Date 15:28 2022/5/28
     * @see com.niceloo.cmc.ex.pojo.dto.BdEeDTO
     **/
    @Override
    public BdEeDTO findBdEeDTOAndSchoolNameByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return new BdEeDTO();
        }
        BdEeDTO bdEeDTO = bdEeMapper.findBdEeDTOAndSchoolNameByUserId(userId);
        return null == bdEeDTO ? new BdEeDTO() : bdEeDTO;
    }

    /**
     * 批量查询员工的组织架构信息
     *
     * @param userIds 员工的userId列表
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.BdEeDptDTO>
     * <AUTHOR>
     * @Date 10:31 2022/10/10
     **/
    @Override
    public List<BdEeDptDTO> findBdEeDptListByUserIds(List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        return bdEeMapper.findBdEeDptListByUserIds(userIds);
    }

    /**
     * @Description: 是否记录缓存命中率
     * <AUTHOR>
     * @Date 2021/12/2 15:36
     */
    private boolean logCacheHitRate() {
        return random.nextDouble() < 0.0001;
    }

    /**
     * 根据外呼账号,外呼通道,呼叫时间匹配员工
     * 可能会匹配到多个员工的原因:
     * 1员工离职后外呼账号交给下一个员工使用: 过滤掉离职时间不为空且打电话时间在离职时间之后的离职员工
     * 2亿讯不同账号下的坐席号有可能相同: 过滤掉绑定的不是当前账号的员工
     * 3多个在职的员工使用了相同的外呼账号: 员工绑定时进行限制,可避免此种情况
     *
     * @param callAccount 外呼账号
     * @param callTime    外呼时间
     * @param callType    外呼通道
     * @return 没匹配到或异常时返回 null
     * <AUTHOR> wangzhenming
     */
    private BdEeDTO queryBdEeDTOByCallAccountTypeAndTime(String callAccount, String callTime, String callType) {
        List<BdEeDTO> bdEes;
        try {
            bdEes = queryByCallAccountAndType(callAccount, callType);
        } catch (Exception e) {
            logger.error(e, "查询MYSQL数据库失败,异常信息:{}", e.getMessage());
            return null;
        }
        if (bdEes.size() == 0) {
            return null;
        }
        if (bdEes.size() == 1) {
            return bdEes.get(0);
        }

        bdEes = bdEes.stream()
                .peek(bdEe -> {
                    if (!BizConst.WORK_LEAVED.equals(bdEe.getEeWorkstatus()) && !StringUtils.isEmpty(bdEe.getEeTermdate())) {
                        // 未离职但有离职时间==>将离职时间置空
                        bdEe.setEeTermdate(null);
                    }
                })
                // 过滤掉已离职但是无离职时间的异常数据
                .filter(bdEe -> !(BizConst.WORK_LEAVED.equals(bdEe.getEeWorkstatus()) && StringUtils.isEmpty(bdEe.getEeTermdate())))
                // 过滤掉离职时间不为空且打电话在离职之后的数据
                .filter(bdEe -> !(!StringUtils.isEmpty(bdEe.getEeTermdate()) && DateUtils.compare(DateUtil.formatYMD_HMS(callTime), DateUtil.formatYMD_HMS(bdEe.getEeTermdate())) > 0))
                .collect(Collectors.toList());
        if (bdEes.size() == 0) {
            return null;
        }
        if (bdEes.size() == 1) {
            return bdEes.get(0);
        }

        // 亿讯不同账号下的坐席号有可能相同
        if (CALL_TYPE_ZK.getType().equals(callType)) {
            String[] accountSplits = callAccount.split("@");
            if (accountSplits.length >= 2) {
                String accountName = callAccount.split("@")[1];
                // 绑定的外呼账号有accountName: 93379@ceshi
                List<BdEeDTO> includeAccountNameBdEe = bdEes.stream().filter(bdEe -> bdEe.getZkyAccount().endsWith(accountName)).collect(Collectors.toList());
                // 绑定的外呼账号无accountName: 93379
                List<BdEeDTO> noneAccountNameBdEe = bdEes.stream().filter(bdEe -> !bdEe.getZkyAccount().endsWith(accountName)).collect(Collectors.toList());
                if (noneAccountNameBdEe.size() > 0) {
                    for (BdEeDTO bdEe : noneAccountNameBdEe) {
                        try {
                            String accountNameByBdEeId = bdCallaccountinfoService.selectAccountNameByBdEeId(bdEe.getEeId(), CALL_TYPE_ZK.getType());
                            if (accountName.equals(accountNameByBdEeId)) {
                                // 绑定的是当前账号下的坐席号
                                includeAccountNameBdEe.add(bdEe);
                            }
                        } catch (Exception e) {
                            logger.error(e, "查询MYSQL数据库失败,异常信息:{}", e.getMessage());
                        }
                    }
                }
                // 已过滤掉绑定的不是当前账号的员工
                bdEes = includeAccountNameBdEe;
            }
        }
        if (bdEes.size() == 0) {
            return null;
        }
        if (bdEes.size() == 1) {
            return bdEes.get(0);
        }

        // ----时间线------呼叫时间------员工1离职时间------员工2离职时间------员工3/员工4离职时间为空------> 更有可能是员工1打的电话
        // 排序bdEes
        // 有离职时间的员工排在无离职时间的员工之前
        // 离职时间越早的员工越靠前
        // 无离职时间的员工排序不确定
        bdEes.sort(Comparator.comparingLong(bdEe -> {
            if (StringUtils.isEmpty(bdEe.getEeTermdate())) {
                return Long.MAX_VALUE;
            }
            Date termDate = DateUtils.toDate(DateUtil.formatYMD_HMS(bdEe.getEeTermdate()));
            if (termDate == null) {
                return Long.MAX_VALUE;
            }
            return termDate.getTime();
        }));
        return bdEes.get(0);
    }

    /**
     * 根据外呼账号和外呼类型获取员工信息
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.BdEeDTO>
     * @paramter callAccount
     * @paramter callType
     * <AUTHOR>
     * @Date 15:07 2022/3/2
     **/
    private List<BdEeDTO> queryByCallAccountAndType(String callAccount, String callType) {
        CallChannelEnum callChannel = CallChannelEnum.getCallChannel(callType);
        if (null == callChannel || StringUtils.isEmpty(callAccount)) {
            return Collections.EMPTY_LIST;
        }

        String select = " SELECT ee.eeId,ee.userId,ee.userName,ee.schoolId,ee.eeNo,ee.eeInnerphone, " +
                " ee.eeWorkstatus,ee.eeHiredate,ee.eeTermdate,ee.eeModifieddate ";
        String otherSelect = " ";
        String from = " FROM BdEe ee ";
        String innerJoin = " ";
        String where;
        if (CALL_TYPE_ZK.equals(callChannel)) {
            // 员工绑定的亿讯外呼账号是坐席号,支持两种格式:93379或93379@ceshi,es中存储的是93379@ceshi这种格式
            String accountNoSuffix = callAccount.split("@")[0];
            if (StringUtils.isEmpty(accountNoSuffix)) {
                return Collections.EMPTY_LIST;
            }
            otherSelect = " ,info.zkyAccount,info.infoModifieddate  ";
            innerJoin = " INNER JOIN BdPersonalinfo info ON ee.eeId = info.eeId ";
            where = " WHERE (info.zkyAccount = '" + callAccount + "' OR info.zkyAccount = '" + accountNoSuffix + "') ";
        } else if (CALL_TYPE_YH.equals(callChannel)) {
            otherSelect = " ,info.infoModifieddate  ";
            innerJoin = " INNER JOIN BdPersonalinfo info ON ee.eeId = info.eeId ";
            where = " WHERE info.zkCallbackaccount = '" + callAccount + "' ";
        } else if (CALL_TYPE_TQ_FIX.equals(callChannel)) {
            otherSelect = " ,info.infoModifieddate  ";
            innerJoin = " INNER JOIN BdPersonalinfo info ON ee.eeId = info.eeId ";
            where = " WHERE info.tqFixaccount = '" + callAccount + "' ";
        } else if (CALL_TYPE_OI.equals(callChannel)) {
            otherSelect = " ,info.infoModifieddate  ";
            innerJoin = " INNER JOIN BdPersonalinfo info ON ee.eeId = info.eeId ";
            where = " WHERE info.oiAccount = '" + callAccount + "' ";
        } else {
            where = " WHERE ee." + getFieldNameForCallAccountByCallType(callChannel) + " = '" + callAccount + "' ";
        }
        String sql = select + otherSelect + from + innerJoin + where;
        List<BdEeDTO> bdEeDTOList = bdEeMapper.selectBdEeInfoUseSQLOrder(sql);
        if (null == bdEeDTOList) {
            return Collections.EMPTY_LIST;
        }
        return bdEeDTOList;
    }

    /**
     * @param callChannel 厂商
     * @return 存储外呼账号的字段名
     * @Description: 查询BdEe表存储各厂商外呼账号的字段名
     * <AUTHOR>
     * @Date 2021/11/29 12:00
     */
    private static String getFieldNameForCallAccountByCallType(CallChannelEnum callChannel) {
        String callAccountField;
        switch (callChannel) {
            case CALL_TYPE_TQ_MOBILE:
                callAccountField = "eeTqaccount";
                break;
            case CALL_TYPE_FY:
                callAccountField = "eeFyaccount";
                break;
            default:
                callAccountField = "userId";
        }
        return callAccountField;
    }
}
