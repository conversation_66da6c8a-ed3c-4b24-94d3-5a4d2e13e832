package com.niceloo.cmc.ex.service.call;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.config.CustProperties;
import com.niceloo.cmc.ex.entity.BdDpt;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.BdEeDTO;
import com.niceloo.cmc.ex.pojo.dto.RecordQueryDTO;
import com.niceloo.cmc.ex.pojo.request.GenerateCallRecordRequest;
import com.niceloo.cmc.ex.pojo.request.GenerateJLYQCallRecordRequest;
import com.niceloo.cmc.ex.pojo.request.PullRecordRequest;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.impl.BdDptServiceImpl;
import com.niceloo.cmc.ex.service.impl.BdEeServiceImpl;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.utils.*;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.mq.client.Client;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.BizConst.CALL_RECORD_ES_PREFIX;
import static com.niceloo.cmc.ex.common.CallChannelEnum.*;

/**
 * 基础的拉取通话记录服务类, 提供公共的方法
 *
 * @author: song
 * @date: 2022/2/24
 */
public class CallRecordsBaseService {
    private static final Logger logger = LoggerFactory.getLogger(CallRecordsBaseService.class);
    private final StringRedisTemplate redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
    private final CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);
    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);
    protected String ES_INDEX = "";
    // 对通话记录的操作类型, C->创建通话记录,U->更新通话记录
    protected String ACTION_TYPE = "C";
    // 存储->查询通话记录失败的请求信息
    protected ConcurrentHashMap<String, Integer> retryMap = new ConcurrentHashMap<>();
    // 存储->查询通话记录失败次数超过阈值的请求信息
    protected List<String> recordList = new LinkedList<>();
    /**
     * 呼叫账号认证 token 缓存，或限流策略
     */
    public static volatile ConcurrentHashMap<String, List<String>> REQ_CACHE = new ConcurrentHashMap<>();
    /**
     * 有录音的通话记录条数
     */
    protected int totalSize = 0;

    /**
     * 拉取通话记录数据(入口,子类应该必须重写)
     * 空实现,不同厂商调用方式不同,在子类中进行实现
     *
     * @param from 开始时间
     * @param to   结束时间
     * <AUTHOR> WangChenyu
     * @Date 16:51 2022/2/25
     **/
    protected void downFile(Date from, Date to) {
        throw new RuntimeException("请使用具体厂商拉取通话记录!!!");
    }

    /**
     * 检查请求时间间隔，避免被限流
     *
     * @paramter key 限流配置KEY,厂商类型
     * @paramter time 间隔时长 毫秒值
     * <AUTHOR> WangChenyu
     * @Date 16:42 2022/2/25
     **/
    protected void checkRequestTime(String key, int time) {
        boolean sleep = false;
        if (null != REQ_CACHE.get(key)) {
            long lastTime = Long.parseLong(REQ_CACHE.get(key).get(0));
            if ((System.currentTimeMillis() - lastTime) < time) {
                sleep = true;
            }
        }
        if (sleep) {
            try {
                Thread.sleep(time);
            } catch (InterruptedException e) {
                logger.error(e, "限流出错");
            }
        }
        List<String> tokenTime = new ArrayList<>();
        tokenTime.add(String.valueOf(System.currentTimeMillis()));
        REQ_CACHE.put(key, tokenTime);
    }

    /**
     * @desc: 调用厂商接口拉取通话记录失败后放入重试Map内进行重试
     * @author: song
     * @date: 2022/2/24
     */
    protected void putRetryMap(String key) {
        if (this.retryMap.get(key) != null) {
            int time = this.retryMap.get(key) + 1;
            this.retryMap.put(key, time);
        } else {
            this.retryMap.put(key, 1);
        }
    }

    /**
     * 开始拉取通话记录
     *
     * @paramter channelType 外呼通道
     * @paramter start 拉取的通话记录开始时间
     * @paramter end 拉取的通话记录结束时间
     * <AUTHOR>
     * @Date 16:43 2022/3/15
     **/
    public void sync(String channelType, String start, String end) {
        this.sync(channelType, start, end, 1);
    }

    /**
     * 按照stepHour拉取同步时间段内的通话记录和录音
     *
     * @param channelType     外呼类型
     * @param start    同步时间段的开始时间 yyyy-MM-dd HH:mm:ss
     * @param end      同步时间段的截止时间 yyyy-MM-dd HH:mm:ss
     * @param stepHour 同步时间段的执行步长, 单位小时
     * <AUTHOR> wangzhenming
     */
    public void sync(String channelType, String start, String end, int stepHour) {
        CallChannelEnum callChannel = CallChannelEnum.getCallChannel(channelType);
        if (null == callChannel) {
            return;
        }
        if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
            throw new RuntimeException(channelType + "拉取通话记录同步时间不能为空");
        }

        // 输入的开始结束日期有时无法正常解析，是cst日期格式，形如：Mon Jul 10 10:00:00 CST 2023，
        // 需要统一转成2023-07-15 10:00:00 形式
        String formatStart = DateUtil.getCompatibleDateTime(start);
        String formatEnd = DateUtil.getCompatibleDateTime(end);

        Date startDate = DateUtils.toDate(formatStart);
        Date endDate = DateUtils.toDate(formatEnd);

        Date fromDate;
        Date toDate;
        // 循环第一次将startDate赋值给fromDate,后面每次序循环都将上次循环后的toDate赋值给fromDate
        for (fromDate = startDate; fromDate.compareTo(endDate) < 0; fromDate = toDate) {
            // 一次同步stepHour小时的数据
            toDate = DateUtils.addHour(fromDate, stepHour);
            // fromDate: 2021-08-30 23:30:00
            // fromDate+1h: 2021-09-01 00:30:00==>同步时间段跨月,2021-09-01 00:00:00-2021-09-01 00:30:00应该存入九月份的索引中
            // toDate需修正为: 2021-09-01 00:00:00
            if (DateUtil.inSameMonth(fromDate, toDate)) {
                // 需处理stepHour步长大于一月的情况
                toDate = DateUtils.addMonth(DateUtil.getFirstDayOfMonth(fromDate), 1);
            }
            if (toDate.compareTo(endDate) >= 0) {
                toDate = endDate;
            }
            // 以fromDate来确定同步时间段内的数据存入那个月的索引
            this.ES_INDEX = RecordUtil.getRecordIndexName(fromDate);
            logger.info(channelType + "===" + this.ES_INDEX + "===拉取人工外呼通话数据" + DateUtils.toStr(fromDate) + " " + DateUtils.toStr(toDate));
            // 每月的第一天检查当月索引是否存在,不存在则创建新的索引,后续不再检查索引是否存在(默认当月第一天索引已创建成功)
            if ("01".equals(String.valueOf(DateUtils.getDay(fromDate)))) {
                checkedIndex(this.ES_INDEX);
            }
            // 执行查询不同厂商通话记录,保存到ES
            downFile(fromDate, toDate);
        }
        logger.info(channelType + " ===拉取人工外呼通话数据停止时间=== " + DateUtils.toStr(fromDate));
        logger.info(channelType + "===通话记录拉取失败的请求参数recordList===" + this.recordList);
        PullRecordRequest.saveFailedPullRecordRequestsToRedisSet(PullRecordRequest.convertUnderlineFormat2JsonString(channelType, this.recordList));
        logger.info(channelType + "===通话录音文件总数===" + this.totalSize + "===拉取周期===" + start + "==" + end);
    }

    /**
     * @param index 索引名称
     * @Description: index索引不存在时新建索引, 并推送对应的mq消息
     * <AUTHOR> wangzhenming, wangchenyu
     * @Date 2021/12/9 9:28
     */
    public static void checkedIndex(String index) {
        CallRecordServiceImpl callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);
        Client client = SpringUtils.getApplicationContext().getBean("default", Client.class);
        boolean flag = callRecordService.createIndex(index);
        if (!flag) {
            client.publish(MQConst.ADD_EE_INFO_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(new RecordQueryDTO(index)));
        }
    }

    /**
     * 上传录音文件到OSS,上传失败时由定时任务继续尝试上传
     *
     * @param callRecord 待入库的CallRecord对象
     * <AUTHOR> WangChenyu
     */
    protected void uploadVoiceToOSS(CallRecord callRecord) {
        if (StringUtils.isEmpty(callRecord.getVoiceSourceUrl())) {
            callRecord.setServerFolder("");
            callRecord.setField1("");
            callRecord.setDataCompleteStatus("Y");
            return;
        }
        callRecord.setVoiceUrl("");
        callRecord.setVoiceSyncStatus(callRecord.getVoiceStatus() == null ? 2 : (callRecord.getVoiceSyncStatus() + 1));
        try {
            Map<String, Object> uploadResMap = HttpUtils.uploadVoiceToOSS(callRecord.getVoiceSourceUrl(), callRecord.getCallId() + ".mp3");
            callRecord.setServerFolder((String) uploadResMap.get("filePath"));
            callRecord.setField1((String) uploadResMap.get("fileKey"));
            callRecord.setDataCompleteStatus("Y");
        } catch (Exception e) {
            logger.info(e, "上传录音到OSS失败");
            callRecord.setServerFolder("");
            callRecord.setField1("");
            callRecord.setDataCompleteStatus("N");
        }
    }

    /**
     * @param callTime   外呼时间
     * @param type       厂商
     * @param callRecord 已设置外呼账号
     * @Description: 添加员工信息
     * <AUTHOR> wangzhenming
     */
    protected void setEE(String callTime, String type, CallRecord callRecord) {
        addEmployeeInfo(callTime, type, callRecord);
    }


    /**
     * @param callTime   外呼时间
     * @param type       厂商类型
     * @param callRecord 通话记录 已设置外呼账号
     * @return 完全补上员工信息返回true, 否则返回false
     * @Description: 匹配员工, 补充通话记录员工信息;未匹配到或异常时通话记录无员工信息
     * <AUTHOR>
     */
    public static boolean addEmployeeInfo(String callTime, String type, CallRecord callRecord) {
        BdEeServiceImpl bdEeService = SpringUtils.getBean(BdEeServiceImpl.class);
        try {
            BdEeDTO bdEeDTO = bdEeService.queryBdEeDTOByCallAccountTypeAndTimeUseCache(callRecord.getCallAccount(), callTime, type);
            if (bdEeDTO == null) {
                return false;
            }
            bdEeDTO.addEeToCallRecord(callRecord);
            String schoolName = bdEeService.findSchoolNameById(bdEeDTO.getSchoolId());
            callRecord.setSchoolName(schoolName);
            BdDptServiceImpl bdDptService = SpringUtils.getBean(BdDptServiceImpl.class);
            BdDpt dpt = bdDptService.findDptIdAndNameByEeId(bdEeDTO.getEeId());
            if (dpt != null) {
                callRecord.setDptId(dpt.getDptId());
                callRecord.setDptName(dpt.getDptName());
            }
            return true;
        } catch (Exception e) {
            logger.error(e, e.getMessage());
            return false;
        }
    }

    /**
     * 公共生成通话记录
     *
     * @param request 通用外呼请求成功生成通话记录参数
     */
    protected Map<String, Object> commonGenerateCallRecord(GenerateCallRecordRequest request) {
        String contactId = request.getContactId();
        // 校验该通话记录是否已经生成
        boolean existRecord = this.isNotExistRecord(contactId, CallChannelEnum.getCallChannel(request.getChannelType()));
        if (existRecord) {
            String index = CALL_RECORD_ES_PREFIX + request.getCreateDate();
            try {
                request.addEeInfo();
                request.addCustInfo();
                request.addOtherInfo();
                CallRecord callRecord = request.getCallRecord();
                if (CALL_TYPE_JLFY.getType().equals(request.getChannelType())) {
                    GenerateJLYQCallRecordRequest recordRequest = (GenerateJLYQCallRecordRequest) request;
                    callRecord.setField5(recordRequest.getAdId().toString());
                    callRecord.setField6(recordRequest.getClueId().toString());
                }
                // 插入通话记录到ES
                this.generateCallRecordToES(index, callRecord);
            } catch (Exception e) {
                logger.error(e, "生成{}通话记录失败,异常信息:{},外呼参数:{}", request.getChannelType(), e.getMessage(), JSONUtils.toJSONString(request));
                // 失败后删除去重ID
                String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + request.getChannelType() + ":" + contactId;
                redisTemplate.delete(redisKeyPrefix);
                return null;
            }
        } else {
            return null;
        }
        String index = CALL_RECORD_ES_PREFIX + request.getCreateDate();
        String createDate = DateUtils.toStr(new Date(), DateUtil.YMD);
        Map<String, Object> mqRequest = new HashMap<>(8);
        mqRequest.put("index", index);
        mqRequest.put("createDate", createDate + " 23:59:59");
        mqRequest.put("channelType", request.getChannelType());
        mqRequest.put("contactId", contactId);
        mqRequest.put("times", 0);
        return mqRequest;
    }

    /**
     * 用来更新通话记录
     *
     * @param callRecord 通话记录(需要包含callTime)
     * @param callId     主键
     * @return java.util.Map<java.lang.String, java.lang.Object> 用来下载通话记录的参数
     * <AUTHOR>
     * @Date 13:41 2022/7/2
     **/
    public Map<String, Object> updateCallRecord(CallRecord callRecord, String callId) {
        // 补充外呼通话信息到ES
        String indexName = CALL_RECORD_ES_PREFIX + DateUtils.toStr(callRecord.getCallTime(), "yyyyMM");
        callRecord.setCallId(callId);
        //这里可能会存在回调和主动查询重复更新通话记录的问题,不过概论比较低，而且重复更新也没有问题,所以没有做处理
        callRecordService.bulkUpdate(indexName, new ArrayList<>(List.of(callRecord)));
        // 更新完成后发送MQ用来下载录音(未接通或通话时长为0不需要补充)
        Integer duration = callRecord.getDuration();
        if (duration > 0
                && (StringUtils.isNotEmpty(callRecord.getVoiceSourceUrl()) || CALL_TYPE_JLFY.getType().equals(callRecord.getChannelType()))) {
            Map<String, Object> data = new HashMap<>(6);
            data.put("channelType", callRecord.getChannelType());
            data.put("indexName", indexName);
            data.put("voiceSourceUrl", callRecord.getVoiceSourceUrl());
            data.put("callId", callId);
            data.put("duration", duration);
            return data;
        }
        return null;
    }


    /**
     * 用来批量更新通话记录
     *
     * @param callRecordList 通话记录(需要包含channelType)
     * @param indexName      索引名称
     * @return java.util.Map<java.lang.String, java.lang.Object> 用来下载通话记录的参数
     * <AUTHOR>
     * @Date 13:41 2022/7/2
     **/
    public List<Map<String, Object>> batchUpdateCallRecord(String indexName, List<CallRecord> callRecordList) {
        List<Map<String, Object>> mqMapList = new ArrayList<>();
        if (CollectionUtils.isEmpty(callRecordList)) {
            return mqMapList;
        }
        // 补充外呼通话信息到ES
        //这里可能会存在回调和主动查询重复更新通话记录的问题,不过概率比较低，而且重复更新也没有问题,所以没有做处理
        try {
            callRecordService.bulkUpdate(indexName, callRecordList);
            for (CallRecord callRecord : callRecordList) {
                // 更新完成后发送MQ用来下载录音(未接通或通话时长为0不需要补充)
                if (callRecord.getDuration() <= 0) {
                    continue;
                }
                if (StringUtils.isNotEmpty(callRecord.getVoiceSourceUrl())
                        || CALL_TYPE_JLFY.getType().equals(callRecord.getChannelType())) {
                    Map<String, Object> data = new HashMap<>(6);
                    data.put("channelType", callRecord.getChannelType());
                    data.put("indexName", indexName);
                    data.put("voiceSourceUrl", callRecord.getVoiceSourceUrl());
                    data.put("callId", callRecord.getCallId());
                    mqMapList.add(data);
                }
            }
        } catch (Exception e) {
            logger.error(e, "批量更新通话记录失败,错误信息:{}", e.getMessage());
        }
        return mqMapList;
    }

    /**
     * 校验该通话记录是否已经生成
     *
     * @param uniqueKey 唯一ID
     * @return boolean 没有生成返回true
     * <AUTHOR>
     * @Date 14:49 2022/5/28
     **/
    protected boolean isNotExistRecord(String uniqueKey, CallChannelEnum channelEnum) {
        if (null == channelEnum) {
            return false;
        }
        // TQ固话和工作手机唯一值校验都是TQ
        if (CALL_TYPE_TQ_FIX == channelEnum) {
            channelEnum = CALL_TYPE_TQ_MOBILE;
        }
        String key = RedisConst.DUPLICATE_RECORD_CHECK + channelEnum.getType() + ":" + uniqueKey;
        Boolean flag = redisTemplate.opsForValue().setIfAbsent(key, "1", Duration.ofDays(92));
        if (null != flag && flag) {
            return true;
        } else {
            logger.info("{}外呼生成通话记录重复投递,唯一key:{}", channelEnum.getType(), key);
            return false;
        }
    }

    /**
     * 校验该通话记录是否已经生成
     *
     * @param uniqueKey   通过智能运营平台拨打成功返回的外呼id
     * @param id          唯一ID
     * @param channelEnum 厂商类型
     * @return boolean
     * <AUTHOR>
     * @Date 17:50 2022/7/8
     **/
    protected boolean isNotExistRecord(String uniqueKey, String id, CallChannelEnum channelEnum) {
        boolean flag = true;
        if (StringUtils.isNotEmpty(uniqueKey)) {
            flag = this.isNotExistRecord(uniqueKey, channelEnum);
        }
        return flag && this.isNotExistRecord(id, channelEnum);
    }

    /**
     * 生成通话记录保存到ES
     *
     * @param index      索引
     * @param callRecord 通话记录
     * <AUTHOR>
     * @Date 17:04 2022/5/28
     **/
    protected void generateCallRecordToES(String index, CallRecord callRecord) {
        callRecordService.bulkSave(index, new ArrayList<>(List.of(callRecord)));
    }

    /**
     * 通话记录添加客户信息<br/>
     * [调用客户中心接口:/api/customers/mobile]
     *
     * @param callRecord 通话记录实体
     * <AUTHOR>
     * @Date 9:56 2022/6/11
     **/
    protected void addCustomerInfoByPhone(CallRecord callRecord) {
        callRecord.setReciverbdUserId("");
        callRecord.setReciverName("");
        callRecord.setAreaCode("");
        callRecord.setAreaName("");
        callRecord.setReciverUserId("");
        String custMobile = FieldCipherUtil.decrypt(callRecord.getReciverPhone());
        if (StringUtils.isEmpty(custMobile) || custMobile.length() != 11) {
            logger.warn("外呼厂商:{}的客户手机号格式不正确,客户手机号:{}", callRecord.getChannelType(), custMobile);
            return;
        }
        Map<String, String> queries = new HashMap<>(2);
        queries.put("BrandId", "YOULU");
        queries.put("Mobile", custMobile);
        String response = okHttpUtil.get(CustProperties.crmUrl + "/api/customers/mobile", queries, null);
        if (StringUtils.isEmpty(response)) {
            return;
        }
        Map<String, Object> responseMap = JSONUtils.toMap(response);
        if (responseMap.containsKey("data")) {
            if (responseMap.get("data") instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
                Object custId = data.get("custId");
                Object userId = data.get("userId");
                Object custName = data.get("custName");
                Object custMobileAreaCode = data.get("custMobileareacode");
                Object custMobileAreaName = data.get("custMobileareaname");
                callRecord.setReciverbdUserId(null == userId ? "" : userId.toString());
                callRecord.setReciverUserId(null == custId ? "" : custId.toString());
                callRecord.setReciverName(null == custName ? "" : FieldCipherUtil.oneEncrypt(custName.toString()));
                callRecord.setAreaCode(null == custMobileAreaCode ? "" : custMobileAreaCode.toString());
                callRecord.setAreaName(null == custMobileAreaName ? "" : custMobileAreaName.toString());
            }
        }
    }


    /**
     * 批量通话记录添加客户信息<br/>
     * [调用客户中心接口:/api/customers/customerinfo]
     *
     * @param callRecordList 通话记录实体List
     * <AUTHOR>
     * @Date 9:56 2022/6/11
     **/
    protected void batchAddCustomerInfoByPhone(List<CallRecord> callRecordList) {
        if (CollectionUtils.isEmpty(callRecordList)) {
            return;
        }
        // 设置客户信息默认值,拼凑客户手机号列表
        List<String> custMobileList = new ArrayList<>(callRecordList.size());
        for (CallRecord callRecord : callRecordList) {
            callRecord.setReciverbdUserId("");
            callRecord.setReciverName("");
            callRecord.setAreaCode("");
            callRecord.setReciverUserId("");
            String custMobile = FieldCipherUtil.decrypt(callRecord.getReciverPhone());
            if (StringUtils.isNotEmpty(custMobile)) {
                custMobileList.add(custMobile);
            }
        }
        // 调用客户营销接口查询客户信息
        Map<String, List<String>> queries = new HashMap<>(1);
        queries.put("mobiles", custMobileList);
        String response = okHttpUtil.post(CustProperties.crmUrl + "/api/customers/customerinfo", JSONUtils.toJSONString(queries));
        if (StringUtils.isEmpty(response)) {
            return;
        }
        // 返回结构解析
        Map<String, Object> responseMap = JSONUtils.toMap(response);
        List<Map<String, Object>> custInfoList = new ArrayList<>(custMobileList.size());
        if (responseMap.containsKey("data")) {
            Object data = responseMap.get("data");
            if (data instanceof Collection) {
                custInfoList = (List<Map<String, Object>>) data;
            }
        }
        if (CollectionUtils.isEmpty(custInfoList)) {
            return;
        }
        // 将从客户营销获取的客户信息添加到通话记录内
        Map<Object, Map<String, Object>> customerInfoMap = custInfoList.stream().collect(Collectors.toMap(m -> (m.get("custMobile")), Function.identity(), (m1, m2) -> m1));
        for (CallRecord callRecord : callRecordList) {
            String receiverPhone = FieldCipherUtil.decrypt(callRecord.getReciverPhone());
            if (customerInfoMap.containsKey(receiverPhone)) {
                Map<String, Object> data = customerInfoMap.get(receiverPhone);
                Object custId = data.get("custId");
                Object userId = data.get("userId");
                Object custName = data.get("custName");
                Object custMobileAreaCode = data.get("custMobileareacode");
                callRecord.setReciverbdUserId(null == userId ? "" : userId.toString());
                callRecord.setReciverUserId(null == custId ? "" : custId.toString());
                callRecord.setReciverName(null == custName ? "" : FieldCipherUtil.oneEncrypt(custName.toString()));
                callRecord.setAreaCode(null == custMobileAreaCode ? "" : custMobileAreaCode.toString());
            }
        }
    }


    /**
     * 接收MQ->下载通话录音并更新ES
     *
     * @param map 通话记录信息
     * <AUTHOR>
     * @Date 11:42 2022/7/1
     **/
    public boolean downLoadRecording(Map<String, Object> map) {
        String voiceSourceUrl = map.get("voiceSourceUrl").toString();
        if (StringUtils.isEmpty(voiceSourceUrl)) {
            return true;
        }
        String indexName = map.get("indexName").toString();
        String channelType = map.get("channelType").toString();
        String callId = map.get("callId").toString();
        String fileName = callId + ".mp3";
        try {
            CallRecord callRecord = new CallRecord();
            Map<String, Object> uploadResMap = HttpUtils.uploadVoiceToOSS(voiceSourceUrl, fileName);
            callRecord.setCallId(callId);
            callRecord.setModifiedTime(new Date());
            callRecord.setVoiceSyncStatus(2);
            callRecord.setServerFolder((String) uploadResMap.get("filePath"));
            callRecord.setField1((String) uploadResMap.get("fileKey"));
            callRecord.setDataCompleteStatus("Y");
            // 更新通话记录
            callRecordService.bulkUpdate(indexName, new ArrayList<>(List.of(callRecord)));
            return true;
        } catch (Exception e) {
            logger.warn(e, channelType + "通道下载通话录音callId为:" + callId + "出现异常");
        }
        return false;
    }
}
