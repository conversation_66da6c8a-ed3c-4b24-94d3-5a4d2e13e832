package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.service.BdService;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 基础数据服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class BdServiceImpl implements BdService {
    private static final Logger logger = LoggerFactory.getLogger(BdServiceImpl.class);

    /**
     * 发送请求到基础数据服务
     *
     * @param api 接口路径
     * @param params 请求参数
     * @return 接口返回结果
     */
    @Override
    public Object request(String api, Map<String, Object> params) {
        logger.info("调用基础数据服务接口: {}, 参数: {}", api, JSONUtils.toJSONString(params));
        // 这里实现调用基础数据服务的逻辑
        // 在实际项目中，这里可能会调用一个Feign客户端或者其他HTTP客户端
        // 为了简化，我们这里直接返回一个模拟的结果
        if ("api/bd/ee/info".equals(api) && params.containsKey("userId")) {
            // 模拟返回员工信息
            return Map.of(
                "userId", params.get("userId"),
                "userName", "员工" + params.get("userId"),
                "dptId", "DPT" + params.get("userId")
            );
        }
        return null;
    }
}
