package com.niceloo.cmc.ex.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.niceloo.cmc.ex.common.*;
import com.niceloo.cmc.ex.config.es.EmptyPageable;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDCallRecordCallback;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDJobDetailsDTO;
import com.niceloo.cmc.ex.pojo.param.JDJobDetailParam;
import com.niceloo.cmc.ex.pojo.param.ManualSyncAiParam;
import com.niceloo.cmc.ex.pojo.param.SyncAiVoiceParam;
import com.niceloo.cmc.ex.pojo.request.AiJobInfoDetailRequest;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.pojo.vo.*;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import com.niceloo.cmc.ex.service.ai.JDYXService;
import com.niceloo.cmc.ex.utils.*;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.file.FileUploadResponse;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.mq.client.Client;
import com.niceloo.mq.client.MessageProp;
import com.niceloo.plugin.sdk.lang.Pair;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import lombok.CustomLog;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.*;
import org.elasticsearch.search.aggregations.metrics.sum.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.ElasticsearchException;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.niceloo.cmc.ex.common.BizConst.AI_JOB_DETAIL_EXCEL;
import static com.niceloo.cmc.ex.common.BizConst.ONE_MINUTE;

/**
 * AI外呼任务下客户信息service
 *
 * <AUTHOR>
 * @since 2022-08-22 18:02
 */
@CustomLog
@Service
public class AICallJobCustomerInfoServiceImpl implements AICallJobCustomerInfoService {

    private static final Logger logger = LoggerFactory.getLogger(AICallJobCustomerInfoServiceImpl.class);

    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private FileUtil fileUtil;
    @Resource
    private JDYXService jdyxService;
    @Resource
    protected RecordProperties recordProperties;

    @Resource
    private Client client;

    // 名字在apollo配置
    @SuppressWarnings("unchecked")
    @Resource(name = "jobAddCustomerRabbitMqClient")
    private Client customerMarketingMqClient;

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @return boolean 创建前是否已经存在该索引
     * <AUTHOR>
     * @since 9:22 2022/8/23
     **/
    @Override
    public boolean createIndex(String indexName) {
        boolean exists = elasticsearchTemplate.indexExists(indexName);
        if (!exists) {
            synchronized (this) {
                exists = elasticsearchTemplate.indexExists(indexName);
                if (!exists) {
                    Map<String, String> setting = new HashMap<>();
                    setting.put("number_of_replicas", "1");
                    setting.put("max_result_window", String.valueOf(BizConst.ES_SEARCH_SIZE));
                    setting.put("number_of_shards", "5");
                    elasticsearchTemplate.createIndex(indexName, setting);
                    elasticsearchTemplate.putMapping(indexName, "_doc", AICallJobCustomerInfo.class);
                }
            }
        }
        return exists;
    }

    /**
     * 批量添加客户的信息
     * <p>
     *
     * @param indexName               索引名称
     * @param callJobCustomerInfoList 客户信息列表
     * <AUTHOR>
     * @since 11:32 2022/8/23
     * @see <a href="www.elastic.co/guide/en/elasticsearch/reference/6.8/modules-http.html">
     * The max content of an HTTP request. Defaults to 100mb. If set to greater than Integer.MAX_VALUE, it will be reset to 100mb.</a>
     **/
    @Override
    public void bulkIndex(String indexName, List<AICallJobCustomerInfo> callJobCustomerInfoList) {
        if (StringUtils.isEmpty(indexName) || CollectionUtils.isEmpty(callJobCustomerInfoList)) {
            return;
        }
        // 分段上传,否则可能会出现请求体过大的异常
        int pageSize = 20000;
        int size = callJobCustomerInfoList.size();
        int page = (size + pageSize - 1) / pageSize;
        List<AICallJobCustomerInfo> segmentCallJobCustomerInfoList = new ArrayList<>(pageSize);
        for (int i = 0; i < page; i++) {
            segmentCallJobCustomerInfoList.addAll(callJobCustomerInfoList.subList(pageSize * i, Math.min(pageSize * (i + 1), size)));
            List<IndexQuery> indexQueries = new ArrayList<>(segmentCallJobCustomerInfoList.size());
            for (AICallJobCustomerInfo callJobCustomerInfo : segmentCallJobCustomerInfoList) {
                IndexQuery indexQuery = new IndexQueryBuilder()
                        .withIndexName(indexName)
                        .withType("_doc")
                        .withId(callJobCustomerInfo.getId())
                        .withObject(callJobCustomerInfo)
                        .build();
                indexQueries.add(indexQuery);
            }
            try {
                elasticsearchTemplate.bulkIndex(indexQueries);
            } catch (ElasticsearchException e) {
                String message = e.getMessage();
                logger.error(e, "批量添加AI外呼任务客户的信息到ES出现异常,索引名称:{},异常信息:{}", indexName, message);
                // 因为索引不存在报错，创建索引后重试
                if (message.contains("type=index_not_found_exception")) {
                    this.retryBulkSave(indexName, segmentCallJobCustomerInfoList);
                }
            } finally {
                segmentCallJobCustomerInfoList.clear();
            }
        }
    }

    /**
     * 重试批量添加，配合 {@link AICallJobCustomerInfoServiceImpl#bulkIndex}失败后重试
     *
     * @param indexName               索引名称
     * @param callJobCustomerInfoList 客户信息列表
     * <AUTHOR>
     * @since 15:07 2022/9/1
     **/
    private void retryBulkSave(String indexName, List<AICallJobCustomerInfo> callJobCustomerInfoList) {
        if (StringUtils.isEmpty(indexName) || CollectionUtils.isEmpty(callJobCustomerInfoList)) {
            return;
        }
        this.createIndex(indexName);
        List<IndexQuery> indexQueries = new ArrayList<>(callJobCustomerInfoList.size());
        for (AICallJobCustomerInfo callJobCustomerInfo : callJobCustomerInfoList) {
            IndexQuery indexQuery = new IndexQueryBuilder()
                    .withIndexName(indexName)
                    .withType("_doc")
                    .withId(callJobCustomerInfo.getId())
                    .withObject(callJobCustomerInfo)
                    .build();
            indexQueries.add(indexQuery);
        }
        elasticsearchTemplate.bulkIndex(indexQueries);
    }

    /**
     * 实体转换器，将客户列表请求参数转换为实体类
     *
     * @param subAiCallJob     子任务信息
     * @param customerRequests 客户信息列表
     * @param lotNo            客户添加的批次号
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo> 转换后的实体类列表
     * <AUTHOR>
     * @since 11:59 2022/8/23
     **/
    @Override
    public List<AICallJobCustomerInfo> entityConverter(CcAiCallJob subAiCallJob, List<JDCallCustomerRequest> customerRequests, String lotNo) {
        List<AICallJobCustomerInfo> callJobCustomerInfoList = new ArrayList<>(customerRequests.size());
        String customerIndex = subAiCallJob.getCustomerIndex();
        //客户信息相同属性的模板
        AICallJobCustomerInfo customerInfoTemplate = AICallJobCustomerInfo.addJobInfo(subAiCallJob);
        for (JDCallCustomerRequest customerRequest : customerRequests) {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            customerRequest.setUuId(RecordUtil.getCustomerVariablesUUID(uuid, customerIndex));
            AICallJobCustomerInfo customerInfo = BeanUtils.copyFromObjToClass(AICallJobCustomerInfo.class, customerInfoTemplate);
            customerInfo.setRecordingDownLoadTimes(0);
            customerInfo.setLotNo(lotNo);
            // 添加每个客户不相同的信息
            customerInfo.setId(uuid);
            customerInfo.addDiffCustomerInfo(customerRequest);
            callJobCustomerInfoList.add(customerInfo);
        }
        return callJobCustomerInfoList;
    }

    /**
     * 更加id批量更新
     *
     * @param indexName        索引名称
     * @param customerInfoList 要更新的客户信息(id字段不能为空)
     * <AUTHOR>
     * @since 14:15 2022/9/3
     * @see <a href="www.elastic.co/guide/en/elasticsearch/reference/6.8/modules-http.html">
     * The max content of an HTTP request. Defaults to 100mb. If set to greater than Integer.MAX_VALUE, it will be reset to 100mb.</a>
     **/
    public void bulkUpdate(String indexName, List<AICallJobCustomerInfo> customerInfoList) {
        if (!CollectionUtils.isEmpty(customerInfoList)) {
            // 分段上传,否则可能会出现请求体过大的异常
            int pageSize = 20000;
            int size = customerInfoList.size();
            int page = (size + pageSize - 1) / pageSize;
            List<AICallJobCustomerInfo> segmentCallJobCustomerInfoList = new ArrayList<>(pageSize);
            for (int i = 0; i < page; i++) {
                segmentCallJobCustomerInfoList.clear();
                segmentCallJobCustomerInfoList.addAll(customerInfoList.subList(pageSize * i, Math.min(pageSize * (i + 1), size)));
                List<UpdateQuery> updateQueryList = new ArrayList<>(segmentCallJobCustomerInfoList.size());
                for (AICallJobCustomerInfo customerInfo : segmentCallJobCustomerInfoList) {
                    UpdateRequest updateRequest = new UpdateRequest();
                    updateRequest.doc(JSONUtils.toMap(JSONUtils.toJSONString(customerInfo)));
                    UpdateQuery updateQuery = new UpdateQueryBuilder()
                            .withIndexName(indexName)
                            .withId(customerInfo.getId())
                            .withType("_doc")
                            .withDoUpsert(false)
                            .withUpdateRequest(updateRequest)
                            .build();
                    updateQueryList.add(updateQuery);
                }
                elasticsearchTemplate.bulkUpdate(updateQueryList);
            }
        }
    }

    /**
     * 根据索引名称和任务主键id查询未同步的客户
     *
     * @param customerIndex 索引名称
     * @param aiJobId       任务主键id
     * @return java.util.List<com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest>
     * <AUTHOR>
     * @since 16:45 2022/8/24
     **/
    @Override
    public List<JDCallCustomerRequest> selectJobCustomerList(String customerIndex, String aiJobId) {
        //查询条件
        TermQueryBuilder jobIdQuery = QueryBuilders.termQuery("jobId", aiJobId);
        TermQueryBuilder syncStatusQuery = QueryBuilders.termQuery("syncStatus", "N");
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(jobIdQuery);
        boolQueryBuilder.must(syncStatusQuery);
        String[] includes = {"id", "customerId", "customerName", "customerPhone", "schoolId", "schoolName"};
        SourceFilter sourceFilter = new FetchSourceFilter(includes, null);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(customerIndex)
                .withFilter(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 3500))
                .withTypes("_doc")
                .withSourceFilter(sourceFilter)
                .build();
        //滚动查询
        ScrolledPage<AICallJobCustomerInfo> customerInfoScrolledPage = elasticsearchTemplate.startScroll(5 * 60 * 1000L, searchQuery, AICallJobCustomerInfo.class);
        String scrollId = customerInfoScrolledPage.getScrollId();
        List<AICallJobCustomerInfo> jobCustomerInfoList = new ArrayList<>(customerInfoScrolledPage.getContent());
        while (customerInfoScrolledPage.hasContent()) {
            customerInfoScrolledPage = elasticsearchTemplate.continueScroll(scrollId, 5 * 60 * 1000L, AICallJobCustomerInfo.class);
            scrollId = customerInfoScrolledPage.getScrollId();
            List<AICallJobCustomerInfo> content = customerInfoScrolledPage.getContent();
            if (CollectionUtils.isEmpty(content)) {
                break;
            }
            jobCustomerInfoList.addAll(content);
        }
        elasticsearchTemplate.clearScroll(scrollId);
        return JDCallCustomerRequest.bulkConvertToCustomerRequest(customerIndex, jobCustomerInfoList);
    }

    /**
     * 根据查询条件滚动查询
     *
     * @param searchQuery 查询条件
     * @return com.niceloo.plugin.sdk.lang.Pair<java.lang.String, java.util.List < com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest>>
     * <AUTHOR>
     * @since 13:42 2022/9/30
     **/
    @Override
    public Pair<String, List<JDCallCustomerRequest>> startScroll(SearchQuery searchQuery) {
        String indexName = searchQuery.getIndices().get(0);
        //滚动查询
        Pair<String, List<JDCallCustomerRequest>> pair = new Pair<>();
        ScrolledPage<AICallJobCustomerInfo> customerInfoScrolledPage = elasticsearchTemplate.startScroll(5 * 60 * 1000L, searchQuery, AICallJobCustomerInfo.class);
        if (customerInfoScrolledPage.hasContent()) {
            List<AICallJobCustomerInfo> customerInfoList = customerInfoScrolledPage.getContent();
            if (!CollectionUtils.isEmpty(customerInfoList)) {
                String scrollId = customerInfoScrolledPage.getScrollId();
                pair.setKey(scrollId);
                pair.setValue(JDCallCustomerRequest.bulkConvertToCustomerRequest(indexName, customerInfoList));
                return pair;
            }
        }
        return pair;
    }

    /**
     * 根据滚动ID滚动查询
     *
     * @param scrollId 滚动ID
     * @return com.niceloo.plugin.sdk.lang.Pair<java.lang.String, java.util.List < com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest>>
     * <AUTHOR>
     * @since 13:43 2022/9/30
     **/
    @Override
    public Pair<String, List<JDCallCustomerRequest>> continueScroll(String indexName, String scrollId) {
        Pair<String, List<JDCallCustomerRequest>> pair = new Pair<>();
        ScrolledPage<AICallJobCustomerInfo> customerInfoScrolledPage = elasticsearchTemplate.continueScroll(scrollId, 5 * 60 * 1000L, AICallJobCustomerInfo.class);
        scrollId = customerInfoScrolledPage.getScrollId();
        if (customerInfoScrolledPage.hasContent()) {
            List<AICallJobCustomerInfo> customerInfoList = customerInfoScrolledPage.getContent();
            if (!CollectionUtils.isEmpty(customerInfoList)) {
                pair.setKey(scrollId);
                pair.setValue((JDCallCustomerRequest.bulkConvertToCustomerRequest(indexName, customerInfoList)));
                return pair;
            }
        }
        elasticsearchTemplate.clearScroll(scrollId);
        return pair;
    }

    /**
     * 根据索引名称、批次号、任务id查询未上传的客户列表
     *
     * @param indexName 索引名称
     * @param lotNo     批次号
     * @param jobId     任务id
     * <AUTHOR>
     * @since 14:00 2022/9/30
     **/
    @Override
    public Pair<String, List<JDCallCustomerRequest>> startScrollByLotNoAndJob(String indexName, String lotNo, String jobId) {
        //查询条件
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (StringUtils.isNotEmpty(lotNo)) {
            TermQueryBuilder lotNoQuery = QueryBuilders.termQuery("lotNo", lotNo);
            boolQueryBuilder.must(lotNoQuery);
        }
        if (StringUtils.isNotEmpty(jobId)) {
            TermQueryBuilder jobIdQuery = QueryBuilders.termQuery("jobId", jobId);
            boolQueryBuilder.must(jobIdQuery);
        }
        TermsQueryBuilder syncStatusQuery = QueryBuilders.termsQuery("syncStatus", "N", "E");
        boolQueryBuilder.must(syncStatusQuery);
        String[] includes = {"id", "customerId", "customerName", "customerPhone", "schoolId", "schoolName", "ext1"};
        SourceFilter sourceFilter = new FetchSourceFilter(includes, null);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 200))
                .withTypes("_doc")
                .withSourceFilter(sourceFilter)
                .build();
        return this.startScroll(searchQuery);
    }

    /**
     * 根据索引名称和任务主键id查询未同步的一个客户
     *
     * @param customerIndex 索引名称
     * @param aiJobId       任务主键id
     * @return com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest
     * <AUTHOR>
     * @since 16:00 2022/9/30
     **/
    @Override
    public List<JDCallCustomerRequest> selectJobCustomerOne(String customerIndex, String aiJobId) {
        //查询条件
        TermQueryBuilder jobIdQuery = QueryBuilders.termQuery("jobId", aiJobId);
        TermQueryBuilder syncStatusQuery = QueryBuilders.termQuery("syncStatus", "N");
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(jobIdQuery);
        boolQueryBuilder.must(syncStatusQuery);
        String[] includes = {"id", "customerId", "customerName", "customerPhone", "schoolId", "schoolName"};
        SourceFilter sourceFilter = new FetchSourceFilter(includes, null);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(customerIndex)
                .withFilter(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1))
                .withTypes("_doc")
                .withSourceFilter(sourceFilter)
                .build();
        List<AICallJobCustomerInfo> customerInfoList = elasticsearchTemplate.queryForList(searchQuery, AICallJobCustomerInfo.class);
        if (!CollectionUtils.isEmpty(customerInfoList)) {
            return JDCallCustomerRequest.bulkConvertToCustomerRequest(customerIndex, customerInfoList);
        }
        return Collections.emptyList();
    }

    /**
     * 获取录音的地址
     *
     * @param indexName 索引名称
     * @param sourceUrl 源录音地址
     * @return java.lang.String
     * <AUTHOR>
     * @since 10:45 2022/11/21
     **/
    @Override
    public String getRecordingUrl(String indexName, String sourceUrl) {
        TermQueryBuilder recordingUrl = QueryBuilders.termQuery("recordingUrl", sourceUrl);
        TermQueryBuilder serverFolder = QueryBuilders.termQuery("serverFolder", "");
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(recordingUrl);
        boolQueryBuilder.mustNot(serverFolder);
        SourceFilter sourceFilter = new FetchSourceFilter(new String[]{"serverFolder"}, null);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1))
                .withTypes("_doc")
                .withSourceFilter(sourceFilter)
                .build();
        List<AICallJobCustomerInfo> customerInfoList = elasticsearchTemplate.queryForList(searchQuery, AICallJobCustomerInfo.class);
        if (!CollectionUtils.isEmpty(customerInfoList)) {
            return customerInfoList.get(0).getServerFolder();
        }
        return null;
    }

    /**
     * 根据外呼任务id获取外呼数据统计总览
     *
     * @param aiJobId   任务id
     * @param indexName 索引名称
     * @return com.niceloo.cmc.ex.pojo.vo.AiJobCallNumStateVO
     * <AUTHOR>
     * @since 17:44 2022/11/22
     **/
    @Override
    public AiJobCallNumStateVO callNumStatAggs(String aiJobId, String indexName) {
        if (StringUtils.isEmpty(aiJobId)) {
            return new AiJobCallNumStateVO();
        }
        // 任务id
        TermQueryBuilder jobIdQuery = QueryBuilders.termQuery("jobId", aiJobId);
        // 聚合器
        // 1、接通总数聚合
        TermQueryBuilder connectAggQuery = QueryBuilders.termQuery("callStatus", 5);
        FilterAggregationBuilder connectAgg = new FilterAggregationBuilder("connectAgg", connectAggQuery);
        // 2、外呼总时长聚合
        SumAggregationBuilder durationSumAgg = new SumAggregationBuilder("durationSumAgg").field("callDuration");
        // 3、挂机数聚合
        RangeQueryBuilder callDurationQuery = QueryBuilders.rangeQuery("callDuration").gte(0).lte(5);
        FilterAggregationBuilder onHookNumAgg = new FilterAggregationBuilder("onHookNumAgg", callDurationQuery);
        connectAgg.subAggregation(durationSumAgg);
        connectAgg.subAggregation(onHookNumAgg);
        // 条件查询
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withQuery(jobIdQuery)
                .withPageable(EmptyPageable.empty())
                .withTypes("_doc")
                .withSourceFilter(new FetchSourceFilter(null, null))
                .addAggregation(connectAgg)
                .build();
        // 查询ES
        AggregatedPage<AICallJobCustomerInfo> aggregated = elasticsearchTemplate.queryForPage(searchQuery, AICallJobCustomerInfo.class);
        // 总客户条数
        long callNum = aggregated.getTotalElements();
        ParsedFilter connectAggResult = (ParsedFilter) aggregated.getAggregation("connectAgg");
        // 接通总数
        long connectNum = connectAggResult.getDocCount();
        Aggregations subAgg = connectAggResult.getAggregations();
        // 外呼总时长
        ParsedSum durationSumAggResult = subAgg.get("durationSumAgg");
        double totalDuration = durationSumAggResult.getValue();
        // 挂机数
        ParsedFilter onHookNumAggResult = subAgg.get("onHookNumAgg");
        long onHookNum = onHookNumAggResult.getDocCount();
        return new AiJobCallNumStateVO(aiJobId, callNum, connectNum, (long) totalDuration, onHookNum);
    }

    /**
     * AI外呼子任务数据聚合统计
     *
     * @param aiJobId   任务id
     * @param indexName 索引名称
     * @return com.niceloo.cmc.ex.pojo.vo.AiJobAggsStateVO
     * <AUTHOR>
     * @since 11:28 2022/11/23
     **/
    @Override
    public AiJobAggsStateVO aiJobAggsState(String aiJobId, String indexName) {
        if (StringUtils.isEmpty(aiJobId)) {
            return new AiJobAggsStateVO();
        }
        // 任务id
        TermQueryBuilder jobIdQuery = QueryBuilders.termQuery("jobId", aiJobId);
        // 多过滤器聚合器
        FiltersAggregator.KeyedFilter[] keyedFilters = this.aiJobAggsFilter().toArray(new FiltersAggregator.KeyedFilter[12]);
        FiltersAggregationBuilder labelAggs = new FiltersAggregationBuilder("labelAggs", keyedFilters);
        // 条件查询
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withQuery(jobIdQuery)
                .withPageable(EmptyPageable.empty())
                .withTypes("_doc")
                .withSourceFilter(new FetchSourceFilter(null, null))
                .addAggregation(labelAggs)
                .build();
        // 查询ES
        AggregatedPage<AICallJobCustomerInfo> aggregated = elasticsearchTemplate.queryForPage(searchQuery, AICallJobCustomerInfo.class);
        // 总客户条数
        long callNum = aggregated.getTotalElements();
        // 获取聚合的数据
        ParsedFilters labelAggsResult = (ParsedFilters) aggregated.getAggregation("labelAggs");
        // 获取过滤器的结果
        AiJobAggsStateVO aiJobAggsStateVO = this.analysisAiJobAggsResult(labelAggsResult);
        aiJobAggsStateVO.setJobId(aiJobId);
        aiJobAggsStateVO.setCallNum(callNum);
        aiJobAggsStateVO.setNotConnectNum(callNum - (aiJobAggsStateVO.getConnectNum() + aiJobAggsStateVO.getNotCalledOut()));
        return aiJobAggsStateVO;
    }

    /**
     * AI外呼子任务客户明细分页查询
     *
     * @param request   列表查询参数
     * @param indexName 索引名称
     * @return com.niceloo.cmc.ex.pojo.vo.BasePageVO<com.niceloo.cmc.ex.pojo.vo.AiJobInfoDetailVO>
     * <AUTHOR>
     * @since 14:10 2022/11/23
     **/
    @Override
    public BasePageVO<AiJobInfoDetailVO> selectAiJobInfoDetail(AiJobInfoDetailRequest request, String indexName) {
        if (StringUtils.isEmpty(request.getAiJobId())) {
            return new BasePageVO<>(0, new ArrayList<>());
        }
        // 组合查询条件
        NativeSearchQuery nativeSearchQuery = this.selectAiJobInfoDetailQuery(request, indexName);
        // 查询ES
        AggregatedPage<AICallJobCustomerInfo> customerInfoList = elasticsearchTemplate.queryForPage(nativeSearchQuery, AICallJobCustomerInfo.class);
        long totalElements = customerInfoList.getTotalElements();
        List<AICallJobCustomerInfo> aiCallJobCustomerInfos = customerInfoList.getContent();
        // 转换为VO
        List<AiJobInfoDetailVO> infoDetailVOList = AiJobInfoDetailVO.bathConvertToAiJobInfoDetailVO(aiCallJobCustomerInfos);
        return new BasePageVO<>(Math.toIntExact(totalElements), infoDetailVOList);
    }

    /**
     * AI外呼子任务客户详情查询(并更新修改时间)
     *
     * @param id        主键Id
     * @param indexName 索引名称
     * @return com.niceloo.cmc.ex.pojo.vo.AiJobInfoDetailVO
     * <AUTHOR>
     * @since 16:43 2022/11/23
     **/
    @Override
    public AiJobInfoDetailVO selectAiJobCustomerInfo(String id, String indexName) {
        // 查询字段
        String[] includes = {"id", "customerName", "customerId", "customerPhone", "callbackStatus",
                "schoolName", "callStatus", "intentLabels", "callDuration", "dialogCount",
                "redialTimes", "ringTime", "lastCallTime", "modifiedTime", "serverFolder"};
        SourceFilter sourceFilter = new FetchSourceFilter(includes, null);
        // 条件查询
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(QueryBuilders.termQuery("id", id))
                .withPageable(PageRequest.of(0, 1))
                .withTypes("_doc")
                .withSourceFilter(sourceFilter)
                .build();
        // 查询ES
        List<AICallJobCustomerInfo> jobCustomerInfoList = elasticsearchTemplate.queryForList(nativeSearchQuery, AICallJobCustomerInfo.class);
        if (CollectionUtils.isEmpty(jobCustomerInfoList)) {
            return new AiJobInfoDetailVO();
        }
        // 更新一下修改时间
        UpdateRequest updateRequest = new UpdateRequest();
        Date date = new Date();
        AICallJobCustomerInfo aiCallJobCustomerInfo = new AICallJobCustomerInfo();
        aiCallJobCustomerInfo.setId(id);
        aiCallJobCustomerInfo.setModifiedTime(date);
        jobCustomerInfoList.get(0).setModifiedTime(date);
        updateRequest.doc(JSONUtils.toMap(JSONUtils.toJSONString(aiCallJobCustomerInfo)));
        UpdateQuery updateQuery = new UpdateQueryBuilder()
                .withIndexName(indexName)
                .withId(id)
                .withType("_doc")
                .withDoUpsert(false)
                .withUpdateRequest(updateRequest)
                .build();
        elasticsearchTemplate.update(updateQuery);
        return AiJobInfoDetailVO.bathConvertToAiJobInfoDetailVO(jobCustomerInfoList).get(0);
    }

    /**
     * AI外呼任务客户列表Excel下载
     *
     * @param aiJobId   任务id
     * @param indexName 索引名称
     * @param jobName   任务名称
     * @param user      登录人
     * @return java.lang.String
     * <AUTHOR>
     * @since 17:37 2022/11/23
     **/
    @Override
    public String aiJobInfoDetailExcel(String aiJobId, String indexName, String jobName, UcUser user) {
        String excelId = RecordUtil.getUUID();
        // 1、重复请求下载校验
        String key = RedisConst.TRAFFIC_STATISTIC_APPLY_EXCEL_KEY + AI_JOB_DETAIL_EXCEL + ":" + user.getUserId();
        Boolean flag = stringRedisTemplate.opsForValue().setIfAbsent(key, excelId, Duration.ofSeconds(30));
        if (Boolean.FALSE.equals(flag)) {
            return stringRedisTemplate.opsForValue().get(key);
        }
        TrafficStatisticServiceImpl.GENERATE_EXCEL_EXECUTOR.execute(() -> {
            // 2、查询ES获取列表明细
            AiJobInfoDetailRequest request = new AiJobInfoDetailRequest();
            request.setAiJobId(aiJobId);
            request.setPageSize(1000);
            List<AICallJobCustomerInfo> customerInfoList = this.scrollSelectList(this.selectAiJobInfoDetailQuery(request, indexName));
            List<AiJobInfoDetailVO> detailVOList = AiJobInfoDetailVO.bathConvertToAiJobInfoDetailVO(customerInfoList);
            // 3、写入到Excel中
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 替换掉Excel的Sheet中不允许出现的字符
            EasyExcelFactory.write(outputStream, AiJobInfoDetailVO.class)
                    .sheet(CommonUtils.legalExcelSheetName(jobName))
                    .doWrite(detailVOList);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 4、上传到阿里云
            try {
                String fileName = jobName + "(" + DateUtils.getNowDString(DateUtil.YMD) + ").xls";
                FileUploadResponse response = fileUtil.uploadExcelFile(inputStream, fileName);
                //5、将结果放到Redis内
                Map<String, String> map = Map.of("type", AI_JOB_DETAIL_EXCEL, "data", JSONUtils.toJSONString(response));
                stringRedisTemplate.opsForValue().set(RedisConst.TRAFFIC_STATISTIC_GENERATE_EXCEL_KEY + excelId, JSONUtils.toJSONString(map), Duration.ofDays(1));
            } catch (Exception e) {
                logger.error(e, "AI外呼任务客户列表Excel上传到阿里云出现异常,异常信息；{}", e.getMessage());
                // 6、将结果放到Redis内
                Map<String, String> map = Map.of("type", AI_JOB_DETAIL_EXCEL, "error", "Excel下载失败,请尝试重新下载");
                stringRedisTemplate.opsForValue().set(RedisConst.TRAFFIC_STATISTIC_GENERATE_EXCEL_KEY + excelId, JSONUtils.toJSONString(map), Duration.ofDays(1));
            }
        });
        return excelId;
    }

    /**
     * 下载并上传未下载的录音文件
     *
     * @param param 包含同步语音参数的对象，用于指定查询、下载和上传的条件
     */
    public void downloadAndUploadRecordings(SyncAiVoiceParam param) {
        // 1. 查询未下载的录音，使用分页
        int pageSize = recordProperties.getBatchSize(); // 每页大小
        int pageNumber = 0; // 当前页码
        List<AICallJobCustomerInfo> recordings;

        do {
            recordings = queryUndownloadedRecordings(param, pageSize, pageNumber);
            if (recordings.isEmpty()) {
                LOGGER.info("没有未下载的AI录音。");
                return;
            }

            // 2. 处理录音下载和上传
            processRecordings(recordings, param);

            pageNumber++; // 翻页
        } while (recordings.size() == pageSize); // 如果返回的记录数等于页大小，继续查询
    }

    /**
     * 根据业务ID集合查询业务记录列表
     *
     * @param indexes      索引标识，用逗号分隔的索引名称字符串
     * @param bizIdSet     业务ID集合，包含需要查询的业务ID
     * @param selectFieldList 需要查询的字段列表，指定返回结果中包含哪些字段
     * @return 包含指定字段的业务记录对象列表，每个元素都是一个BizRecordVO对象
     */
    @Override
    public List<BizRecordVO> queryRecordsByBizIds(String indexes, List<String> bizIdSet, List<String> selectFieldList) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termsQuery("id", bizIdSet));
        SourceFilter sourceFilter = new FetchSourceFilter(selectFieldList.toArray(new String[0]), null);
        PageRequest pageRequest = PageRequest.of(0, 1000, Sort.by(Sort.Direction.DESC, "answerTime"));

        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexes.split(","))
                .withFilter(boolQuery)
                .withTypes("_doc")
                .withPageable(pageRequest)
                .withSourceFilter(sourceFilter)
                .build();

        // 使用 List<Map> 来接收查询结果
        List<Map> searchResults = elasticsearchTemplate.queryForList(searchQuery, Map.class);

        return convertToBizRecordVO(searchResults);
    }

    /**
     * 根据任务类型拉取通话记录
     *
     * @param param 包含作业类型和拉取通话记录所需参数的对象
     */
    @Override
    public void pullCallRecord(ManualSyncAiParam param) {
        String jobType = param.getJobType();
        switch (jobType) {
            case "JDYX":
                pullJdyxCallRecord(param);
                break;
            case "BYAI":
                pullByaiCallRecord(param);
                break;
            default:
                LOGGER.warn("不支持的作业类型：{}", jobType);
        }
    }

    /**
     * 通过AI调用记录同步手动同步
     *
     * @param param 手动同步AI参数
     */
    private void pullByaiCallRecord(ManualSyncAiParam param) {
        LOGGER.warn("暂不支持百应外呼通话记录同步");
    }

    /**
     * 从京东言犀拉取通话记录
     *
     * @param param 手动同步AI参数，包含任务ID列表
     * @return 无返回值
     * @throws NumberFormatException 如果任务ID无法转换为长整型
     */
    private void pullJdyxCallRecord(ManualSyncAiParam param) {
        String[] jobIds = param.getJobIds().split(",");
        if (jobIds.length == 0) {
            LOGGER.warn("任务ID为空，无需同步京东言犀AI通话记录");
            return;
        }

        LOGGER.info("开始同步京东言犀AI通话记录，任务ID：{}", Arrays.toString(jobIds));
        for (String jobIdStr : jobIds) {
            try {
                JDJobDetailParam jdyxParam = new JDJobDetailParam();
                jdyxParam.setJobId(jobIdStr);
                List<JDJobDetailsDTO.JobDetail> jobDetailList = jdyxService.getAllJobDetails(jobIdStr);

                LOGGER.info("获取京东言犀AI通话记录成功，任务ID：{}，通过言犀接口获取的通话记录数量：{}", jobIdStr, jobDetailList.size());

                // 处理通话记录
                if (!jobDetailList.isEmpty()) {
                    for (JDJobDetailsDTO.JobDetail jobDetail : jobDetailList) {
                        JDCallRecordCallback callRecordCallback = CallRecordConverterUtils.convertToCallRecordCallback(jobDetail);
                        // 构造回调请求

                        Map<String, Object> request = new HashMap<>();
                        request.put("code", "10000"); // 假设处理成功的代码
                        request.put("data", callRecordCallback); // 这里需根据实际情况调整

                        // 调用回调处理逻辑
                        handleCallRecordCallback(request);
                    }
                }
            } catch (ApplicationException e) {
                LOGGER.error(ApiErrorCodes.execute_failed, "同步京东言犀AI通话记录时发生异常，任务ID：{}, 报错信息为：{}", jobIdStr, e);
            }
        }

        LOGGER.info("开始保存京东言犀AI通话记录到ES，任务ID：{}", param.getJobIds());
    }

    /**
     * 处理外呼通话记录回调
     *
     * @param request 包含回调信息的请求参数，类型为Map<String, Object>
     */
    private void handleCallRecordCallback(Map<String, Object> request) {
        // 这里将调用 jdAICallRecordsCallBack 中的逻辑
        logger.info("京东言犀AI机器人外呼通话记录处理逻辑参数:{}", JSONUtils.toJSONString(request));
        if ("10000".equals(request.get("code"))) {
            Object data = request.get("data");
            JDCallRecordCallback callRecordCallback = JSONUtils.toObject(JSONUtils.toJSONString(data), JDCallRecordCallback.class);

            // 发布到消息队列
            Map<String, String> queue = new HashMap<>(3);
            queue.put("channelType", CallChannelEnum.CALL_TYPE_JDYX.getType());
            queue.put("data", JSONUtils.toJSONString(callRecordCallback));
            queue.put("type", "callRecordEvent");
            client.publish(MQConst.CALL_RECORD_CALLBACK_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(queue));

            // 发送MQ到客户营销 TODO:下游需要时，可放开注释，给下游推送MQ
            /*MessageProp messageProp = new MessageProp();
            messageProp.setHeaders(Map.of("cap-msg-name", "call_jdyx_detailresult"));
            customerMarketingMqClient.publish(MQConst.CALL_JDYX_DETAIL_RESULT_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(request), messageProp);*/
        }
    }

    /**
     * 将搜索结果列表转换为业务记录对象列表
     *
     * @param searchResults 搜索结果列表，每个元素为一个包含业务记录信息的Map
     * @return 转换后的业务记录对象列表
     */
    private List<BizRecordVO> convertToBizRecordVO(List<Map> searchResults) {
        List<BizRecordVO> bizRecordVOS = new ArrayList<>();

        for (Map result : searchResults) {
            BizRecordVO record = new BizRecordVO();
            record.setCallTime((String) result.get("answerTime")); // 或使用适当的类型转换
            Object durationValue = result.get("callDuration");
            if (durationValue != null) {
                record.setDuration(durationValue.toString());
            } else {
                record.setDuration(null); // 处理 null 情况
            } // 根据实际类型调整
            record.setField2((String) result.get("id"));
            record.setBizId((String) result.get("id"));

            // 处理 callStatus 字段
            Object callStatusValue = result.get("callStatus");
            String callStatus = null;
            if (callStatusValue instanceof String) {
                callStatus = (String) callStatusValue; // 正常处理 String
            } else if (callStatusValue instanceof Integer) {
                callStatus = String.valueOf(callStatusValue); // 将 Integer 转换为 String
            } else {
                callStatus = null; // 处理其他类型或 null
            }
            // 设置 voiceStatus
            record.setVoiceStatus("5".equals(callStatus) ? "D" : callStatus);

            record.setServerFolder((String) result.get("serverFolder"));

            bizRecordVOS.add(record);
        }

        return bizRecordVOS;
    }

    /**
     * 处理录音文件的下载和其他相关操作
     *
     * @param recordings 包含待处理录音信息的AICallJobCustomerInfo对象列表
     * @param param      包含同步语音参数的对象，用于指定操作的条件
     *
     * 方法说明：
     * 此方法负责处理传入的录音列表。对于列表中的每个录音，如果其已标记为已下载（"Y".equals(recording.getHasRecordingDownLoad())），
     * 则执行下载和其他相关操作。为了提高处理效率，使用线程池进行并发处理。
     *
     * 方法流程：
     * 1. 创建一个固定大小的线程池，线程数由recordProperties配置决定。
     * 2. 遍历录音列表，对于每个录音：
     *    a. 检查录音是否已标记为已下载。
     *    b. 如果是，则准备包含必要操作参数的数据映射。
     *    c. 提交一个任务到线程池，任务中调用jdyxService的downLoadRecording方法进行下载和其他操作。
     * 3. 关闭线程池，并等待所有提交的任务完成。如果等待过程中被中断，则记录错误日志。
     */
    private void processRecordings(List<AICallJobCustomerInfo> recordings, SyncAiVoiceParam param) {
        // 使用线程池进行并发处理
        ExecutorService executorService = Executors.newFixedThreadPool(recordProperties.getThreadCount()); // 控制并发线程数

        for (AICallJobCustomerInfo recording : recordings) {
            executorService.submit(() -> {
                if ("Y".equals(recording.getHasRecordingDownLoad())) {
                    Map<String, Object> data = new HashMap<>(4);
                    data.put("indexName", param.getIndex());
                    data.put("recordingDownLoadTimes", recording.getRecordingDownLoadTimes());
                    data.put("voiceSourceUrl", recording.getRecordingUrl());
                    data.put("id", recording.getId());

                    try {
                        jdyxService.downLoadRecording(data);
                    } catch (Exception e) {
                        LOGGER.error("下载录音失败，ID: " + recording.getId(), e);
                    }
                }
            });
        }

        executorService.shutdown(); // 关闭线程池
        try {
            executorService.awaitTermination(1, TimeUnit.HOURS); // 等待所有任务完成
        } catch (InterruptedException e) {
            LOGGER.error("处理录音任务被中断", e);
        }
    }

    /**
     * 查询未下载的录音记录，支持分页
     *
     * @param param     包含查询条件的参数对象
     * @param pageSize  每页大小
     * @param pageNumber 当前页码
     * @return 包含未下载录音信息的AICallJobCustomerInfo对象列表
     * @throws ElasticsearchException 如果查询过程中发生Elasticsearch相关异常，则抛出此异常
     */
    private List<AICallJobCustomerInfo> queryUndownloadedRecordings(SyncAiVoiceParam param, int pageSize, int pageNumber) {
        // 1. 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        // 添加索引名
        String index = param.getIndex();
        queryBuilder.withIndices(index);

        // 添加状态过滤条件
        queryBuilder.withQuery(QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("serverFolder", ""))
                // 添加其他条件，比如时间范围、已同步次数等
                .filter(QueryBuilders.rangeQuery("ringTime").gte(param.getRingTimeStart()).lte(param.getRingTimeEnd()))
                .filter(QueryBuilders.rangeQuery("recordingDownLoadTimes").gte(param.getDownloadTimesStart()).lte(param.getDownloadTimesEnd()))
                .filter(QueryBuilders.rangeQuery("callDuration").gte(1)) // 过滤掉通话时长为0的记录
                .filter(QueryBuilders.termQuery("callStatus", 5)) // 已接通
        );

        if (param.getJobType() != null) {
            queryBuilder.withQuery(QueryBuilders.termQuery("jobType", param.getJobType()));
        }

        // 2. 设置分页
        queryBuilder.withPageable(PageRequest.of(pageNumber, pageSize));

        // 3. 执行查询
        NativeSearchQuery searchQuery = queryBuilder.build();

        return elasticsearchTemplate.queryForList(searchQuery, AICallJobCustomerInfo.class);
    }

    /**
     * 根据查询条件进行滚动查询
     *
     * @param query 查询条件
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo>
     * <AUTHOR>
     * @since 10:21 2022/11/24
     **/
    public List<AICallJobCustomerInfo> scrollSelectList(SearchQuery query) {
        //滚动查询
        ScrolledPage<AICallJobCustomerInfo> recordScrolledPage = elasticsearchTemplate.startScroll(5 * ONE_MINUTE, query, AICallJobCustomerInfo.class);
        String scrollId = recordScrolledPage.getScrollId();
        List<AICallJobCustomerInfo> customerInfoList = new ArrayList<>(recordScrolledPage.getContent());
        while (recordScrolledPage.hasContent()) {
            recordScrolledPage = elasticsearchTemplate.continueScroll(scrollId, 5 * ONE_MINUTE, AICallJobCustomerInfo.class);
            scrollId = recordScrolledPage.getScrollId();
            List<AICallJobCustomerInfo> content = recordScrolledPage.getContent();
            if (CollectionUtils.isEmpty(content)) {
                break;
            }
            customerInfoList.addAll(content);
        }
        elasticsearchTemplate.clearScroll(scrollId);
        return customerInfoList;
    }

    /**
     * 组合查询AI外呼任务客户详情列表条件
     *
     * @param request   查询请求参数
     * @param indexName 索引名称
     * @return org.springframework.data.elasticsearch.core.query.NativeSearchQuery
     * <AUTHOR>
     * @since 15:46 2022/11/23
     **/
    private NativeSearchQuery selectAiJobInfoDetailQuery(AiJobInfoDetailRequest request, String indexName) {
        // 任务id
        TermQueryBuilder jobIdQuery = QueryBuilders.termQuery("jobId", request.getAiJobId());
        // 分页信息
        Integer pageIndex = request.getPageIndex();//offset
        Integer pageSize = request.getPageSize();
        int page = (pageIndex + pageSize - 1) / pageSize;
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "lastCallTime", "modifiedTime"));
        // 查询字段
        String[] includes = {"id", "customerName", "customerId", "customerPhone", "callbackStatus",
                "schoolName", "callStatus", "intentLabels", "callDuration", "dialogCount",
                "redialTimes", "ringTime", "lastCallTime", "modifiedTime", "serverFolder"};
        SourceFilter sourceFilter = new FetchSourceFilter(includes, null);
        // 条件查询
        return new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(jobIdQuery)
                .withPageable(pageable)
                .withTypes("_doc")
                .withSourceFilter(sourceFilter)
                .build();
    }

    /**
     * 整理AI外呼任务聚合过滤器
     *
     * @return java.util.List<org.elasticsearch.search.aggregations.bucket.filter.FiltersAggregator.KeyedFilter>
     * <AUTHOR>
     * @since 12:12 2022/11/23
     **/
    private List<FiltersAggregator.KeyedFilter> aiJobAggsFilter() {
        // AI外呼任务聚合过滤器列表
        List<FiltersAggregator.KeyedFilter> aiJobAggsFilterList = new ArrayList<>();
        // 1、意向标签过滤聚合条件
        // A意向
        FiltersAggregator.KeyedFilter AIntentionFilter = new FiltersAggregator.KeyedFilter("AIntentionFilter", this.intentionLabelQuery("A"));
        // B意向
        FiltersAggregator.KeyedFilter BIntentionFilter = new FiltersAggregator.KeyedFilter("BIntentionFilter", this.intentionLabelQuery("B"));
        // C意向
        FiltersAggregator.KeyedFilter CIntentionFilter = new FiltersAggregator.KeyedFilter("CIntentionFilter", this.intentionLabelQuery("C"));
        // D意向
        FiltersAggregator.KeyedFilter DIntentionFilter = new FiltersAggregator.KeyedFilter("DIntentionFilter", this.intentionLabelQuery("D"));
        // E意向
        FiltersAggregator.KeyedFilter EIntentionFilter = new FiltersAggregator.KeyedFilter("EIntentionFilter", this.intentionLabelQuery("E"));
        // F意向
        FiltersAggregator.KeyedFilter FIntentionFilter = new FiltersAggregator.KeyedFilter("FIntentionFilter", this.intentionLabelQuery("F"));
        aiJobAggsFilterList.add(AIntentionFilter);
        aiJobAggsFilterList.add(BIntentionFilter);
        aiJobAggsFilterList.add(CIntentionFilter);
        aiJobAggsFilterList.add(DIntentionFilter);
        aiJobAggsFilterList.add(EIntentionFilter);
        aiJobAggsFilterList.add(FIntentionFilter);
        // 2、通话情况过滤聚合条件
        // 未呼出
        TermQueryBuilder notCalledOutQuery = QueryBuilders.termQuery("callbackStatus", "N");
        FiltersAggregator.KeyedFilter notCalledOutFilter = new FiltersAggregator.KeyedFilter("notCalledOutFilter", notCalledOutQuery);
        // 已接通
        BoolQueryBuilder connectQuery = QueryBuilders.boolQuery();
        connectQuery.filter(QueryBuilders.termQuery("callbackStatus", "Y"));
        connectQuery.filter(QueryBuilders.termQuery("callStatus", 5));
        FiltersAggregator.KeyedFilter connectFilter = new FiltersAggregator.KeyedFilter("connectFilter", connectQuery);
        aiJobAggsFilterList.add(notCalledOutFilter);
        aiJobAggsFilterList.add(connectFilter);
        // 3、对话伦次过滤聚合条件
        // 0-1轮对话伦次
        BoolQueryBuilder dialogCountGe0AndLe1Query = QueryBuilders.boolQuery();
        RangeQueryBuilder dialogCountRangeQuery = QueryBuilders.rangeQuery("dialogCount");
        dialogCountRangeQuery.gte(0).lte(1);
        dialogCountGe0AndLe1Query.filter(dialogCountRangeQuery);
        dialogCountGe0AndLe1Query.filter(QueryBuilders.termQuery("callStatus", 5));
        FiltersAggregator.KeyedFilter dialogCountGe0AndLe1Filter = new FiltersAggregator.KeyedFilter("dialogCountGe0AndLe1Filter", dialogCountGe0AndLe1Query);
        // 2-5轮对话伦次
        RangeQueryBuilder dialogCountGe2AndLe5Query = QueryBuilders.rangeQuery("dialogCount");
        dialogCountGe2AndLe5Query.gte(2).lte(5);
        FiltersAggregator.KeyedFilter dialogCountGe2AndLe5Filter = new FiltersAggregator.KeyedFilter("dialogCountGe2AndLe5Filter", dialogCountGe2AndLe5Query);
        // 6-9轮对话伦次
        RangeQueryBuilder dialogCountGe6AndLe9Query = QueryBuilders.rangeQuery("dialogCount");
        dialogCountGe6AndLe9Query.gte(6).lte(9);
        FiltersAggregator.KeyedFilter dialogCountGe6AndLe9Filter = new FiltersAggregator.KeyedFilter("dialogCountGe6AndLe9Filter", dialogCountGe6AndLe9Query);
        // 10轮及以上对话伦次
        RangeQueryBuilder dialogCountGe10Query = QueryBuilders.rangeQuery("dialogCount");
        dialogCountGe10Query.gte(10);
        FiltersAggregator.KeyedFilter dialogCountGe10Filter = new FiltersAggregator.KeyedFilter("dialogCountGe10Filter", dialogCountGe10Query);
        aiJobAggsFilterList.add(dialogCountGe0AndLe1Filter);
        aiJobAggsFilterList.add(dialogCountGe2AndLe5Filter);
        aiJobAggsFilterList.add(dialogCountGe6AndLe9Filter);
        aiJobAggsFilterList.add(dialogCountGe10Filter);
        return aiJobAggsFilterList;
    }

    /**
     * 意向标签聚合查询条件
     *
     * @param intention 意向标签
     * @return org.elasticsearch.index.query.BoolQueryBuilder
     * <AUTHOR>
     * @since 11:43 2022/11/23
     **/
    private BoolQueryBuilder intentionLabelQuery(String intention) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.should(QueryBuilders.termQuery("intentLabels", intention));
        boolQuery.should(QueryBuilders.wildcardQuery("intentLabels", intention + ",*"));
        boolQuery.should(QueryBuilders.wildcardQuery("intentLabels", "*," + intention + ",*"));
        boolQuery.should(QueryBuilders.wildcardQuery("intentLabels", "*," + intention));
        return boolQuery;
    }

    /**
     * 解析AI外呼任务的过滤器的结果
     *
     * @param labelAggsResult 聚合返回值
     * @return com.niceloo.cmc.ex.pojo.vo.AiJobAggsStateVO
     * <AUTHOR>
     * @since 12:37 2022/11/23
     **/
    private AiJobAggsStateVO analysisAiJobAggsResult(ParsedFilters labelAggsResult) {
        // 获取过滤器的结果
        ParsedFilters.ParsedBucket AIntentionParsed = labelAggsResult.getBucketByKey("AIntentionFilter");
        ParsedFilters.ParsedBucket BIntentionParsed = labelAggsResult.getBucketByKey("BIntentionFilter");
        ParsedFilters.ParsedBucket CIntentionParsed = labelAggsResult.getBucketByKey("CIntentionFilter");
        ParsedFilters.ParsedBucket DIntentionParsed = labelAggsResult.getBucketByKey("DIntentionFilter");
        ParsedFilters.ParsedBucket EIntentionParsed = labelAggsResult.getBucketByKey("EIntentionFilter");
        ParsedFilters.ParsedBucket FIntentionParsed = labelAggsResult.getBucketByKey("FIntentionFilter");
        ParsedFilters.ParsedBucket notCalledOutParsed = labelAggsResult.getBucketByKey("notCalledOutFilter");
        ParsedFilters.ParsedBucket connectParsed = labelAggsResult.getBucketByKey("connectFilter");
        ParsedFilters.ParsedBucket dialogCountGe0AndLe1Parsed = labelAggsResult.getBucketByKey("dialogCountGe0AndLe1Filter");
        ParsedFilters.ParsedBucket dialogCountGe2AndLe5Parsed = labelAggsResult.getBucketByKey("dialogCountGe2AndLe5Filter");
        ParsedFilters.ParsedBucket dialogCountGe6AndLe9Parsed = labelAggsResult.getBucketByKey("dialogCountGe6AndLe9Filter");
        ParsedFilters.ParsedBucket dialogCountGe10Parsed = labelAggsResult.getBucketByKey("dialogCountGe10Filter");
        AiJobAggsStateVO aiJobAggsStateVO = new AiJobAggsStateVO();
        aiJobAggsStateVO.setIntentionA(AIntentionParsed.getDocCount());
        aiJobAggsStateVO.setIntentionB(BIntentionParsed.getDocCount());
        aiJobAggsStateVO.setIntentionC(CIntentionParsed.getDocCount());
        aiJobAggsStateVO.setIntentionD(DIntentionParsed.getDocCount());
        aiJobAggsStateVO.setIntentionE(EIntentionParsed.getDocCount());
        aiJobAggsStateVO.setIntentionF(FIntentionParsed.getDocCount());
        aiJobAggsStateVO.setNotCalledOut(notCalledOutParsed.getDocCount());
        aiJobAggsStateVO.setConnectNum(connectParsed.getDocCount());
        aiJobAggsStateVO.setDialogCountGe0AndLe1(dialogCountGe0AndLe1Parsed.getDocCount());
        aiJobAggsStateVO.setDialogCountGe2AndLe5(dialogCountGe2AndLe5Parsed.getDocCount());
        aiJobAggsStateVO.setDialogCountGe6AndLe9(dialogCountGe6AndLe9Parsed.getDocCount());
        aiJobAggsStateVO.setDialogCountGe10(dialogCountGe10Parsed.getDocCount());
        return aiJobAggsStateVO;
    }
}
