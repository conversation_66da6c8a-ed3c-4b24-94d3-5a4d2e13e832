package com.niceloo.cmc.ex.service.impl;

import com.alibaba.excel.EasyExcel;
import com.niceloo.cmc.ex.config.es.EmptyPageable;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.AccountDTO;
import com.niceloo.cmc.ex.pojo.dto.EmployeeCallAnalysisExcelDTO;
import com.niceloo.cmc.ex.service.BdCallaccountinfoService;
import com.niceloo.cmc.ex.service.DataCollectionService;
import com.niceloo.cmc.ex.service.WechatGroupRobotService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.FileUtil;
import com.niceloo.framework.file.FileUploadResponse;
import com.niceloo.framework.utils.DateUtils;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据收集服务实现类
 */
@Service
@CustomLog
public class DataCollectionServiceImpl implements DataCollectionService {

    @Resource
    private BdCallaccountinfoService bdCallaccountinfoService;

    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Resource
    private WechatGroupRobotService wechatGroupRobotService;

    @Value("${wechat.channeltypes}")
    private String channelTypesKey;

    @Resource
    private FileUtil fileUtil;

    /**
     * 设置通话结果占比阀值
     */
    @Value("${wechat.warningThreshold.record}")
    private int callResultThreshold;

    /**
     * 设置无状态通话记录占比阀值
     */
    @Value("${wechat.warningThreshold.statelessRecordRatio}")
    private int statelessRecordRatioThreshold;

    @Override
    public void collectAndAnalyzeData() {
        analyzeCallRecords();

        // 员工维度呼叫结果分析
        analyzeEmployeeCallResults();
    }

    /**
     * 分析呼叫记录
     *
     * @return 无返回值
     */
    public void analyzeCallRecords() {
        String indexName = getIndexName();
        String startOfTodayStr = DateUtils.toStr(DateUtil.getTodayStartDate());
        List<String> warningMessages = new ArrayList<>(); // 用于收集告警信息

        for (String channelType : channelTypesKey.split(",")) {
            List<AccountDTO> accountDTOS = bdCallaccountinfoService.selectUniqueAccountsByType(channelType);

            // 针对每个账号查询ES中的记录数
            accountDTOS.forEach(accountDTO -> {
                long recordCount = analyzeAccountRecords(accountDTO, channelType, startOfTodayStr, indexName);
                long recordHasResultCount = analyzeAccountHasResultRecords(accountDTO, channelType, startOfTodayStr, indexName);
                double percent = Math.round((double) recordHasResultCount / recordCount * 10000.0) / 100.0;

                if (recordCount > 0 && percent < callResultThreshold) {
                    String warningMessage = String.format("外呼通道 %s 的账号 %s(%s)当天总外呼量%d条，有通话结果的外呼记录%d条，占比： %.2f%%， 小于： %d%%，请关注!",
                            channelType, accountDTO.getApiAccount(), accountDTO.getAccountName(), recordCount, recordHasResultCount, percent, callResultThreshold);
                    warningMessages.add(warningMessage); // 收集告警信息
                }
            });
        }

        // 统一发送告警信息
        sendWarnings(warningMessages);
    }

    /**
     * 获取索引名称
     *
     * @return 索引名称
     */
    private String getIndexName() {
        String nowDString = DateUtils.getNowDString();
        return "call_record_" + nowDString.substring(0, 7).replace("-", "");
    }

    /**
     * 分析账户记录并返回记录数
     *
     * @param accountDTO 账户DTO对象
     * @param channelType 渠道类型
     * @param startOfTodayStr 今天开始时间
     * @param indexName 索引名称
     * @return 记录数
     */
    private long analyzeAccountRecords(AccountDTO accountDTO, String channelType, String startOfTodayStr, String indexName) {
        String apiAccount = accountDTO.getApiAccount();

        // 构建查询条件
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("field4", apiAccount))
                .must(QueryBuilders.termQuery("channelType", channelType))
                .must(QueryBuilders.rangeQuery("callTime").gte(startOfTodayStr));

        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(queryBuilder)
                .withTypes("_doc")
                .withSort(SortBuilders.fieldSort("_doc"))
                .build();

        // 执行查询并返回记录数
        return elasticsearchTemplate.count(searchQuery);
    }

    /**
     * 分析账户是否有结果记录
     *
     * @param accountDTO 账户信息DTO
     * @param channelType 渠道类型
     * @param startOfTodayStr 当天开始时间
     * @param indexName 索引名称
     * @return 结果记录数
     */
    private long analyzeAccountHasResultRecords(AccountDTO accountDTO, String channelType, String startOfTodayStr, String indexName) {
        String apiAccount = accountDTO.getApiAccount();

        // 构建查询条件
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("field4", apiAccount))
                .must(QueryBuilders.termQuery("channelType", channelType))
                .must(QueryBuilders.rangeQuery("callTime").gte(startOfTodayStr))
                .must(QueryBuilders.rangeQuery("duration").gte(0));

        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withFilter(queryBuilder)
                .withTypes("_doc")
                .withSort(SortBuilders.fieldSort("_doc"))
                .build();

        // 执行查询并返回记录数
        return elasticsearchTemplate.count(searchQuery);
    }

    /**
     * 发送告警信息
     *
     * @param warningMessages 包含告警信息的字符串列表
     * @return 无返回值
     */
    private void sendWarnings(List<String> warningMessages) {
        if (!warningMessages.isEmpty()) {
            // 将所有告警信息拼接成一条消息
            String combinedMessage = String.join("\n", warningMessages);

            // 发送合并后的告警信息
            wechatGroupRobotService.sendMessage(combinedMessage);
        }
    }


    /**
     * 分析员工通话结果
     *
     * 执行一系列查询和分析，包括第一次聚合查询，第二次针对时长为-1的聚合查询，结果对比，
     *            并将结果写入Excel文件，最后上传到阿里云服务器
     */
    private void analyzeEmployeeCallResults() {
        String indexName = getIndexName();
        String startTime = DateUtils.toStr(DateUtil.getTodayStartDate());

        // Perform the first query
        Map<String, Map<String, Long>> results = queryAggregations(indexName, startTime);
        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        // Perform the second query with duration = -1
        Map<String, Map<String, Long>> resultsWithStatelessRecord = queryAggregationsByDuration(indexName, startTime, -1);
        if (CollectionUtils.isEmpty(resultsWithStatelessRecord)) {
            return;
        }

        // Compare results and write to Excel
        String fileName = writeResultsToExcel(results, resultsWithStatelessRecord);

        if (StringUtils.isBlank(fileName)) {
            return;
        }
        // Upload the Excel file to 阿里云服务器
        uploadExcelToAliyun(fileName);
    }

    /**
     * 查询聚合结果
     *
     * @param indexName 索引名
     * @param startTime 起始时间
     * @return 聚合结果映射，键为callerUserId，值为channelType与通话量的映射
     */
    private Map<String, Map<String, Long>> queryAggregations(String indexName, String startTime) {

        // Construct the first query
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.rangeQuery("callTime").gte(startTime));
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withQuery(boolQuery)
                .withTypes("_doc")
                .withPageable(EmptyPageable.empty())
                .addAggregation(AggregationBuilders.terms("caller_agg").field("callerUserId").size(5000)
                        .subAggregation(AggregationBuilders.terms("channel_agg").field("channelType").size(20)
                                .subAggregation(AggregationBuilders.count("count").field("_id"))));

        AggregatedPage<CallRecord> response = elasticsearchTemplate.queryForPage(queryBuilder.build(), CallRecord.class);
        return parseAggregationResults(response);
    }

    /**
     * 根据指定索引名、起始时间和时长查询聚合结果
     *
     * @param indexName 索引名
     * @param startTime 起始时间
     * @param duration  时长
     * @return 聚合结果映射，键为callerUserId，值为channelType与通话量的映射
     */
    private Map<String, Map<String, Long>> queryAggregationsByDuration(String indexName, String startTime, int duration) {

        // Construct the first query
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.rangeQuery("callTime").gte(startTime))
                .must(QueryBuilders.termQuery("duration", duration));

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder()
                .withIndices(indexName)
                .withQuery(boolQuery)
                .withTypes("_doc")
                .withPageable(EmptyPageable.empty())
                .addAggregation(AggregationBuilders.terms("caller_agg").field("callerUserId").size(5000)
                        .subAggregation(AggregationBuilders.terms("channel_agg").field("channelType").size(20)
                                .subAggregation(AggregationBuilders.count("count").field("_id"))
                        )
                );

        AggregatedPage<CallRecord> response = elasticsearchTemplate.queryForPage(queryBuilder.build(), CallRecord.class);
        return parseAggregationResults(response);
    }

    /**
     * 解析聚合结果并返回结果映射
     *
     * @param response 聚合结果
     * @return 结果映射，键为callerUserId，值为channelType与通话量的映射
     */
    private Map<String, Map<String, Long>> parseAggregationResults(AggregatedPage<?> response) {
        Map<String, Map<String, Long>> resultMap = new HashMap<>();

        Terms callerAgg = (Terms) response.getAggregation("caller_agg");
        for (Terms.Bucket callerBucket : callerAgg.getBuckets()) {
            String callerUserId = callerBucket.getKeyAsString();
            Map<String, Long> channelCounts = new HashMap<>();
            Terms channelAgg = callerBucket.getAggregations().get("channel_agg");
            for (Terms.Bucket channelBucket : channelAgg.getBuckets()) {
                String channelType = channelBucket.getKeyAsString();
                long count = channelBucket.getDocCount();
                channelCounts.put(channelType, count);
            }
            resultMap.put(callerUserId, channelCounts);
        }
        return resultMap;
    }

    /**
     * 将通话记录分析结果写入Excel文件
     *
     * @param results                     包含通话记录的Map，键为callerUserId，值为channelType与通话量的映射
     * @param resultsWithStatelessRecord 包含状态未更新通话记录的Map，键为callerUserId，值为channelType与通话量的映射
     * @return 写入成功的Excel文件路径
     * @throws IOException               文件写入异常
     * @throws RuntimeException        其他运行时异常
     */
    private String writeResultsToExcel(Map<String, Map<String, Long>> results, Map<String, Map<String, Long>> resultsWithStatelessRecord) {
        List<EmployeeCallAnalysisExcelDTO> dataList = new ArrayList<>();
        Map<String, Long> emptyMap = new HashMap<>();

        for (String callerId : results.keySet()) {
            for (String channelType : results.get(callerId).keySet()) {
                long totalCount = results.get(callerId).get(channelType);
                long statelessRecordCount = resultsWithStatelessRecord.getOrDefault(callerId, emptyMap).getOrDefault(channelType, 0L);

                // Compute ratio
                double ratio = totalCount > 0 ? (double) statelessRecordCount / totalCount * 100 : 0;

                if (ratio > statelessRecordRatioThreshold) {
                    EmployeeCallAnalysisExcelDTO employeeCallAnalysisExcelDTO = new EmployeeCallAnalysisExcelDTO();
                    employeeCallAnalysisExcelDTO.setCallerUserId(callerId);
                    employeeCallAnalysisExcelDTO.setChannelType(channelType);
                    employeeCallAnalysisExcelDTO.setTotalCount(totalCount);
                    employeeCallAnalysisExcelDTO.setStatelessRecordCount(statelessRecordCount);
                    employeeCallAnalysisExcelDTO.setStatelessRecordRatio(String.format("%.2f", ratio));
                    dataList.add(employeeCallAnalysisExcelDTO);
                }
            }
        }

        // Check if there are any data to write
        if (dataList.isEmpty()) {
            // No data to write, return null
            return null;
        }

        // Write to Excel
        String filePath = "CallRecordsAnalysis.xlsx";
        EasyExcel.write(filePath, EmployeeCallAnalysisExcelDTO.class).sheet("员工通话无状态统计").doWrite(dataList);

        return filePath;
    }

    /**
     * 将Excel文件上传到阿里云
     *
     * @param filePath Excel文件路径
     * @return 无返回值
     */
    private void uploadExcelToAliyun(String filePath) {
        try (FileInputStream inputStream = new FileInputStream(filePath)) {
            String fileName = "员工无状态通话分析(" + DateUtil.getTodayStartDate() + ").xlsx"; // Set the file name
            FileUploadResponse response = fileUtil.uploadExcelFile(inputStream, fileName);

            // Handle the response if needed
            if (response != null) {
                LOGGER.info("File uploaded successfully: " + response.getFilePath());
                String downloadUrl = fileUtil.getTemporaryDownloadUrlByFilePath(response.getFilePath());
                sendUploadSuccessNotification(downloadUrl);
            }
        } catch (Exception e) {
            LOGGER.error(e, "上传Excel到阿里云OSS出现异常,异常信息：{}", e.getMessage());
        }
    }

    /**
     * 发送告警信息
     *
     * @param downloadUrl 文件下载链接
     */
    private void sendUploadSuccessNotification( String downloadUrl) {
        List<String> notificationMessages = new ArrayList<>();
        String message = String.format("存在今日通话无状态的通话告警，员工通话无状态统计信息已上传到阿里云OSS服务器。请点击文件 %s ，下载查看详情。", downloadUrl);
        notificationMessages.add(message);

        // 发送通知信息
        wechatGroupRobotService.sendMessage(message);
    }

}