package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.entity.es.CallRecording;
import com.niceloo.cmc.ex.service.CallRecordingService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import lombok.SneakyThrows;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @description: 通话录音service
 * @author: WangChenyu
 * @create: 2022-02-17 15:05
 */
@Service
public class CallRecordingServiceImpl implements CallRecordingService {
    private static final Logger logger = LoggerFactory.getLogger(CallRecordingServiceImpl.class);
    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;

    /**
     * 根据修改时间升序查询 查询操作次数大于或小于24次的前三千五百条数据
     *
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.CallRecording>
     * @paramter operator 大于或小于等于 [0 小于等于,1 大于]
     * @paramter page 当前页数
     * <AUTHOR>
     * @Date 15:08 2022/2/17
     **/
    @Override
    public List<CallRecording> selectCallRecording(int operator, int page) {
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("attempts");
        switch (operator) {
            case 0:
                rangeQuery.lte(24);
                break;
            case 1:
                rangeQuery.gt(24);
                break;
            default:
                break;
        }
        TermQueryBuilder termQuery = QueryBuilders.termQuery("voiceSourceUrl", "");
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(rangeQuery);
        boolQueryBuilder.mustNot(termQuery);
        Pageable pageable = PageRequest.of(page, 3500, Sort.by(Sort.Direction.ASC, "modifierTime"));
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices("call_recording")
                .withTypes("_doc")
                .withFilter(boolQueryBuilder)
                .withPageable(pageable)
                .build();
        List<CallRecording> callRecordings = elasticsearchTemplate.queryForList(searchQuery, CallRecording.class);
        return CollectionUtils.isEmpty(callRecordings) ? new ArrayList<>() : new ArrayList<>(callRecordings);
    }

    /**
     * 根据id批量删除
     *
     * @return boolean 成功返回true,失败返回false
     * @paramter ids
     * <AUTHOR>
     * @Date 9:29 2022/2/18
     **/
    @Override
    public boolean batchDeleteByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        DeleteQuery deleteQuery = new DeleteQuery();
        deleteQuery.setIndex("call_recording");
        deleteQuery.setType("_doc");
        IdsQueryBuilder idsQueryBuilder = QueryBuilders.idsQuery().addIds(ids.toArray(new String[0]));
        deleteQuery.setQuery(idsQueryBuilder);
        try {
            elasticsearchTemplate.delete(deleteQuery);
        } catch (Exception e) {
            logger.error(e, "根据id批量删除通话录音失败,异常信息:{}", e.getMessage());
            logger.error("根据id批量删除通话录音失败,失败callId列表:{}", ids.toString());
            return false;
        }
        return true;
    }

    /**
     * 批量修改通话录音信息
     *
     * @return void
     * @paramter callRecordings
     * <AUTHOR>
     * @Date 9:33 2022/2/18
     **/
    @Override
    public void bulkUpdate(List<CallRecording> callRecordings) {
        if (CollectionUtils.isEmpty(callRecordings)) {
            return;
        }
        List<UpdateQuery> updateQueryList = new ArrayList<>(callRecordings.size());
        for (CallRecording callRecording : callRecordings) {
            UpdateRequest updateRequest = new UpdateRequest();
            updateRequest.doc(JSONUtils.toMap(JSONUtils.toJSONString(callRecording)));
            UpdateQuery updateQuery = new UpdateQueryBuilder()
                    .withIndexName("call_recording")
                    .withId(callRecording.getCallId())
                    .withType("_doc")
                    .withDoUpsert(false)
                    .withUpdateRequest(updateRequest)
                    .build();
            updateQueryList.add(updateQuery);
        }
        elasticsearchTemplate.bulkUpdate(updateQueryList);
        callRecordings.clear();
    }

    /**
     * 查询没有通话录音地址,但是通话时长大于20秒的前2000条数据
     *
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.CallRecording>
     * @paramter
     * <AUTHOR>
     * @Date 10:48 2022/2/18
     **/
    @Override
    public List<CallRecording> selectNonRecordingList() {
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("attempts").lte(3);
        TermQueryBuilder termQuery = QueryBuilders.termQuery("voiceSourceUrl", "");
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(rangeQuery);
        boolQueryBuilder.must(termQuery);
        Pageable pageable = PageRequest.of(0, 2000, Sort.by(Sort.Direction.ASC, "modifierTime"));
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices("call_recording")
                .withTypes("_doc")
                .withFilter(boolQueryBuilder)
                .withPageable(pageable)
                .build();
        List<CallRecording> callRecordings = elasticsearchTemplate.queryForList(searchQuery, CallRecording.class);
        return CollectionUtils.isEmpty(callRecordings) ? new ArrayList<>() : new ArrayList<>(callRecordings);
    }

    /**
     * 批量添加通话录音
     *
     * @param callRecordList 通话记录列表
     * <AUTHOR>
     * @Date 10:34 2022/2/23
     * @see CallChannelEnum channelType
     **/
    @Override
    public void bulkSave(List<CallRecord> callRecordList) {
        if (CollectionUtils.isEmpty(callRecordList)) {
            return;
        }
        List<IndexQuery> indexQueries = new ArrayList<>(callRecordList.size());
        List<CallRecording> callRecordingList = callRecordingConverter(callRecordList);
        for (CallRecording callRecording : callRecordingList) {
            IndexQuery indexQuery = new IndexQueryBuilder()
                    .withIndexName("call_recording")
                    .withObject(callRecording)
                    .withId(callRecording.getCallId())
                    .build();
            indexQueries.add(indexQuery);
        }
        if (indexQueries.size() > 0) {
            elasticsearchTemplate.bulkIndex(indexQueries);
        }
    }

    @Override
    public void save(CallRecording callRecording) {
        if (callRecording == null || StringUtils.isEmpty(callRecording.getCallId())) {
            return;
        }
        IndexQuery indexQuery = new IndexQueryBuilder()
                .withIndexName("call_recording")
                .withObject(callRecording)
                .withId(callRecording.getCallId())
                .build();
        elasticsearchTemplate.index(indexQuery);
    }

    /**
     * 创建录音索引
     *
     * @paramter
     * <AUTHOR>
     * @Date 9:53 2022/3/17
     **/
    @Override
    public void createIndex() {
        boolean exists = elasticsearchTemplate.indexExists(CallRecording.class);
        if (!exists) {
            Map<String, String> setting = new HashMap<>();
            setting.put("number_of_replicas", "1");
            setting.put("max_result_window", String.valueOf(BizConst.ES_SEARCH_SIZE));
            setting.put("number_of_shards", "5");
            elasticsearchTemplate.createIndex(CallRecording.class, setting);
            elasticsearchTemplate.putMapping(CallRecording.class);
        }
    }

    /**
     * 类型转换器: 将 List<CallRecord>转换为 List<CallRecording>
     *
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.CallRecording>
     * @paramter callRecordList 没有上传录音信息的通话记录列表
     * <AUTHOR>
     * @Date 10:42 2022/2/23
     **/
    private static List<CallRecording> callRecordingConverter(List<CallRecord> callRecordList) {
        List<CallRecording> callRecordingList = new ArrayList<>(callRecordList.size());
        for (CallRecord callRecord : callRecordList) {
            if ("N".equals(callRecord.getDataCompleteStatus())) {
                CallRecording callRecording = CallRecording.initialize();
                callRecording.setCallId(callRecord.getCallId());
                callRecording.setChannelType(callRecord.getChannelType());
                callRecording.setVoiceSourceUrl(callRecord.getVoiceSourceUrl());
                callRecording.setDuration(callRecord.getDuration());
                callRecording.setDate(DateUtils.toStr(callRecord.getCallTime(), "yyyy-MM"));
                callRecordingList.add(callRecording);
            }
        }
        return callRecordingList;
    }

    /**
     * 删除昨天之前的未下载录音记录
     * operationSign=1 未下载，云客的这类记录后面也无法下载，多了会影响正常录音下载
     * 2024-08-27 17:21:34 修改为批量删除数据,而不是一条一条地删除。这样可以大幅提高删除的效率
     */
    @SneakyThrows
    @Override
    public void deleteDataBeforeYesterday() {
        // 获取当前日期
        Date today = new Date();
        // 昨天
        Date yesterday = DateUtils.addDay(today, -1);
        String yesterdayEndTime = DateUtils.toStr(yesterday, DateUtil.YMD) + " 23:59:59";

        // 初始化分页参数
        int pageSize = 1000;
        int pageNumber = 0;
        boolean hasMoreData = true;

        // 记录总删除数据的数量
        int totalDeleted = 0;

        // 先查询符合条件的总记录数
        BoolQueryBuilder countQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("operationSign", "1"))
                .must(QueryBuilders.termQuery("channelType", CallChannelEnum.CALL_TYPE_YPHONE.getType()))
                .must(QueryBuilders.rangeQuery("createTime").lte(yesterdayEndTime));

        SearchResponse countResponse = elasticsearchTemplate.getClient().search(
                new SearchRequest("call_recording")
                        .source(new SearchSourceBuilder()
                                .query(countQueryBuilder)
                                .size(0) // 不需要返回具体数据，只需要总数
                        )
        );

        long totalRecords = countResponse.getHits().getTotalHits(); // 获取总记录数

        logger.info("总共需要删除 {} 条数据", totalRecords);

        while (hasMoreData) {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("operationSign", "1"))
                    .must(QueryBuilders.termQuery("channelType", CallChannelEnum.CALL_TYPE_YPHONE.getType()))
                    .must(QueryBuilders.rangeQuery("createTime").lte(yesterdayEndTime));

            // 执行搜索请求，设置分页
            SearchResponse searchResponse = elasticsearchTemplate.getClient().search(
                    new SearchRequest("call_recording")
                            .source(new SearchSourceBuilder()
                                    .query(queryBuilder)
                                    .size(pageSize)
                                    // 设置偏移量进行分页
                                    .from(pageNumber * pageSize))
            );

            SearchHit[] hits = searchResponse.getHits().getHits();

            if (hits.length == 0) {
                // 如果没有更多数据，退出循环
                hasMoreData = false;
            } else {
                BulkRequest bulkRequest = new BulkRequest();

                for (SearchHit hit : hits) {
                    try {
                        bulkRequest.add(new DeleteRequest("call_recording", "_doc", hit.getId()));
                        totalDeleted++; // 增加删除计数
                    } catch (Exception e) {
                        logger.error("添加删除请求到BulkRequest时出现异常, id: {}", hit.getId(), e);
                    }
                }

                if (bulkRequest.numberOfActions() > 0) {
                    elasticsearchTemplate.getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
                } else {
                    logger.info("没有需要删除的数据");
                }

                // 输出当前进度
                logger.info("已处理第 {} 页，共删除 {} 条数据，总共 {} 条需要删除", pageNumber + 1, totalDeleted, totalRecords);

                // 增加页码，继续下一页
                pageNumber++;
            }
        }

        logger.info("删除操作完成，共删除 {} 条数据", totalDeleted);
    }
}
