package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.entity.CcCountmonth;
import com.niceloo.cmc.ex.mapper.CcCountmonthMapper;
import com.niceloo.cmc.ex.service.CcCountmonthService;
import com.niceloo.framework.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 话务统计表(月) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Service
public class CcCountmonthServiceImpl implements CcCountmonthService {

    @Resource
    private CcCountmonthMapper ccCountmonthMapper;

    /**
     * 根据日期和外呼类型删除,参数不能为空
     *
     * @param date
     *            日期
     * @param channelType
     *            外呼类型
     * <AUTHOR>
     * @since 14:57 2022/2/24
     **/
    @Override
    public void deleteByDate(String date, String channelType) {
        if (StringUtils.isEmpty(date) || StringUtils.isEmpty(channelType)) {
            return;
        }
        ccCountmonthMapper.deleteByDate(date, channelType);
    }

    /**
     * 批量添加
     *
     * @param ccCountMonths
     *            统计列表
     * <AUTHOR>
     * @Date 16:09 2022/3/1
     **/
    @Override
    public void batchSave(List<CcCountmonth> ccCountMonths) {
        ccCountmonthMapper.batchSave(ccCountMonths);
    }
}
