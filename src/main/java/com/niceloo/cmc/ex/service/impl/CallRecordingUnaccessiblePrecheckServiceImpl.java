package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecordingUnaccessible;
import com.niceloo.cmc.ex.pojo.param.UrlCheckResult;
import com.niceloo.cmc.ex.service.CallRecordingUnaccessiblePrecheckService;
import com.niceloo.cmc.ex.service.base.BaseCallRecordingPrecheckService;
import com.niceloo.cmc.ex.service.call.TQService;
import com.niceloo.cmc.ex.utils.AudioAddressCheckerUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.utils.DateUtils;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_TQ_MOBILE;
import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_YPHONE;
import static com.niceloo.cmc.ex.entity.es.CallRecordingUnaccessible.*;

/**
 * 处理可下载的录音的服务实现。
 * <AUTHOR>
 * @since 2024-10-24 11:43:21
 */
@CustomLog
@Service
public class CallRecordingUnaccessiblePrecheckServiceImpl extends BaseCallRecordingPrecheckService implements CallRecordingUnaccessiblePrecheckService {

    /**
     * 处理可下载的录音记录
     *
     * @throws Exception 如果处理过程中出现异常，则抛出异常
     */
    @Override
    public void processAccessibleRecordings() throws Exception {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        LOGGER.info("开始处理可下载的录音...");

        // 第一步：获取可下载的录音
        List<CallRecordingUnaccessible> accessibleRecordings = fetchAccessibleRecordings();
        int totalRecords = accessibleRecordings.size();
        LOGGER.info("共获取到可下载的录音记录：{}", totalRecords);

        // 第二步：准备批量请求
        BulkRequest moveBulkRequest = new BulkRequest();
        BulkRequest deleteBulkRequest = new BulkRequest();

        // 处理可下载的录音记录
        for (int i = 0; i < accessibleRecordings.size(); i++) {
            CallRecordingUnaccessible callRecordingAccessible = accessibleRecordings.get(i);
            String indexName = callRecordingAccessible.getIndexName();

            // 对应call_record_yyyymm索引中的通话录音如果仍未下载，则移动到可下载索引中，并从不可下载索引中删除。
            DeleteRequest deleteRequest = new DeleteRequest(UNACCESSIBLE_INDEX, "_doc", callRecordingAccessible.getCallId());

            if (!isRecordingDownloadComplete(indexName, callRecordingAccessible.getCallId())) {
                // 移动到可下载索引
                moveBulkRequest.add(new IndexRequest(RECORDING_INDEX, "_doc")
                        .id(callRecordingAccessible.getCallId())
                        .source(buildSourceMapForAccessible(callRecordingAccessible)));
            }

            // 添加删除请求到批量请求
            deleteBulkRequest.add(deleteRequest);

            // 打印处理进度日志
            if ((i + 1) % 50 == 0 || i == totalRecords - 1) {
                LOGGER.info("可下载的录音已处理 {} 条录音记录，进度：{}/{}", (i + 1), (i + 1), totalRecords);
            }

            // 如果达到批处理大小，执行批量操作
            if ((i + 1) % BATCH_SIZE == 0 || i == accessibleRecordings.size() - 1) {
                executeBulkRequests(moveBulkRequest, deleteBulkRequest);
                // 清空请求
                moveBulkRequest = new BulkRequest();
                deleteBulkRequest = new BulkRequest();
            }
        }

        // 记录结束时间并计算耗时
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        LOGGER.info("处理完成，耗时：{} 毫秒", duration);
    }

    /**
     * 获取可访问的录音记录列表
     *
     * @return 可访问的录音记录列表
     * @throws IOException 如果在操作过程中发生I/O异常
     */
    private List<CallRecordingUnaccessible> fetchAccessibleRecordings() throws IOException {
        List<CallRecordingUnaccessible> accessibleRecordings = Collections.synchronizedList(new ArrayList<>());

        // 设置 scroll 参数
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(recordProperties.getScrollTimeout()));
        SearchRequest searchRequest = createInitialSearchRequest(scroll);

        // 初始查询
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();

        int totalHits = (int) searchResponse.getHits().getTotalHits(); // 总记录数
        int processedHits = 0; // 已处理记录数

        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(recordProperties.getThreadCount()); // 根据需求设置线程数

        // 处理每一批记录
        while (true) {
            processBatch(searchResponse, executorService, accessibleRecordings, processedHits, totalHits);

            // 获取下一个 scroll
            searchResponse = client.scroll(new SearchScrollRequest(scrollId).scroll(scroll), RequestOptions.DEFAULT);
            if (searchResponse.getHits().getHits().length == 0) {
                break; // 没有更多记录，退出循环
            }
            scrollId = searchResponse.getScrollId();
        }

        executorService.shutdown(); // 关闭线程池
        clearScroll(scrollId); // 清除 scroll 上下文
        return accessibleRecordings;
    }

    /**
     * 创建初始搜索请求
     *
     * @param scroll scroll 参数
     * @return 初始搜索请求
     */
    private SearchRequest createInitialSearchRequest(Scroll scroll) {
        return new SearchRequest(UNACCESSIBLE_INDEX)
                .scroll(scroll)
                .source(new SearchSourceBuilder()
                        .size(recordProperties.getBatchSize())
                        .query(QueryBuilders.boolQuery()
                                .must(QueryBuilders.rangeQuery("nextInspectTime")
                                        .lte(DateUtils.toStr(new Date())))));
    }

    /**
     * 处理一批记录
     *
     * @param searchResponse 搜索响应
     * @param executorService 线程池
     * @param accessibleRecordings 可访问的录音记录列表
     * @param processedHits 已处理记录数
     * @param totalHits 总记录数
     * @throws IOException 如果在操作过程中发生I/O异常
     */
    private void processBatch(SearchResponse searchResponse, ExecutorService executorService, List<CallRecordingUnaccessible> accessibleRecordings, int processedHits, int totalHits) throws IOException {
        List<Future<CallRecordingUnaccessible>> futures = new ArrayList<>();

        for (SearchHit hit : searchResponse.getHits().getHits()) {
            // 提交任务到线程池
            futures.add(executorService.submit(() -> processHit(hit)));
        }

        // 等待所有任务完成并处理结果
        for (Future<CallRecordingUnaccessible> future : futures) {
            try {
                CallRecordingUnaccessible result = future.get();
                if (result != null) {
                    accessibleRecordings.add(result);
                    processedHits++; // 增加已处理记录数

                    // 打印处理进度日志
                    if (processedHits % 30 == 0 || processedHits == totalHits) {
                        LOGGER.info("可下载录音，已获取 {} 条录音记录，进度：{}/{}", processedHits, processedHits, totalHits);
                    }
                }
            } catch (InterruptedException e) {
                LOGGER.error("获取录音记录时发生中断异常", e);
                // 重新设置中断状态
                Thread.currentThread().interrupt(); // 保持中断状态
            } catch (ExecutionException e) {
                LOGGER.error("获取录音记录时发生异常", e);
            }
        }
    }

    /**
     * 处理搜索结果中的一条命中记录，将其转换为可访问的录音对象
     *
     * @param hit 搜索结果中的一条命中记录
     * @return 可访问的录音对象，如果URL为空、无法获取可访问URL、URL不可访问或文件大小小于最小文件大小则返回null
     */
    private CallRecordingUnaccessible processHit(SearchHit hit) {
        Map<String, Object> sourceAsMap = hit.getSourceAsMap();
        CallRecordingUnaccessible callRecordingUnaccessible = convertMapToUnaccessible(sourceAsMap);
        String voiceSourceUrl = callRecordingUnaccessible.getVoiceSourceUrl();

        if (StringUtils.isEmpty(voiceSourceUrl)) {
            // URL 为空，直接跳过
            return null;
        }

        String accessibleUrl = voiceSourceUrl;
        if (CALL_TYPE_YPHONE.equals(CallChannelEnum.getCallChannel(callRecordingUnaccessible.getChannelType()))) {
            try {
                accessibleUrl = fileUtil.getAccessibleUrl(voiceSourceUrl);
                if (accessibleUrl == null) {
                    return null; // 获取可访问 URL 失败
                }
            } catch (ApplicationException e) {
                LOGGER.error("获取可访问URL时发生异常", e);
                return null;
            }
        }

        UrlCheckResult urlCheckResult = AudioAddressCheckerUtil.checkUrlAccessibility(accessibleUrl);
        // 如果TQ手机外呼，则录音地址切换后，再次尝试
        if (!urlCheckResult.isUrlAccessible() && CALL_TYPE_TQ_MOBILE.equals(CallChannelEnum.getCallChannel(callRecordingUnaccessible.getChannelType()))) {
            accessibleUrl = TQService.switchHostName(accessibleUrl);
            urlCheckResult = AudioAddressCheckerUtil.checkUrlAccessibility(accessibleUrl);
        }
        if (urlCheckResult.isUrlAccessible() && urlCheckResult.getFileSize() >= MINIMUM_FILE_SIZE) {
            callRecordingUnaccessible.setAudioSize(urlCheckResult.getFileSize());
            callRecordingUnaccessible.setAttempts(callRecordingUnaccessible.getAttempts()+1);

            // 通过指数退避算法设置下次检查时间
            Date nextInspectTime = DateUtils.addMinutes(new Date(), (int) Math.pow(2, callRecordingUnaccessible.getAttempts()));

            // 计算2天后的时间
            Date twoDaysLater = DateUtils.addDay(new Date(), 2);

            // 确保下次检查时间不超过2天后的时间
            if (nextInspectTime.after(twoDaysLater)) {
                nextInspectTime = twoDaysLater;
            }

            callRecordingUnaccessible.setNextInspectTime(nextInspectTime);
            return callRecordingUnaccessible; // 返回可访问的录音
        }
        // URL 不可访问，返回 null
        return null;
    }
}
