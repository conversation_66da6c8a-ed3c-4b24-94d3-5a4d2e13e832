package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecordingUnaccessible;
import com.niceloo.cmc.ex.pojo.param.UrlCheckResult;
import com.niceloo.cmc.ex.service.CallRecordingPrecheckService;
import com.niceloo.cmc.ex.service.base.BaseCallRecordingPrecheckService;
import com.niceloo.cmc.ex.service.call.TQService;
import com.niceloo.cmc.ex.utils.AudioAddressCheckerUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.utils.DateUtils;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_TQ_MOBILE;
import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_YPHONE;
import static com.niceloo.cmc.ex.entity.es.CallRecordingUnaccessible.buildSourceMapForUnaccessible;
import static com.niceloo.cmc.ex.entity.es.CallRecordingUnaccessible.convertMapToUnaccessible;

/**
 * 处理不可下载的录音的服务实现。
 * <AUTHOR>
 * @since 2024-10-14 14:50:15
 */
@CustomLog
@Service
public class CallRecordingPrecheckServiceImpl extends BaseCallRecordingPrecheckService implements CallRecordingPrecheckService {

    /**
     * 处理不可下载的录音记录。
     *
     * <p>该方法用于处理不可下载的录音记录，将这些记录移动到不可下载索引中，并从可下载索引中删除。
     * 首先，获取所有不可下载的录音记录。然后，对于每条记录，检查其对应索引中的 serverFolder 和 dataCompleteStatus 字段。
     * 如果记录未下载完成，则将其移动到不可下载索引中，并从可下载索引中删除。
     * 如果达到批处理大小或处理完所有记录后，执行批量操作。</p>
     *
     * @throws Exception 如果在处理过程中出现异常，则抛出该异常。
     */
    @Override
    public void processUnaccessibleRecordings() throws Exception {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        LOGGER.info("开始处理不可下载的录音...");

        // 第一步：获取不可下载的录音
        List<CallRecordingUnaccessible> unaccessibleRecordings = fetchUnaccessibleRecordings();
        int totalRecords = unaccessibleRecordings.size();
        LOGGER.info("共获取到不可下载的录音记录：{}", totalRecords);

        // 第二步：准备批量请求
        BulkRequest moveBulkRequest = new BulkRequest();
        BulkRequest deleteBulkRequest = new BulkRequest();

        // 处理不可下载的录音记录
        for (int i = 0; i < unaccessibleRecordings.size(); i++) {
            CallRecordingUnaccessible callRecordingUnaccessible = unaccessibleRecordings.get(i);
            String indexName = callRecordingUnaccessible.getIndexName();

            // 对应call_record_yyyymm索引中的通话录音如果仍未下载，则移动到不可下载索引中，并从可下载索引中删除。
            if (!isRecordingDownloadComplete(indexName, callRecordingUnaccessible.getCallId())) {
                // 如果是云客通道外呼，或者createTime未半年，则移动到不可下载索引中，并从可下载索引中删除。
                if (CALL_TYPE_YPHONE.equals(CallChannelEnum.getCallChannel(callRecordingUnaccessible.getChannelType())) ||
                        DateUtils.compare(DateUtils.addDay(callRecordingUnaccessible.getCreateTime(), 180), new Date()) > 0) {
                    // 移动到不可下载索引
                    moveBulkRequest.add(new IndexRequest(UNACCESSIBLE_INDEX, "_doc")
                            .id(callRecordingUnaccessible.getCallId())
                            .source(buildSourceMapForUnaccessible(callRecordingUnaccessible)));
                }

                // 从可下载索引中删除
                deleteBulkRequest.add(new DeleteRequest(RECORDING_INDEX, "_doc", callRecordingUnaccessible.getCallId()));
            }

            // 打印处理进度日志
            if ((i + 1) % 50 == 0 || i == totalRecords - 1) {
                LOGGER.info("不可下载的录音已处理 {} 条录音记录，进度：{}/{}", (i + 1), (i + 1), totalRecords);
            }

            // 如果达到批处理大小，执行批量操作
            if ((i + 1) % BATCH_SIZE == 0 || i == unaccessibleRecordings.size() - 1) {
                executeBulkRequests(moveBulkRequest, deleteBulkRequest);
                // 清空请求
                moveBulkRequest = new BulkRequest();
                deleteBulkRequest = new BulkRequest();
            }
        }

        // 记录结束时间并计算耗时
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        LOGGER.info("处理完成，耗时：{} 毫秒", duration);
    }

    /**
     * 获取不可下载的录音记录列表。
     *
     * @return 不可下载的录音记录列表
     * @throws IOException 如果发生I/O异常，则抛出IOException
     */
    private List<CallRecordingUnaccessible> fetchUnaccessibleRecordings() throws IOException {
        List<CallRecordingUnaccessible> unaccessibleRecordings = Collections.synchronizedList(new ArrayList<>());

        // 设置 scroll 参数
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(recordProperties.getScrollTimeout()));
        SearchRequest searchRequest = new SearchRequest(RECORDING_INDEX)
                .scroll(scroll)
                .source(new SearchSourceBuilder().size(recordProperties.getBatchSize()));

        // 初始查询
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();

        int totalHits = (int) searchResponse.getHits().getTotalHits(); // 总记录数
        int processedHits = 0; // 已处理记录数

        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(recordProperties.getThreadCount()); // 根据需求设置线程数

        // 处理每一批记录
        while (true) {
            processBatch(searchResponse, unaccessibleRecordings, totalHits, processedHits, executorService);

            // 获取下一个 scroll
            searchResponse = client.scroll(new SearchScrollRequest(scrollId).scroll(scroll), RequestOptions.DEFAULT);
            if (searchResponse.getHits().getHits().length == 0) {
                break; // 没有更多记录，退出循环
            }
            scrollId = searchResponse.getScrollId();
        }

        executorService.shutdown(); // 关闭线程池
        clearScroll(scrollId); // 清除 scroll 上下文
        return unaccessibleRecordings;
    }

    /**
     * 批量处理搜索结果中的录音记录。
     *
     * @param searchResponse       搜索结果响应对象，包含需要处理的录音记录信息。
     * @param unaccessibleRecordings 不可访问的录音记录列表，用于存储处理后的结果。
     * @param totalHits            搜索结果中的总命中数。
     * @param processedHits        已处理的命中数。
     * @param executorService      线程池服务，用于并发处理任务。
     */
    private void processBatch(SearchResponse searchResponse, List<CallRecordingUnaccessible> unaccessibleRecordings, int totalHits, int processedHits, ExecutorService executorService) {
        List<Future<CallRecordingUnaccessible>> futures = new ArrayList<>();

        for (SearchHit hit : searchResponse.getHits().getHits()) {
            // 提交任务到线程池
            futures.add(executorService.submit(() -> processHit(hit)));
        }

        // 等待所有任务完成并处理结果
        for (Future<CallRecordingUnaccessible> future : futures) {
            try {
                CallRecordingUnaccessible result = future.get();
                if (result != null) {
                    unaccessibleRecordings.add(result);
                    processedHits++; // 增加已处理记录数

                    // 打印处理进度日志
                    if (processedHits % 30 == 0 || processedHits == totalHits) {
                        LOGGER.info("不可下载录音，已获取 {} 条录音记录，进度：{}/{}", processedHits, processedHits, totalHits);
                    }
                }
            } catch (InterruptedException e) {
                LOGGER.error("获取录音记录时发生中断异常", e);
                // 重新设置中断状态
                Thread.currentThread().interrupt(); // 保持中断状态
            } catch (ExecutionException e) {
                LOGGER.error("获取录音记录时发生异常", e);
            }
        }
    }

    /**
     * 处理一个搜索命中项，将其转换为不可访问的录音记录对象，并检查录音的URL是否可访问。
     *
     * @param hit 搜索命中项，包含录音记录的源数据
     * @return 如果录音记录的URL为空或不可访问，则返回该录音记录对象；如果URL可访问，则返回null
     */
    private CallRecordingUnaccessible processHit(SearchHit hit) {
        Map<String, Object> sourceAsMap = hit.getSourceAsMap();
        CallRecordingUnaccessible callRecordingUnaccessible = convertMapToUnaccessible(sourceAsMap);
        String voiceSourceUrl = callRecordingUnaccessible.getVoiceSourceUrl();

        if (StringUtils.isEmpty(voiceSourceUrl)) {
            // URL 为空，直接跳过
            return null;
        }

        String accessibleUrl = voiceSourceUrl;
        if (CALL_TYPE_YPHONE.equals(CallChannelEnum.getCallChannel(callRecordingUnaccessible.getChannelType()))) {
            try {
                accessibleUrl = fileUtil.getAccessibleUrl(voiceSourceUrl);
                if (accessibleUrl == null) {
                    return null; // 获取可访问 URL 失败
                }
            } catch (ApplicationException e) {
                LOGGER.error("获取可访问URL时发生异常", e);
                return null;
            }
        }

        UrlCheckResult urlCheckResult = AudioAddressCheckerUtil.checkUrlAccessibility(accessibleUrl);
        // 如果TQ手机外呼，则录音地址切换后，再次尝试
        if (!urlCheckResult.isUrlAccessible() && CALL_TYPE_TQ_MOBILE.equals(CallChannelEnum.getCallChannel(callRecordingUnaccessible.getChannelType()))) {
            accessibleUrl = TQService.switchHostName(accessibleUrl);
            urlCheckResult = AudioAddressCheckerUtil.checkUrlAccessibility(accessibleUrl);
        }
        if (!urlCheckResult.isUrlAccessible() || urlCheckResult.getFileSize() < MINIMUM_FILE_SIZE) {
            callRecordingUnaccessible.setAudioSize(urlCheckResult.getFileSize());
            callRecordingUnaccessible.setAttempts(callRecordingUnaccessible.getAttempts()+1);

            // 通过指数退避算法设置下次检查时间
            Date nextInspectTime = DateUtils.addMinutes(new Date(), (int) Math.pow(2, callRecordingUnaccessible.getAttempts()));

            // 计算2天后的时间
            Date twoDaysLater = DateUtils.addDay(new Date(), 2);

            // 确保下次检查时间不超过2天后的时间
            if (nextInspectTime.after(twoDaysLater)) {
                nextInspectTime = twoDaysLater;
            }

            callRecordingUnaccessible.setNextInspectTime(nextInspectTime);
            return callRecordingUnaccessible; // 返回不可访问的录音
        }
        // URL 可访问，返回 null
        return null;
    }
}
