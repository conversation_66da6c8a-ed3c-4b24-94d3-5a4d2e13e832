package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MenuCodeEnum;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.entity.CcCallAccountModifyLog;
import com.niceloo.cmc.ex.feign.UserFeignClient;
import com.niceloo.cmc.ex.mapper.CcCallAccountConfigMapper;
import com.niceloo.cmc.ex.pojo.request.*;
import com.niceloo.cmc.ex.pojo.vo.BasePageVO;
import com.niceloo.cmc.ex.pojo.vo.CallAccountOperationListVO;
import com.niceloo.cmc.ex.pojo.vo.CallAccountVO;
import com.niceloo.cmc.ex.pojo.vo.EeCallAccountChannelVO;
import com.niceloo.cmc.ex.service.CcCallAccountConfigService;
import com.niceloo.cmc.ex.utils.EncryptDealUtil;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.segment.core.NicelooIdTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.utils.EncryptDealUtil.*;

/**
 * <p>
 * 外呼账号配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@Service
public class CcCallAccountConfigServiceImpl implements CcCallAccountConfigService {

    @Resource
    private CcCallAccountConfigMapper ccCallAccountConfigMapper;

    @Resource
    private NicelooIdTemplate nicelooIdTemplate;

    @Resource
    private UserFeignClient userFeignClient;

    /**
     * 根据外呼账号表id查询账号信息
     *
     * @param id 外呼账号表id
     * @return com.niceloo.cmc.ex.entity.CcCallAccountConfig
     * <AUTHOR>
     * @since 10:03 2022/4/28
     **/
    @Override
    public CcCallAccountConfig searchCallAccountInfoById(String id) {
        if (StringUtils.isEmpty(id)) {
            return new CcCallAccountConfig();
        }
        CcCallAccountConfig ccCallAccountConfig = ccCallAccountConfigMapper.searchCallAccountInfoById(id);

        // 对象中的加密字段进行解密处理
        if (ccCallAccountConfig != null) {
            decryptFields(ccCallAccountConfig);
        }

        return ccCallAccountConfig;
    }

    /**
     * 根据主叫账号和外呼通道查询账号信息(一个外呼通道里,账号和员工一对一)
     *
     * @param account     外呼账号
     * @param channelEnum 通道枚举
     * @return com.niceloo.cmc.ex.entity.CcCallAccountConfig
     * <AUTHOR>
     * @since 10:19 2022/4/28
     **/
    @Override
    public CcCallAccountConfig searchCallAccountInfoByAccount(String account, CallChannelEnum channelEnum) {
        if (StringUtils.isEmpty(account) || channelEnum == null) {
            return new CcCallAccountConfig();
        }
        CcCallAccountConfig ccCallAccountConfig = ccCallAccountConfigMapper.searchCallAccountInfoByAccount(account, channelEnum.getType());

        // 对象中的加密字段进行解密处理
        if (ccCallAccountConfig != null) {
            decryptFields(ccCallAccountConfig);
        }

        return ccCallAccountConfig;
    }

    /**
     * 分页查询
     *
     * @param request 查询条件
     * @return com.niceloo.cmc.ex.pojo.vo.BasePageVO<com.niceloo.cmc.ex.pojo.vo.CallAccountListVO>
     * <AUTHOR>
     * @since 10:27 2022/4/28
     **/
    @Override
    public BasePageVO<CallAccountVO> searchPage(CallAccountSelectRequest request) {
        if (request.getPageSize() == null || request.getPageSize() <= 0) {
            request.setPageSize(10);
        }
        if (request.getPageIndex() == null || request.getPageIndex() < 0) {
            request.setPageIndex(0);
        }
        if (StringUtils.isNotEmpty(request.getStartTime()) && StringUtils.isNotEmpty(request.getEndTime())) {
            request.setStartTime(request.getStartTime() + " 00:00:00");
            request.setEndTime(request.getEndTime() + "23:59:59");
        }
        List<CcCallAccountConfig> accountConfigList = ccCallAccountConfigMapper.searchPage(request);
        if (CollectionUtils.isEmpty(accountConfigList)) {
            return new BasePageVO<>(0, new ArrayList<>());
        }
        // 返回结果类型转换
        List<CallAccountVO> callAccountVOList = accountEntityConvertToVO(accountConfigList);
        Integer totalCount = ccCallAccountConfigMapper.selectTotalCount(request);
        return new BasePageVO<>(totalCount, callAccountVOList);
    }

    /**
     * 根据外呼账号id查询操作记录列表
     *
     * @param callAccountId 外呼账号id
     * @return java.util.List<com.niceloo.cmc.ex.entity.CcCallAccountModifyLog>
     * <AUTHOR>
     * @since 10:27 2022/4/28
     **/
    @Override
    public List<CallAccountOperationListVO> searchAccountOperationLogList(String callAccountId) {
        if (StringUtils.isEmpty(callAccountId)) {
            return new ArrayList<>();
        }
        // 查询操作记录列表
        List<CcCallAccountModifyLog> callAccountModifyLogList = ccCallAccountConfigMapper.searchAccountOperationLogList(callAccountId);
        if (CollectionUtils.isEmpty(callAccountModifyLogList)) {
            return new ArrayList<>();
        }
        List<CallAccountOperationListVO> operationListVOList = new ArrayList<>(callAccountModifyLogList.size());
        for (CcCallAccountModifyLog callAccountModifyLog : callAccountModifyLogList) {
            CallAccountOperationListVO operationListVO = new CallAccountOperationListVO();

            // 字段解密
            decryptFields(callAccountModifyLog);
            BeanUtils.copyProperties(callAccountModifyLog, operationListVO);
            operationListVO.setOperatorName(callAccountModifyLog.getAccountLogCreatorName());
            operationListVO.setOperationTime(callAccountModifyLog.getAccountLogCreateddate());
            operationListVOList.add(operationListVO);
        }
        return operationListVOList;
    }

    /**
     * 外呼账号是否存在(未绑定到员工也为存在,删除状态为'Y'时为不存在)
     *
     * @param account     外呼账号
     * @param channelEnum 通道枚举
     * @return boolean  true->存在,false->不存在
     * <AUTHOR>
     * @since 11:46 2022/4/28
     **/
    @Override
    public boolean hasCallAccount(String account, CallChannelEnum channelEnum) {
        if (StringUtils.isEmpty(account) || channelEnum == null) {
            throw new IllegalArgumentException("缺少参数");
        }
        Integer count = ccCallAccountConfigMapper.hasCallAccount(account, channelEnum.getType());
        return null != count && count > 0;
    }


    /**
     * 该员工在该通道下是否存在账号(未绑定到员工为不存在,删除状态为'Y'时为不存在)
     *
     * @param userId      员工id
     * @param channelEnum 通道枚举
     * @return boolean  true->存在,false->不存在
     * <AUTHOR>
     * @since 11:46 2022/4/28
     **/
    @Override
    public boolean hasCallAccountByUserId(String userId, CallChannelEnum channelEnum) {
        if (StringUtils.isEmpty(userId) || channelEnum == null) {
            throw new IllegalArgumentException("缺少参数");
        }
        Integer count = ccCallAccountConfigMapper.hasCallAccountByUserId(userId, channelEnum.getType());
        return null != count && count > 0;
    }

    /**
     * 添加外呼账号(包括生成记录到记录表)
     * <p>***没有做是否重复校验,请在调用该方法之前校验,可以使用 {@link #hasCallAccount}方法进行校验</p>
     *
     * @param request 添加请求参数
     * <AUTHOR>
     * @since 11:58 2022/4/28
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertCallAccount(CallAccountAddRequest request) {
        // 参数转换
        String accountId = nicelooIdTemplate.get("ACCOUNT");
        CcCallAccountConfig callAccountConfig = request.typeConverter();
        callAccountConfig.setCallAccountId(accountId);

        // 字段加密
        encryptFields(callAccountConfig);

        // 添加到外呼账号配置
        ccCallAccountConfigMapper.insert(callAccountConfig);
        CcCallAccountModifyLog ccCallAccountModifyLog = request.generateOperationLog(MenuCodeEnum.OperationTypeEnum.ADD.getType(), new CcCallAccountConfig());
        ccCallAccountModifyLog.setCallAccountLogId(nicelooIdTemplate.get("LOG"));
        ccCallAccountModifyLog.setCallAccountId(accountId);
        ccCallAccountModifyLog.setAccountLogCreateddate(callAccountConfig.getAccountCreateddate());

        // 字段加密
        encryptFields(ccCallAccountModifyLog);

        // 添加到操作记录
        ccCallAccountConfigMapper.insertOperationLog(ccCallAccountModifyLog);
    }

    /**
     * 修改操作外呼账号(解绑、换绑、绑定)[包括生成记录到记录表]
     *
     * @param request 操作请求参数
     * <AUTHOR>
     * @since 15:10 2022/4/28
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accountOperation(CallAccountOperationRequest request) {
        // 条件校验
        CcCallAccountConfig callAccountConfig = this.searchCallAccountInfoById(request.getCallAccountId());
        if (null == callAccountConfig) {
            throw new IllegalArgumentException("未找到该外呼账号的配置信息");
        }
        // 复制出旧外呼账号配置信息
        CcCallAccountConfig oldCallAccountConfig = new CcCallAccountConfig();
        BeanUtils.copyProperties(callAccountConfig, oldCallAccountConfig);
        // 账号操作前的一些处理(校验、赋值)
        this.validateBeforeAccountOperation(request, callAccountConfig);
        callAccountConfig.setAccountModifieddate(DateUtils.getNowDString());
        callAccountConfig.setAccountModifier(request.getAccountCreator());
        callAccountConfig.setAccountModifierName(request.getAccountCreatorName());

        // 字段加密（如果有）
        encryptFields(callAccountConfig);

        // 修改外呼账号配置
        ccCallAccountConfigMapper.accountOperation(callAccountConfig);

        // 添加到操作记录
        CcCallAccountModifyLog ccCallAccountModifyLog = request.generateOperationLog(request.getOperationType(), oldCallAccountConfig);
        ccCallAccountModifyLog.setCallAccountLogId(nicelooIdTemplate.get("LOG"));
        ccCallAccountModifyLog.setCallAccountId(request.getCallAccountId());
        ccCallAccountModifyLog.setAccountLogCreateddate(callAccountConfig.getAccountModifieddate());

        // 字段加密（如果有）
        encryptFields(ccCallAccountModifyLog);

        ccCallAccountConfigMapper.insertOperationLog(ccCallAccountModifyLog);
    }

    /**
     * 批量查询员工拥有的外呼通道
     *
     * @param eeUserIds 员工外呼id
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.EeCallAccountVO>
     * <AUTHOR>
     * @since 9:53 2022/4/29
     **/
    @Override
    public List<EeCallAccountChannelVO> batchQueryEeCallChannel(List<String> eeUserIds) {
        // 批量查询员工拥有的外呼账号
        List<CcCallAccountConfig> callAccountConfigList = ccCallAccountConfigMapper.batchQueryEeCallAccount(eeUserIds);
        if (null == callAccountConfigList) {
            callAccountConfigList = new ArrayList<>();
        }
        // 处理结果获取员工拥有的外呼通道
        Map<String, List<CcCallAccountConfig>> callAccountMap = callAccountConfigList.stream().collect(Collectors.groupingBy(CcCallAccountConfig::getEeUserId));
        List<EeCallAccountChannelVO> eeCallAccountVOList = new ArrayList<>(callAccountMap.size());
        for (Map.Entry<String, List<CcCallAccountConfig>> entry : callAccountMap.entrySet()) {
            EeCallAccountChannelVO eeCallAccountVO = new EeCallAccountChannelVO();
            eeCallAccountVO.setEeUserId(entry.getKey());
            List<String> channelType = entry.getValue().stream().map(CcCallAccountConfig::getChannelType).collect(Collectors.toList());
            eeCallAccountVO.setChannelType(channelType);
            eeCallAccountVOList.add(eeCallAccountVO);
        }
        // 补充没有外呼通道的值
        for (String eeUserId : eeUserIds) {
            if (!callAccountMap.containsKey(eeUserId)) {
                eeCallAccountVOList.add(new EeCallAccountChannelVO(eeUserId));
            }
        }
        return eeCallAccountVOList;
    }

    /**
     * 根据员工id和外呼通道查询账号信息(一个外呼通道里,账号和员工一对一)
     *
     * @param userId      员工id
     * @param channelType 外呼通道
     * @return com.niceloo.cmc.ex.entity.CcCallAccountConfig
     * <AUTHOR>
     * @since 16:21 2022/4/30
     **/
    @Override
    public CcCallAccountConfig searchCallAccountInfoByUserId(String userId, String channelType) {
        CallChannelEnum callChannel = CallChannelEnum.getCallChannel(channelType);
        if (null == callChannel || StringUtils.isEmpty(userId)) {
            return new CcCallAccountConfig();
        }
        CcCallAccountConfig callAccountConfig = ccCallAccountConfigMapper.searchCallAccountInfoByUserId(userId, channelType);
        return null == callAccountConfig ? new CcCallAccountConfig() : callAccountConfig;
    }

    /**
     * 根据id列表查询出绑定着员工的外呼账号配置列表
     *
     * @param ids id列表 [不能为空]
     * @return java.util.List<com.niceloo.cmc.ex.entity.CcCallAccountConfig>
     * <AUTHOR>
     * @since 14:28 2022/5/27
     **/
    @Override
    public List<CcCallAccountConfig> searchBandingAccountListByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return ccCallAccountConfigMapper.searchBandingAccountListByIds(ids);
    }

    /**
     * 批量解绑
     *
     * @param request           包含解绑人的信息
     * @param accountConfigList 需要解绑的账号列表
     * <AUTHOR>
     * @since 15:16 2022/5/27
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUnbind(CallAccountBatchUnbindRequest request, List<CcCallAccountConfig> accountConfigList) {
        if (null == request || CollectionUtils.isEmpty(accountConfigList)) {
            return;
        }
        // 批量解绑修改
        CcCallAccountConfig updatedCallAccountConfig = new CcCallAccountConfig();
        updatedCallAccountConfig.setAccountModifier(request.getAccountCreator());
        updatedCallAccountConfig.setAccountModifierName(request.getAccountCreatorName());
        updatedCallAccountConfig.setAccountModifieddate(DateUtils.getNowDString());
        List<String> callAccountIds = accountConfigList.stream().map(CcCallAccountConfig::getCallAccountId).collect(Collectors.toList());
        ccCallAccountConfigMapper.batchUnbind(updatedCallAccountConfig, callAccountIds);
        // 批量新增解绑的操作记录
        List<CcCallAccountModifyLog> callAccountModifyLogList = new ArrayList<>(accountConfigList.size());
        for (CcCallAccountConfig callAccountConfig : accountConfigList) {
            // 创建操作记录
            CcCallAccountModifyLog ccCallAccountModifyLog = request.generateOperationLog(MenuCodeEnum.OperationTypeEnum.BATCH_UNBIND.getType(), callAccountConfig);
            ccCallAccountModifyLog.setCallAccountLogId(nicelooIdTemplate.get("LOG"));
            ccCallAccountModifyLog.setCallAccountId(callAccountConfig.getCallAccountId());
            ccCallAccountModifyLog.setAccountLogCreateddate(updatedCallAccountConfig.getAccountModifieddate());
            callAccountModifyLogList.add(ccCallAccountModifyLog);
        }
        ccCallAccountConfigMapper.batchInsertOperationLog(callAccountModifyLogList);
    }

    /**
     * 获取当前账号创建者的组织架构相关id (schoolId,dptId,userId)
     *
     * @param id 主键id
     * @return com.niceloo.cmc.ex.pojo.request.AuthBaseRequest
     * <AUTHOR>
     * @since 14:07 2022/8/13
     **/
    @Override
    public AuthBaseRequest getOrgStructureOfCreator(String id) {
        return ccCallAccountConfigMapper.getOrgStructureOfCreator(id);
    }


    /**
     * 将外呼账号实体转换为前端需要的VO
     *
     * @param accountConfigList 数据库查询出来的外呼账号实体
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.CallAccountVO>
     * <AUTHOR>
     * @since 10:36 2022/8/17
     **/
    private List<CallAccountVO> accountEntityConvertToVO(List<CcCallAccountConfig> accountConfigList) {
        List<CallAccountVO> callAccountVOList = new ArrayList<>(accountConfigList.size());
        for (CcCallAccountConfig callAccountConfig : accountConfigList) {
            // 解析转换(解密、脱敏)
            CallAccountVO callAccountVO = CallAccountVO.typeConvertor(callAccountConfig);
            if ("N".equals(callAccountVO.getBindStatus())) {
                callAccountVO.setEeUserId("");
                callAccountVO.setEeUserName("");
                callAccountVO.setEeNo("");
            }
            callAccountVOList.add(callAccountVO);
        }
        return callAccountVOList;
    }

    /**
     * 账号操作前的一些处理(校验、赋值)
     *
     * @param request           web请求参数
     * @param callAccountConfig 账号信息
     * <AUTHOR>
     * @since 9:26 2022/4/30
     **/
    private void validateBeforeAccountOperation(CallAccountOperationRequest request, CcCallAccountConfig callAccountConfig) {
        MenuCodeEnum.OperationTypeEnum operationType = MenuCodeEnum.OperationTypeEnum.getEnum(request.getOperationType());
        if (null != operationType) {
            switch (operationType) {
                // 解绑
                case UNBIND:
                    assert "Y".equals(callAccountConfig.getBindStatus()) : "当前该账号未绑定员工";
                    callAccountConfig.setBindStatus("N");
                    break;

                // 换绑
                case EXCHANGE_BINDING:
                    assert "Y".equals(callAccountConfig.getBindStatus()) : "当前该账号未绑定员工";
                    assert StringUtils.isNotEmpty(request.getAfterEeUserId()) : "绑定账号为空";
                    String eeUserId = callAccountConfig.getEeUserId();
                    assert !eeUserId.equals(request.getAfterEeUserId()) : "当前该账号已绑定在此员工";
                    changeOrUnbindAssignment(request, callAccountConfig);
                    break;

                // 绑定
                case BINDING:
                    assert !"Y".equals(callAccountConfig.getBindStatus()) : "当前该账号已处于绑定状态";
                    changeOrUnbindAssignment(request, callAccountConfig);
                    break;

                // 启用
                case ENABLE:
                    assert "Y".equals(callAccountConfig.getDisableStatus()) : "当前该账号不处于禁用状态";
                    boolean flag = hasCallAccountByUserId(callAccountConfig.getEeUserId(), CallChannelEnum.getCallChannel(callAccountConfig.getChannelType()));
                    assert !flag : "一个员工只能有一个启用状态的外呼账号";
                    callAccountConfig.setDisableStatus("N");
                    break;

                // 禁用
                case DISABLE:
                    assert "N".equals(callAccountConfig.getDisableStatus()) : "当前该账号不处于启用状态";
                    callAccountConfig.setDisableStatus("Y");
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 换绑或解绑操作修改前的属性赋值
     *
     * @param request           web请求参数
     * @param callAccountConfig 账号信息
     * <AUTHOR>
     * @since 9:25 2022/4/30
     **/
    private void changeOrUnbindAssignment(CallAccountOperationRequest request, CcCallAccountConfig callAccountConfig) {
        // 校验该员工是否绑定过该通道的外呼账号
        boolean flag = this.hasCallAccountByUserId(request.getAfterEeUserId(), CallChannelEnum.getCallChannel(callAccountConfig.getChannelType()));
        if (flag) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "该员工在此外呼通道下已有账号，无法重复配置。");
        }
        callAccountConfig.setBindStatus("Y");
        callAccountConfig.setDptId(request.getDptId());
        callAccountConfig.setEeNo(request.getEeNo());
        callAccountConfig.setEeUserId(request.getAfterEeUserId());
        callAccountConfig.setEeUserName(request.getEeUserName());
        callAccountConfig.setSchoolId(request.getSchoolId());
        callAccountConfig.setSchoolName(request.getSchoolName());
    }
}
