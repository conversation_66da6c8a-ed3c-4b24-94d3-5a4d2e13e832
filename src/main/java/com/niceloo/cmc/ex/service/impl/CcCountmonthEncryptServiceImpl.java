package com.niceloo.cmc.ex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.entity.CcCountday;
import com.niceloo.cmc.ex.entity.CcCountmonth;
import com.niceloo.cmc.ex.mapper.CcAiCallJobMapper;
import com.niceloo.cmc.ex.mapper.CcCountdayMapper;
import com.niceloo.cmc.ex.mapper.CcCountmonthMapper;
import com.niceloo.cmc.ex.service.CcCountmonthEncryptService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.db.dynamic.core.DS;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 加密月统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21 18:01:24
 */
@Service
@DS("cmc")
public class CcCountmonthEncryptServiceImpl extends ServiceImpl<CcCountmonthMapper, CcCountmonth> implements CcCountmonthEncryptService {
    private static final Logger logger = LoggerFactory.getLogger(CcCountmonthEncryptServiceImpl.class);

    @Resource
    private CcCountmonthMapper ccCountmonthMapper;

    /**
     * 对表中字段进行加密
     * 对输入的MySQL中的tableName的encryptedFlag为空记录的countcallerName字段进行加密，
     * 并将加密后的结果存入到入参tableName表的countcallerName字段中。
     * 加密方法采用 {@link FieldCipherUtil#encrypt(String)} 。
     * 加密完成，将加密记录的encryptedFlag字段修改为1，每批次处理batch条记录，批次间sleep 2000ms
     * 采用异步方式处理，加密开始后即返回“已开始加密"的提示，无需等待加密完成
     * 另外，目前CcCountmonth有219384条记录，CcCountday表有3809167条记录，处理中打印处理日志，便于进度监控
     *
     * @param batch 批次号
     * @return 返回加密后的字符串，如果返回null则表示加密失败
     */
    @Override
    public void encryptTable(int batch) {
        logger.info("加密开始，表名：CcCountmonth, 开始时间：{}", DateUtils.getNowDString());

        processBatch(batch);

        logger.info("加密完成，表名：CcCountmonth, 完成时间：{}", DateUtils.getNowDString());
    }

    /**
     * 处理批次中的记录。
     *
     * @param batch     每批次处理数量
     */
    private void processBatch(int batch) {
        // 从tableName表中获取encryptedFlag为空的记录
        // 将加密后的结果更新到countcallerName字段中
        List<CcCountmonth> entitiesToUpdate = new ArrayList<>();
        String encryptedFlagValue = "1";

        LambdaQueryWrapper<CcCountmonth> wrapperCcCountmonth = new LambdaQueryWrapper<>();
        wrapperCcCountmonth.isNull(CcCountmonth::getEncryptedFlag).
                last("LIMIT 0," + batch);
        List<CcCountmonth> ccCountmonths = ccCountmonthMapper.selectList(wrapperCcCountmonth);

        // 更新表CcCountmonth的encryptedFlag字段为1
        for (CcCountmonth ccCountmonth : ccCountmonths) {
            String countcallerName = ccCountmonth.getCountcallerName();
            if (!FieldCipherUtil.checkEncrypt(countcallerName)) {
                String encryptedCountcallerName = FieldCipherUtil.encrypt(ccCountmonth.getCountcallerName());
                ccCountmonth.setCountcallerName(encryptedCountcallerName);
            }
            ccCountmonth.setEncryptedFlag(encryptedFlagValue);
            entitiesToUpdate.add(ccCountmonth);
        }

        // 批量更新数据库
        if (!entitiesToUpdate.isEmpty()) {
            this.updateBatchById(entitiesToUpdate);
        }
    }
}
