package com.niceloo.cmc.ex.service.call;

import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.entity.BdDpt;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.feign.ThirdPartyAdapterFeignClient;
import com.niceloo.cmc.ex.pojo.dto.BdEeDTO;
import com.niceloo.cmc.ex.pojo.dto.JLFYCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.request.GenerateCallRecordRequest;
import com.niceloo.cmc.ex.pojo.request.GenerateJLYQCallRecordRequest;
import com.niceloo.cmc.ex.service.BdDptService;
import com.niceloo.cmc.ex.service.BdEeService;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.CcCallAccountConfigService;
import com.niceloo.cmc.ex.service.impl.BdDptServiceImpl;
import com.niceloo.cmc.ex.service.impl.BdEeServiceImpl;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.service.impl.CcCallAccountConfigServiceImpl;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.HttpUtils;
import com.niceloo.cmc.ex.utils.OkHttpUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.mq.client.Client;
import okhttp3.Headers;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.niceloo.cmc.ex.common.BizConst.CALL_RECORD_ES_PREFIX;
import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_JLFY;

/**
 * 巨量飞鱼厂商
 *
 * <AUTHOR>
 * @Date 2022-05-28 14:26
 */
@Service
public class JLFYService extends CallRecordsBaseService {
    private static final Logger logger = LoggerFactory.getLogger(JLFYService.class);
    // 巨量飞鱼录音下载地址
    private final String jlfyRecordingDownloadUrl = SpringUtils.getProperty("jlfy.record_url");
    // 巨量飞鱼通话记录信息地址
    private final String jlfyRecordDataUrl = SpringUtils.getProperty("jlfy.record_list");

    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);
    private final StringRedisTemplate redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
    private final CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);
    private final CcCallAccountConfigService callAccountConfigService = SpringUtils.getBean(CcCallAccountConfigServiceImpl.class);
    private final BdEeService bdEeService = SpringUtils.getBean(BdEeServiceImpl.class);
    private final BdDptService bdDptService = SpringUtils.getBean(BdDptServiceImpl.class);
    private final ThirdPartyAdapterFeignClient thirdPartyAdapterFeignClient = SpringUtils.getBean(ThirdPartyAdapterFeignClient.class);
    private final Client client = SpringUtils.getBean(Client.class);

    /**
     * 主动查询还没有补充完整的通话记录信息
     *
     * @paramter map 请求参数
     * <AUTHOR>
     * @Date 15:21 2022/5/31
     **/
    public void activeQueryIncompleteRecord(Map<String, Object> mqRequest) {
        String adId = mqRequest.get("adId").toString();
        String index = mqRequest.get("index").toString();
        String contactId = mqRequest.get("contactId").toString();
        // 校验此条通话记录是否已经补充完整了
        CallRecord callRecord1 = callRecordService.querySupplementaryFieldByUniqueId(index, CALL_TYPE_JLFY.getType(), contactId);
        if (callRecord1 == null || callRecord1.getDuration() != -1) {
            return;
        }
        String accessToken = this.getAccessToken(adId);
        // 查询厂商获取通话记录信息
        JLFYCallRecordsDTO jlfyCallRecordsDTO = this.searchCallDataFromVendor(accessToken, mqRequest);
        if (null != jlfyCallRecordsDTO) {
            CallRecord callRecord = jlfyCallRecordsDTO.callRecordsConverter();
            String callId = callRecord1.getCallId();
            callRecord.setCreatedTime(null);
            // 补充外呼通话信息到ES
            Map<String, Object> recordingData = super.updateCallRecord(callRecord, callId);
            if (null != recordingData) {
                recordingData.put("adId", callRecord1.getField5());
                recordingData.put("clue_id", jlfyCallRecordsDTO.getClueId());
                recordingData.put("contact_id", jlfyCallRecordsDTO.getContactId());
                recordingData.put("times", 1);
                // 发送下载录音MQ
                client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordingData));
            }
        } else { // 没有查到重新投到MQ内,五次还不成功则打日志,忽略
            int times = Integer.parseInt(mqRequest.get("times").toString()) + 1;
            if (times >= 5) {
                logger.warn("巨量飞鱼主动查询还没有补充完整的通话记录信息超过五次没有成功,参数:{}", JSONUtils.toJSONString(mqRequest));
                return;
            }
            mqRequest.put("times", times);
            client.publish(MQConst.ACTIVE_QUERY_RECORD_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(10));
        }
    }

    /**
     * 接收智能外呼MQ外呼成功后返回的主叫被叫信息生成缺少通话信息的通话记录
     *
     * @param request 主被叫信息
     * <AUTHOR>
     * @Date 14:39 2022/5/28
     **/
    public void generateCallRecord(GenerateCallRecordRequest request) {
        // 生成通话记录保存到ElasticSearch 
        Map<String, Object> mqRequest = super.commonGenerateCallRecord(request);
        if (mqRequest != null) {
            // 推送到延时队列5分钟后去查询此通通话记录是否被补充完整，如果还没有补充完整，则调用查询接口去查询更新
            GenerateJLYQCallRecordRequest recordRequest = (GenerateJLYQCallRecordRequest) request;
            mqRequest.put("adId", recordRequest.getAdId());
            mqRequest.put("clueId", recordRequest.getClueId());
            client.publish(MQConst.ACTIVE_QUERY_RECORD_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(5));
        }
    }


    /**
     * MQ回调->补充巨量飞鱼外呼信息到ES
     *
     * @paramter jlfyCallRecordsDTO 回调数据
     * <AUTHOR>
     * @Date 11:09 2022/5/30
     **/
    public void supplementaryCallRecordInfoToES(JLFYCallRecordsDTO jlfyCallRecordsDTO) {
        Long startTime = jlfyCallRecordsDTO.getStartTime();
        String indexName = CALL_RECORD_ES_PREFIX + DateUtils.toStr(new Date(startTime), "yyyyMM");
        // 查询ES验证是否已经生成此条通话记录(学员回拨呼入没有通话记录，需要去创建)
        CallRecord record = callRecordService.querySupplementaryFieldByUniqueId(indexName, CALL_TYPE_JLFY.getType(), jlfyCallRecordsDTO.getContactId());
        if (null == record) {
            this.generateCallInCallRecord(jlfyCallRecordsDTO);
            return;
        }
        // 表示已经补充过通话信息了
        if (record.getDuration() != -1) {
            return;
        }
        CallRecord callRecord = jlfyCallRecordsDTO.callRecordsConverter();
        String callId = record.getCallId();
        callRecord.setCreatedTime(null);
        // 补充外呼通话信息到ES
        Map<String, Object> recordingData = super.updateCallRecord(callRecord, callId);
        if (null != recordingData) {
            recordingData.put("adId", record.getField5());
            recordingData.put("clue_id", jlfyCallRecordsDTO.getClueId());
            recordingData.put("contact_id", jlfyCallRecordsDTO.getContactId());
            recordingData.put("times", 1);
            // 发送下载录音MQ
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordingData));
        }
    }


    /**
     * 下载通话录音并更新到ES
     *
     * @paramter map 参数数据
     * <AUTHOR>
     * @Date 11:51 2022/5/30
     **/
    public boolean downLoadRecording(Map<String, Object> map) {
        String times = map.get("times").toString();
        String callId = map.get("callId").toString();
        if (Integer.parseInt(times) > 3) {
            logger.error("巨量飞鱼下载通话录音次数达到三次未下载成功,callId:{}", callId);
            return false;
        }
        //调用第三方适配器接口获取AccessToken
        String accessToken = this.getAccessToken(map.get("adId").toString());
        // 调用巨量飞鱼厂商接口获取录音地址
        String contact_url = this.searchContactUrlFromVendor(accessToken, map);
        if (StringUtils.isNotEmpty(contact_url)) {
            CallRecord callRecord = new CallRecord();
            callRecord.setCallId(callId);
            callRecord.setVoiceSourceUrl(contact_url);
            callRecord.setModifiedTime(new Date());
            // 更新通话记录
            callRecordService.bulkUpdate(map.get("indexName").toString(), new ArrayList<>(List.of(callRecord)));
            try {
                // 下载录音到OSS
                Map<String, Object> voiceToOSS = HttpUtils.uploadVoiceToOSS(contact_url, callId + ".mp3");
                if (!MapUtils.isEmpty(voiceToOSS)) {
                    callRecord.setDataCompleteStatus("Y");
                    callRecord.setServerFolder((String) voiceToOSS.get("filePath"));
                    callRecord.setField1((String) voiceToOSS.get("fileKey"));
                    callRecord.setVoiceSyncStatus(2);
                }
            } catch (Exception e) {
                logger.warn(e, "巨量飞鱼URL网络地址录音文件上传失败,callId:{}", callId);
                map.put("times", Integer.parseInt(times) + 1);
                client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(map));
                return false;
            }
            // 更新通话记录
            callRecordService.bulkUpdate(map.get("indexName").toString(), new ArrayList<>(List.of(callRecord)));
        }
        return true;
    }

    /**
     * 创建呼入的通话记录
     *
     * @param jlfyCallRecordsDTO 回调信息
     * <AUTHOR>
     * @Date 17:41 2022/6/10
     **/
    private void generateCallInCallRecord(JLFYCallRecordsDTO jlfyCallRecordsDTO) {
        String contactId = jlfyCallRecordsDTO.getContactId();
        // 校验此条通话记录是否已经生成
        boolean existRecord = super.isNotExistRecord(contactId, CALL_TYPE_JLFY);
        if (!existRecord) {
            return;
        }
        CallRecord callRecord = new CallRecord();
        Date callTime = new Date(jlfyCallRecordsDTO.getStartTime());
        String index = CALL_RECORD_ES_PREFIX + DateUtils.toStr(callTime, "yyyyMM");
        try {
            callRecord = jlfyCallRecordsDTO.callRecordsConverter();
            this.callInAddEe(jlfyCallRecordsDTO.getCalleeNumber(), callRecord);
            super.addCustomerInfoByPhone(callRecord);
            CallRecord.defaultValue(callRecord);
            callRecord.setChannelType(CALL_TYPE_JLFY.getType());
            callRecord.setField5(jlfyCallRecordsDTO.getAdId());
            callRecord.setField6(jlfyCallRecordsDTO.getClueId());
            // 校验是否有此索引，保存到ES内
            super.generateCallRecordToES(index, callRecord);
        } catch (Exception e) {
            logger.error(e, "生成客户呼入巨量飞鱼通话记录失败,异常信息:{},呼入回调参数:{}", e.getMessage(), JSONUtils.toJSONString(jlfyCallRecordsDTO));
            // 失败后删除去重ID
            String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_JLFY.getType() + ":" + contactId;
            redisTemplate.delete(redisKeyPrefix);
            return;
        }
        // 创建成功后发送MQ下载录音
        if (callRecord.getDuration() > 0) {
            Map<String, Object> data = new HashMap<>(5);
            data.put("channelType", CALL_TYPE_JLFY.getType());
            data.put("adId", jlfyCallRecordsDTO.getAdId());
            data.put("clue_id", jlfyCallRecordsDTO.getClueId());
            data.put("contact_id", jlfyCallRecordsDTO.getContactId());
            data.put("indexName", index);
            data.put("times", 1);
            data.put("callId", callRecord.getCallId());
            client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data));
        }
    }

    /**
     * 调用第三方适配器接口获取accessToken
     *
     * @param adId 广告主id
     * @return java.lang.String
     * <AUTHOR>
     * @Date 10:49 2022/5/5
     **/
    private String getAccessToken(String adId) {
        try {
            Map<String, String> adIdParam = new HashMap<>();
            adIdParam.put("adId", adId);
            Map<String, Object> response = thirdPartyAdapterFeignClient.getAccessToken(JSONUtils.toJSONString(adIdParam));
            if (response != null && response.containsKey("accessToken")) {
                return response.get("accessToken").toString();
            }
        } catch (Exception e) {
            logger.error(e, "调用第三方适配器接口获取accessToken出现异常,异常信息:{},广告主id:{}", e.getMessage(), adId);
        }
        throw new RuntimeException("调用第三方适配器接口获取accessToken出现异常");
    }

    /**
     * 调用巨量飞鱼厂商接口获取录音地址
     *
     * @return java.lang.String
     * @paramter accessToken 访问令牌
     * @paramter map 参数数据
     * <AUTHOR>
     * @Date 14:33 2022/5/30
     **/
    private String searchContactUrlFromVendor(String accessToken, Map<String, Object> map) {
        // 参数
        Map<String, String> filter = new HashMap<>(1);
        filter.put("contact_id", map.get("contact_id").toString());
        Map<String, String> request = new HashMap<>(3);
        request.put("advertiser_id", map.get("adId").toString());
        request.put("clue_id", map.get("clue_id").toString());
        request.put("filter", JSONUtils.toJSONString(filter));
        // 请求头
        Map<String, String> heads = new HashMap<>(1);
        heads.put("Access-Token", accessToken);
        //调用飞鱼接口获取录音地址
        logger.info("调用巨量飞鱼厂商接口获取录音地址请求参数:{}", JSONUtils.toJSONString(request));
        String response = okHttpUtil.get(jlfyRecordingDownloadUrl, request, Headers.of(heads));
        logger.info("调用巨量飞鱼厂商接口获取录音地址返回结果:{}", response);
        // 解析返回值
        Map<String, Object> responseMap = JSONUtils.toMap(response);
        String contact_url = "";
        if (null != responseMap && "0".equals(responseMap.get("code").toString())) {
            Object data = responseMap.get("data");
            Map<String, Object> dataMap = JSONUtils.toMap(JSONUtils.toJSONString(data));
            if (dataMap.containsKey("list")) {
                String listStr = JSONUtils.toJSONString(dataMap.get("list"));
                if (StringUtils.isNotEmpty(listStr)) {
                    List<Map> list = JSONUtils.toList(listStr, Map.class);
                    if (!CollectionUtils.isEmpty(list)) {
                        contact_url = list.get(0).get("contact_url").toString();
                    }
                }
            }
        }
        return contact_url;
    }

    /**
     * 调用巨量飞鱼厂商接口获取通话信息
     *
     * @param accessToken 访问令牌
     * @param map         请求参数
     * @return java.lang.String
     * @paramter accessToken 访问令牌
     * @paramter map 参数数据
     * <AUTHOR>
     * @Date 14:33 2022/5/30
     */
    private JLFYCallRecordsDTO searchCallDataFromVendor(String accessToken, Map<String, Object> map) {
        String adId = map.get("adId").toString();
        String contactId = map.get("contactId").toString();
        String clueId = map.get("clueId").toString();
        // 参数
        Map<String, String> filter = new HashMap<>(1);
        filter.put("contact_id", contactId);
        Map<String, String> request = new HashMap<>(3);
        request.put("advertiser_id", adId);
        request.put("clue_id", clueId);
        request.put("filter", JSONUtils.toJSONString(filter));
        // 请求头
        Map<String, String> heads = new HashMap<>(1);
        heads.put("Access-Token", accessToken);
        //调用飞鱼接口获取录音地址
        logger.info("调用巨量飞鱼厂商接口获取通话信息请求参数:{}", JSONUtils.toJSONString(request));
        String response = okHttpUtil.get(jlfyRecordDataUrl, request, Headers.of(heads));
        logger.info("调用巨量飞鱼厂商接口获取通话信息返回结果:{}", response);
        // 解析返回值
        Map<String, Object> responseMap = JSONUtils.toMap(response);
        if (null != responseMap && "0".equals(responseMap.get("code").toString())) {
            Object data = responseMap.get("data");
            Map<String, Object> dataMap = JSONUtils.toMap(JSONUtils.toJSONString(data));
            if (dataMap.containsKey("list")) {
                String listStr = JSONUtils.toJSONString(dataMap.get("list"));
                if (StringUtils.isNotEmpty(listStr)) {
                    List<Map> list = JSONUtils.toList(listStr, Map.class);
                    if (!CollectionUtils.isEmpty(list)) {
                        Map<String, Object> value = list.get(0);
                        return new JLFYCallRecordsDTO(value);
                    }
                }
            }
        }
        return null;
    }


    /**
     * 学员呼入通话记录添加员工信息
     *
     * @param account    员工外呼账号
     * @param callRecord 已经含有呼叫信息的通话记录实体
     * <AUTHOR>
     * @Date 9:41 2022/6/11
     **/
    private void callInAddEe(String account, CallRecord callRecord) {
        // 员工基本信息
        CcCallAccountConfig ccCallAccountConfig = callAccountConfigService.searchCallAccountInfoByAccount(account, CALL_TYPE_JLFY);
        if (ccCallAccountConfig == null) {
            callRecord.setCallAccount("");
            callRecord.setCallerUserId("");
            callRecord.setCallerName("");
            callRecord.setCallerPhone("");
            callRecord.setSchoolId("");
            callRecord.setSchoolName("");
            callRecord.setDptId("");
            callRecord.setDptName("");
            return;
        }
        callRecord.setCallAccount(ccCallAccountConfig.getAccount());
        callRecord.setCallerUserId(ccCallAccountConfig.getEeUserId());
        BdEeDTO bdEeDTO = bdEeService.findBdEeDTOAndSchoolNameByUserId(ccCallAccountConfig.getEeUserId());
        callRecord.setCallerName(bdEeDTO.getUserName());
        callRecord.setCallerPhone(bdEeDTO.getEeInnerphone());
        callRecord.setSchoolId(bdEeDTO.getSchoolId());
        callRecord.setSchoolName(bdEeDTO.getSchoolName());
        callRecord.setDptId("");
        callRecord.setDptName("");
        BdDpt bdDpt = bdDptService.findDptIdAndNameByEeId(bdEeDTO.getEeId());
        if (null != bdDpt) {
            callRecord.setDptId(bdDpt.getDptId());
            callRecord.setDptName(bdDpt.getDptName());
        }
    }
}
