package com.niceloo.cmc.ex.service.call;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.OICallRecordDto;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.utils.EasyMap;
import com.niceloo.cmc.ex.utils.HttpUtils;
import com.niceloo.cmc.ex.utils.RedisUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.encrypt.MD5Utils;
import com.niceloo.plugin.sdk.lang.collection.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_OI;

/**
 * @desc: 一号互联通话记录
 * @author: song
 * @date: 2022/2/23
 */
public class OIService extends CallRecordsBaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OIService.class);

    private final CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);

    @Override
    protected void downFile(Date from, Date to) {
        // 默认页码从 1 开始
        String start = DateUtils.toStr(from);
        String end = DateUtils.toStr(to);
        //查询数据
        int maxId = 0;
        do {
            List<EasyMap> dataList = getCallRecord(start, end, maxId);
            //重试次数：null表示请求失败，需要重试，数组为空则表示请求没有数据
            if (null == dataList) {
                dataList = retryGetCallRecord();
            }
            if (!CollectionUtil.isEmpty(dataList)) {
                maxId = Integer.parseInt(dataList.get(dataList.size() - 1).get("id").toString());
                //处理数据
                saveCallRecord(dataList);
            } else {
                maxId = 0;
            }
        } while (maxId > 0);
    }

    /**
     * 循环重试拉去通话记录
     *
     * @return
     */
    private List<EasyMap> retryGetCallRecord() {
        while (!retryMap.isEmpty()) {
            for (String reqParams : retryMap.keySet()) {
                String[] keys = reqParams.split("_");
                String startTime = keys[0];
                String endTime = keys[1];
                String num = keys[2];

                Integer failCount = retryMap.get(reqParams);
                if (failCount > 10) {
                    retryMap.remove(reqParams);
                    continue;
                }
                int maxId = Integer.parseInt(num);
                List<EasyMap> records = getCallRecord(startTime, endTime, maxId);
                if (records != null) {
                    return records;
                }
            }
        }
        return new ArrayList<>();
    }

    protected void saveCallRecord(List<EasyMap> list) {
        List<CallRecord> recordList = new ArrayList<>();
        for (EasyMap map : list) {
            CallRecord record = OICallRecordDto.callRecordCovert(map);
            //过滤掉不需要的通话录音
            if (checkTaskCode(record.getField5())) {
                continue;
            }
            if (!checkRecord(record.getVoiceSourceId())) {
                continue;
            }
            setEE(DateUtils.getNowDString(), CallChannelEnum.CALL_TYPE_OI.getType(), record);
            //上传录音
            uploadVoiceToOSS(record);
            recordList.add(record);
        }
        if (!CollectionUtils.isEmpty(recordList)) {
            batchSaveRecords(this.ES_INDEX, recordList);
        }
    }

    /**
     * @return boolean
     * @desc 判断呼入和个人手机呼出通话记录
     * <AUTHOR>
     * @date 2022/3/28
     */
    private boolean checkTaskCode(String task) {
        String callIn = CallProperties.OIProperty.callIn;
        String callOut = CallProperties.OIProperty.callOutNot;
        List<String> callInList = Arrays.asList(callIn.split(","));
        List<String> callOutList = Arrays.asList(callOut.split(","));
        if (callInList.contains(task)) {
            return true;
        }
        return callOutList.contains(task);
    }

    /**
     * 批量添加通话记录
     *
     * @paramter index
     * @paramter voiceVoList
     * <AUTHOR>
     * @Date 10:54 2022/2/23
     **/
    private void batchSaveRecords(String index, List<CallRecord> callRecordList) {
        try {
            super.batchAddCustomerInfoByPhone(callRecordList);
            callRecordService.bulkSave(index, callRecordList);
            LOGGER.debug("同步一号互联的数据条数: [{}] 条", callRecordList.size());
        } catch (Exception e) {
            // 失败后删除去重ID
            LOGGER.error(e, "一号互联-批量添加通话记录信息失败,索引:{},异常信息:{}", index, e.getMessage());
            String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_OI.getType() + ":";
            Set<String> keys = callRecordList.stream().map(record -> redisKeyPrefix + record.getVoiceSourceId()).collect(Collectors.toSet());
            RedisUtil.redisTemplate.delete(keys);
        }
    }

    /**
     * @desc: 录音问题处理
     * @author: song
     * @date: 2022/2/24
     */
    protected boolean checkRecord(String uniqueId) {
        String key = RedisConst.DUPLICATE_RECORD_CHECK + CallChannelEnum.CALL_TYPE_OI.getType() + ":" + uniqueId;
        return RedisUtil.redisTemplate.opsForValue().setIfAbsent(key, "1", Duration.ofDays(92));
    }

    /**
     * @desc: 获取通话记录
     * @author: song
     * @date: 2022/2/24
     */
    private List<EasyMap> getCallRecord(String start, String end, int maxId) {
        String key = start + "_" + end + "_" + maxId;
        this.checkRequestTime(key, 5000);
        EasyMap resMap = reqGetCallRecord(start, end, maxId);
        String resCode = resMap.getStr("code");
        if ("40001".equals(resCode) || "40002".equals(resCode) || "40003".equals(resCode)) {
            RedisUtil.redisTemplate.opsForHash().delete(RedisConst.OI_TOKEN_CHECK, CallProperties.OIProperty.account);
            putRetryMap(key);
            return null;
        }
        if ("200".equals(resCode)) {
            retryMap.remove(key);
            EasyMap recordMap = JSONUtils.toObject(JSONUtils.toJSONString(resMap.get("data")), EasyMap.class);
            return JSONUtils.toList(JSONUtils.toJSONString(recordMap.get("list")), EasyMap.class);
        }
        return new ArrayList<>();
    }

    /**
     * @desc: 请求获取通话记录
     * @author: song
     * @date: 2022/2/23
     */
    private EasyMap reqGetCallRecord(String start, String end, int maxId) {
        EasyMap tMap = getToken();
        if (StringUtils.isNotBlank(tMap.getStr("code"))) {
            return EasyMap.getEasyMap();
        }
        EasyMap easyMap = EasyMap.getEasyMap();
        easyMap.set("appId", tMap.getStr("appId"))
                .set("appToken", tMap.getStr("appToken"))
                .set("field", new String[]{"content", "task_tag", "callphone", "begin_time", "stop_time", "address", "username"})
                .set("starttime", DateUtils.toDate(start).getTime() / 1000)
                .set("endtime", DateUtils.toDate(end).getTime() / 1000)
                .set("maxId", maxId)
                .set("check_time", System.currentTimeMillis() / 1000);
        String param = JSONUtils.toJSONString(easyMap);
        String result = HttpUtils.sendPost(CallProperties.OIProperty.recordUrl + getSign(param), param);
        return JSONUtils.toObject(result, EasyMap.class);
    }

    /**
     * @desc: 从redis获取认证信息
     * @author: song
     * @date: 2022/2/23
     */
    private EasyMap getToken() {
        Object cache = RedisUtil.redisTemplate.opsForHash().get(RedisConst.OI_TOKEN_CHECK, CallProperties.OIProperty.account);
        if (!ObjectUtils.isEmpty(cache)) {
            try {
                return JSONUtils.toObject(cache.toString(), EasyMap.class);
            } catch (Exception ex) {
                RedisUtil.redisTemplate.opsForHash().delete(RedisConst.OI_TOKEN_CHECK, CallProperties.OIProperty.account);
            }
        }
        try {
            int num = (int) (Math.random() * 2 + 1);
            TimeUnit.SECONDS.sleep(num * 60);
        } catch (InterruptedException e) {
            LOGGER.error(e, e.toString());
        }
        EasyMap resMap = getAuth();
        String resCode = resMap.getStr("code");
        if (!"200".equals(resCode)) {
            LOGGER.debug("一号互联认证失败，code: [{}], msg: [{}]", resCode, resMap.getStr("msg"));
            return resMap;
        }
        //全局缓存
        String cacheData = JSONUtils.toJSONString(resMap.get("data"));
        RedisUtil.redisTemplate.opsForHash().put(RedisConst.OI_TOKEN_CHECK, CallProperties.OIProperty.account, cacheData);
        return JSONUtils.toObject(cacheData, EasyMap.class);
    }

    /**
     * @desc: 认证
     * @author: song
     * @date: 2022/2/23
     */
    private EasyMap getAuth() {
        EasyMap easyMap = EasyMap.getEasyMap();
        easyMap.set("mobilePhone", CallProperties.OIProperty.account)
                .set("password", CallProperties.OIProperty.password)
                .set("from", CallProperties.OIProperty.type)
                .set("check_time", System.currentTimeMillis() / 1000);

        String param = JSONUtils.toJSONString(easyMap);
        String result = HttpUtils.sendPost(CallProperties.OIProperty.authUrl + getSign(param), param);
        return JSONUtils.toObject(result, EasyMap.class);
    }

    /**
     * @desc: 生成sign
     * @author: song
     * @date: 2022/2/23
     */
    private String getSign(String str) {
        String md5String = MD5Utils.md5(str);
        return md5String.substring(8, 18).toUpperCase();
    }

    @Override
    public void checkRequestTime(String key, int time) {
        synchronized (OIService.class) {
            super.checkRequestTime(key, time);
        }
    }
}
