package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo;
import com.niceloo.cmc.ex.pojo.param.ManualSyncAiParam;
import com.niceloo.cmc.ex.pojo.param.SyncAiVoiceParam;
import com.niceloo.cmc.ex.pojo.request.AiJobInfoDetailRequest;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.pojo.vo.*;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.plugin.sdk.lang.Pair;
import org.springframework.data.elasticsearch.core.query.SearchQuery;

import javax.validation.Valid;
import java.util.List;

/**
 * AI外呼任务下客户信息service
 *
 * <AUTHOR>
 * @Date 2022-08-22 18:01
 */
public interface AICallJobCustomerInfoService {


    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @return boolean 创建前是否已经存在该索引
     */
    boolean createIndex(String indexName);

    /**
     * 批量添加客户的信息
     *
     * @param indexName               索引名称
     * @param callJobCustomerInfoList 客户信息列表
     */
    void bulkIndex(String indexName, List<AICallJobCustomerInfo> callJobCustomerInfoList);

    /**
     * 实体转换器，将客户列表请求参数转换为实体类
     *
     * @param subAiCallJob     子任务信息
     * @param customerRequests 客户信息列表
     * @param lotNo            客户添加的批次号
     * @return 转换后的实体类列表
     */
    List<AICallJobCustomerInfo> entityConverter(CcAiCallJob subAiCallJob, List<JDCallCustomerRequest> customerRequests, String lotNo);

    /**
     * 批量修改AI外呼任务下的客户信息
     *
     * @param indexName        索引名称
     * @param customerInfoList 客户信息列表
     */
    void bulkUpdate(String indexName, List<AICallJobCustomerInfo> customerInfoList);

    /**
     * 根据索引名称和任务主键id查询未同步的客户
     *
     * @param customerIndex 索引名称
     * @param aiJobId       任务主键id
     * @return 客户列表
     */
    List<JDCallCustomerRequest> selectJobCustomerList(String customerIndex, String aiJobId);

    /**
     * 根据查询条件滚动查询
     *
     * @param searchQuery 查询条件
     * @return 滚动id和数据列表
     */
    Pair<String, List<JDCallCustomerRequest>> startScroll(SearchQuery searchQuery);

    /**
     * 根据滚动id继续滚动查询
     *
     * @param scrollId 滚动id
     * @return 滚动id和数据列表
     */
    Pair<String, List<JDCallCustomerRequest>> continueScroll(String indexName, String scrollId);

    /**
     * 根据索引名称、批次号、任务id查询未上传的客户列表
     *
     * @param indexName 索引名称
     * @param lotNo     批次号
     * @param jobId     任务id
     */
    Pair<String, List<JDCallCustomerRequest>> startScrollByLotNoAndJob(String indexName, String lotNo, String jobId);


    /**
     * 根据索引名称和任务主键id查询未同步的一个客户
     *
     * @param customerIndex 索引名称
     * @param aiJobId       任务主键id
     * @return 客户信息
     */
    List<JDCallCustomerRequest> selectJobCustomerOne(String customerIndex, String aiJobId);

    /**
     * 获取录音的地址
     *
     * @param indexName 索引名称
     * @param sourceUrl 源录音地址
     * @return 通讯中心的录音地址
     */
    String getRecordingUrl(String indexName, String sourceUrl);

    /**
     * 根据外呼任务id获取外呼数据统计总览
     *
     * @param aiJobId   任务id
     * @param indexName 索引名称
     * @return 统计数据
     */
    AiJobCallNumStateVO callNumStatAggs(String aiJobId, String indexName);

    /**
     * AI外呼子任务数据聚合统计
     *
     * @param aiJobId   任务id
     * @param indexName 索引名称
     * @return 聚合统计的数据
     */
    AiJobAggsStateVO aiJobAggsState(String aiJobId, String indexName);

    /**
     * AI外呼子任务客户明细分页查询
     *
     * @param request   列表查询参数
     * @param indexName 索引名称
     * @return 任务客户明细列表
     */
    BasePageVO<AiJobInfoDetailVO> selectAiJobInfoDetail(AiJobInfoDetailRequest request, String indexName);

    /**
     * AI外呼子任务客户详情查询(并更新修改时间)
     *
     * @param id        主键id
     * @param indexName 索引名称
     * @return 任务客户明细
     */
    AiJobInfoDetailVO selectAiJobCustomerInfo(String id, String indexName);

    /**
     * AI外呼任务客户列表Excel下载
     *
     * @param aiJobId   任务id
     * @param indexName 索引名称
     * @param jobName   任务名称
     * @param user      登录人
     * @return excelId
     */
    String aiJobInfoDetailExcel(String aiJobId, String indexName, String jobName, UcUser user);

    /**
     * 下载并上传录音文件
     *
     * @param param 包含同步语音参数的对象，用于指定录音文件的下载和上传条件
     */
    void downloadAndUploadRecordings(SyncAiVoiceParam param);

    /**
     * 根据业务ID集合查询业务记录列表
     *
     * @param indexes      索引标识，用于指定查询的索引或条件（具体含义取决于实现）
     * @param bizIdSet     业务ID集合，包含需要查询的业务ID
     * @param selectFieldList 需要查询的字段列表，指定返回结果中包含哪些字段
     * @return 包含指定字段的业务记录列表，每个元素都是一个BizRecordVO对象
     */
    List<BizRecordVO> queryRecordsByBizIds(String indexes, List<String> bizIdSet, List<String> selectFieldList);

    /**
     * 拉取通话记录
     *
     * @param param 拉取通话记录的参数，该参数需通过@Valid注解验证其有效性
     */
    void pullCallRecord(@Valid ManualSyncAiParam param);
}
