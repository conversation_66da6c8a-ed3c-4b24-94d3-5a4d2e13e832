package com.niceloo.cmc.ex.service.call;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.entity.es.CallRecording;
import com.niceloo.cmc.ex.pojo.dto.YKCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.request.GenerateCallRecordRequest;
import com.niceloo.cmc.ex.pojo.request.PullYKRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.YKCallRecordsPageVO;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.CallRecordingService;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.service.impl.CallRecordingServiceImpl;
import com.niceloo.cmc.ex.utils.*;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.utils.encrypt.MD5Utils;
import com.niceloo.mq.client.Client;
import lombok.NoArgsConstructor;
import okhttp3.Headers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.CallChannelEnum.CALL_TYPE_YPHONE;

/**
 * 云客厂商服务
 * <p>云客厂商通话记录的生成和更新已经下载录音的流程:</p>
 * <ul>
 *     <li>1、通过智能运营平台调用智能外呼服务的接口外呼，外呼成功后通过发送MQ(topic: {@link MQConst#GENERATE_CALL_RECORD_TOPIC})到通讯中心来生成通话记录</li>
 *     <li>2、通讯中心调用方法({@link YKService#generateCallRecord})创建通话记录保存到ES内,但是此时通话记录没有通话时长等信息,需要通过第三方厂商通过回调推送进行补充更新</li>
 *     <li>3、创建通话记录成功后发送延时消息主动调用接口查询通话记录来补全通话记录,防止因为第三方厂商的问题没有推送而导致缺少通话时长等信息({@link YKService#activeQueryIncompleteRecord})</li>
 *     <li>4.接收第三方的回调推送来补充通话记录，补充完成后以发送MQ的方式来去下载通话录音({@link YKService#supplementaryCallRecordInfoToES}),需要注意的是第三方厂商推送的通话记录有不是通过智能运营平台拨打的通话，
 *          这种通话记录需要新创建通话记录并添加客户、员工信息</li>
 *     <li>5、说明：还有通过接口来拉取某个时间段的通话记录，参见方法:({@link YKService#downFile})</li>
 * </ul>
 *
 * @author: WangChenyu
 * @create: 2022-02-18 11:51
 */
@Service
@NoArgsConstructor
public class YKService extends CallRecordsBaseService {
    private static final Logger logger = LoggerFactory.getLogger(YKService.class);
    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);
    private final StringRedisTemplate redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
    private final CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);
    private final CallRecordingService callRecordingService = SpringUtils.getBean(CallRecordingServiceImpl.class);
    private final Client client = SpringUtils.getBean(Client.class);


    public YKService(String actionType) {
        super.ACTION_TYPE = actionType;
    }

    /**
     * 拉取通话记录数据(入口)
     *
     * @param from 开始时间
     * @param to   结束时间
     * <AUTHOR>
     * @Date 11:09 2022/2/26
     **/
    @Override
    protected void downFile(Date from, Date to) {
        if (super.ACTION_TYPE.equals(BizConst.UPDATE)) {
            logger.error("云客厂商不支持通过定时任务补充通话记录");
            return;
        }
        //厂商查询通话记录
        String startTime = DateUtils.toStr(from);
        String endTime = DateUtils.toStr(to);
        int page = 1; //当前页数
        int pageCount; //总页数
        PullYKRecordRequest request = new PullYKRecordRequest(page, BizConst.PAGE_SIZE, startTime, endTime);
        //循环分页查询
        do {
            request.setPage(page);
            String response = this.selectRecordsFromVendor(request);
            // 结果转换器
            YKCallRecordsPageVO ykCallRecordsPageVO = this.recordsResultConverter(response);
            //校验调用接口是否失败,失败进行重试[重试十次,再失败放入异常数组内]
            if (null == ykCallRecordsPageVO) {
                ykCallRecordsPageVO = this.retrySelectRecordsFromVendor(request);
                if (null == ykCallRecordsPageVO) {
                    return;
                }
            }
            pageCount = ykCallRecordsPageVO.getPageCount();
            this.saveRecordsToEs(ykCallRecordsPageVO.getYkCallRecordsDTOS());
        } while (pageCount > page++);
    }

    /**
     * 回调接口调用:
     * 批量补充通话记录
     *
     * @paramter ykCallRecordsDTOS
     * <AUTHOR>
     * @Date 9:49 2022/2/23
     **/
    public void supplementaryCallRecordInfoToES(List<YKCallRecordsDTO> ykCallRecordsDTOS) {
        List<YKCallRecordsDTO> filteredList = getYkCallRecordsByRemoveFyAxb(ykCallRecordsDTOS);
        // 输出剔除飞鱼AXB中间号码后的记录
        logger.info("剔除飞鱼AXB中间号码后的记录：" + filteredList);

        /*filteredList = getYkCallRecordsByRemoveZk(filteredList);
        // 输出剔除中科亿讯中间号码后的记录
        logger.info("剔除中科亿讯后的记录：" + filteredList);*/

        //根据月份进行分组,添加到正确的索引内
        Map<String, List<YKCallRecordsDTO>> ykCallRecordsDTOMap = filteredList.stream().collect(Collectors.groupingBy(dto -> dto.getCreateTime().substring(0, 7).replace("-", "")));
        // 循环月份(最多只有两个月,即最多循环两次)
        for (Map.Entry<String, List<YKCallRecordsDTO>> ykCallRecords : ykCallRecordsDTOMap.entrySet()) {
            String indexName = RecordUtil.getRecordIndexName(ykCallRecords.getKey(), "yyyyMM");
            List<YKCallRecordsDTO> ykCallRecordsValue = ykCallRecords.getValue();
            // 将通话记录列表转为key为callId的map,过滤掉callId为空的数据
            List<String> uniqueIds = ykCallRecordsValue.stream().map(YKCallRecordsDTO::getCallId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            //根据云客callId查询ES(对应ES的field2字段)
            List<CallRecord> recordList = callRecordService.querySupplementaryFieldByUniqueIds(indexName, CALL_TYPE_YPHONE.getType(), uniqueIds);
            Map<String, CallRecord> recordMap = recordList.stream().collect(Collectors.toMap(CallRecord::getField2, Function.identity(), (m1, m2) -> m2));
            // 需要更新的通话记录列表
            List<CallRecord> updateRecordList = new ArrayList<>(recordList.size());
            // 需要新增的通话记录列表
            List<YKCallRecordsDTO> createYkCallRecordsDTOList = new ArrayList<>(ykCallRecordsValue.size() - recordList.size());
            for (YKCallRecordsDTO ykCallRecordsDTO : ykCallRecordsValue) {
                if (recordMap.containsKey(ykCallRecordsDTO.getCallId())) {
                    CallRecord callRecord1 = recordMap.get(ykCallRecordsDTO.getCallId());
                    // 表示已经补充过通话信息了
                    if (callRecord1.getDuration() != -1) {
                        continue;
                    }
                    // 将云客返回数据转为通话记录
                    CallRecord callRecord = ykCallRecordsDTO.callRecordsConverter();
                    callRecord.setCreatedTime(null);
                    callRecord.setCallId(callRecord1.getCallId());
                    updateRecordList.add(callRecord);
                } else {
                    createYkCallRecordsDTOList.add(ykCallRecordsDTO);
                }
            }
            // 更新通话记录
            if (!updateRecordList.isEmpty()) {
                List<Map<String, Object>> recordingData = super.batchUpdateCallRecord(indexName, updateRecordList);
                // 发送下载录音MQ
                recordingData.forEach(data -> client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data)));
            }
            // 创建通话记录
            if (!createYkCallRecordsDTOList.isEmpty()) {
                this.generateCallInCallRecord(indexName, createYkCallRecordsDTOList);
            }
        }
    }

    /**
     * 获得剔除飞鱼Axb中间号作为被叫号码的通话
     *
     * @param ykCallRecords 云客通话回调记录
     * @return 剔除后剩余的云客通话记录
     */
    private List<YKCallRecordsDTO> getYkCallRecordsByRemoveFyAxb(List<YKCallRecordsDTO> ykCallRecords) {
        // 获取 JLFY_VIRTUAL_NUMBER_KEY 集合中的所有成员
        Optional<Set<String>> membersOpt = Optional.ofNullable(redisTemplate.opsForSet().members(RedisConst.JLFY_VIRTUAL_NUMBER_KEY));
        Set<String> members = membersOpt.orElse(Collections.emptySet());

        // 创建一个新的列表，用于保存剔除飞鱼AXB外呼后的元素
        List<YKCallRecordsDTO> filteredFyList = new ArrayList<>();

        // 遍历 ykCallRecordsDTOS 列表，将未被剔除的元素添加到 filteredList 中
        for (YKCallRecordsDTO dto : ykCallRecords) {
            if (!members.contains(dto.getPhone())) {
                filteredFyList.add(dto);
            }
        }
        return filteredFyList;
    }

    /**
     * 将云客回调通话中，剔除中科亿讯回拨记录
     *
     * @param filteredList 剔除后的云客记录
     */
    /*private List<YKCallRecordsDTO> getYkCallRecordsByRemoveZk(List<YKCallRecordsDTO> ykCallRecords) {
        // 获取 ZK_CALLER_DISPLAY_KEY 集合中的所有成员
        Optional<Set<String>> membersOpt = Optional.ofNullable(redisTemplate.opsForSet().members(RedisConst.ZK_CALLER_DISPLAY_KEY));
        Set<String> members = membersOpt.orElse(Collections.emptySet());

        // 创建一个新的列表，用于保存剔除飞鱼AXB外呼后的元素
        List<YKCallRecordsDTO> filteredList = new ArrayList<>();

        // 遍历 ykCallRecordsDTOS 列表，将未被剔除的元素添加到 filteredList 中
        for (YKCallRecordsDTO dto : ykCallRecords) {
            if (!members.contains(dto.getUserPhone())) {
                filteredList.add(dto);
            }
        }
        return filteredList;
    }*/


    /**
     * 接收智能外呼MQ外呼成功后返回的主叫被叫信息生成缺少通话信息的通话记录
     *
     * @param request 主被叫信息
     * <AUTHOR>
     * @Date 14:39 2022/5/28
     **/
    public void generateCallRecord(GenerateCallRecordRequest request) {
        // 生成通话记录保存到ElasticSearch 
        Map<String, Object> mqRequest = super.commonGenerateCallRecord(request);
        if (mqRequest != null) {
            // 推送到延时队列30分钟后去查询此通通话记录是否被补充完整，如果还没有补充完整，则调用查询接口去查询更新
            client.publish(MQConst.ACTIVE_QUERY_RECORD_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(30));
        }
    }

    /**
     * 生成呼入的通话记录(还有少数的不是通过智能运营平台打的)
     *
     * @param indexName                  索引名称
     * @param createYkCallRecordsDTOList 通话记录
     * <AUTHOR>
     * @Date 11:24 2022/6/30
     **/
    private void generateCallInCallRecord(String indexName, List<YKCallRecordsDTO> createYkCallRecordsDTOList) {
        List<CallRecord> callRecordList = new ArrayList<>();
        for (YKCallRecordsDTO ykCallRecordsDTO : createYkCallRecordsDTOList) {
            // 校验此条通话记录是否已经生成
            boolean existRecord = super.isNotExistRecord(ykCallRecordsDTO.getCallId(), ykCallRecordsDTO.getId(), CALL_TYPE_YPHONE);
            if (existRecord) {
                //补充通话信息
                CallRecord callRecord = ykCallRecordsDTO.callRecordsConverter();
                //补充空值
                ykCallRecordsDTO.replenishEmpty(callRecord);
                //补充员工信息
                super.setEE(ykCallRecordsDTO.getCreateTime(), CALL_TYPE_YPHONE.getType(), callRecord);
                callRecordList.add(callRecord);
            }
        }
        // 校验是否有此索引，保存到ES内
        boolean flag = this.batchSaveRecords(indexName, callRecordList);
        // 创建成功后发送MQ下载录音
        if (flag) {
            for (CallRecord callRecord : callRecordList) {
                if (callRecord.getDuration() > 0 && StringUtils.isNotEmpty(callRecord.getVoiceSourceUrl())) {
                    Map<String, Object> data = new HashMap<>(4);
                    data.put("channelType", CALL_TYPE_YPHONE.getType());
                    data.put("indexName", indexName);
                    data.put("voiceSourceUrl", callRecord.getVoiceSourceUrl());
                    data.put("callId", callRecord.getCallId());
                    client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data));
                }
            }
        }
    }

    /**
     * 通过智能运营平台拨打电话后，三十分钟内还没有接收到回调，就主动查询来补充通话记录,查询不到此条通话记录重复查询,最多重复查询5次
     *
     * @param mqRequest 查询通话记录的参数
     * <AUTHOR>
     * @Date 10:51 2022/7/1
     **/
    public void activeQueryIncompleteRecord(Map<String, Object> mqRequest) {
        String index = mqRequest.get("index").toString();
        String contactId = mqRequest.get("contactId").toString();
        String createDate = mqRequest.get("createDate").toString();
        // 校验此条通话记录是否已经补充完整了
        CallRecord callRecord1 = callRecordService.querySupplementaryFieldByUniqueId(index, CALL_TYPE_YPHONE.getType(), contactId);
        if (callRecord1 == null || callRecord1.getDuration() != -1) {
            return;
        }
        // 查询厂商获取通话记录信息
        String startDate = DateUtil.addDay(createDate, -2, DateUtils.DEFAULT_FORMAT);
        PullYKRecordRequest request = new PullYKRecordRequest(1, 10, startDate, createDate);
        request.setUserId(callRecord1.getCallAccount());
        request.setPhone(FieldCipherUtil.decrypt(callRecord1.getReciverPhone()));
        logger.info("主动查询云客厂商外呼后三十分钟没有回调的通话记录,主动查询参数:{},mq接收参数:{}", JSONUtils.toJSONString(request), JSONUtils.toJSONString(mqRequest));
        String response = this.selectRecordsFromVendor(request);
        YKCallRecordsPageVO ykCallRecordsPageVO = this.recordsResultConverter(response);
        if (null != ykCallRecordsPageVO) {
            List<YKCallRecordsDTO> ykCallRecordsDTOS = ykCallRecordsPageVO.getYkCallRecordsDTOS();
            // 循环对比通过智能外呼平台发起外呼返回的callId
            for (YKCallRecordsDTO ykCallRecordsDTO : ykCallRecordsDTOS) {
                if (callRecord1.getField2().equals(ykCallRecordsDTO.getCallId())) {
                    CallRecord callRecord = ykCallRecordsDTO.callRecordsConverter();
                    callRecord.setCallId(callRecord1.getCallId());
                    callRecord.setCreatedTime(null);
                    // 更新通话记录信息到ES
                    Map<String, Object> recordingData = super.updateCallRecord(callRecord, callRecord1.getCallId());
                    if (null != recordingData) {
                        // 发送下载录音MQ
                        client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordingData));
                    }
                    return;
                }
            }
        }
        // 没有查到重新投到MQ内,两次还不成功则打日志,忽略
        int times = Integer.parseInt(mqRequest.get("times").toString()) + 1;
        if (times >= 2) {
            logger.warn("云客主动查询还没有补充完整的通话记录信息超过两次没有成功,参数:" + JSONUtils.toJSONString(mqRequest));
            return;
        }
        mqRequest.put("times", times);
        client.publish(MQConst.ACTIVE_QUERY_RECORD_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(mqRequest), DateUtil.getMinuteMSeconds(60));
    }

    /**
     * 从厂商查询通话记录列表
     *
     * @return java.lang.String
     * @paramter startTime 通话开始时间
     * @paramter endTime   通话结束时间
     * @paramter page      当前页数
     * <AUTHOR>
     * @Date 14:13 2021/12/22
     **/
    public String selectRecordsFromVendor(PullYKRecordRequest request) {
        Map<String, String> header = this.getHeader();
        Headers headers = Headers.of(header);
        return okHttpUtil.post(CallProperties.YKProperty.url, JSONUtils.toJSONString(request), headers);
    }

    /**
     * 查询通话记录失败后重试处理[重试十次]
     *
     * @return com.niceloo.cmc.ex.pojo.vo.YKCallRecordsPageVO
     * @paramter startTime 通话开始时间
     * @paramter endTime   通话结束时间
     * @paramter page      当前页数
     * <AUTHOR>
     * @Date 14:41 2021/12/22
     **/
    private YKCallRecordsPageVO retrySelectRecordsFromVendor(PullYKRecordRequest request) {
        for (int i = 0; i < 10; i++) {
            String response = this.selectRecordsFromVendor(request);
            YKCallRecordsPageVO ykCallRecordsPageVO = this.recordsResultConverter(response);
            if (null != ykCallRecordsPageVO) {
                return ykCallRecordsPageVO;
            }
        }
        // [重试十次,失败放入异常数组内]
        recordList.add(request.getBeginTime() + "_" + request.getEndTime() + "_" + super.ACTION_TYPE);
        return null;
    }

    /**
     * 结果转换器
     *
     * @return com.niceloo.cmc.ex.pojo.vo.YKCallRecordsPageVO
     * @paramter response 返回结果字符串
     * <AUTHOR>
     * @Date 14:14 2021/12/22
     **/
    public YKCallRecordsPageVO recordsResultConverter(String response) {
        if (StringUtils.isEmpty(response)) {
            return null;
        }
        Map<String, Object> map = JSONUtils.toMap(response);
        String success = map.get("success").toString();
        if (!"true".equals(success)) {
            return null;
        }
        Map<String, Object> data = (Map<String, Object>) map.get("data");
        YKCallRecordsPageVO recordsPage = new YKCallRecordsPageVO();
        recordsPage.setPageSize(Integer.parseInt(data.get("pageSize").toString()));
        recordsPage.setPageIndex(Integer.parseInt(data.get("pageIndex").toString()));
        recordsPage.setPageCount(Integer.parseInt(data.get("pageCount").toString()));
        recordsPage.setTotalCount(Integer.parseInt(data.get("totalCount").toString()));
        List<YKCallRecordsDTO> ykCallRecordsDTOS = JSONUtils.toList(JSONUtils.toJSONString(data.get("page")), YKCallRecordsDTO.class);
        recordsPage.setYkCallRecordsDTOS(ykCallRecordsDTOS);
        return recordsPage;
    }

    /**
     * 定时任务拉取的通话记录,保存通话记录到ES
     *
     * @paramter recordsDTOList
     * <AUTHOR>
     * @Date 14:50 2021/12/22
     **/
    private void saveRecordsToEs(List<YKCallRecordsDTO> recordsDTOList) {
        List<CallRecord> callRecordList = new ArrayList<>();
        for (YKCallRecordsDTO recordsDTO : recordsDTOList) {
            // 赋值通话记录数据到实体类
            CallRecord callRecord = this.saveEntity(recordsDTO);
            if (null == callRecord) {
                continue;
            }
            if (!StringUtils.isEmpty(callRecord.getVoiceSourceUrl())) {
                this.totalSize++;
            }
            callRecordList.add(callRecord);
        }
        if (!CollectionUtils.isEmpty(callRecordList)) {
            //添加通话录音到ES[call_recording]
            this.batchSaveRecording(callRecordList);
            //批量添加通话记录
            this.batchSaveRecords(super.ES_INDEX, callRecordList);
        }
        logger.info("定时任务云客通话记录数量去重过滤后补充数量:" + callRecordList.size());
    }

    /**
     * 请求头 HEADER
     *
     * @param 无
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @since 11:31 2021/12/22
     **/
    public Map<String, String> getHeader() {
        long timestamp = System.currentTimeMillis();
        Map<String, String> headers = new HashMap<>();
        headers.put("partnerId", CallProperties.YKProperty.partnerId);
        headers.put("company", CallProperties.YKProperty.company);
        headers.put("timestamp", String.valueOf(timestamp));
        headers.put("sign", MD5Utils.md5(CallProperties.YKProperty.sign + CallProperties.YKProperty.company + CallProperties.YKProperty.partnerId + timestamp).toUpperCase());
        headers.put("Content-Type", "application/json");
        return headers;
    }

    /**
     * 将厂商返回的话务信息转为通话记录实体(没有赋值客户信息)
     *
     * @return com.niceloo.cmc.ex.entity.es.CallRecord
     * @paramter ykCallRecordsDTO
     * 3
     * <AUTHOR>
     * @Date 11:23 2022/2/27
     **/
    private CallRecord saveEntity(YKCallRecordsDTO ykCallRecordsDTO) {
        //校验是否重复
        String indexName = RecordUtil.getRecordIndexName(DateUtils.toDate(ykCallRecordsDTO.getCreateTime()));
        if (super.isNotExistRecord(null, ykCallRecordsDTO.getId(), CALL_TYPE_YPHONE)
                && this.isNotExistRecord(ykCallRecordsDTO.getCallId(), indexName)) {
            //补充通话信息
            CallRecord callRecord = ykCallRecordsDTO.callRecordsConverter();
            //补充空值
            ykCallRecordsDTO.replenishEmpty(callRecord);
            //补充员工信息
            super.setEE(ykCallRecordsDTO.getCreateTime(), CALL_TYPE_YPHONE.getType(), callRecord);
            if (StringUtils.isEmpty(callRecord.getCallerUserId())) {
                callRecord.setCallerUserId(callRecord.getCallAccount());
            }
            return callRecord;
        }
        return null;
    }


    /**
     * 校验索引下云客的通话记录是否已经有了field2=uniqueKey的值,如果有返回并且duration不等于-1返回true，否则返回false
     *
     * @param uniqueKey 通过智能运营平台外呼的外呼id
     * @param indexName 索引名称
     * @return boolean
     * <AUTHOR>
     * @Date 16:16 2022/8/19
     **/
    private boolean isNotExistRecord(String uniqueKey, String indexName) {
        if (StringUtils.isEmpty(uniqueKey)) {
            return true;
        }
        CallRecord callRecord = callRecordService.querySupplementaryFieldByUniqueId(indexName, CALL_TYPE_YPHONE.getType(), uniqueKey);
        if (callRecord == null) {
            return true;
        }
        if (callRecord.getDuration() != -1) {
            return false;
        }
        String callId = callRecord.getCallId();
        try {
            // 删除该条没有补充完整的通话记录
            callRecordService.deleteDoc(indexName, callId);
            return true;
        } catch (Exception e) {
            logger.error(e, "云客校验通话记录是否存在->根据id删除ES内通话时长为-1的通话记录失败,索引名称:{},主键ID:{},异常信息:{}", indexName, callId, e.getMessage());
            return false;
        }
    }


    /**
     * 批量添加云客通话录音
     *
     * @paramter callRecordList
     * <AUTHOR>
     * @Date 10:53 2022/2/23
     **/
    private void batchSaveRecording(List<CallRecord> callRecordList) {
        try {
            callRecordingService.bulkSave(callRecordList);
        } catch (Exception e) {
            // 失败后删除去重ID
            logger.error(e, "云客通话记录->批量添加通话录音信息失败,异常信息:{}", e.getMessage());
            String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_YPHONE.getType() + ":";
            redisTemplate.delete(callRecordList.stream().map(callRecord -> (redisKeyPrefix + callRecord.getVoiceSourceId())).collect(Collectors.toList()));
        }
    }


    /**
     * 批量添加云客通话记录(更新失败返回false)
     *
     * @paramter index
     * @paramter voiceVoList
     * <AUTHOR>
     * @Date 10:54 2022/2/23
     **/
    private boolean batchSaveRecords(String index, List<CallRecord> callRecordList) {
        try {
            super.batchAddCustomerInfoByPhone(callRecordList);
            callRecordService.bulkSave(index, callRecordList);
            return true;
        } catch (Exception e) {
            logger.error(e, "云客通话记录->批量添加通话记录信息失败,索引:{},异常信息:{}", index, e.getMessage());
            // 失败后删除去重ID
            String redisKeyPrefix = RedisConst.DUPLICATE_RECORD_CHECK + CALL_TYPE_YPHONE.getType() + ":";
            redisTemplate.delete(callRecordList.stream().map(callRecord -> (redisKeyPrefix + callRecord.getVoiceSourceId())).collect(Collectors.toList()));
            List<String> ids = callRecordList.stream().map(CallRecord::getCallId).collect(Collectors.toList());
            callRecordingService.batchDeleteByIds(ids);
        }
        return false;
    }

    /**
     * 通话记录返回的录音地址有可能不是本条记录的(云客系统的bug)
     * 如果录音地址里的手机号是本条通话记录的被叫手机号且时间(应该是yyyyMMddHHmmss,但是也有可能是毫秒值)是本条通话记录的呼叫时间, 可以认为录音地址是本条的
     * 录音地址中的手机号加密:2021-10-09开启
     * <p>
     * 带有()的录音地址中并没有和外呼时间保持一致
     * <tr/>例如---->
     * 录音地址: http://hqyl-yunke.oss-cn-hangzhou.aliyuncs.com/cellPhone-record-18636703400(18636703400)_20210527164009.mp3
     * 外呼时间: 2021-05-27 16:39:52<tr/>
     * </p>
     * <p>
     * 云客返回的录音地址示例:
     * http://hqyl-yunke.oss-cn-hangzhou.aliyuncs.com/cellPhone-record-180****2226plrss148rRGv3yAdFW_43A==_20211008164739_376000.mp3
     * http://hqyl-yunke.oss-cn-hangzhou.aliyuncs.com/cellPhone-record-097****8456IxlmO_AAAJZmFfynt71JlQ==_1634088993311.mp3
     * http://hqyl-yunke.oss-cn-hangzhou.aliyuncs.com/cellPhone-record-95****86s_8VawdlXaSMAyB1lQu1vA==_20211013085630_12000.mp3
     * http://hqyl-yunke.oss-cn-hangzhou.aliyuncs.com/cellPhone-record-1****33UJG4GbZWmvjMhvq_jExRYA==_1634175369246.mp3
     */
    public static boolean voiceSourceUrlIsCorrect(String voiceSourceUrl, Date callTime, String receiverPhone) {
        if (StringUtils.isEmpty(voiceSourceUrl) || StringUtils.isEmpty(receiverPhone) || callTime == null) {
            return false;
        }
        // 校验录音地址中是否带有(*),带有(*)的录音地址中不进行外呼时间比对(2022年9月之后不需要做这个校验,因为在这之后就没有这种情况了)
        boolean flag = callTime.after(DateUtils.toDate("2022-09-01 00:00:00")) || !voiceSourceUrl.contains("(") || !voiceSourceUrl.contains(")");
        if (flag) {
            // 下载地址必须包括外呼时间:yyyyMMddHHmmss格式或者毫秒值
            if (!Pattern.compile(".*(" + DateUtils.toStr(callTime, "yyyyMMddHHmmss") + "|" + callTime.getTime() / 1000 + ").*").matcher(voiceSourceUrl).matches()) {
                return false;
            }
        }


        // 录音地址中被叫手机号已加密
        // 外呼号码长度为11或12位时，继续校验是否包括后四位
        return (receiverPhone.length() == 11 || receiverPhone.length() == 12) &&
                voiceSourceUrl.contains(receiverPhone.substring(receiverPhone.length() - 4));
        // 外呼号码非11位时,不继续校验:不清楚厂商的加密规则,容易出错,效果也不明显
    }

    //-----------------------------------------------下载通话录音(MQ消费使用)---------------------------
    @Value("${yunke.ossUrl}")
    private String yunKeUrl;
    @Resource
    private FileUtil fileUtil;

    /**
     * 下载通话录音并更新ES
     *
     * @param map 通话记录信息
     * <AUTHOR>
     * @Date 11:42 2022/7/1
     **/
    @Override
    public boolean downLoadRecording(Map<String, Object> map) {
        String voiceSourceUrl = map.get("voiceSourceUrl").toString();
        if (StringUtils.isEmpty(voiceSourceUrl)) {
            return true;
        }
        String indexName = map.get("indexName").toString();
        String callId = map.get("callId").toString();
        String fileName = callId + ".mp3";
        try {
            // Bucket地址copy
            Map<String, String> ossCopyFile = fileUtil.ossCopy(voiceSourceUrl, fileName, yunKeUrl);
            if (MapUtils.isEmpty(ossCopyFile)) {
                return false;
            }
            String filePath = ossCopyFile.get("filePath");
            String fileKey = ossCopyFile.get("fileKey");
            CallRecord callRecord = new CallRecord();
            callRecord.setCallId(callId);
            callRecord.setModifiedTime(new Date());
            callRecord.setDataCompleteStatus("Y");
            callRecord.setServerFolder(filePath);
            callRecord.setField1(fileKey);
            callRecord.setVoiceSyncStatus(2);
            // 更新通话记录
            callRecordService.bulkUpdate(indexName, new ArrayList<>(List.of(callRecord)));
            return true;
        } catch (Exception e) {
            logger.warn(e, "云客下载通话录音callId为:{}出现异常,异常信息:{}", callId, e.getMessage());
            // 下载失败后放入ES CallRecording索引内
            CallRecording callRecording = CallRecording.initialize();
            callRecording.setCallId(callId);
            String date = RecordUtil.getDateByIndexName(indexName);
            callRecording.setDate(date);
            callRecording.setVoiceSourceUrl(voiceSourceUrl);
            callRecording.setChannelType("YP");
            callRecording.setDuration(20);
            try {
                callRecordingService.save(callRecording);
            } catch (Exception ex) {
                logger.error(ex, "保存云客通话录音到索引callRecording出现异常,callId:{},异常信息:{}", callId, ex.getMessage());
            }
        }
        return false;
    }
}
