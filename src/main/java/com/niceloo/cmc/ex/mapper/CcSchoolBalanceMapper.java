package com.niceloo.cmc.ex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niceloo.cmc.ex.entity.CcSchoolBalance;
import com.niceloo.framework.db.dynamic.core.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 分校账户余额表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31 15:03:33
 */
@DS("cmc")
public interface CcSchoolBalanceMapper extends BaseMapper<CcSchoolBalance> {

    void batchSave(@Param("ccSchoolBalanceList") List<CcSchoolBalance> ccSchoolBalanceList);

}
