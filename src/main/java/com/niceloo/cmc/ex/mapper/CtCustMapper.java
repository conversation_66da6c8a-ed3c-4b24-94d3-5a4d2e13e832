package com.niceloo.cmc.ex.mapper;

import com.niceloo.cmc.ex.entity.CtCust;
import com.niceloo.framework.db.dynamic.core.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@DS("cmc")
public interface CtCustMapper{

    /**
     * 根据客户手机号查询客户信息
     * @param receiverPhone 加
     *                      密后的客户手机号
     * @return 客户信息列表
     */
    List<CtCust> selectByPhone(@Param("receiverPhone") String receiverPhone);

    /**
     * 根据客户手机号查询客户信息
     * @param tel 加密后的客户电话
     * @return 客户信息列表
     */
    List<CtCust> selectByTel(@Param("tel") String tel);

    /**
     * @desc: 查询客户手机号
     * @author: song
     * @date: 2022/2/25
     */
    List<String> getMobileListByPage(@Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * @desc 根据手机号查询客户信息
     * <AUTHOR>
     * @date 2022/3/24
     * @return java.util.List<com.niceloo.cmc.ex.entity.CtCust>
     */
    List<CtCust> getListByMobileList(@Param("list") List<String> mobiles);

    /**
     * @desc: 批量添加[存在则覆盖,使用主键校验是否存在]
     * @author: song
     * @date: 2022/2/26
     */
    int replaceInsertBatch(@Param("list") List<CtCust> list);

    /**
     * @desc: 批量修改
     * @author: song
     * @date: 2022/2/26
     */
    int batchUpdate(@Param("list") List<CtCust> list);

    /**
     * 通过使用主键范围代替分页查询客户手机(防止深分页的问题)
     *
     * @param minPrimaryValue 最小主键值 <span>(custId > #{minPrimaryValue})</span>
     * @param size            查询多少条数据 <span>( limit size)</span>
     */
    List<CtCust> selectMobileListUsePrimaryRange(@Param("custId") String minPrimaryValue,@Param("size") Integer size);
}
