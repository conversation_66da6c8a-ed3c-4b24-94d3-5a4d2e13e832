package com.niceloo.cmc.ex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.request.AICallJobListRequest;
import com.niceloo.framework.db.dynamic.core.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * AI外呼任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@DS("cmc")
public interface CcAiCallJobMapper extends BaseMapper<CcAiCallJob> {

    /**
     * 根据条件分页查询父任务的信息
     *
     * @param request 接口请求参数
     * @return 父任务的数据详情列表
     */
    List<CcAiCallJob> selectParentJobPage(@Param("request") AICallJobListRequest request);

    /**
     * 根据条件查询父任务的总数量
     *
     * @param request 接口请求参数
     * @return 父任务的总数量
     */
    int selectParentJobCount(@Param("request") AICallJobListRequest request);

    /**
     * 根据父任务id列表得到子任务列表
     *
     * @param parentIds 父任务id列表
     * @return 子任务列表
     */
    List<CcAiCallJob> selectChildrenJobPageParentIds(@Param("parentIds") List<String> parentIds);

    /**
     * 根据父任务更新任务进度为已完成(创建失败的除外)
     *
     * @param jobId 任务id
     */
    @Update("update CcAiCallJob set customerAddStatus = 'Y', jobProgress = 4 , modifyDate = #{date} where parentJobId = #{jobId} and jobProgress != 3")
    void updateJobProgressToFinish(@Param("jobId") String jobId, @Param("date") String date);

    @Update("update CcAiCallJob set customerAddStatus = 'Y' , modifyDate = #{date} where parentJobId = #{jobId}")
    void updateCustomerAddStatusToFinish(@Param("jobId") String parentJobId, @Param("date") String date);
}
