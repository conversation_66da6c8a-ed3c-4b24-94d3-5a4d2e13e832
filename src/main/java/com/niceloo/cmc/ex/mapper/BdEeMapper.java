package com.niceloo.cmc.ex.mapper;

import com.niceloo.cmc.ex.pojo.dto.BdEeDTO;
import com.niceloo.cmc.ex.pojo.dto.BdEeDptDTO;
import com.niceloo.cmc.ex.pojo.dto.EeSchoolInfoDTO;
import com.niceloo.framework.db.dynamic.core.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 员工 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@DS("bd")
public interface BdEeMapper{

    @Select("SELECT schoolName FROM BdSchool WHERE schoolId =#{schoolId}")
    String findSchoolNameById(@Param("schoolId") String schoolId);

    BdEeDTO findBdEeDTOByUserId(@Param("userId") String userId);

    List<BdEeDTO> selectBdEeInfoUseSQLOrder(@Param("sql") String sql);

    List<EeSchoolInfoDTO> findSchoolNameByIds(@Param("schoolIds") List<String> schoolIds);

    BdEeDTO findBdEeDTOAndSchoolNameByUserId(@Param("userId") String userId);

    /**
     * 根据用户id列表查询员工的组织架构信息
     *
     * @param userIds 员工id列表
     * @return 员工的组织架构信息列表
     */
    List<BdEeDptDTO> findBdEeDptListByUserIds(@Param("userIds") List<String> userIds);
}
