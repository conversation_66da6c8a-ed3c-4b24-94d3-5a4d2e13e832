package com.niceloo.cmc.ex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niceloo.cmc.ex.entity.CcCountday;
import com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData;
import com.niceloo.cmc.ex.pojo.vo.CallStatsVO;
import com.niceloo.cmc.ex.pojo.vo.RecordEeStatisticsVO;
import com.niceloo.framework.db.dynamic.core.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 话务统计表(天) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@DS("cmc")
public interface CcCountdayMapper extends BaseMapper<CcCountday> {

    /**
     * 根据日期和外呼类型删除话务统计信息
     *
     * @param date        日期
     * @param channelType 外呼类型
     */
    void deleteByDateAndChannelType(@Param("date") String date, @Param("channelType") String channelType);

    void batchSave(@Param("commuCounts") List<CcCountday> commuCounts);

    List<CcCountday> selectListByDate(@Param("start") String start, @Param("end") String end, @Param("channelType") String channelType);

    List<RecordDayStatisticsData> getTrafficStatisticGroupDateSql(@Param("callerUserId") String callerUserId,
                                                                  @Param("deptList") List<String> deptList,
                                                                  @Param("dates") List<String> dates,
                                                                  @Param("channelType") String channelType);

    List<RecordDayStatisticsData> getTrafficStatisticGroupDateSql2(@Param("callerUserId") String callerUserId,
                                                                  @Param("deptList") List<String> deptList,
                                                                  @Param("minDate") String minDate,
                                                                  @Param("maxDate") String maxDate,
                                                                  @Param("channelType") String channelType);

    List<RecordEeStatisticsVO> getTrafficStatisticGroupEeSql(@Param("callerUserId") String callerUserId,
                                                             @Param("deptList") List<String> deptList,
                                                             @Param("dates") List<String> dates,
                                                             @Param("channelType") String channelType);

    List<RecordEeStatisticsVO> getTrafficStatisticGroupEeSql2(@Param("callerUserId") String callerUserId,
                                                              @Param("deptList") List<String> deptList,
                                                              @Param("minDate") String minDate,
                                                              @Param("maxDate") String maxDate,
                                                              @Param("channelType") String channelType);

    /**
     * 根据userId查询员工通话量和通话时长
     *
     * @param callerUserId 外呼用户标识
     * @param dates 日期集合
     * @return 呼叫统计数据
     */
    CallStatsVO getCallStatsByUserId(@Param("callerUserId") String callerUserId,
                                     @Param("dates") List<String> dates);
}
