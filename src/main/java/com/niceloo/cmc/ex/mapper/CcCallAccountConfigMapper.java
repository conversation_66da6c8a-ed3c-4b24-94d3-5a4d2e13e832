package com.niceloo.cmc.ex.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.entity.CcCallAccountModifyLog;
import com.niceloo.cmc.ex.pojo.request.AuthBaseRequest;
import com.niceloo.cmc.ex.pojo.request.CallAccountSelectRequest;
import com.niceloo.framework.db.dynamic.core.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外呼账号配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@DS("cmc")
public interface CcCallAccountConfigMapper extends BaseMapper<CcCallAccountConfig> {

    /**
     * 根据外呼账号表id查询账号信息
     *
     * @param id 主键id
     */
    CcCallAccountConfig searchCallAccountInfoById(@Param("id") String id);

    /**
     * 根据主叫账号和外呼通道查询账号信息(一个外呼通道里,账号和员工一对一)
     *
     * @param account 外呼账号
     * @param type    通道枚举 Type
     */
    CcCallAccountConfig searchCallAccountInfoByAccount(@Param("account") String account, @Param("type") String type);

    /**
     * 分页查询外呼账号列表
     *
     * @param request 请求参数
     */
    List<CcCallAccountConfig> searchPage(@Param("request") CallAccountSelectRequest request);

    /**
     * 根据条件查询数量
     *
     * @param request 请求参数
     */
    Integer selectTotalCount(@Param("request") CallAccountSelectRequest request);

    /**
     * 查询外呼账号操作记录列表
     *
     * @param callAccountId 外呼账号表id
     */
    List<CcCallAccountModifyLog> searchAccountOperationLogList(@Param("callAccountId") String callAccountId);

    /**
     * 外呼账号存在数量(未绑定状态也为存在)
     *
     * @param account 外呼账号
     * @param type    通道枚举
     */
    Integer hasCallAccount(@Param("account") String account, @Param("type") String type);

    /**
     * 该员工在该通道下是否存在账号(未绑定状态不为存在)
     *
     * @param userId 员工Id
     * @param type   通道枚举
     */
    Integer hasCallAccountByUserId(@Param("userId") String userId, @Param("type") String type);

    /**
     * 新增外呼账号配置
     *
     * @param callAccountConfig 账号配置
     */
    void insertAccountConfig(@Param("account") CcCallAccountConfig callAccountConfig);

    /**
     * 新增外呼账号配置操作记录表
     *
     * @param ccCallAccountModifyLog 操作记录
     */
    void insertOperationLog(@Param("operation") CcCallAccountModifyLog ccCallAccountModifyLog);

    /**
     * 修改(操作)账号配置
     *
     * @param config 操作请求
     */
    void accountOperation(@Param("config") CcCallAccountConfig config);

    /**
     * 批量查询员工拥有的外呼账号列表 [状态为已删除的查询不到]
     *
     * @param eeUserIds 员工id列表
     */
    List<CcCallAccountConfig> batchQueryEeCallAccount(@Param("eeUserIds") List<String> eeUserIds);

    /**
     * 根据员工id和外呼通道查询账号信息(一个外呼通道里,账号和员工一对一)[状态为已删除的查询不到]
     *
     * @param userId      员工id
     * @param channelType 外呼类型
     */
    CcCallAccountConfig searchCallAccountInfoByUserId(@Param("userId") String userId, @Param("channelType") String channelType);

    /**
     * 根据id列表查询出绑定着员工的外呼账号配置列表
     *
     * @param ids id列表 [不能为空]
     */
    List<CcCallAccountConfig> searchBandingAccountListByIds(@Param("ids") List<String> ids);

    /**
     * 根据id列表批量解绑
     * @param config 包含解绑人的信息
     * @param ids id列表
     */
    void batchUnbind(@Param("config")CcCallAccountConfig config, @Param("ids") List<String> ids);

    /**
     * 批量新增外呼账号配置操作记录表
     * @param ccCallAccountModifyLogList 操作记录列表
     */
    void batchInsertOperationLog(@Param("operationList") List<CcCallAccountModifyLog> ccCallAccountModifyLogList);

    AuthBaseRequest getOrgStructureOfCreator(@Param("id") String id);
}
