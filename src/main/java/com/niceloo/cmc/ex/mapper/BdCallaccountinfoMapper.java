package com.niceloo.cmc.ex.mapper;

import com.niceloo.cmc.ex.pojo.dto.AccountDTO;
import com.niceloo.framework.db.dynamic.core.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 通讯厂商开放API对接密钥 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@DS("bd")
public interface BdCallaccountinfoMapper{

    List<String> selectSchoolIdByAccountType(@Param("accountType") String accountType);

    AccountDTO selectAccount(@Param("account") String account, @Param("type") String type);

    List<AccountDTO> selectUniqueAccountsByType(@Param("channelType") String channelType);

    String selectAccountNameByBdEeId(@Param("eeId") String eeId, @Param("type") String type);
}
