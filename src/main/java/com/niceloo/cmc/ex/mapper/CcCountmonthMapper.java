package com.niceloo.cmc.ex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.niceloo.cmc.ex.entity.CcCountmonth;
import com.niceloo.framework.db.dynamic.core.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 话务统计表(月) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@DS("cmc")
public interface CcCountmonthMapper extends BaseMapper<CcCountmonth> {

    void deleteByDate(@Param("date") String date, @Param("channelType") String channelType);

    void batchSave(@Param("ccCountMonths") List<CcCountmonth> ccCountMonths);
}
