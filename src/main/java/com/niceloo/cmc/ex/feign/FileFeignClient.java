package com.niceloo.cmc.ex.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * 调用文件服务接口
 *
 * @author: <PERSON><PERSON>henyu
 * @create: 2022-02-17 15:52
 */
@FeignClient(url = "${fs.url:}", name = "file-producer")
public interface FileFeignClient {
    /**
     * 获取OSS上传文件的URL地址
     *
     * @param params 请求参数，具体内容依赖于业务逻辑
     * @return 包含OSS上传URL的Map，键为URL的名称，值为具体的URL地址
     */
    @PostMapping(value = "api/file/oss/url", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String,Object> ossUrl(String params);

    /**
     * OSS复制文件到另一个BK中
     * @param params 参数 json字符串
     * @return 文件路径等信息
     */
    @PostMapping(value = "api/file/ali/oss/copy/file", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String,String> ossCopyFile(String params);

    /**
     * OSS复制文件到另一个BK中
     * @param params 参数 json字符串
     * @return 文件路径等信息
     */
    @PostMapping(value = "api/file/ali/oss/copy/file", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String,Object> ossCopyFile2(String params);

    /**
     * 从文件服务中获取音频文件 token
     * @param params 参数 json字符串
     * @return 文件Token等信息
     */
    @PostMapping(value = "api/file/download/token", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String,Object> getVoiceToken(String params);


    /**
     * 有访问权限的文件需求从文件服务获取临时可访问的url
     * @param params 参数 json字符串
     * @return 文件Token等信息
     */
    @PostMapping(value = "api/file/download/url", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String,Object> queryTemporaryURL(String params);
}
