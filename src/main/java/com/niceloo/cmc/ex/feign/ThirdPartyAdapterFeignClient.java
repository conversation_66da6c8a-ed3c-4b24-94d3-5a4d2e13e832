package com.niceloo.cmc.ex.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * 调用第三方适配器服务
 *
 * <AUTHOR>
 * @Date 2022年5月30日13:56:30
 */
@FeignClient(url = "${ta.url:}", name = "ta-producer")
public interface ThirdPartyAdapterFeignClient {

    @PostMapping(value = "/api/ta/jlyq/getTokenByAdId", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String, Object> getAccessToken(String param);
}
