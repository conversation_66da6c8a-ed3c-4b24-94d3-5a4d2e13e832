package com.niceloo.cmc.ex.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 调用认证中心服务
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-17 15:52
 */
@FeignClient(url = "${auth.url:}", name = "auth-producer")
public interface AuthFeignClient {

    /**
     * 根据菜单检测用户权限(缺少权限抛出异常)[不用接收返回值(因为不加返回值不会抛出异常),返回值为null]
     * @param params 参数 json字符串
     */
    @PostMapping(value = "api/ua/auth/datapolicy/checkAuth", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Object checkAuth(String params);

}
