package com.niceloo.cmc.ex.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;

/**
 * 调用用户中心服务接口
 *
 * <AUTHOR>
 * @since 2022/2/25
 */
@FeignClient(url = "${uc.url:}", name = "user-producer")
public interface UserFeignClient {

    /**
     * 带权限分校列表
     *
     * <AUTHOR>
     * @since 2023/07/21
     */
    @PostMapping(value = "api/uc/school/auth/list", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    List<Map<String, Object>> authSchoolList(String param);

    /**
     * 手机号获取优路号
     *
     * <AUTHOR>
     * @since 2022/2/25
     */
    @PostMapping(value = "api/uc/user/list/moblies", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Object getYouluNumByMobiles(String param);

    /**
     * 用户ID获取手机号
     *
     * <AUTHOR>
     * @since 2022/2/25
     */
    @PostMapping(value = "api/uc/user/id/list/namemobile", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Object getMobileByUserIds(String param);

    /**
     * 根据优路号调用用户中心接口获取用户ID
     *
     * <AUTHOR>
     * @since 2022/2/25
     */
    @PostMapping(value = "api/uc/user/getuserids/byyoulunums", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Object getUserIdByYouluNum(String param);

    @PostMapping(value = "api/uc/ee/info", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String, Object> getUserInfoByUserId(String param);

    /**
     * 转发查询用户中心[获取员工信息-分页]
     *
     * @param param 前端传递的参数+权限校验后的参数拼接
     */
    @PostMapping(value = "api/uc/ee/list", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Object getEeInfoPage(String param);

    /**
     * 批量根据UserID获取员工详情
     */
    @PostMapping(value = "api/uc/ee/info/list", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    List<Map<String, Object>> getUserInfoByUserIds(String param);

    /**
     * 转发查询用户中心[根据分校ID获取该分校下的部门树]
     *
     * @param param 前端传递的参数+权限校验后的参数拼接
     */
    @PostMapping(value = "api/uc/school/tree/school", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String, Object> getDptTreeBySchoolId(String param);

    /**
     * 转发查询用户中心[根据userID获取该员工的部门树]
     *
     * @param param 前端传递的参数+权限校验后的参数拼接
     */
    @PostMapping(value = "api/uc/ee/finddptbyee", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    Map<String, Object> findDptByUserId(String param);
}
