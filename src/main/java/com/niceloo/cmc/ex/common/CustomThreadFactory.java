package com.niceloo.cmc.ex.common;

import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * @desc: 自定义线程池工厂,可以给线程名设置前缀
 * @author: wangzhenming
 * @date: 2022/2/28
 */
public class CustomThreadFactory implements ThreadFactory {

    private final ThreadFactory defaultThreadFactory = Executors.defaultThreadFactory();
    private final String threadNamePrefix;


    public CustomThreadFactory(String threadNamePrefix) {
        this.threadNamePrefix = threadNamePrefix;
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread thread = defaultThreadFactory.newThread(r);
        thread.setName(threadNamePrefix + "-" + thread.getName());
        return thread;
    }
}
