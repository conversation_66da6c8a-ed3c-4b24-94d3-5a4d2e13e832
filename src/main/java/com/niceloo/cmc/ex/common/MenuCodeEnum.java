package com.niceloo.cmc.ex.common;

import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;

import java.util.Objects;

import static com.niceloo.cmc.ex.common.MenuCodeEnum.OperationTypeEnum.*;


/**
 * 菜单Code记录
 */
public enum MenuCodeEnum {
    AI_CALL_JOB(AIJobMenuEnum.DEFAULT),
    AI_CALL_ACCOUNT(AICallAccountMenuEnum.DEFAULT),
    JLFY_CALL_ACCOUNT(JLFYCallAccountMenuEnum.DEFAULT),
    ZHZX_CALL_ACCOUNT(ZHZXCallAccountMenuEnum.DEFAULT),
    OTHER_MENU(OtherMenuEnum.DEFAULT);
    // 不同的页面菜单功能
    public final MenuCode menuEnum;

    MenuCodeEnum(MenuCode menuEnum) {
        this.menuEnum = menuEnum;
    }

    public MenuCode getMenuEnum() {
        return menuEnum;
    }

    /**
     * 验证该菜单是否需要数据权限验证
     * @param menuCode 菜单编码
     * @return boolean
     * <AUTHOR>
     * @Date 10:05 2022/5/27
     **/
    public static boolean checkMenuIsDataAuth(String menuCode) {
        if (StringUtils.isEmpty(menuCode)) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "缺少menuCode");
        }
        MenuCodeEnum[] values = MenuCodeEnum.values();
        for (MenuCodeEnum menuCodeEnum : values) {
            MenuCodeEnum.MenuCode menuEnum = menuCodeEnum.getMenuEnum();
            if (menuEnum.checkMenu(menuCode)) {
                return menuEnum.checkMenuIsDataAuth(menuCode);
            }
        }
        throw new ApplicationException(ApiErrorCodes.argument_invalided, "没有找到此menuCode的菜单信息");
    }
    
    /**
     * 菜单code顶级接口,
     * 不同的页面菜单都会继承该接口
     */
    public interface MenuCode {
        /**
         * 根据不同的操作类型获取该操作类型的菜单编码
         *
         * @param operationTypeEnum 操作类型枚举类
         * @return 菜单编码
         */
        String getMenuCodeOperationType(OperationTypeEnum operationTypeEnum);

        /**
         * 校验该页面是否有此菜单编码
         *
         * @param menuCode 菜单编码
         * @return 该页面有此菜单编码则返回true
         */
        boolean checkMenu(String menuCode);

        /**
         * 校验该菜单编码是否需要数据权限校验
         *
         * @param menuCode 菜单编码
         * @return 该菜单需要数据权限校验则返回true
         */
        boolean checkMenuIsDataAuth(String menuCode);
    }

    /**
     * AI外呼任务菜单枚举类
     */
    public enum AIJobMenuEnum implements MenuCode {
        DEFAULT(null, null, true, "空的默认值"),
        AI_CALL_JOB_LIST("ic/AI/job-list", SEARCH, true, "AI外呼任务列表");

        private final String code;
        private final OperationTypeEnum operationTypeEnum;
        private final boolean authData;
        private final String desc;


        public String getCode() {
            return code;
        }

        public OperationTypeEnum getOperationTypeEnum() {
            return operationTypeEnum;
        }

        public boolean isAuthData() {
            return authData;
        }

        public String getDesc() {
            return desc;
        }

        AIJobMenuEnum(String code, OperationTypeEnum operationTypeEnum, boolean authData, String desc) {
            this.code = code;
            this.operationTypeEnum = operationTypeEnum;
            this.authData = authData;
            this.desc = desc;
        }

        @Override
        public String getMenuCodeOperationType(OperationTypeEnum operationTypeEnum) {
            AIJobMenuEnum[] values = AIJobMenuEnum.values();
            for (AIJobMenuEnum jobMenuEnum : values) {
                if (AIJobMenuEnum.DEFAULT.equals(jobMenuEnum)) {
                    continue;
                }
                if (operationTypeEnum.getType().equals(jobMenuEnum.getOperationTypeEnum().getType())) {
                    return jobMenuEnum.getCode();
                }
            }
            return null;
        }

        @Override
        public boolean checkMenu(String menuCode) {
            AIJobMenuEnum[] values = AIJobMenuEnum.values();
            for (AIJobMenuEnum jobMenuEnum : values) {
                if (menuCode.equals(jobMenuEnum.getCode())) {
                    return true;
                }
            }
            return false;
        }

        @Override
        public boolean checkMenuIsDataAuth(String menuCode) {
            AIJobMenuEnum[] values = AIJobMenuEnum.values();
            for (AIJobMenuEnum jobMenuEnum : values) {
                if (menuCode.equals(jobMenuEnum.getCode()) && jobMenuEnum.isAuthData()) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * AI外呼账号功能菜单编码枚举类
     */
    public enum AICallAccountMenuEnum implements MenuCode {
        DEFAULT(null, null, true, "空的默认值"),
        JDYX_ADD("ic/AI/config-add", ADD, true, "京东言犀AI外呼-添加"),
        JDYX_SEARCH("ic/AI/config-list", SEARCH, true, "京东言犀AI外呼-列表"),
        JDYX_ENABLE("ic/AI/config-enable", ENABLE, false, "京东言犀AI外呼-启用"),
        JDYX_DISABLE("ic/AI/config-disable", DISABLE, false, "京东言犀AI外呼-禁用");
        private final String code;
        private final OperationTypeEnum operationTypeEnum;
        private final boolean authData;
        private final String desc;

        public String getCode() {
            return code;
        }

        public boolean isAuthData() {
            return authData;
        }

        public OperationTypeEnum getOperationTypeEnum() {
            return operationTypeEnum;
        }

        public String getDesc() {
            return desc;
        }

        AICallAccountMenuEnum(String code, OperationTypeEnum operationTypeEnum, boolean authData, String desc) {
            this.code = code;
            this.operationTypeEnum = operationTypeEnum;
            this.authData = authData;
            this.desc = desc;
        }

        @Override
        public String getMenuCodeOperationType(OperationTypeEnum operationTypeEnum) {
            AICallAccountMenuEnum[] values = AICallAccountMenuEnum.values();
            for (AICallAccountMenuEnum aiCallAccountMenuEnum : values) {
                if (AICallAccountMenuEnum.DEFAULT.equals(aiCallAccountMenuEnum)) {
                    continue;
                }
                if (operationTypeEnum.getType().equals(aiCallAccountMenuEnum.getOperationTypeEnum().getType())) {
                    return aiCallAccountMenuEnum.getCode();
                }
            }
            return null;
        }

        @Override
        public boolean checkMenu(String menuCode) {
            AICallAccountMenuEnum[] values = AICallAccountMenuEnum.values();
            for (AICallAccountMenuEnum aiCallAccountMenuEnum : values) {
                if (menuCode.equals(aiCallAccountMenuEnum.getCode())) {
                    return true;
                }
            }
            return false;
        }

        @Override
        public boolean checkMenuIsDataAuth(String menuCode) {
            AICallAccountMenuEnum[] values = AICallAccountMenuEnum.values();
            for (AICallAccountMenuEnum aiCallAccountMenuEnum : values) {
                if (menuCode.equals(aiCallAccountMenuEnum.getCode()) && aiCallAccountMenuEnum.isAuthData()) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 巨量飞鱼外呼账号功能菜单枚举类
     */
    public enum JLFYCallAccountMenuEnum implements MenuCode {
        DEFAULT(null, null, true, "空的默认值"),
        JLFY_SEARCH("ic/JLFY/config-list", SEARCH, true, "飞鱼云外呼-列表"),
        JLFY_ADD("ic/JLFY/config-add", ADD, true, "飞鱼云外呼-添加"),
        JLFY_EXCHANGE_BINDING("ic/JLFY/config-changeBind", EXCHANGE_BINDING, true, "飞鱼云外呼-换绑"),
        JLFY_UNBIND("ic/JLFY/config-relieve", UNBIND, false, "飞鱼云外呼-解绑"),
        JLFY_BINDING("ic/JLFY/config-bind", BINDING, true, "飞鱼云外呼-绑定"),
        JLFY_INFO("ic/JLFY/config-view", INFO, true, "飞鱼云外呼-查看"),
        JLFY_BATCH_UNBIND("ic/JLFY/config-relieve-batch", BATCH_UNBIND, false, "飞鱼云外呼-批量解绑");

        private final String code;
        private final OperationTypeEnum operationTypeEnum;
        private final boolean authData;
        private final String desc;

        public String getCode() {
            return code;
        }

        public boolean isAuthData() {
            return authData;
        }

        public String getDesc() {
            return desc;
        }

        public OperationTypeEnum getOperationTypeEnum() {
            return operationTypeEnum;
        }

        JLFYCallAccountMenuEnum(String code, OperationTypeEnum operationTypeEnum, boolean authData, String desc) {
            this.code = code;
            this.operationTypeEnum = operationTypeEnum;
            this.authData = authData;
            this.desc = desc;
        }


        @Override
        public String getMenuCodeOperationType(OperationTypeEnum operationTypeEnum) {
            JLFYCallAccountMenuEnum[] values = JLFYCallAccountMenuEnum.values();
            for (JLFYCallAccountMenuEnum jlfyCallAccountMenuEnum : values) {
                if (JLFYCallAccountMenuEnum.DEFAULT.equals(jlfyCallAccountMenuEnum)) {
                    continue;
                }
                if (operationTypeEnum.getType().equals(jlfyCallAccountMenuEnum.getOperationTypeEnum().getType())) {
                    return jlfyCallAccountMenuEnum.getCode();
                }
            }
            return null;
        }

        @Override
        public boolean checkMenu(String menuCode) {
            JLFYCallAccountMenuEnum[] values = JLFYCallAccountMenuEnum.values();
            for (JLFYCallAccountMenuEnum jlfyCallAccountMenuEnum : values) {
                if (menuCode.equals(jlfyCallAccountMenuEnum.getCode())) {
                    return true;
                }
            }
            return false;
        }

        @Override
        public boolean checkMenuIsDataAuth(String menuCode) {
            JLFYCallAccountMenuEnum[] values = JLFYCallAccountMenuEnum.values();
            for (JLFYCallAccountMenuEnum jlfyCallAccountMenuEnum : values) {
                if (menuCode.equals(jlfyCallAccountMenuEnum.getCode()) && jlfyCallAccountMenuEnum.isAuthData()) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 中弘智享外呼账号功能菜单枚举类
     */
    public enum ZHZXCallAccountMenuEnum implements MenuCode {
        DEFAULT(null, null, true, "空的默认值"),
        ZHZX_SEARCH("ic/ZHZX/config-list", SEARCH, true, "中弘智享云外呼-列表"),
        ZHZX_ADD("ic/ZHZX/config-add", ADD, true, "中弘智享外呼-添加"),
        ZHZX_EXCHANGE_BINDING("ic/ZHZX/config-changeBind", EXCHANGE_BINDING, true, "中弘智享云外呼-换绑"),
        JLFY_UNBIND("ic/ZHZX/config-relieve", UNBIND, false, "中弘智享云外呼-解绑"),
        ZHZX_BINDING("ic/ZHZX/config-bind", BINDING, true, "中弘智享云外呼-绑定"),
        ZHZX_INFO("ic/ZHZX/config-view", INFO, true, "中弘智享云外呼-查看"),
        ZHZX_BATCH_UNBIND("ic/ZHZX/config-relieve-batch", BATCH_UNBIND, false, "中弘智享云外呼-批量解绑");

        private final String code;
        private final OperationTypeEnum operationTypeEnum;
        private final boolean authData;
        private final String desc;

        public String getCode() {
            return code;
        }

        public boolean isAuthData() {
            return authData;
        }

        public String getDesc() {
            return desc;
        }

        public OperationTypeEnum getOperationTypeEnum() {
            return operationTypeEnum;
        }

        ZHZXCallAccountMenuEnum(String code, OperationTypeEnum operationTypeEnum, boolean authData, String desc) {
            this.code = code;
            this.operationTypeEnum = operationTypeEnum;
            this.authData = authData;
            this.desc = desc;
        }

        @Override
        public String getMenuCodeOperationType(OperationTypeEnum operationTypeEnum) {
            ZHZXCallAccountMenuEnum[] values = ZHZXCallAccountMenuEnum.values();
            for (ZHZXCallAccountMenuEnum zhzxCallAccountMenuEnum : values) {
                if (ZHZXCallAccountMenuEnum.DEFAULT.equals(zhzxCallAccountMenuEnum)) {
                    continue;
                }
                if (operationTypeEnum.getType().equals(zhzxCallAccountMenuEnum.getOperationTypeEnum().getType())) {
                    return zhzxCallAccountMenuEnum.getCode();
                }
            }
            return null;
        }

        @Override
        public boolean checkMenu(String menuCode) {
            ZHZXCallAccountMenuEnum[] values = ZHZXCallAccountMenuEnum.values();
            for (ZHZXCallAccountMenuEnum zhzxCallAccountMenuEnum : values) {
                if (menuCode.equals(zhzxCallAccountMenuEnum.getCode())) {
                    return true;
                }
            }
            return false;
        }

        @Override
        public boolean checkMenuIsDataAuth(String menuCode) {
            ZHZXCallAccountMenuEnum[] values = ZHZXCallAccountMenuEnum.values();
            for (ZHZXCallAccountMenuEnum zhzxCallAccountMenuEnum : values) {
                if (menuCode.equals(zhzxCallAccountMenuEnum.getCode()) && zhzxCallAccountMenuEnum.isAuthData()) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 不便分类的菜单枚举类
     */
    public enum OtherMenuEnum implements MenuCode {
        DEFAULT(null, null, true, "空的默认值"),
        QUERY_RECORDS_BY_CUST_ID("CT/CustomerDetails/Query/Follow", SEARCH, true, "根据客户id查询通话记录"),
        INSTANT_STATISTICS_LIST("instantTrafficStatistic/list", SEARCH, true, "通话记录实时统计"),
        INSTANT_STATISTICS_EXCEL("instantTrafficStatistic/download", SEARCH, false, "通话记录实时统计Excel下载"),
        TRAFFIC_ANALYSIS_LIST("trafficCase/list", SEARCH, true, "通话分析查询"),
        TRAFFIC_ANALYSIS_EXCEL("trafficCase/download", SEARCH, false, "通话分析Excel下载"),
        QUERY_CALL_DETAIL("commu/record/search", SEARCH, true, "通话明细查询");

        private final String code;
        private final OperationTypeEnum operationTypeEnum;
        private final boolean authData;
        private final String desc;

        OtherMenuEnum(String code, OperationTypeEnum operationTypeEnum, boolean authData, String desc) {
            this.code = code;
            this.operationTypeEnum = operationTypeEnum;
            this.authData = authData;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public OperationTypeEnum getOperationTypeEnum() {
            return operationTypeEnum;
        }

        public boolean isAuthData() {
            return authData;
        }

        public String getDesc() {
            return desc;
        }


        @Override
        public String getMenuCodeOperationType(OperationTypeEnum operationTypeEnum) {
            OtherMenuEnum[] values = OtherMenuEnum.values();
            for (OtherMenuEnum otherMenuEnum : values) {
                if (OtherMenuEnum.DEFAULT.equals(otherMenuEnum)) {
                    continue;
                }
                if (operationTypeEnum.getType().equals(otherMenuEnum.getOperationTypeEnum().getType())) {
                    return otherMenuEnum.getCode();
                }
            }
            return null;
        }

        @Override
        public boolean checkMenu(String menuCode) {
            OtherMenuEnum[] values = OtherMenuEnum.values();
            for (OtherMenuEnum otherMenuEnum : values) {
                if (menuCode.equals(otherMenuEnum.getCode())) {
                    return true;
                }
            }
            return false;
        }

        @Override
        public boolean checkMenuIsDataAuth(String menuCode) {
            OtherMenuEnum[] values = OtherMenuEnum.values();
            for (OtherMenuEnum otherMenuEnum : values) {
                if (menuCode.equals(otherMenuEnum.getCode()) && otherMenuEnum.isAuthData()) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 外呼账号操作类型
     */
    public enum OperationTypeEnum {
        SEARCH(0), ADD(1), UNBIND(2), EXCHANGE_BINDING(3), BINDING(4), INFO(5), BATCH_UNBIND(6), ENABLE(7), DISABLE(8);

        private final Integer type;

        OperationTypeEnum(Integer type) {
            this.type = type;
        }

        public Integer getType() {
            return type;
        }

        public static OperationTypeEnum getEnum(Integer type) {
            for (OperationTypeEnum operationType : OperationTypeEnum.values()) {
                if (Objects.equals(type, operationType.getType())) {
                    return operationType;
                }
            }
            return null;
        }
    }
}
