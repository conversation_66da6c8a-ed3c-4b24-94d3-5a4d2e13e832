package com.niceloo.cmc.ex.common;

/**
 * 京东言犀的接口枚举类
 *
 * <AUTHOR>
 * @Date 2022-08-04 17:39
 */
public enum JDInterfaceTypeEnum {

    CONTEXT_LIST("CONTEXT_LIST", "/api/voice_call/context_getContextList", "查询某一个机器人对应的话术模板列表"),
    TAG_LIST("TAG_LIST", "/api/common_setting/answerTagGroup_findTagList", "查询话术标签列表"),
    GROUP_LIST("GROUP_LIST", "/api/common_setting/answerTagGroup_findGroupList", "查询意向标签分组"),
    TEL_LINE_LIST("TEL_LINE_LIST", "/api/voice_call/tel_getLineList", "查询电话线路列表"),
    APPEND_CUSTOMER("APPEND_CUSTOMER", "/api/voice_call/customer_appendCustomer", "任务追加客户名单（名单列表形式）"),
    CREATE_JOB("CREATE_JOB", "/api/voice_call/job_createJob", "创建外呼任务"),
    DELETE_JOB("DELETE_JOB", "/api/voice_call/job_deleteJob", "删除外呼任务"),
    CONTINUE_JOB("CONTINUE_JOB", "/api/voice_call/job_continueJob", "继续外呼任务"),
    PAUSE_JOB("PAUSE_JOB", "/api/voice_call/job_pauseJob", "暂停外呼任务"),
    START_JOB("START_JOB", "/api/voice_call/job_startJob", "启动外呼任务"),
    JOB_LIST("JOB_LIST", "/api/voice_call/job_getList", "查询指定机器人任务列表"),
    JOB_GET_REPORT_LIST("JOB_GET_REPORT_LIST", "/api/voice_call/job_getReportByIds", "批量任务结果查询接口"),
    JOB_INFO("JOB_INFO", "/api/voice_call/job_getJobInfo", "查询指定任务ID执行状态"),
    ROLLIING_GET_JOB_DETAILS("ROLLIING_GET_JOB_DETAILS", "/api/voice_call/job_rollingGetJobDetails", "外呼任务明细数据滚动查询");


    private final String name;

    private final String url;

    private final String desc;

    JDInterfaceTypeEnum(String name, String url, String desc) {
        this.name = name;
        this.url = url;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getUrl() {
        return url;
    }

    public String getDesc() {
        return desc;
    }
}
