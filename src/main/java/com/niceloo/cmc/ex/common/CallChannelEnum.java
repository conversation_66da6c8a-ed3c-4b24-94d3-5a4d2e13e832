package com.niceloo.cmc.ex.common;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 外呼通道类型常量枚举类
 *
 * <AUTHOR>
 * @since 2022-02-16 16:00
 */
@Getter
public enum CallChannelEnum {
    CALL_TYPE_TQ_FIX("TQ固话", "TF"),
    CALL_TYPE_TQ_MOBILE("TQ手机", "TQ"),
    CALL_TYPE_FY("云呼", "FS"),
    CALL_TYPE_YCC("云客CC外呼", "YS"),
    CALL_TYPE_YPHONE("云客手机外呼", "YP"),
    CALL_TYPE_ZK("中科云外呼", "ZK"),
    CALL_TYPE_YH("亿讯回拨外呼", "YH"),
    CALL_TYPE_YX_SIP("亿讯SIP外呼", "YXSIP"),
    CALL_TYPE_OI("一号互联", "OI"),
    CALL_TYPE_JLFY("巨量飞鱼", "JLFY"),
    CALL_TYPE_ZHZX("中弘智享", "ZHZX"),
    CALL_TYPE_JDYX("京东言犀AI外呼", "JDYX"),
    CALL_TYPE_BYAI("百应AI外呼", "BYAI");

    private final String name;
    private final String type;

    CallChannelEnum(String name, String type) {
        this.name = name;
        this.type = type;
    }

    /**
     * 根据外呼通道类型获取通道枚举
     *
     * @param type 外呼通道类型常量枚举类型
     * @return com.niceloo.cmc.ex.common.CallChannelEnum
     * <AUTHOR>
     * @since 11:54 2022/2/17
     **/
    public static CallChannelEnum getCallChannel(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        CallChannelEnum[] values = CallChannelEnum.values();
        for (CallChannelEnum value : values) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 校验该外呼厂商是否需要话务统计
     *
     * @param callChannelEnum 厂商类型
     * @return boolean
     * <AUTHOR>
     * @since 9:39 2022/8/16
     **/
    public static boolean isNotTrafficStatistic(CallChannelEnum callChannelEnum) {
        // 京东言犀不需要进行话务统计
        return callChannelEnum == CallChannelEnum.CALL_TYPE_JDYX || callChannelEnum == CallChannelEnum.CALL_TYPE_BYAI;
    }
}
