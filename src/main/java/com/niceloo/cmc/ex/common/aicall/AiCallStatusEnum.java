package com.niceloo.cmc.ex.common.aicall;

import lombok.Getter;

/**
 * AI外呼通话状态
 * <AUTHOR>
 * @since 2024/11/25
 */
@Getter
public enum AiCallStatusEnum {

    ANSWERED("已接听", "Answered", 0, 5),
    REJECT("拒接", "Reject", 1, 12),
    UNAVAILABLE("无法接通", "Unavailabe", 2, 4),
    OUTBOUND_FAIL("外呼失败", "OutboundFail", 3, 1013),
    DEAD("空号", "Dead", 4, 6),
    CLOSED("关机", "Closed", 5, 7),
    BUSY("占线", "Busy", 6, 8),
    STOPPED("停机", "Stopped", 7, 11),
    MISSED("未接/无人接听", "Missed", 8, 9),
    CALLER_ARREARS("主叫欠费", "CallerArrears", 9, 1012),
    CONCURRENT_CALL_LOSS("并发呼损", "ConcurrentCallLoss", 10, 1014),
    BLACKLIST("黑名单", "Blacklist", 11, 1015),
    ANTI_NUISANCE_CALL("防骚扰拦截/天盾拦截", "AntiNuisanceCall", 12, 1018),
    CALL_LIMIT("呼叫受限/线路盲区", "CallLimit", 22, 1017),
    INTERCEPTED("呼出拦截", "Intercepted", 23, 1011),
    NO_AVAILABLE_LINE("无可用线路", "NoAvailableLine", 25, 1016);

    private final String name;
    private final String code;
    private final Integer byaiNumber;
    private final Integer jdyxNumber;

    AiCallStatusEnum(String name, String code, Integer byaiNumber, Integer jdyxNumber) {
        this.name = name;
        this.code = code;
        this.byaiNumber = byaiNumber;
        this.jdyxNumber = jdyxNumber;
    }

    public static AiCallStatusEnum getByByaiNumber(Integer byaiNumber) {
        for (AiCallStatusEnum value : AiCallStatusEnum.values()) {
            if (value.byaiNumber.equals(byaiNumber)) {
                return value;
            }
        }
        throw new IllegalArgumentException("AiCallRedialReasonEnum -- byaiNumber:" + byaiNumber + "不存在");
    }

}
