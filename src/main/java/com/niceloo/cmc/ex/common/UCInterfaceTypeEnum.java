package com.niceloo.cmc.ex.common;

/**
 * @description: 通讯中心-话务查询RPC调用用户中心接口枚举类
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-09-16 09:33
 */

public enum UCInterfaceTypeEnum {
    YOULOO_GET_USER_ID("优路号获取用户ID", "YU","/api/uc/user/getuserids/byyoulunums"),
    USER_ID_GET_MOBILE("用户ID获取手机号", "UM","/api/uc/user/id/list/namemobile"),
    MOBILE_GET_YOULOO("手机号获取优路号", "MY","/api/uc/user/list/moblies");


    private final String name;

    private final String type;

    private final String url;

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public String getUrl() {
        return url;
    }

    UCInterfaceTypeEnum(String name, String type, String url) {
        this.name = name;
        this.type = type;
        this.url = url;
    }

    /**
     * 根据type获取接口类型枚举
     *
     * @param type 接口类型
     * @return com.niceloo.cmc.ex.common.UCInterfaceTypeEnum
     * <AUTHOR>
     * @Date 11:35 2021/9/16
     **/
    public static UCInterfaceTypeEnum getValue(String type) {
        if (null == type || "".equals(type)) {
            return null;
        }
        for (UCInterfaceTypeEnum value : UCInterfaceTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }
}
