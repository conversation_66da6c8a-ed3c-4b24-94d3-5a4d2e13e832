package com.niceloo.cmc.ex.common;

/**
 *  MQ常量类
 * <AUTHOR>
 * @create: 2022-02-16 16:03
 */
public class MQConst {

    /**
     * 前缀,本地测试
     */
    private static final String WCY_PREFIX = "WCY_";
    private static final String DEFAULT_PREFIX = "";
    private static final String PREFIX = DEFAULT_PREFIX;

    /**
     * 通话记录同步 TOPIC
     */
    public static final String CALL_RECORD_TOPIC = PREFIX + "call_record_pull";

    /**
     * 通话记录回调 TOPIC
     */
    public static final String CALL_RECORD_CALLBACK_TOPIC = PREFIX + "CALL_RECORD_CALL_BACK";
    
    /**
     * 通话记录补充添加员工信息 TOPIC
     */
    public static final String ADD_EE_INFO_TOPIC = PREFIX + "ADD_EE_INFO_INDEX";

    /**
     * 生成通话记录 TOPIC[智能外呼打通电话后发送MQ]
     */
    public static final String GENERATE_CALL_RECORD_TOPIC = PREFIX + "GENERATE_CALL_RECORD";

    /**
     * 下载通话录音 TOPIC[下载通话记录的录音更新到ES]
     */
    public static final String DOWNLOAD_RECORDING_TOPIC = PREFIX + "DOWNLOAD_RECORDING";

    /**
     * 主动查询还没有补充完整的通话记录信息,延时队列 TOPIC
     */
    public static final String ACTIVE_QUERY_RECORD_TOPIC = PREFIX + "ACTIVE_QUERY_RECORD";

    /**
     * AI外呼任务追加客户信息TOPIC
     */
    public static final String ADD_CUSTOMER_INFO_TO_JOB_TOPIC = PREFIX + "ADD_CUSTOMER_INFO_TO_JOB";
    
    /**
     * AI外呼任务状态变更TOPIC
     */
    public static final String AI_TASK_STATUS_CHANGE_TOPIC = PREFIX + "AI_TASK_STATUS_CHANGE";

    /**
     * AI外呼任务追加百应客户信息TOPIC
     */
    public static final String ADD_CUSTOMER_INFO_TO_BY_JOB_TOPIC = PREFIX + "ADD_CUSTOMER_INFO_TO_BY_JOB";

    /**
     * 推送京东言犀的外呼通话记录回调TOPIC
     */
    public static final String CALL_JDYX_DETAIL_RESULT_TOPIC = DEFAULT_PREFIX + "call_jdyx_detailresult";

    /**
     * 推送京东言犀的任务状态变更回调TOPIC
     */
    public static final String CALL_JDYX_JOB_RESULT_TOPIC = DEFAULT_PREFIX + "call_jdyx_jobresult";

    /**
     * 推送百应的回调到客户营销TOPIC
     */
    public static final String CALL_BY_DETAIL_RESULT_TOPIC = DEFAULT_PREFIX + "call_by_detailresult";
}
