package com.niceloo.cmc.ex.common;

/**
 * @description: 业务常量类
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-16 17:27
 */
public class BizConst {

    /**
     * 重播中未呼叫号码优先功能的关闭状态
     */
    public static final Long REPEAT_NO_CALL_PRIORITY_OFF = 0L;
    /**
     * 重播中未呼叫号码优先功能的打开状态
     */
    public static final Long REPEAT_NO_CALL_PRIORITY_ON = 1L;

    /**
     * 通话记录 ES索引前缀
     */
    public static final String CALL_RECORD_ES_PREFIX = "call_record_";
    /**
     * 通话记录 ES索引前缀
     */
    public static final String CALL_RECORD_ES_ALL_PREFIXS = "call_record_*";
    /**
     * ES 分页搜索最大数据量: 十万
     */
    public static final int ES_SEARCH_SIZE = 10_0000;
    /**
     * 呼叫方式（0 呼出，1呼入）
     */
    public static final int CALL_IN = 1;
    public static final int CALL_OUT = 0;
    /**
     * 通话是否接通（Y，N）
     */
    public static final String CONNECTED_SUCCESS = "Y";
    public static final String CONNECTED_FAIL = "N";
    /**
     * 通话是否呼叫成功（Y，N）
     */
    public static final String CONNECT_SUCCESS_Y = "Y";
    public static final String CONNECT_SUCCESS_N = "N";
    /**
     * 是否有效通话（通话时长超过 30s）（Y，N）
     * 2024-09-06 业务老师任泉凯提出有效通话时长由20s改为30s
     */
    public static final String VALID_CALL_Y = "Y";
    public static final String VALID_CALL_N = "N";
    /**
     * 通话处理状态（D，N，R，Q，E，L，B，K，S，A，O）
     * dealing（已接听）、notDeal（振铃未接听，挂断）、refuse（被叫拒接）、queueLeak（排队放弃-呼入）、voicemail（已留言）、
     * leak（IVR放弃）、blackList（黑名单）、blank（空号）、shutdown（停机）、lineabnormal（线路问题未接通）、other（其他）
     */
    public static final String VOICE_DEALING = "D";
    public static final String VOICE_NO_DEAL = "N";
    public static final String VOICE_REFUSE = "R";
    public static final String VOICE_Q_LEAK = "Q";
    public static final String VOICE_EMAIL = "E";
    public static final String VOICE_LEAK = "L";
    public static final String VOICE_BLACK = "B";
    public static final String VOICE_BLANK = "K";
    public static final String VOICE_SHUTDOWN = "S";
    public static final String VOICE_LINE_ERROR = "A";
    public static final String VOICE_OTHER = "O";
    /**
     * 员工在职状态
     */
    public static final String WORK_ON = "O";
    public static final String WORK_LEAVED = "L";
    /**
     * 记录统计类型
     */
    public static final String TYPE_DAY = "D";
    public static final String TYPE_MONTH = "M";
    /**
     * 字符串连接符
     */
    public static final String LINK_SYMBOL = "~~~";
    /**
     * 补充通话记录的类型 M:手动,A:自动
     **/
    public static final String MANUAL = "M";
    public static final String AUTO = "A";
    /**
     * 操作通话记录类型：C->创建,U->更新
     */
    public static final String CREATE = "C";
    public static final String UPDATE = "U";
    /**
     * 从厂商查询通话记录每次数量
     */
    public static final int PAGE_SIZE = 100;
    /**
     * 通话记录ES的第一个索引
     */
    public static final String RECORD_INIT_DATE_STR = "2017-01-01 00:00:00";


    /**
     * 一分钟  毫秒
     */
    public static final long ONE_MINUTE = 60 * 1000L;
    /**
     * 默认的品牌
     */
    public static final String BRAND_YOULU = "YOULU";

    /**
     * AI外呼通话记录 ES索引前缀
     */
    public static final String AI_JOB_CUSTOMER_ES_PREFIX = "ai_job_customer_";

    /**
     * 新话务统计支持查询的最早时间
     */
    public static final String NEW_TRAFFIC_STATISTIC_FIRST_DATE_STR = "2022-10-01 00:00:00";

    /**
     * Excel下载的不同类型
     */
    // AI外呼任务客户列表明细Excel下载
    public static final String AI_JOB_DETAIL_EXCEL = "aiJobInfoDetail";
    // 话务分析Excel下载
    public static final String TRAFFIC_ANALYSIS_EXCEL = "trafficAnalysis";
    // 呼入/呼出话务排名Excel下载
    public static final String TRAFFIC_TOP_EXCEL = "trafficTop";

    // AI外呼任务类型 分校任务
    public static final String AI_SCHOOL_TASK = "1";
    // AI外呼任务类型 全国任务
    public static final String AI_ALL_TASK = "2";
    // AI外呼任务类型 学服任务
    public static final String AI_SS_TASK = "3";

    /**
     * 全国分校（环球优路）schoolId
     */
    public static final String TOP_SCHOOL_ID = "SCHOOL20190411010000000019";

    /**
     * 亿讯通话记录回调中的 service 对应的业务类型
     * 手拨呼叫
     */
    public static final String SERVICE_MANUAL_CALL = "1";

    /**
     * 亿讯通话记录回调中的 service 对应的业务类型
     * 呼入呼叫
     */
    public static final String SERVICE_INBOUND_CALL = "2";

    /**
     * 亿讯通话记录回调中的 service 对应的业务类型
     * 内部呼叫
     */
    public static final String SERVICE_INTERNAL_CALL = "3";

    /**
     * 亿讯通话记录回调中的 service 对应的业务类型
     * 自动外呼
     */
    public static final String SERVICE_AUTO_OUTBOUND = "4";

    /**
     * 亿讯通话记录回调中的 service 对应的业务类型
     * 点拨呼叫
     */
    public static final String SERVICE_CLICK_TO_CALL = "5";

    /**
     * 亿讯通话记录回调中的 service 对应的业务类型
     * 语音验证码
     */
    public static final String SERVICE_VOICE_VERIFICATION = "19";

    /**
     * 亿讯通话记录回调中的 service 对应的业务类型
     * 语音通知
     */
    public static final String SERVICE_VOICE_NOTIFICATION = "21";

    /**
     * 通话记录有效时间
     */
    public static final Integer EFFECTIVE_TIME = 30;

}
