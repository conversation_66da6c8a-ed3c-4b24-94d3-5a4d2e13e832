package com.niceloo.cmc.ex.common;


import static com.niceloo.cmc.ex.common.MenuCodeEnum.OperationTypeEnum.getEnum;

/**
 * 通讯中心外呼账号菜单code
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-29 14:06
 */
public enum CallAccountMenuCodeConst {
    JLFY_ACCOUNT_CHANNEL(CallChannelEnum.CALL_TYPE_JLFY, MenuCodeEnum.JLFY_CALL_ACCOUNT),
    JDYX_ACCOUNT_CHANNEL(CallChannelEnum.CALL_TYPE_JDYX, MenuCodeEnum.AI_CALL_ACCOUNT),
    ZHZX_ACCOUNT_CHANNEL(CallChannelEnum.CALL_TYPE_ZHZX, MenuCodeEnum.ZHZX_CALL_ACCOUNT);
    /**
     * 外呼类型
     */
    private final CallChannelEnum channelEnum;

    private final MenuCodeEnum menuCodeEnum;

    CallAccountMenuCodeConst(CallChannelEnum channelEnum, MenuCodeEnum menuCodeEnum) {
        this.channelEnum = channelEnum;
        this.menuCodeEnum = menuCodeEnum;
    }

    public CallChannelEnum getChannelEnum() {
        return channelEnum;
    }

    public MenuCodeEnum getMenuCodeEnum() {
        return menuCodeEnum;
    }


    public static String getMenuCode(MenuCodeEnum.OperationTypeEnum operationType, CallChannelEnum channelEnum) {
        CallAccountMenuCodeConst[] values = CallAccountMenuCodeConst.values();
        for (CallAccountMenuCodeConst callAccountMenuCodeConst : values) {
            if (channelEnum == callAccountMenuCodeConst.getChannelEnum()) {
                MenuCodeEnum menuCodeEnum = callAccountMenuCodeConst.getMenuCodeEnum();
                MenuCodeEnum.MenuCode menuEnum = menuCodeEnum.getMenuEnum();
                return menuEnum.getMenuCodeOperationType(operationType);
            }
        }
        return null;
    }

    /**
     * 获取外呼菜单code
     *
     * @param operationType 外呼账号操作类型
     *                      {@link MenuCodeEnum.OperationTypeEnum operationType}
     * @param channelType   外呼账号类型
     *                      {@link CallChannelEnum channelType}
     * @return java.lang.String
     * <AUTHOR>
     * @Date 17:56 2022/5/20
     **/
    public static String getMenuCode(Integer operationType, String channelType) {
        MenuCodeEnum.OperationTypeEnum operationEnum = getEnum(operationType);
        if (operationEnum == null) {
            return null;
        }
        CallChannelEnum callChannel = CallChannelEnum.getCallChannel(channelType);
        if (callChannel == null) {
            return null;
        }
        return getMenuCode(operationEnum, callChannel);
    }

}
