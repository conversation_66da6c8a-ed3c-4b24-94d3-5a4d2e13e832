package com.niceloo.cmc.ex.common;

/**
 * AI外呼任务状态枚举类
 * 任务状态（waiting 队列中, running 执行中, finished 已完成, paused 暂停中, canceled 已取消,stopped 已结束
 */
public enum AICallJobStatus {
    waiting("队列中"),
    running("执行中"),
    finished("已完成"),
    paused("暂停中"),
    canceled("已取消"),
    stopped("已结束");

    private final String desc;

    public String getDesc() {
        return desc;
    }

    AICallJobStatus(String desc) {
        this.desc = desc;
    }
}
