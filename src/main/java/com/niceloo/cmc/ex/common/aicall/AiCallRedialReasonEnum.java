package com.niceloo.cmc.ex.common.aicall;

import lombok.Getter;

/**
 * AI外呼需要重拨的通话状态
 * <AUTHOR>
 * @since 2024/11/25
 */
@Getter
public enum AiCallRedialReasonEnum {

    NEED_REDIAL("意向标签-需重拨", "NeedRedial", -1),
    CALL_LIMIT("呼叫受限", "CallLimit", -1),
    INTERCEPTED("呼出拦截", "Intercepted", -1),

    REJECT("拒接", "Reject", 1),
    UNAVAILABLE("无法接通", "Unavailabe", 2),
    OUTBOUND_FAIL("外呼失败", "OutboundFail", 3),
    DEAD("空号", "Dead", 4),
    CLOSED("关机", "Closed", 5),
    BUSY("占线", "Busy", 6),
    STOPPED("停机", "Stopped", 7),
    MISSED("未接/无人接听", "Missed", 8),
    CALLER_ARREARS("主叫欠费", "CallerArrears", 9),
    CONCURRENT_CALL_LOSS("并发呼损", "ConcurrentCallLoss", 10),
    NO_AVAILABLE_LINE("无可用线路", "NoAvailableLine", 25);

    private final String name;
    private final String code;
    private final Integer number;

    AiCallRedialReasonEnum(String name, String code, Integer number) {
        this.name = name;
        this.code = code;
        this.number = number;
    }

    public static AiCallRedialReasonEnum getByCode(String code) {
        for (AiCallRedialReasonEnum value : AiCallRedialReasonEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("AiCallRedialReasonEnum -- code:" + code + "不存在");
    }

}
