package com.niceloo.cmc.ex.common.aicall;

import lombok.Getter;

/**
 * 百应外呼回调类型
 * <AUTHOR>
 * @since 2024/11/28
 */
@Getter
public enum ByCallBackTypeEnum {

    CALL_INSTANCE_RESULT("外呼回调"),
    JOB_INFO_RESULT("任务状态回调"),
    USER_INTENTION_RESULT("意向等级回调"),
    INBOUND_CALL_INSTANCE_RESULT("呼入回调"),
    AICC_CS_INSTANCE_RESULT("人工外呼回调");

    private final String name;

    ByCallBackTypeEnum(String name) {
        this.name = name;
    }

}
