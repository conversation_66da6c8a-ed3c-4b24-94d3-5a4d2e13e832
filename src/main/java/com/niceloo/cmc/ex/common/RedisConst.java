package com.niceloo.cmc.ex.common;

/**
 * @description: Redis KEY常量类
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-16 17:15
 */
public class RedisConst {

    /**
     * 一天 毫秒
     */
    public static final long ONE_DAY_MILLIS = 24 * 60 * 60 * 1000L;
    /**
     * 一周 毫秒
     */
    public static final long ONE_WEEK_MILLIS = ONE_DAY_MILLIS * 7;

    /**
     * 缓存命名空间
     */
    public static final String CACHE_NAMESPACE = "NICELOO_CMC:";

    /**
     * AI外呼任务列表缓存key
     */
    public static final String AI_JOB_REPORT_LIST =  CACHE_NAMESPACE + "JOB_REPORT_LIST:";

    /**
     * 通话记录拉取key
     */
    public static final String CALL_RECORD_PULL_KEY = CACHE_NAMESPACE + "CALL_RECORD_PULL";

    /**
     * 今日实时统计 KEY,(原:call_record_realtime)
     */
    public static final String REAL_TIME_KEY = CACHE_NAMESPACE + "CALL_RECORD_REALTIME";
    /**
     * 客户数据补偿机制 KEY
     */
    public static final String CUST_FAIL_SET = "call_cust_set";
    /**
     * 手动补充通话记录的时间记录KEY前缀
     **/
    public static final String RECORD_SYNC_KEY = "record_check_recordSync";
    /**
     * 拉取通话记录数据失败缓存
     */
    public static final String FAILED_PULL_RECORD_REQUEST = CACHE_NAMESPACE + "FAILED_PULL_RECORD_REQUEST";
    /**
     * 员工账号缓存
     */
    public static final String EE_CALL_ACCOUNT = CACHE_NAMESPACE + "EE_CALL_ACCOUNT:";
    /**
     * 幂等校验
     */
    public static final String IDEMPOTENT_CHECK = CACHE_NAMESPACE + "IDEMPOTENT_CHECK:";
    /**
     * 重复拉取通话记录校验
     */
    public static final String DUPLICATE_RECORD_CHECK = CACHE_NAMESPACE + "DUPLICATE_RECORD_CHECK:";
    /**
     * OI一号互联认证信息
     */
    public static final String OI_TOKEN_CHECK = CACHE_NAMESPACE + "OI_TOKEN_CHECK";
    /**
     * 员工话务统计key
     */
    private static final String TRAFFIC_STATISTIC_KEY = CACHE_NAMESPACE + "TRAFFIC_STATISTIC:";
    /**
     * 员工话务日统计key
     */
    public static final String TRAFFIC_STATISTIC_DAY_KEY = TRAFFIC_STATISTIC_KEY + "DAY";
    /**
     * 员工话务月统计key
     */
    public static final String TRAFFIC_STATISTIC_MONTH_KEY = TRAFFIC_STATISTIC_KEY + "MONTH";
    /**
     * 员工话务统计失败存放 key
     */
    private static final String TRAFFIC_STATISTIC_ERROR_KEY = TRAFFIC_STATISTIC_KEY + "ERROR:";
    /**
     * 员工话务日统计失败存放 key
     */
    public static final String TRAFFIC_STATISTIC_ERROR_DAY_KEY = TRAFFIC_STATISTIC_ERROR_KEY + "DAY";
    /**
     * 员工话务月统计失败存放 key
     */
    public static final String TRAFFIC_STATISTIC_ERROR_MONTH_KEY = TRAFFIC_STATISTIC_ERROR_KEY + "MONTH";
    /**
     * 话务统计Excel下载 key
     */
    public static final String TRAFFIC_STATISTIC_GENERATE_EXCEL_KEY = TRAFFIC_STATISTIC_KEY + "EXCEL:";
    /**
     * 话务统计Excel下载申请但是未完成下载的 key
     */
    public static final String TRAFFIC_STATISTIC_APPLY_EXCEL_KEY = TRAFFIC_STATISTIC_KEY + "APPLY_EXCEL:";

    /**
     * 巨量飞鱼AXB外呼中间号的key
     */
    public static final String JLFY_VIRTUAL_NUMBER_KEY = CACHE_NAMESPACE +"JLFY_VIRTUAL_NUMBER";

    /**
     * ZK亿讯 主叫来电显的key
     * 通常为一个中间号，形如：9717386409
     * 可据此判断该条通话外呼通道的归属
     */
    // public static final String ZK_CALLER_DISPLAY_KEY = CACHE_NAMESPACE +"ZK_CALLER_DISPLAY";

    /**
     * 中弘智享验证码识别码vid的key
     */
    public static final String ZHZX_SMS_VID = CACHE_NAMESPACE +"ZHZX:SMS_VID";

    /**
     * 百应AI的ACCESS_TOKEN
     */
    public static final String BYAI_ACCESS_TOKEN = CACHE_NAMESPACE +"BYAI:ACCESS_TOKEN";
    /**
     * 百应AI的REFRESH_TOKEN
     */
    public static final String BYAI_REFRESH_TOKEN = CACHE_NAMESPACE +"BYAI:REFRESH_TOKEN";
}
