package com.niceloo.cmc.ex.common.aicall;

import lombok.Getter;

/**
 * AI外呼任务类型
 * <AUTHOR>
 * @since 2024/11/25
 */
@Getter
public enum AiCallJobTypeEnum {

    MANUAL("手动", "Manual", 2),
    TIMING("定时", "Timing", 1);

    private final String name;
    private final String code;
    private final Integer number;

    AiCallJobTypeEnum(String name, String code, Integer number) {
        this.name = name;
        this.code = code;
        this.number = number;
    }

    public static AiCallJobTypeEnum getByCode(String code) {
        for (AiCallJobTypeEnum value : AiCallJobTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("AiCallJobTypeEnum -- code:" + code + "不存在");
    }

}
