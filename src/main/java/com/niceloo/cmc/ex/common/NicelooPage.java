package com.niceloo.cmc.ex.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Setter;

/**
 * 分页请求
 * <AUTHOR>
 * @date 2021/1/6 9:34
 */
public class NicelooPage<T> extends Page<T> {

    @Setter
    private int pageIndex;

    public NicelooPage() {
    }

    public NicelooPage(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public NicelooPage(int size, int pageIndex) {
        this.pageIndex = pageIndex;
        super.size = size;
    }

    @Override
    public long offset() {
        return pageIndex;
    }
}
