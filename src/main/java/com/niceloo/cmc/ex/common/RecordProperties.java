package com.niceloo.cmc.ex.common;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class RecordProperties {

    /**
     * 并行处理线程数
     */
    @Value("${record.threadCount}")
    private Integer threadCount = 5;

    /**
     * 批次处理记录数
     */
    @Value("${record.batchSize}")
    private Integer batchSize = 500;

    /**
     * 滚动查询超时时间（单位：分钟）
     */
    @Value("${record.scrollTimeout}")
    private Long scrollTimeout = 30L;

}
