package com.niceloo.cmc.ex;

import com.niceloo.framework.job.EnableNicelooJob;
import com.niceloo.mq.client.spring.starter.rabbit.EnableRabbitMqClientAutoConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * 通讯中心启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = MybatisAutoConfiguration.class)
@MapperScan("com.niceloo.cmc.ex.mapper")
@EnableRabbitMqClientAutoConfiguration
@EnableNicelooJob(datasource = "cmc")
@EnableFeignClients(basePackages = {"com.niceloo.auth.service", "com.niceloo.cmc.ex.feign"})
public class CommuCenterApplication {
    public static ConfigurableApplicationContext applicationContext;
    public static void main(String[] args) {
        applicationContext = SpringApplication.run(CommuCenterApplication.class);
    }
}
