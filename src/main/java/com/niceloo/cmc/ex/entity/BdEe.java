package com.niceloo.cmc.ex.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdEe implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工标识
     */
    private String eeId;

    /**
     * 用户标识
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 分校标识
     */
    private String schoolId;

    /**
     * 员工编号
     */
    private String eeNo;

    /**
     * 员工正式状态(O:正式;P:试用)
     */
    private String eeOfficialstatus;

    /**
     * 员工权限
     */
    private String eePermission;

    /**
     * 员工权重等级
     */
    private String eeWeightgrade;

    /**
     * 员工在职状态(O:在职;L:离职;W:预入职)
     */
    private String eeWorkstatus;

    /**
     * 员工Tq账号
     */
    private String eeTqaccount;

    /**
     * 员工Tq密码
     */
    private String eeTqpassword;

    /**
     * 员工风云账号
     */
    private String eeFyaccount;

    /**
     * 员工风云密码
     */
    private String eeFypassword;

    /**
     * 员工外呼方式(T:TQ;F:风云)
     */
    private String eeCallouttype;

    /**
     * 员工云客手机外呼可用状态(Y:可用;N:不可用)
     */
    private String eeYkphonestatus;

    /**
     * 员工云客软电话外呼可用状态(Y:可用;N:不可用)
     */
    private String eeYksoftphonestatus;

    /**
     * 员工业绩核算状态(Y:核算;N:不核算)
     */
    private String eeAchvstatus;

    /**
     * 员工教学身份(C:班主任;T:教研老师)
     */
    private String eeEdutype;

    /**
     * 员工基本工资
     */
    private Long eeSalarybaseamount;

    /**
     * 员工最低工资
     */
    private Long eeSalaryminamount;

    /**
     * 员工浮动工资
     */
    private Long eeSalaryfloatamount;

    /**
     * 员工订单是否达标(Y:达标;N:不达标)
     */
    private String eeOrdersuccessnum;

    /**
     * 员工订单未达标数
     */
    private Integer eeOrderfailnum;

    /**
     * 员工联系电话(企业微信)
     */
    private String eePhone;

    /**
     * 员工内部手机号(销售使用)
     */
    private String eeInnerphone;

    /**
     * 员工企业邮箱
     */
    private String eeCompanyemail;

    /**
     * 员工企业微信
     */
    private String eeCompanywechat;

    /**
     * 员工现住地址
     */
    private String eeAdress;

    /**
     * 员工入职日期
     */
    private String eeHiredate;

    /**
     * 员工离职日期
     */
    private String eeTermdate;

    /**
     * 员工备注
     */
    private String eeMemo;

    /**
     * 员工可用状态(Y:可用;N:不可用)
     */
    private String eeAvlstatus;

    /**
     * 员工删除状态(Y:删除;N:未删除)
     */
    private String eeDelstatus;

    /**
     * 员工创建人
     */
    private String eeCreater;

    /**
     * 员工创建时间
     */
    private String eeCreateddate;

    /**
     * 员工修改人
     */
    private String eeModifier;

    /**
     * 员工修改时间
     */
    private String eeModifieddate;

    /**
     * 员工来源标识
     */
    private String eeSourceid;

    /**
     * 员工角色冗余
     */
    private String eeRoles;

    /**
     * 员工品牌
     */
    private String eeBrands;

    /**
     * 员工姓名简拼
     */
    private String eeNamesimple;

    /**
     * HR系统分校ID
     */
    private String eeHrschoolid;

    /**
     * HR系统分校名称
     */
    private String eeHrschoolname;

    /**
     * HR系统部门ID
     */
    private String eeHrdptid;

    /**
     * HR系统部门名称
     */
    private String eeHrdptname;

    /**
     * 员工职级(P:校长;M:经理;S:主管;E:员工)
     */
    private String eePostype;

    /**
     * 职级编码（EHR）
     */
    private String eeRankcode;

    /**
     * 职级名称（EHR）
     */
    private String eeRankname;

    /**
     * 员工上级编号
     */
    private String eeParentno;

    /**
     * 员工百应账号可用状态(Y:可用;N:不可用)
     */
    private String eeByavlstatus;

    /**
     * 员工百应账号
     */
    private String eeByaccount;

    /**
     * EHR状态(I:优路员工;D:疑似代理商;G:废弃;R:重复在职;M:人工处理;S:系统账号;)
     */
    private String eeEhrstatus;

    /**
     * 员工类型(1正式工 2实习生 5劳务人员 6劳务派遣 7退休返聘 8兼职)
     */
    private String eeUsertype;

    /**
     * 职级类别名称编码
     */
    private String postLeveltypecode;

    /**
     * 预计转正日期
     */
    private String expectedDate;

    /**
     * 实际转正日期
     */
    private String actualDate;

    /**
     * 岗位标签编码
     */
    private String posSortTagid;

    /**
     * 岗位序列编码
     */
    private String serialId;


}
