package com.niceloo.cmc.ex.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 部门
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdDpt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门标识
     */
    private String dptId;

    /**
     * 部门名称
     */
    private String dptName;

    /**
     * 部门编码
     */
    private String dptCode;

    /**
     * 部门层级编码
     */
    private String dptLevelcode;

    /**
     * 部门类型(D:部门;S:分校;A:大区;C:全国)
     */
    private String dptType;

    /**
     * 部门关联对象标识(类型为分校时存储分校标识)
     */
    private String dptRelationid;

    /**
     * 部门顺序
     */
    private Integer dptSeq;

    /**
     * 部门创建时间
     */
    private String dptCreateddate;

    /**
     * 部门创建人
     */
    private String dptCreater;

    /**
     * 部门修改人
     */
    private String dptModifier;

    /**
     * 部门修改时间
     */
    private String dptModifieddate;

    /**
     * 部门启用状态(Y:可用;N:不可用)
     */
    private String dptAvlstatus;

    /**
     * 部门删除状态(Y:已删除;N:未删除)
     */
    private String dptDelstatus;

    /**
     * 部门来源标识
     */
    private String dptSourceid;

    /**
     * 部门标识(人事)
     */
    private String dptHrid;

    /**
     * 部门父级标识(人事)
     */
    private String dptParenthrid;

    /**
     * 部门负责人标识(人事)
     */
    private String dptManagerhrid;

    /**
     * 部门层级类型(人事)
     */
    private String dptTypehr;

    /**
     * 部门路径(人事)
     */
    private String dptPath;

    /**
     * 部门备注
     */
    private String dptMemo;

    /**
     * 部门开始时间
     */
    private String dptStartdate;

    /**
     * 部门结束时间
     */
    private String dptEnddate;

    private String dptEhrstatus;

    /**
     * 组织类型标签
     */
    private String dptTypetag;

    /**
     * 部门组织层级
     */
    private String dptHierarchy;

    /**
     * 部门路径编码
     */
    private String dptPathcode;

    /**
     * 部门所在地区
     */
    private String dptAddress;


}
