package com.niceloo.cmc.ex.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niceloo.segment.annotation.Id4yl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 分校账户余额表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31 10:31:47
 */
@Data
@TableName("CcSchoolBalance")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CcSchoolBalance对象", description = "分校账户余额表")
public class CcSchoolBalance implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计标识
     */
    @ApiModelProperty(value = "余额标识")
    @TableId("balanceId")
    @Id4yl(tag = "CcSchoolBalance")
    private String balanceId;

    /**
     * 分校标识
     */
    @ApiModelProperty(value = "分校标识")
    @TableField("schoolId")
    private String schoolId;

    /**
     * 渠道类型（ZHZX：中弘智享， ZK：亿讯）
     */
    @ApiModelProperty(value = "渠道类型（ZHZX：中弘智享， ZK：亿讯）")
    @TableField("channelType")
    private String channelType;

    /**
     * 月消费（元）
     */
    @ApiModelProperty(value = "月消费（元）")
    @TableField("monthlySpending")
    private Double monthlySpending;

    /**
     * 日消费（元）
     */
    @ApiModelProperty(value = "日消费（元）")
    @TableField("dailySpending")
    private Double dailySpending;

    /**
     * 账户余额（元）
     */
    @ApiModelProperty(value = "账户余额（元）")
    @TableField("accountBalance")
    private Double accountBalance;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    @TableField("createdDate")
    private String createdDate;

    /**
     * 统计日，形如：********
     */
    @ApiModelProperty(value = "统计日，形如：********")
    @TableField("countDay")
    private String countDay;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField("creator")
    private String creator;

    /**
     * 修改日期
     */
    @ApiModelProperty(value = "修改日期")
    @TableField("modifiedDate")
    private String modifiedDate;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField("modifier")
    private String modifier;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

}
