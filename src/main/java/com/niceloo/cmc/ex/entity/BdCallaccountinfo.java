package com.niceloo.cmc.ex.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 通讯厂商开放API对接密钥
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdCallaccountinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键标识
     */
    private String callaccountinfoId;

    /**
     * 部门层级编码
     */
    private String dptLevelcode;

    /**
     * 账号类型（ZK:亿讯外呼;YH:亿讯回拨）
     */
    private String accountType;

    /**
     * 账号标识
     */
    private String apiAccount;

    /**
     * 账号密码
     */
    private String apiSecret;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 账号备注
     */
    private String accountMemo;

    /**
     * 删除状态(Y:已删除;N:未删除)
     */
    private String accountDelstatus;

    /**
     * 创建人
     */
    private String accountCreator;

    /**
     * 创建时间
     */
    private String accountCreateddate;

    /**
     * 修改人
     */
    private String accountModifier;

    /**
     * 修改时间
     */
    private String accountModifieddate;


}
