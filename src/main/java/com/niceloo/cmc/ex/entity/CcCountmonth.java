package com.niceloo.cmc.ex.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.niceloo.cmc.ex.config.CompensateConfig;
import com.niceloo.segment.annotation.Id4yl;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <p>
 * 话务统计表(月)
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("CcCountmonth")
public class CcCountmonth implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计标识
     */
    @TableId
    @Id4yl(tag = "COUNT", customCompensationClass = CompensateConfig.class)
    private String countId;

    /**
     * 拨打人姓名
     */
    private String countcallerName;

    /**
     * 拨打人用户标识
     */
    private String countcallerUserId;

    /**
     * 拨打人部门标识
     */
    private String countdptId;

    /**
     * 拨打人所属分校
     */
    private String countschoolId;

    /**
     * 拨打人呼叫账号
     */
    private String countcallAccount;

    /**
     * 呼入个数
     */
    private Integer countcallinNum;

    /**
     * 呼入成功个数
     */
    private Integer countcallinSuccessNum;

    /**
     * 呼入通话个数
     */
    private Integer countinConnectNum;

    /**
     * 呼出个数
     */
    private Integer countcalloutNum;

    /**
     * 呼出成功个数
     */
    private Integer countcalloutSuccessNum;

    /**
     * 呼出通话个数
     */
    private Integer countoutConnectNum;

    /**
     * 总有效通话个数
     */
    private Integer countvalidCallNum;

    /**
     * 总通话总时长(秒)
     */
    private BigInteger countdurationTotal;

    /**
     * 总平均通话时长(秒)
     */
    private BigDecimal countdurationAvg;

    /**
     * 呼入通话总时长(秒)
     */
    private BigInteger countinDurationTotal;

    /**
     * 外呼通话总时长(秒)
     */
    private BigInteger countoutDurationTotal;

    /**
     * 平均日通话时长(秒)
     */
    private BigDecimal countdurationDayAvg;

    /**
     * 呼出成功率
     */
    private BigDecimal countcalloutSuccess;

    /**
     * 总接通率
     */
    private BigDecimal countconnectSuccess;

    /**
     * 总有效沟通率
     */
    private BigDecimal countvalidCall;

    /**
     * 呼入接通率
     */
    private BigDecimal countinConnectSuccess;

    /**
     * 外呼接通率
     */
    private BigDecimal countoutConnectSuccess;

    /**
     * 呼叫通道(TQ:TQ手机;TF:TQ固话;FS:风云;YP:云客手机;YS:云客软电话;ZK:中科云软电话;JLFY:巨量飞鱼)
     */
    private String countchannelType;

    /**
     * 统计时间
     */
    private String countcreatedTime;

    /**
     * 修改时间
     */
    private String countmodifiedTime;

    /**
     * 是否加密，加密为1
     */
    private String encryptedFlag;

}
