package com.niceloo.cmc.ex.entity.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * @description: ES通话录音实体类
 * @author: WangChenyu
 * @create: 2022-02-14 16:19
 */
@Data
@Document(indexName = "call_recording", type = "_doc", shards = 5, replicas = 1)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CallRecording {

    /**
     * ID/[也为call_record_xxxxxx索引ID]
     */
    @Id
    @Field(type = FieldType.Keyword)
    private String callId;

    /**
     * 厂商返回的录音地址
     */
    @Field(type = FieldType.Keyword)
    private String voiceSourceUrl;

    /**
     * 通话时长(单位秒)
     */
    @Field(type = FieldType.Integer)
    private Integer duration;

    /**
     * 下载到服务器本地的路径[下载成功，上传失败时会存储字段]
     */
    @Field(type = FieldType.Keyword)
    private String voicePath;

    /**
     * 外呼类型
     */
    @Field(type = FieldType.Keyword)
    private String channelType;

    /**
     * 操作标志: 0->未上传，1->上传失败,(-1)->厂商还没有返回录音地址,定时任务查询
     */
    @Field(type = FieldType.Integer)
    private Integer operationSign;

    /**
     * 尝试次数 下载或上传尝试次数超过十次做另处理
     */
    @Field(type = FieldType.Integer)
    private Integer attempts;

    /**
     * 通话记录所属年月 xxxx-xx
     */
    @Field(type = FieldType.Keyword)
    private String date;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date modifierTime;


    public static CallRecording initialize(){
        CallRecording callRecordingBo = new CallRecording();
        callRecordingBo.setAttempts(0);
        callRecordingBo.setOperationSign(0);
        callRecordingBo.setVoicePath("");
        callRecordingBo.setCreateTime(new Date());
        callRecordingBo.setModifierTime(callRecordingBo.getCreateTime());
        return callRecordingBo;
    }
}
