package com.niceloo.cmc.ex.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CtCust implements Serializable {

    private static final long serialVersionUID = 1L;

    private String custId;

    private String custName;

    private String userId;

    private String custMobile;

    private String custMobileareacode;

    private String custAreacode;

    private String custTel;

    private String custCreateddate;

    private String custModifieddate;

}
