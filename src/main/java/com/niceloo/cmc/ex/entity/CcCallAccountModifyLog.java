package com.niceloo.cmc.ex.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 外呼账号操作日志记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("CcCallAccountModifyLog")
public class CcCallAccountModifyLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外呼账号操作记录主键ID
     */
    @TableId
    private String callAccountLogId;

    /**
     * 外呼账号配置表对应账号id
     */
    private String callAccountId;

    /**
     * 操作类型(1->创建,2->解绑,3->换绑,4->绑定,6->批量解绑,7->启用,8->禁用)
     */
    private Integer operationType;

    /**
     * 操作人IP地址
     */
    private String operationIp;

    /**
     * 操作前账号所属员工id
     */
    private String beforeEeUserId;

    /**
     * 操作后账号所属员工id
     */
    private String afterEeUserId;

    /**
     * 操作前账号所属员工名称
     */
    private String beforeEeUserName;

    /**
     * 操作后账号所属员工名称
     */
    private String afterEeUserName;

    /**
     * 操作前账号所属员工分校id
     */
    private String beforeEeSchoolId;

    /**
     * 操作后账号所属员工分校id
     */
    private String afterEeSchoolId;

    /**
     * 操作前账号所属员工分校名称
     */
    private String beforeEeSchoolName;

    /**
     * 操作后账号所属员工分校名称
     */
    private String afterEeSchoolName;

    /**
     * 创建人(操作人)userId
     */
    private String accountLogCreator;

    /**
     * 创建人(操作人)名称
     */
    private String accountLogCreatorName;

    /**
     * 创建时间(操作时间)
     */
    private String accountLogCreateddate;


}
