package com.niceloo.cmc.ex.entity.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.Setter;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @description: ES通话记录实体类
 * @author: WangChenyu
 * @create: 2022-02-14 16:40
 */
@Setter
@Getter
// 只格式化有值的数据
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CallRecord {

    /**
     * 自定义生成的通话记录标识，主键
     */
    private String callId;

    /**
     * 呼叫人用户标识
     */
    private String callerUserId;

    /**
     * 呼叫人名称
     */
    private String callerName;

    /**
     * 呼叫人eeInnerphone: 员工内部手机号(销售使用),从BdEe表获取
     */
    private String callerPhone;

    /**
     * 呼叫人呼叫账号
     */
    private String callAccount;

    /**
     * 呼叫人主叫手机号
     */
    private String callPhone;

    /**
     * 呼叫人部门标识
     */
    private String dptId;

    /**
     * 呼叫人部门名称
     */
    private String dptName;

    /**
     * 呼叫人座席号
     */
    private String agentId;

    /**
     * 呼叫人座席电话
     */
    private String agentPhone;

    /**
     * 接听人用户标识
     */
    private String reciverUserId;

    /**
     * 接听人基础数据用户标识
     */
    private String reciverbdUserId;

    /**
     * 接听人用户名称
     */
    private String reciverName;

    /**
     * 接听人手机号
     */
    private String reciverPhone;

    /**
     * 号码归属地编码
     */
    private String areaCode;

    /**
     * 号码归属地名称
     */
    private String areaName;

    /**
     * 接听人用户类型（E 员工，S 学员，C 客户，O 其他）
     */
    private String reciverType;

    /**
     * 员工分校标识
     */
    private String schoolId;

    /**
     * 员工分校名称
     */

    private String schoolName;

    /**
     * 报考项目标识
     */

    private String projectId;

    /**
     * 报考项目名称
     */
    private String projectName;

    /**
     * 呼叫方式（0 呼出，1呼入）
     */
    private Integer callType;

    /**
     * 呼叫时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callTime;

    /**
     * 通话时长(单位秒)[-1表示通话信息还没有同步,缺少通话时长]
     */
    private Integer duration;

    /**
     * 通话满意度（A，B，C）
     */
    private String satisfaction;

    /**
     * 通话是否接通（Y，N）
     */
    private String isConnected;

    /**
     * 通话是否呼叫成功（Y，N）
     */

    private String isConnectSuccess;

    /**
     * 是否有效通话（通话时长超过 20s）（Y，N）
     */
    private String isValidCall;

    /**
     * 录音 URL 链接
     */
    private String voiceSourceUrl;

    /**
     * 下载到本地的录音 URL 链接[已不使用]
     */
    private String voiceUrl;

    /**
     * 通话记录来源id（通话记录唯一标识）
     */
    private String voiceSourceId;

    /**
     * 录音同步状态(0无录音地址,1有录音地址未开始下载,其他大于1的数值表示已下载次数+1）[不包含云客和亿迅、巨量引擎]
     */
    private Integer voiceSyncStatus;

    /**
     * 通话处理状态（D，N，Q，E，L，B，K，O）
     * dealing（已接听）、notDeal（振铃未接听，挂断）、queueLeak（排队放弃-呼入）、voicemail（已留言）、 leak（IVR放弃）、blackList（黑名单）、blank（空号）、other（其他）
     */
    private String voiceStatus;

    /**
     * 备注内容（包含留言）
     */
    private String remarks;

    /**
     * 沟通意向（A,B,C）
     */
    private String commuIntention;

    /**
     * 意向描述
     */
    private String intentionMsg;

    /**
     * 挂机方（1：座席侧  2：客户侧  3：系统挂机）
     */
    private Integer hangUp;

    /**
     * 通道类型（TQ:TQ，FS:风云，YP 云客手机，YS 云客软电话，ZK 中科云软电话，OI 一号互联，JLFY 巨量飞鱼）
     */
    private String channelType;

    /**
     * 数据补全状态（N 未补全，Y 已补全）
     */
    private String dataCompleteStatus;

    /**
     * ali bucket 阿里云录音文件地址
     */
    private String serverFolder;

    /**
     * 录音下载完成状态（N 未完成，Y 已完成）
     */
    private String downloadCompleteStatus;

    /**
     * 录音下载时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date downloadTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedTime;

    /**
     * 阿里 OSS 文件 key（下载使用）
     */
    private String field1;

    /**
     * 外呼时返回的 id(TQ:client_id,FY:ActionID,YK:callId,YX:userdata,JLFY:contactId,OI:"")
     */
    private String field2;

    /**
     * tq使用的电话途径(1：呼叫中心-固话 2：工作手机 3：免费电话 4：400电话 5：SIP电话)
     * 亿讯服务类型(21:亿讯提供的回拨专用服务产生的回拨通话记录)
     */
    private String field3;
    public static final String CALL_TYPE_YH_FIELD3 = "21";
    public static final String CALL_TYPE_TQ_FIX_FIELD3 = "1";
    public static final String CALL_TYPE_TQ_MOBILE_FIELD3 = "2";
    /**
     * 亿讯:存储API对接账号
     */
    private String field4;

    /**
     * 扩展字段:
     * <p>1、存储一号互联通话记录task码</p>
     * <p>2、存储巨量飞鱼广告主id|2022年5月28日16:05:25</p>
     */
    private String field5;

    /**
     * 扩展字段:
     * <p>1、session: 亿迅话单唯一标识[2021年12月28日11:30:57]</p>
     * <p/2、存储巨量飞鱼线索id[2022年5月28日16:06:12]</p>
     */
    private String field6;
    /**
     * 数据同步时间(记录各个厂商获取到通话回执的时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dataSyncTime;

    /**
     * 创建索引时,获取通话记录的mapping映射关系
     *
     * @return java.lang.String
     * @paramter
     * <AUTHOR>
     * @Date 17:31 2022/2/14
     **/
    public static String getCallRecordMapping() {
        Map<String, Map<String, Object>> properties = new HashMap<>(60);
        for(Field field : CallRecord.class.getDeclaredFields()){
            if (Modifier.isStatic(field.getModifiers())) {
                continue;
            }
            Type genericType = field.getGenericType();
            if(String.class == genericType){
                Map<String, Object> map = new HashMap<>();
                if ((field.getName().endsWith("Name") && !"reciverName".equals(field.getName())) || field.getName().endsWith("Large")) {
                    map.put("type", "text");
                    map.put("analyzer", "ik_max_word");
                } else {
                    map.put("type", "keyword");
                    // 可能需要使用到聚合功能的字段不要关闭列索引
                    String aggregationsFieldList = "channelType,callerUserId,reciverbdUserId,voiceStatus,callerPhone,callAccount,schoolId,reciverName,reciverUserId,reciverPhone,callPhone,dptId,downloadCompleteStatus";
                    if (!aggregationsFieldList.contains(field.getName())){
                        map.put("doc_values", false);
                    }
                }
                properties.put(field.getName(), map);
            }
            if (Date.class == genericType) {
                Map<String, Object> map = new HashMap<>();
                map.put("type", "date");
                map.put("format", "yyyy-MM-dd HH:mm:ss");
                properties.put(field.getName(), map);
            }
            if (Integer.class == genericType) {
                Map<String, Object> map = new HashMap<>();
                map.put("type", "integer");
                properties.put(field.getName(), map);
            }
        }
        Map<String, Object> map = new HashMap<>(1);
        map.put("properties", properties);
        return new Gson().toJson(map);
    }

    /**
     * 大多数厂商都没有值的字段赋值,如果有厂商需要赋值下面某些字段可以进行外部覆盖赋值
     *
     * @param callRecord 通话记录
     * <AUTHOR>
     * @Date 10:24 2022/6/11
     **/
    public static void defaultValue(CallRecord callRecord) {
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setCreatedTime(new Date());
        callRecord.setModifiedTime(callRecord.getCreatedTime());
        callRecord.setModifier("SYSTEM");
        callRecord.setRemarks("");
        callRecord.setIntentionMsg("");
        callRecord.setServerFolder("");
        callRecord.setField1("");
        callRecord.setServerFolder("");
        callRecord.setDataCompleteStatus("N");
        callRecord.setCommuIntention("");
        callRecord.setSatisfaction("");
        callRecord.setProjectId("");
        callRecord.setProjectName("");
        callRecord.setReciverType("");
        callRecord.setAgentId("");
        callRecord.setAgentPhone("");
        callRecord.setVoiceSyncStatus(0);
        callRecord.setVoiceSourceUrl("");
        callRecord.setVoiceUrl("");
    }
}
