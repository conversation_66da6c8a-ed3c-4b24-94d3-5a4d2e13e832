package com.niceloo.cmc.ex.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * AI外呼任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("CcAiCallJob")
public class CcAiCallJob implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("jobId")
    private String jobId;

    /**
     * 父任务id
     */
    @TableField("parentJobId")
    private String parentJobId;

    /**
     * 任务等级(0->父任务,1->子任务)
     */
    @TableField("jobLevel")
    private Integer jobLevel;

    /**
     * 任务进度(1->已创建,2->同步客户,3->创建失败,4->已完成)
     */
    @TableField("jobProgress")
    private Integer jobProgress;

    /**
     * 任务所属分校id
     */
    @TableField("jobSchoolId")
    private String jobSchoolId;

    /**
     * 任务所属分校名称
     */
    @TableField("jobSchoolName")
    private String jobSchoolName;

    /**
     * 外呼通道类型(京东言犀->JDYX,百应->BYAI)
     */
    @TableField("aiJobType")
    private String aiJobType;

    /**
     * 第三方厂商 任务id
     */
    @TableField("vendorJobId")
    private Integer vendorJobId;

    /**
     * 任务名称
     */
    @TableField("jobName")
    private String jobName;

    /**
     * 外呼job任务描述
     */
    @TableField("description")
    private String description;

    /**
     * 外呼时段星期列表[(1-7),多个星期用逗号隔开]
     */
    @TableField("jobOfflineWeek")
    private String jobOfflineWeek;

    /**
     * 启动模式(Manual->手动、Timing->定时),不传默认手动
     */
    @TableField("startupMode")
    private String startupMode;

    /**
     * 任务的启动时间
     */
    @TableField("startTime")
    private String startTime;

    /**
     * 时段_开始时分秒
     */
    @TableField("timeBegin")
    private String timeBegin;

    /**
     * 时段_结束时分秒
     */
    @TableField("timeEnd")
    private String timeEnd;

    /**
     * 坐席并发数量
     */
    @TableField("concurrency")
    private Integer concurrency;

    /**
     * 线路id
     */
    @TableField("lineId")
    private Long lineId;

    /**
     * 线路名称
     */
    @TableField("lineName")
    private String lineName;

    /**
     * 外显号码
     */
    @TableField("displayNumber")
    private String displayNumber;

    /**
     * 机器人id
     */
    @TableField("botId")
    private Integer botId;

    /**
     * 租户id
     */
    @TableField("tenantId")
    private String tenantId;

    /**
     * 创建人使用账号
     */
    @TableField("userPin")
    private String userPin;

    /**
     * 话术模板id
     */
    @TableField("contextId")
    private Integer contextId;

    /**
     * 话术模板名称
     */
    @TableField("contextName")
    private String contextName;

    /**
     * 是否是重拨任务(0->不重拨,1->重播)
     */
    @TableField("isRedial")
    private Integer isRedial;

    /**
     * 自动重拨次数取值范围: [1~5]
     */
    @TableField("redialTimes")
    private Integer redialTimes;

    /**
     * 重拨间隔（分钟）,京东言犀: [1~1440], 百应:[0-120]
     */
    @TableField("redialInterval")
    private Integer redialInterval;

    /**
     * 重拨的原因(多个原因之间用竖线分隔)
     */
    @TableField("redialReason")
    private String redialReason;

    /**
     * 任务状态（waiting 队列中, running 执行中, finished 已完成, paused 暂停中, canceled 已取消, stopped 已结束）
     */
    @TableField("status")
    private String status;

    /**
     * 外呼项目
     */
    @TableField("callProject")
    private String callProject;

    /**
     * 挖掘项目
     */
    @TableField("exploitProject")
    private String exploitProject;

    /**
     * 任务进度
     */
    @TableField("outboundProcess")
    private Double outboundProcess;

    /**
     * 接通数量
     */
    @TableField("connectedTaskNum")
    private Integer connectedTaskNum;

    /**
     * 客户总数量
     */
    @TableField("totalTaskNum")
    private Integer totalTaskNum;

    /**
     * 创建人的部门id
     */
    @TableField("dptId")
    private String dptId;

    /**
     * 创建人所属分校id
     */
    @TableField("schoolId")
    private String schoolId;

    /**
     * 创建人所属分校名称
     */
    @TableField("schoolName")
    private String schoolName;

    /**
     * 创建人userId
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建人名称
     */
    @TableField("creatorName")
    private String creatorName;

    /**
     * 创建时间
     */
    @TableField("createDate")
    private String createDate;

    /**
     * 更新人userId
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 更新人名称
     */
    @TableField("modifyName")
    private String modifyName;

    /**
     * 更新时间
     */
    @TableField("modifyDate")
    private String modifyDate;

    /**
     * 异常信息
     */
    @TableField("errorLog")
    private String errorLog;

    /**
     * 客户索引名称
     */
    @TableField("customerIndex")
    private String customerIndex;

    /**
     * 客户添加状态(Y->添加完成,N->进行中)
     */
    @TableField("customerAddStatus")
    private String customerAddStatus;

    /**
     * 外呼任务类型(1->分校任务,2->全国任务,3->学服任务)
     */
    @TableField("callTaskType")
    private String callTaskType;

    /**
     * 百应天盾策略组id
     */
    @TableField("tiandunRuleStrategyGroupId")
    private Integer tiandunRuleStrategyGroupId;

    /**
     * 百应天盾策略组名
     */
    @TableField("tiandunRuleStrategyGroupName")
    private String tiandunRuleStrategyGroupName;

    /**
     * 任务优先级，数字越大任务优先级越高(1-10)
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 公司ID
     */
    @TableField("companyId")
    private Long companyId;
}
