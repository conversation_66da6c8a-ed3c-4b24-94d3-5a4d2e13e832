package com.niceloo.cmc.ex.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 外呼账号配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("CcCallAccountConfig")
public class CcCallAccountConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外呼账号主键ID
     */
    @TableId
    private String callAccountId;

    /**
     * 外呼账号所属类型(JLFY->巨量飞鱼,ZHZX->中弘智享,JDYX->京东言犀AI外呼)
     */
    private String channelType;

    /**
     * 外呼账号所属分校id
     */
    private String schoolId;

    /**
     * 外呼账号所属部门id
     */
    private String dptId;

    /**
     * 外呼账号所属分校名称
     */
    private String schoolName;

    /**
     * 外呼账号对应员工表userId
     */
    private String eeUserId;

    /**
     * 员工编号
     */
    private String eeNo;

    /**
     * 员工姓名
     */
    private String eeUserName;

    /**
     * 外呼账号
     */
    private String account;

    /**
     * 外呼账号密码(不需要设为空字符串)
     */
    private String password;

    /**
     * 账号绑定员工状态(N->未绑定，Y->已绑定)
     */
    private String bindStatus;
    
    /**
     * 禁用状态(Y->禁用,N->启用)
     */
    private String disableStatus;

    /**
     * 外部厂商该账号状态(N->不可用,Y->可用)
     */
    private String vendorAccountStatus;

    /**
     * 外部厂商账号存储的信息JSON字符串
     */
    private String vendorAccountInfo;

    /**
     * 删除状态(Y:已删除;N:未删除)
     */
    private String callDelstatus;

    /**
     * 创建人userId
     */
    private String accountCreator;

    /**
     * 创建人名称
     */
    private String accountCreatorName;
    /**
     * 创建时间
     */
    private String accountCreateddate;

    /**
     * 修改人userId
     */
    private String accountModifier;

    /**
     * 修改人名称
     */
    private String accountModifierName;

    /**
     * 修改时间
     */
    private String accountModifieddate;

    /**
     * 租户ID=商户ID，是确定企业身份账号的唯一标识
     */
    private String tenantId;


}
