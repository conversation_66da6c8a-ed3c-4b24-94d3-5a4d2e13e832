package com.niceloo.cmc.ex.entity.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.json.JSONUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * AI外呼任务下的客户信息实体类
 *
 * <AUTHOR>
 * @since 2022-08-22 17:07
 */
@Setter
@Getter
// 只格式化有值的数据
@JsonInclude(JsonInclude.Include.NON_NULL)
@Document(indexName = "ai_job_customer", type = "_doc", shards = 5, replicas = 1)
public class AICallJobCustomerInfo {

    /**
     * 主键id(唯一id)
     */
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    /**
     * 百应companyId
     */
    @Field(type = FieldType.Keyword)
    private String companyId;

    /**
     * 任务id
     */
    @Field(type = FieldType.Keyword)
    private String jobId;

    /**
     * 父任务id
     */
    @Field(type = FieldType.Keyword)
    private String parentJobId;

    /**
     * 第三方厂商任务id
     */
    @Field(type = FieldType.Integer)
    private Integer vendorJobId;

    /**
     * 任务名称
     */
    @Field(type = FieldType.Keyword)
    private String jobName;

    /**
     * 外呼通道类型(京东言犀->JDYX,百应->BYAI)
     */
    @Field(type = FieldType.Keyword)
    private String jobType;

    /**
     * 客户id
     */
    @Field(type = FieldType.Keyword)
    private String customerId;

    /**
     * 客户名称
     */
    @Field(type = FieldType.Keyword)
    private String customerName;

    /**
     * 客户手机号
     */
    @Field(type = FieldType.Keyword)
    private String customerPhone;

    /**
     * 客户所属分校id
     */
    @Field(type = FieldType.Keyword)
    private String schoolId;

    /**
     * 客户所属分校名称
     */
    @Field(type = FieldType.Keyword)
    private String schoolName;

    /**
     * 同步状态(N->未同步,Y->已同步,E->同步失败---即:是否已经将客户追加到京东言犀的任务里)
     */
    @Field(type = FieldType.Keyword)
    private String syncStatus;

    /**
     * 回调状态(N->还未接收到回调,Y->已接收到回调---即:是否接收到了外呼结果明细回调接口的请求)
     */
    @Field(type = FieldType.Keyword)
    private String callbackStatus;

    /**
     * 主叫号码(外呼时使用的虚拟号码或线路对应的外显号码)
     */
    @Field(type = FieldType.Keyword)
    private String callingPhone;

    /**
     * 通话状态[京东言犀->(5:已接听,9:无人接听,8:占线,7:关机,11:停机,12:拒接,6:空号,4:无法接通,1012:主叫欠费,
     * 1013:外呼失败,1014:并发呼损,1018:防骚扰拦截,1015:黑名单,,1011:呼出拦截,1016:无可用线路,1017:呼叫受限)]
     */
    @Field(type = FieldType.Integer)
    private Integer callStatus;

    /**
     * 通话时长（单位：秒）
     */
    @Field(type = FieldType.Long)
    private Long callDuration;

    /**
     * 对话轮次
     */
    @Field(type = FieldType.Long)
    private Long dialogCount;

    /**
     * 呼叫开始时间
     */
    @Field(type = FieldType.Keyword)
    private String ringTime;

    /**
     * 对话接通时间
     */
    @Field(type = FieldType.Keyword)
    private String answerTime;

    /**
     * 最后通话时间
     */
    @Field(type = FieldType.Keyword)
    private String lastCallTime;

    /**
     * 第三方厂商的session id
     */
    @Field(type = FieldType.Keyword)
    private String sessionId;

    /**
     * 重拨次数（当前任务中，当通会话当前用户）
     */
    @Field(type = FieldType.Integer)
    private Integer redialTimes;

    /**
     * 意图标签列表,逗号分隔
     */
    @Field(type = FieldType.Keyword)
    private String intentLabels;

    /**
     * 该条明细所在任务对应的测试/正式任务属性（0-测试 1-正式）
     */
    @Field(type = FieldType.Integer)
    private Integer jobEnv;

    /**
     * 录音地址(京东言犀返回的录音地址)
     */
    @Field(type = FieldType.Keyword)
    private String recordingUrl;

    /**
     * 是否需要下载录音,可能会根意向标签进行判断(N->不需要,Y->需要)
     */
    @Field(type = FieldType.Keyword)
    private String hasRecordingDownLoad;

    /**
     * ali bucket 阿里云录音文件地址
     */
    @Field(type = FieldType.Keyword)
    private String serverFolder;

    /**
     * ali bucket 阿里云录音文件KEY
     */
    @Field(type = FieldType.Keyword)
    private String fileKey;

    /**
     * 录音下载次数
     */
    @Field(type = FieldType.Integer)
    private int recordingDownLoadTimes;

    /**
     * 接收回调时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callbackTime;

    /**
     * 创建人userId
     */
    @Field(type = FieldType.Keyword)
    private String creator;

    /**
     * 创建人名称
     */
    @Field(type = FieldType.Keyword)
    private String creatorName;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedTime;

    /**
     * 将客户信息追加到京东言犀任务时出现异常,异常信息
     */
    @Field(type = FieldType.Keyword)
    private String errorLog;

    /**
     * 录音文本
     */
    @Field(type = FieldType.Keyword)
    private String chatText;

    /**
     * 批次号
     */
    @Field(type = FieldType.Keyword)
    private String lotNo;

    /**
     * 扩展字段1
     * 存储京东言犀的customerVariables，json格式
     */
    @Field(type = FieldType.Keyword)
    private String ext1;

    /**
     * 扩展字段2
     */
    @Field(type = FieldType.Keyword)
    private String ext2;

    /**
     * 扩展字段3
     */
    @Field(type = FieldType.Keyword)
    private String ext3;

    /**
     * 添加任务信息
     *
     * @param subAiCallJob 子任务信息
     * @return com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo
     * <AUTHOR>
     * @Date 13:53 2022/8/23
     **/
    public static AICallJobCustomerInfo addJobInfo(CcAiCallJob subAiCallJob) {
        AICallJobCustomerInfo aiCallJobCustomerInfo = new AICallJobCustomerInfo();
        aiCallJobCustomerInfo.setJobId(subAiCallJob.getJobId());
        aiCallJobCustomerInfo.setParentJobId(subAiCallJob.getParentJobId());
        aiCallJobCustomerInfo.setJobName(subAiCallJob.getJobName());
        aiCallJobCustomerInfo.setJobType(subAiCallJob.getAiJobType());
        aiCallJobCustomerInfo.setSyncStatus("N");
        aiCallJobCustomerInfo.setCallbackStatus("N");
        aiCallJobCustomerInfo.setCreator(subAiCallJob.getCreator());
        aiCallJobCustomerInfo.setCreatorName(subAiCallJob.getCreatorName());
        aiCallJobCustomerInfo.setCreatedTime(new Date());
        aiCallJobCustomerInfo.setModifiedTime(aiCallJobCustomerInfo.getCreatedTime());
        return aiCallJobCustomerInfo;
    }

    /**
     * 添加每个客户不相同的信息
     *
     * @param customerRequest 客户信息
     * <AUTHOR>
     * @since 14:00 2022/8/23
     **/
    public void addDiffCustomerInfo(JDCallCustomerRequest customerRequest) {
        if (customerRequest != null) {
            if (customerRequest.getCustId() != null) {
                this.customerId = customerRequest.getCustId();
            }
            if (customerRequest.getName() != null) {
                this.customerName = FieldCipherUtil.oneEncrypt(customerRequest.getName());
            }
            if (customerRequest.getPhone() != null) {
                this.customerPhone = FieldCipherUtil.oneEncrypt(customerRequest.getPhone());
            }
            if (customerRequest.getSchoolId() != null) {
                this.schoolId = customerRequest.getSchoolId();
            }
            if (customerRequest.getSchoolName() != null) {
                this.schoolName = customerRequest.getSchoolName();
            }
            if (customerRequest.getVariable() != null) {
                this.ext1 = JSONUtils.toJSONString(customerRequest.getVariable());
            }
        }
    }

    /**
     * 根据呼叫状态获取描述
     *
     * @param callStatus 呼叫状态码
     * @return java.lang.String
     * <AUTHOR>
     * @Date 15:44 2022/11/23
     **/
    public static String getCallStatusDesc(Integer callStatus) {
        if (callStatus == null) {
            return "未知的通话状态";
        }
        switch (callStatus) {
            case 5: {
                return "已接听";
            }
            case 9: {
                return "无人接听";
            }
            case 8: {
                return "占线";
            }
            case 7: {
                return "关机";
            }
            case 11: {
                return "停机";
            }
            case 12: {
                return "拒接";
            }
            case 6: {
                return "空号";
            }
            case 4: {
                return "无法接通";
            }
            case 1012: {
                return "主叫欠费";
            }
            case 1013: {
                return "外呼失败";
            }
            case 1014: {
                return "并发呼损";
            }
            case 1018: {
                return "防骚扰拦截";
            }
            case 1015: {
                return "黑名单";
            }
            case 1011: {
                return "呼出拦截";
            }
            case 1016: {
                return "无可用线路";
            }
            case 1017: {
                return "呼叫受限";
            }
            default:
        }
        return "未知的通话状态";
    }
}
