package com.niceloo.cmc.ex.entity.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.framework.utils.DateUtils;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * ES不可下载通话录音实体类
 * <AUTHOR>
 * @since 2024-10-15 16:24:15
 */

@Data
@Document(indexName = "call_recording_unaccessible", type = "_doc", shards = 5, replicas = 1)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CallRecordingUnaccessible {

    /**
     * ID/[也为call_record_xxxxxx索引ID]
     */
    @Field(type = FieldType.Keyword)
    private String callId;

    /**
     * 厂商返回的录音地址
     */
    @Field(type = FieldType.Keyword)
    private String voiceSourceUrl;

    /**
     * 通话时长(单位秒)
     */
    @Field(type = FieldType.Integer)
    private Integer duration;

    /**
     * 下载到服务器本地的路径[下载成功，上传失败时会存储字段]
     */
    @Field(type = FieldType.Keyword)
    private String voicePath;

    /**
     * 外呼类型
     */
    @Field(type = FieldType.Keyword)
    private String channelType;

    /**
     * 操作标志: 0->未上传，1->上传失败,(-1)->厂商还没有返回录音地址,定时任务查询
     */
    @Field(type = FieldType.Integer)
    private Integer operationSign;

    /**
     * 尝试次数 下载或上传尝试次数超过十次做另处理
     */
    @Field(type = FieldType.Integer)
    private Integer attempts;

    /**
     * 通话记录所属年月 xxxx-xx
     */
    @Field(type = FieldType.Keyword)
    private String date;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifierTime;

    /**
     * 索引名称
     */
    @Field(type = FieldType.Keyword)
    private String indexName;

    /**
     * 音频大小（单位：字节）
     */
    @Field(type = FieldType.Long)
    private long audioSize;

    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextInspectTime;

    public static CallRecordingUnaccessible initialize() {
        CallRecordingUnaccessible recordingUnaccessible = new CallRecordingUnaccessible();
        recordingUnaccessible.setAttempts(0);
        recordingUnaccessible.setOperationSign(0);
        recordingUnaccessible.setVoicePath("");
        recordingUnaccessible.setCreateTime(new Date());
        recordingUnaccessible.setModifierTime(recordingUnaccessible.getCreateTime());
        return recordingUnaccessible;
    }

    /**
     * 构建通话记录的资源映射图
     *
     * @param recording 通话记录实体
     * @return 返回包含通话记录相关信息的Map对象
     * @description 该方法接收一个CallRecordingUnaccessible类型的对象作为参数，根据该对象中的属性构建一个包含通话记录信息的Map对象，
     *              并返回该Map对象。Map中的键值对包括：callId（通话ID）、voiceSourceUrl（语音源地址）、duration（通话时长）、
     *              voicePath（语音文件路径）、channelType（通道类型）、operationSign（操作标识）、attempts（尝试次数）、
     *              date（日期）、createTime（创建时间，格式化为字符串）、modifierTime（修改时间，格式化为字符串）、
     *              indexName（索引名称）、audioSize（音频文件大小）、nextInspectTime（下次检测时间）。
     */
    public static Map<String, Object> buildSourceMapForUnaccessible(CallRecordingUnaccessible recording) {
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("callId", recording.getCallId());
        sourceMap.put("voiceSourceUrl", recording.getVoiceSourceUrl());
        sourceMap.put("duration", recording.getDuration());
        sourceMap.put("voicePath", recording.getVoicePath());
        sourceMap.put("channelType", recording.getChannelType());
        sourceMap.put("operationSign", recording.getOperationSign());
        sourceMap.put("attempts", recording.getAttempts());
        sourceMap.put("date", recording.getDate());
        sourceMap.put("createTime", DateUtils.toStr(recording.getCreateTime()));
        sourceMap.put("modifierTime", DateUtils.toStr(recording.getModifierTime()));
        sourceMap.put("indexName", recording.getIndexName());
        sourceMap.put("audioSize", recording.getAudioSize());
        sourceMap.put("nextInspectTime", DateUtils.toStr(recording.getNextInspectTime()));
        return sourceMap;
    }

    /**
     * 根据提供的不可访问的录音记录构建数据源映射。
     *
     * @param recording 不可访问的录音记录对象
     * @return 包含录音记录详细信息的映射，键为属性名称，值为对应的属性值
     */
    public static Map<String, Object> buildSourceMapForAccessible(CallRecordingUnaccessible recording) {
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("callId", recording.getCallId());
        sourceMap.put("voiceSourceUrl", recording.getVoiceSourceUrl());
        sourceMap.put("duration", recording.getDuration());
        sourceMap.put("voicePath", recording.getVoicePath());
        sourceMap.put("channelType", recording.getChannelType());
        sourceMap.put("operationSign", recording.getOperationSign());
        sourceMap.put("attempts", recording.getAttempts());
        sourceMap.put("date", recording.getDate());
        sourceMap.put("createTime", DateUtils.toStr(recording.getCreateTime()));
        sourceMap.put("modifierTime", DateUtils.toStr(recording.getModifierTime()));
        sourceMap.put("indexName", recording.getIndexName());
        sourceMap.put("audioSize", recording.getAudioSize());
        return sourceMap;
    }

    /**
     * 将包含通话记录的Map转换为CallRecordingUnaccessible对象
     *
     * @param sourceMap 包含通话记录的Map对象
     * @return 转换后的CallRecordingUnaccessible对象
     */
    public static CallRecordingUnaccessible convertMapToUnaccessible(Map<String, Object> sourceMap) {
        CallRecordingUnaccessible unaccessible = new CallRecordingUnaccessible();
        unaccessible.setCallId((String) sourceMap.get("callId"));
        unaccessible.setVoiceSourceUrl((String) sourceMap.get("voiceSourceUrl"));
        unaccessible.setDuration((Integer) sourceMap.get("duration"));
        unaccessible.setVoicePath((String) sourceMap.get("voicePath"));
        unaccessible.setChannelType((String) sourceMap.get("channelType"));
        unaccessible.setOperationSign((Integer) sourceMap.get("operationSign"));
        unaccessible.setAttempts((Integer) sourceMap.get("attempts"));
        unaccessible.setDate((String) sourceMap.get("date"));
        unaccessible.setCreateTime(DateUtils.toDate((String) sourceMap.get("createTime")));
        unaccessible.setModifierTime(DateUtils.toDate((String) sourceMap.get("modifierTime")));
        unaccessible.setIndexName(BizConst.CALL_RECORD_ES_PREFIX + sourceMap.get("date").toString().replace("-", ""));

        return unaccessible;
    }
}