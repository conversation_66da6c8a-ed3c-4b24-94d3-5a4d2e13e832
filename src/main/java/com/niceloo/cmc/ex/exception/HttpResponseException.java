package com.niceloo.cmc.ex.exception;

/**
 * <AUTHOR>
 * @Description: HTTP Status-Code 4XX: client error  5XX: server error
 * @Date 2022/1/21 15:48
 */
public class HttpResponseException extends Exception {

    private String url;
    private int responseCode;
    private String responseMessage;

    public HttpResponseException(String url, String message) {
        super(url + " ==> " + message);
        this.url = url;
    }

    public HttpResponseException(String url, int responseCode, String responseMessage) {
        super(url + " ==> " + responseCode + " " + responseMessage);
        this.url = url;
        this.responseCode = responseCode;
        this.responseMessage = responseMessage;
    }

    public String getUrl() {
        return url;
    }

    public int getResponseCode() {
        return responseCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }
}
