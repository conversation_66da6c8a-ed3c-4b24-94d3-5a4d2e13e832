package com.niceloo.cmc.ex.pojo.dto;


import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

import static com.niceloo.cmc.ex.utils.AudioAddressCheckerUtil.isAddressEncrypted;

/**
 * 亿迅SIP通话记录业务传输类
 * <AUTHOR>
 * @since 2024-08-20 17:38:16
 */
@Data
@ApiModel("亿迅SIP通话记录业务传输类")
public class YXSipCallRecordsDTO {

    /**
     * 标识
     */
    @ApiModelProperty(value = "标识", example = "billing")
    private String type;

    /**
     * 呼叫开始时间
     */
    @ApiModelProperty(value = "呼叫开始时间", example = "2020-01-09 09:41:00")
    private String startTime;

    /**
     * 振铃开始时间
     */
    @ApiModelProperty(value = "振铃开始时间", example = "2020-01-09 09:41:00")
    private String ringTime;

    /**
     * 呼叫接通时间
     */
    @ApiModelProperty(value = "呼叫接通时间", example = "2020-01-09 09:41:00")
    private String answerTime;

    /**
     * 呼叫结束时间
     */
    @ApiModelProperty(value = "呼叫结束时间", example = "2020-01-09 09:41:00")
    private String byeTime;

    /**
     * 坐席工号
     */
    @ApiModelProperty(value = "坐席工号", example = "2020")
    private String staffNo;

    /**
     * 班组1
     */
    @ApiModelProperty(value = "班组1", example = "-")
    private String group1;

    /**
     * 班组2
     */
    @ApiModelProperty(value = "班组2", example = "-")
    private String group2;

    /**
     * 被叫号码
     */
    @ApiModelProperty(value = "被叫号码", example = "13100000000")
    private String callee;

    /**
     * 主叫号码
     */
    @ApiModelProperty(value = "主叫号码", example = "01000000000")
    private String caller;

    /**
     * 客户按键
     */
    @ApiModelProperty(value = "客户按键", example = "-")
    private String keyPress;

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", example = "10750")
    private String taskID;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称", example = "-")
    private String taskName;

    /**
     * 录音文件
     */
    @ApiModelProperty(value = "录音文件", example = "-")
    private String recordFile;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", example = "1、手拨呼叫；2、呼入呼叫；3、内部呼叫；4、自动外呼；5、点拨呼叫；19、语音验证码；21、语音通知")
    private String service;

    /**
     * 通话标识
     */
    @ApiModelProperty(value = "通话标识", example = "-")
    private String session;

    /**
     * 消息序列号
     */
    @ApiModelProperty(value = "消息序列号", example = "-")
    private String seq;

    /**
     * 用户回传数据
     */
    @ApiModelProperty(value = "用户回传数据", example = "-")
    private String userData;

    /**
     * 通话时长
     */
    @ApiModelProperty(value = "通话时长", example = "-")
    private String timeLength;

    /**
     * 挂断原因
     */
    @ApiModelProperty(value = "挂断原因", example = "错误码，详情见下面错误码说明表")
    private String releaseCause;

    /**
     * ivrID
     */
    @ApiModelProperty(value = "ivrID", example = "只有api配置了推送ivr记录才有")
    private String ivr;

    /**
     * ivr记录
     */
    @ApiModelProperty(value = "ivr记录", example = "只有api配置了推送ivr记录才有")
    private String[] ivrHistory;

    /**
     * 分析结果
     */
    @ApiModelProperty(value = "分析结果", example = "错误码，详情见下面错误码说明表")
    private String callResult;

    /**
     * 分析结果
     */
    @ApiModelProperty(value = "分析结果", example = "中文说明")
    private String callResultMsg;

    /**
     * 挂断类型
     */
    @ApiModelProperty(value = "挂断类型", example = "success=成功 fail=失败 leak=漏接 illegal=错误")
    private String typeResult;

    /**
     * 呼叫方式
     */
    @ApiModelProperty(value = "呼叫方式", example = "WEB=页面发起呼叫 API=接口发起呼叫 APP=电话管家呼叫 OTHERS=其他方式")
    private String usageMode;

    /**
     * 客户标签
     */
    @ApiModelProperty(value = "客户标签")
    private String[] label;

    /**
     * 账号标识
     */
    private String account;

    /**
     * 是否是回拨服务器 true->中科云(78),false->回拨(175)
     * SIP外呼暂未启用，传什么都不处理
     * 此字段只为和亿讯其他外呼入参保持一致
     */
    private boolean sign;

    /**
     * 账号名称
     */
    private String accountName;



    /**
     * 将通话记录转换成CallRecord对象
     *
     * @return CallRecord 通话记录对象
     */
    public CallRecord callRecordsConverter() {
        CallRecord callRecord = new CallRecord();
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setChannelType(CallChannelEnum.CALL_TYPE_YX_SIP.getType());
        callRecord.setField4(this.account);
        callRecord.setField2(this.userData);
        callRecord.setCallPhone(notEmpty(this.caller));
        callRecord.setAgentId(notEmpty(this.staffNo));
        callRecord.setCallAccount("");
        callRecord.setVoiceSourceId("");
        callRecord.setField6(notEmpty(this.session));
        callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.callee));
        callRecord.setCallTime(DateUtil.getFirstNonNullDate(this.startTime, this.ringTime, this.answerTime, this.byeTime));
        callRecord.setDataSyncTime(new Date());
        callRecord.setCreatedTime(new Date());
        int duration = Integer.parseInt(this.timeLength);
        callRecord.setDuration(duration);
        callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_N);
        callRecord.setVoiceStatus(BizConst.VOICE_NO_DEAL);
        callRecord.setIsValidCall(BizConst.VALID_CALL_N);
        callRecord.setIsConnected(BizConst.CONNECTED_FAIL);
        callRecord.setVoiceSyncStatus(0);
        callRecord.setDataCompleteStatus("Y");
        // 服务器标志
        if (this.sign) {
            if (!ObjectUtils.isEmpty(callRecord.getAgentId())) {
                callRecord.setCallAccount(callRecord.getAgentId() + "@" + this.accountName);
            }
        } else {
            callRecord.setCallAccount(notEmpty(this.caller));
            callRecord.setField3(CallRecord.CALL_TYPE_YH_FIELD3);
        }
        // 呼叫类型[5->回拨]
        if (BizConst.SERVICE_CLICK_TO_CALL.equals(service) || BizConst.SERVICE_MANUAL_CALL.equals(this.service) || BizConst.SERVICE_INTERNAL_CALL.equals(service) || BizConst.SERVICE_AUTO_OUTBOUND.equals(service)) {
            callRecord.setCallType(BizConst.CALL_OUT);
        } else if (BizConst.SERVICE_INBOUND_CALL.equals(service)) {
            callRecord.setCallType(BizConst.CALL_IN);
        } else {
            callRecord.setCallType(Integer.valueOf(service));
        }
        if (duration > 0) {
            callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_Y);
            callRecord.setIsConnected(BizConst.CONNECTED_SUCCESS);
            callRecord.setVoiceStatus(BizConst.VOICE_DEALING);
        }
        if (duration >= BizConst.EFFECTIVE_TIME) {
            callRecord.setIsValidCall(BizConst.VALID_CALL_Y);
        }
        if (!StringUtils.isEmpty(this.recordFile)) {
            String host = CallProperties.YXProperty.YX_SIP_HOST;
            // 判断录音地址是否为密文
            if (isAddressEncrypted(this.recordFile)) {
                // 如果录音地址是密文，则使用密文下载API来设置通话录音的声音源URL
                callRecord.setVoiceSourceUrl(host + CallProperties.YXProperty.RECORD_DOWNLOAD_API_CIPHERTEXT + this.recordFile);
            } else {
                // 如果录音地址不是密文，则使用普通下载API来设置通话录音的声音源URL
                callRecord.setVoiceSourceUrl(host + CallProperties.YXProperty.RECORD_DOWNLOAD_API + this.recordFile);
            }
            callRecord.setVoiceSyncStatus(1);
            callRecord.setDataCompleteStatus("N");
        } else {
            if (duration >= BizConst.EFFECTIVE_TIME) {
                callRecord.setDataCompleteStatus("N");
            }
        }
        callRecord.setModifiedTime(callRecord.getCreatedTime());
        callRecord.setModifier("SYSTEM");
        callRecord.setRemarks("");
        callRecord.setIntentionMsg("");
        callRecord.setServerFolder("");
        callRecord.setVoiceSourceUrl(notEmpty(callRecord.getVoiceSourceUrl()));
        return callRecord;
    }

    /**
     * 判断给定字符串是否为空，如果为空则返回空字符串，否则返回原字符串。
     *
     * @param str 给定的字符串
     * @return 如果给定字符串为空，则返回空字符串，否则返回原字符串
     */
    private static String notEmpty(String str) {
        return StringUtils.nullToEmpty(str);
    }

    /**
     * 将Map类型的呼叫记录转换成YXSipCallRecordsDTO类型的对象
     *
     * @param map 包含呼叫记录信息的Map对象，key为字段名，value为字段值
     * @param userData 用户数据
     * @return 转换后的YXSipCallRecordsDTO对象，如果传入的map为null，则返回null
     */
    public YXSipCallRecordsDTO oneCdrResultConverter(Map<String, String> map, String userData) {
        if (null == map) {
            return null;
        }
        this.caller = map.get("caller");
        this.callee = map.get("callee");
        this.startTime = map.get("startTime");
        this.recordFile = map.get("filename");
        this.timeLength = map.get("timeLen");
        this.session = map.get("session");
        this.staffNo = map.get("agent");
        // 到现在亿迅都是呼出
        this.service = BizConst.SERVICE_CLICK_TO_CALL;
        this.userData = userData;
        this.sign = true;
        return this;
    }
}
