package com.niceloo.cmc.ex.pojo.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 京东言犀-创建外呼任务请求参数
 *
 * <AUTHOR>
 * @Date 2022-08-02 15:47
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JDCreateJobParam {
    /**
     * 言犀pin(登录言犀平台的账号)
     */
    private String userPin;

    /**
     * 机器人id(应用id)
     */
    private Integer botId;

    /**
     * 外呼任务job名称(需要唯一)
     */
    private String name;

    /**
     * 外呼job任务描述
     */
    private String description;

    /**
     * 启动模式(默认为 Manual): Manual(手动)、Timing(定时)、Repeat(循环)
     */
    private String startupMode;

    /**
     * 定时任务的开始时间。
     * 例如: 2022-04-30 10:00:00
     * 1、定时模式时该字段必填
     * 2、手动任务不需要填写
     */
    private String startTime;

    /**
     * 拨打时间段限制: Simple(简易限制)、Workday(高级限制-工作日限制)
     * (配合jobOfflineTimes进行使用。简易限制可选择时间段：08:00:00 - 21:00:00)
     */
    private String dialTimeStyle;

    /**
     * 设置任务执行的时段
     */
    private List<JDCallPeriodParam> jobOfflineTimes;

    /**
     * 外呼主叫号码数组(虽然是数组,但是言犀厂商只支持传递一个) <br/>
     * 1、map的key-> innerPhoneNumber <br/>
     * 2、(用户接到外呼电话时,手机上显示的号码,通常为95118开头的一串虚拟外显)
     */
    private List<Map<String, String>> jobPhoneCalls;

    /**
     * 外呼线路id,线路申请完成后由客户经理提供
     */
    private Long lineId;

    /**
     * 当前机器人线路可设并发上限,不填写默认为最大并发值（此处设置的并发数为任务维度）
     */
    private Integer concurrentNumber;

    /**
     * 外呼用户组id（选填,如果customers和该字段均为空,则只建立空任务,追加名单后方可执行）
     */
    private Integer groupId;

    /**
     * 外呼话术模板id,可通过话术模板查询接口获取到
     */
    private Integer contextId;

    /**
     * 外呼用户名单信息
     */
    private List<JDCallCustomerParam> customers;

    public void setRedial(boolean redial) {
        isRedial = redial;
    }

    /**
     * 是否是重拨任务,false表示不是重拨任务,默认不重拨
     */
    private boolean isRedial;

    /**
     * 自动重拨次数,若是重拨任务的话,此项必填
     * 取值范围: [1~5]
     */
    private Integer redialTimes;

    /**
     * 重拨间隔（分钟）,若是重拨任务的话,此项必填
     * 取值范围: [1~1440]
     */
    private Integer redialInterval;

    /**
     * 重拨的原因,多个原因之间用竖线分隔,若是重拨任务的话,此项必填
     * 原因说明：<p>“NeedRedial”,“意向标签-需重拨”；“Closed”, “关机”；”Busy”, “占线”；”Missed”, “未接/无人接听”；”Stopped”, “停机”；”Unavailabe”, “无法接通”；”Dead”, “空号”；”ConcurrentCallLoss”, “并发呼损”；”NoAvailableLine”, “无可用线路”；”Reject”, “拒接”；”CallLimit”, “呼叫受限”；”CallerArrears”, “主叫欠费”；”OutboundFail”, “外呼失败”；”Intercepted”, “呼出拦截”</p>
     */
    private String redialReason;

    /**
     * 是否开启防骚扰拦截配置，默认关闭
     */
    private boolean isCallIntercept;

    /**
     * 防骚扰拦截配置列表
     */
    private List<JDCallInterceptsParam> jobCallIntercepts;

    /**
     * 是否开启黑名单
     */
    private boolean hasBlacklist;

    /**
     * 黑名单分组id，可通过黑名单分组查询接口获取(开启黑名单,此参数必填)
     */
    private Integer blacklistGroupId;

    public static JDCreateJobParam defaultValue() {
        JDCreateJobParam jdCreateJobParam = new JDCreateJobParam();
        jdCreateJobParam.hasBlacklist = false;
        jdCreateJobParam.isCallIntercept = false;

        return jdCreateJobParam;
    }

    public boolean getIsRedial() {
        return isRedial;
    }

    public boolean getIsCallIntercept() {
        return isCallIntercept;
    }
}
