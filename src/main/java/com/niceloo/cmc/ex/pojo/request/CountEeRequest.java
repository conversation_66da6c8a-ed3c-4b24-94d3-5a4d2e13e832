package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 员工维度话务统计请求参数
 * <AUTHOR>
 * @since 2022-03-03 13:55
 */
@Data
public class CountEeRequest extends AuthBaseRequest {

    /**
     * 分页起始,即offset
     */
    @ApiModelProperty("分页起始")
    private Integer pageIndex = 0;

    /**
     * 当页显示数
     */
    @ApiModelProperty("当页显示数")
    private Integer pageSize = 0;

    /**
     * 员工用户标识
     */
    @IsId
    @ApiModelProperty("员工用户标识")
    private String callerUserId;

    /**
     * 部门标识,多个逗号分割
     */
    @IsIds
    @ApiModelProperty("部门标识")
    private String dptIds;

    @Override
    public String getEeUserId() {
        return callerUserId;
    }

    @Override
    public void setEeUserId(String eeUserId) {
        this.callerUserId = eeUserId;
    }

    @Override
    public String getDptId() {
        return dptIds;
    }

    @Override
    public void setDptId(String dptId) {
        this.dptIds = dptId;
    }

    /**
     * 呼叫通道
     */
    @IsRangeVal(value = {"TQ","TF","FS","YP","YS","ZK","OI","JLFY","ZHZX"})
    @ApiModelProperty("呼叫通道")
    private String channelType;

    /**
     * 查询时间起始
     */
    @IsYMDHMS
    @NotNull
    @ApiModelProperty("查询时间起始")
    private String createdTimeStart;

    /**
     * 查询时间结束
     */
    @IsYMDHMS
    @NotNull
    @ApiModelProperty("查询时间结束")
    private String createdTimeEnd;

    /**
     * 查询统计类型
     */
    @IsRangeVal(value = {"D", "M"})
    @ApiModelProperty("查询统计类型,[枚举]D:日;M:月")
    private String countType;
}
