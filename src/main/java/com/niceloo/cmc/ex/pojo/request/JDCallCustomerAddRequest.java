package com.niceloo.cmc.ex.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

/**
 * 客户信息追加参数
 *
 * <AUTHOR>
 * @Date 2022-09-30 10:35
 */
@Data
@ApiModel("客户信息追加参数")
public class JDCallCustomerAddRequest {

    /**
     * 父任务的id
     */
    @NotBlank(message = "父任务的id不能为空")
    @ApiModelProperty("父任务的id")
    private String jobId;

    /**
     * 客户传输完成标志(Y->已完成,N->未完成)
     */
    @ApiModelProperty("客户传输完成标志(Y->已完成,N->未完成)")
    @NotBlank(message = "客户传输完成标志不能为空")
    private String finishFlag;
    /**
     * 批次号,防止客户信息追加重复
     */
    private String LotNo = UUID.randomUUID().toString().replace("-", "");

    /**
     * 京东的客户信息列表
     */
    @ApiModelProperty("客户信息列表")
    private List<JDCallCustomerRequest> jdCallCustomerRequestList;
}
