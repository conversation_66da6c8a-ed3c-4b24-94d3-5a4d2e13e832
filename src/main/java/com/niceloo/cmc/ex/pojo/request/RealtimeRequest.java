package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 今日实时统计数据请求参数
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RealtimeRequest extends AuthBaseRequest {

    /**
     * 分校标识
     */
    @NotNull(message = "分校id不能为空")
    @IsId
    @ApiModelProperty("分校id")
    private String schoolId;

    @Override
    public String getSchoolId() {
        return schoolId;
    }

    @Override
    public void setSchoolId(String schoolId) {
        this.schoolId = schoolId;
    }
}
