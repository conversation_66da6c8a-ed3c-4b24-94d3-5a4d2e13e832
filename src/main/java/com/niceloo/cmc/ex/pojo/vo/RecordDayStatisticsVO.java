package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.cmc.ex.pojo.dto.RecordDayStatisticsData;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 日话务统计
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-03-03 13:43
 */
@Data
@AllArgsConstructor
public class RecordDayStatisticsVO {

    /**
     * 统计的日期
     */
    @ApiModelProperty("统计的日期")
    private String date;

    /**
     * 统计的数据
     */
    @ApiModelProperty("统计的数据")
    private RecordDayStatisticsData value;

}


