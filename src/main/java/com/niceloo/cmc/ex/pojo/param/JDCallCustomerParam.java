package com.niceloo.cmc.ex.pojo.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.utils.JDAESUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 京东言犀外呼客户参数
 *
 * <AUTHOR>
 * @Date 2022-08-02 14:06
 */
@Data
@NoArgsConstructor
public class JDCallCustomerParam implements Serializable {

    private static final long serialVersionUID = -7359259625330163249L;
    /**
     * 客户名称
     */
    private String name;

    /**
     * 客户手机号
     */
    private String phone;

    /**
     * 客户属性
     */
    private Map<String, Object> variable;

    /**
     * 转换客户参数
     *
     * @param customerRequestList 客户参数
     * @return java.util.List<com.niceloo.cmc.ex.pojo.param.JDCallCustomerParam>
     * <AUTHOR>
     * @Date 15:33 2022/8/23
     **/
    public static List<JDCallCustomerParam> convertToParam(List<JDCallCustomerRequest> customerRequestList, String parentJobId) {
        List<JDCallCustomerParam> customerParamList = new ArrayList<>(customerRequestList.size());
        for (JDCallCustomerRequest customerRequest : customerRequestList) {
            Map<String, Object> mapObj = new LinkedHashMap<>();

            if (customerRequest.getCustId() != null) {
                mapObj.put("custId", customerRequest.getCustId());
            }
            if (customerRequest.getUuId() != null) {
                mapObj.put("uuid", customerRequest.getUuId());
            }
            if (parentJobId != null) {
                mapObj.put("parentJobId", parentJobId);
            }
            if (customerRequest.getVariable() != null) {
                for (Map.Entry<String, Object> entry : customerRequest.getVariable().entrySet()) {
                    if (entry.getValue() != null) {
                        mapObj.put(entry.getKey(), entry.getValue());
                    }
                }
            }
            JDCallCustomerParam customerParam = new JDCallCustomerParam(customerRequest.getName(),
                    // 通过我们这里解密后再通过京东言犀进行加密
                    JDAESUtil.encrypt(customerRequest.getPhone()),
                    mapObj);
            customerParamList.add(customerParam);
        }
        return customerParamList;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NoArgsConstructor
    public static class Variable implements Serializable {

        private static final long serialVersionUID = 5116923727411965117L;
        private String custId;
        /**
         * 唯一id,和es内ai_job_customer_xxxxxx的id一致
         */
        private String uuid;

        /**
         * 父任务id
         */
        private String parentJobId;

        public Variable(String custId) {
            this.custId = custId;
        }

        public Variable(String custId, String uuid, String parentJobId) {
            this.custId = custId;
            this.uuid = uuid;
            this.parentJobId = parentJobId;
        }
    }

        public JDCallCustomerParam(String name, String phone, Map<String, Object> variable) {
        this.name = name;
        this.phone = phone;
        this.variable = variable;
    }
}

