package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.utils.RedisUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description: 拉取通话记录请求对象
 * @Date 2021/12/13 16:50
 */
@Data
@NoArgsConstructor
public class PullRecordRequest {
    private static final Logger logger = LoggerFactory.getLogger(PullRecordRequest.class);

    /**
     * 厂商通道
     */
    private String channelType;
    private String startTime;
    private String endTime;
    /**
     * 操作类型 C->创建通话记录,U->更新通话记录
     */
    private String actionType;
    /**
     * 分页参数
     */
    private String startKey;
    /**
     * 厂商账号
     */
    private String accountId;

    public PullRecordRequest(String channelType, String startTime, String endTime, String actionType) {
        this.channelType = channelType;
        this.startTime = startTime;
        this.endTime = endTime;
        this.actionType = actionType;
    }

    public PullRecordRequest(String channelType, String startTime, String endTime, String actionType, String startKey, String accountId) {
        this.channelType = channelType;
        this.startTime = startTime;
        this.endTime = endTime;
        this.actionType = actionType;
        this.startKey = startKey;
        this.accountId = accountId;
    }

    /**
     * @return 请求参数不正确时返回null
     * @Description: 构建请求字符串
     * <AUTHOR>
     * @Date 2021/12/14 16:35
     */
    public String buildPullRecordRequest() {
        if (StringUtils.isEmpty(this.channelType)) {
            return null;
        }
        if (StringUtils.isEmpty(this.startTime)) {
            return null;
        }
        if (StringUtils.isEmpty(this.endTime)) {
            return null;
        }
        String request = this.channelType + BizConst.LINK_SYMBOL + this.startTime + BizConst.LINK_SYMBOL + this.endTime + BizConst.LINK_SYMBOL + BizConst.AUTO + BizConst.LINK_SYMBOL + actionType;

        if (StringUtils.isEmpty(this.startKey) || StringUtils.isEmpty(this.accountId)) {
            return request;
        }
        return request + BizConst.LINK_SYMBOL + this.startKey + BizConst.LINK_SYMBOL + this.accountId;
    }

    /**
     * @param channelType       厂商通道
     * @param underlineRequests 下划线分割的请求
     * @return json字符串格式的请求
     * @Description: 将下划线分割的请求转化为json字符串格式的请求
     * <AUTHOR>
     * @Date 2021/12/15 9:32
     */
    public static Set<String> convertUnderlineFormat2JsonString(String channelType, List<String> underlineRequests) {
        Set<String> jsonStrRequests = new HashSet<>();
        for (String request : underlineRequests) {
            String[] requestParam = request.trim().split("_");
            PullRecordRequest pullRecordRequest = null;

            // 只有亿讯的请求包含startKey和accountId,因为目前只有ZKService支持按startKey和accountId进行重试
            if (CallChannelEnum.CALL_TYPE_ZK.getType().equals(channelType) || CallChannelEnum.CALL_TYPE_YH.getType().equals(channelType)) {
                if (requestParam.length >= 4) {
                    pullRecordRequest = new PullRecordRequest(channelType, requestParam[0], requestParam[1], requestParam[2], requestParam[3], requestParam[4]);
                }
            } else {
                if (requestParam.length >= 2) {
                    pullRecordRequest = new PullRecordRequest(channelType, requestParam[0], requestParam[1], requestParam[2]);
                }
            }

            if (pullRecordRequest != null) {
                jsonStrRequests.add(JSONUtils.toJSONString(pullRecordRequest));
            }
        }
        return jsonStrRequests;
    }

    /**
     * @param failedRequests 失败的请求
     * @return 成功保存的个数
     * @Description: 将调用厂商接口查询通话记录失败的请求持久化到redis
     * <AUTHOR>
     * @Date 2021/12/13 17:38
     */
    public static long saveFailedPullRecordRequestsToRedisSet(Set<String> failedRequests) {
        if (failedRequests.size() > 0) {
            try {
                RedisUtil.redisTemplate.opsForSet().add(RedisConst.FAILED_PULL_RECORD_REQUEST,failedRequests.toArray(new String[]{}));
            } catch (Exception e) {
                logger.error(e, "FailedPullRecordRequest保存失败: " + failedRequests);
            }
        }
        return 0;
    }

}
