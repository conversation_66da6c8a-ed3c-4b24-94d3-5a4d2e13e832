package com.niceloo.cmc.ex.pojo.vo;


import com.niceloo.cmc.ex.entity.CcCountday;
import com.niceloo.cmc.ex.entity.CcCountmonth;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.niceloo.cmc.ex.common.BizConst.*;


/**
 * @description: 员工话务统计Vo
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-12-07 09:29
 */
public class EeTrafficStatisticVO implements Serializable {

    private static final long serialVersionUID = 6773632267199633942L;

    /**
     * 拨打人姓名[员工姓名]
     */
    private String name;

    /**
     * 拨打人用户标识[用户Id]
     */
    private String userId;

    /**
     * 拨打人呼叫账号
     */
    private String callAccount;

    /**
     * 呼叫通道(TQ:TQ手机;TF:TQ固话;FS:风云;YP:云客手机;YS:云客软电话;ZK:中科云软电话;OI:一号互联)
     */
    private String channelType;

    /**
     * 部门Id
     */
    private String dptId;

    /**
     * 所属分校Id
     */
    private String schoolId;

    /**
     * 呼入个数
     */
    private int callInNum;

    /**
     * 呼入成功个数
     */
    private int callInSuccessNum;

    /**
     * 呼入通话个数
     */
    private int callInConnectNum;

    /**
     * 呼出个数
     */
    private int callOutNum;

    /**
     * 呼出成功个数
     */
    private int callOutSuccessNum;

    /**
     * 呼出通话个数
     */
    private int callOutConnectNum;

    /**
     * 总有效通话个数
     */
    private int validCallSum;

    /**
     * 总通话时长(秒)
     */
    private BigInteger durationTotal = new BigInteger("0");

    /**
     * 总平均通话时长(秒)
     */
    private BigDecimal durationAvg = new BigDecimal("0");

    /**
     * 呼入通话总时长(秒)
     */
    private BigInteger callInDurationTotal = new BigInteger("0");

    /**
     * 呼出通话总时长(秒)
     */
    private BigInteger callOutDurationTotal = new BigInteger("0");
    /**
     * 呼出成功率
     */
    private BigDecimal callOutSuccessRate = new BigDecimal("0");
    /**
     * 总接通率
     */
    private BigDecimal connectSuccessRate = new BigDecimal("0");
    /**
     * 总有效沟通率
     */
    private BigDecimal validCallRate = new BigDecimal("0");

    /**
     * 呼入接通率
     */
    private BigDecimal callInConnectSuccessRate = new BigDecimal("0");
    /**
     * 呼出接通率
     */
    private BigDecimal callOutConnectSuccessRate = new BigDecimal("0");

    /**
     * 平均日通话时长，平均通话时长
     */
    private BigDecimal durationDayAvg = new BigDecimal("0");

    /**
     * 根据员工的通话记录列表进行话务统计
     *
     * @paramter callRecords 员工通话记录列表
     * <AUTHOR>
     * @Date 10:08 2021/12/7
     **/
    public void trafficStatistic(List<Map<String, Object>> callRecords) {
        if (CollectionUtils.isEmpty(callRecords)) {
            return;
        }
        //获取外呼方式分别为呼入、呼出的通话记录列表
        List<Map<String, Object>> callInRecords = callRecords.stream()
                .filter(map -> null != map.get("callType") && CALL_IN == (int) map.get("callType"))
                .collect(Collectors.toList());
        List<Map<String, Object>> callOutRecords = callRecords.stream()
                .filter(map -> null != map.get("callType") && CALL_OUT == (int) map.get("callType"))
                .collect(Collectors.toList());
        //呼入数据统计
        if (!CollectionUtils.isEmpty(callInRecords)) {
            this.callInNum = callInRecords.size();
            this.callInSuccessNum = (int) callInRecords.stream().filter(map -> CONNECT_SUCCESS_Y.equals(map.get("isConnectSuccess"))).count();
            this.callInConnectNum = (int) callInRecords.stream().filter(map -> CONNECT_SUCCESS_Y.equals(map.get("isConnectSuccess")) && Integer.parseInt(map.get("duration").toString()) > 0).count();
            this.callInConnectSuccessRate = new BigDecimal(this.callInConnectNum).divide(new BigDecimal(this.callInNum), 2, RoundingMode.HALF_DOWN);
            callInRecords.forEach(t1 -> this.callInDurationTotal = this.callInDurationTotal.add(new BigInteger(t1.get("duration").toString())));
        }
        //呼出数据统计
        if (!CollectionUtils.isEmpty(callOutRecords)) {
            this.callOutNum = callOutRecords.size();
            this.callOutSuccessNum = (int) callOutRecords.stream().filter(map -> CONNECT_SUCCESS_Y.equals(map.get("isConnectSuccess"))).count();
            this.callOutConnectNum = (int) callOutRecords.stream().filter(map -> CONNECT_SUCCESS_Y.equals(map.get("isConnectSuccess")) && Integer.parseInt(map.get("duration").toString()) > 0).count();
            this.callOutConnectSuccessRate = new BigDecimal(this.callOutConnectNum).divide(new BigDecimal(this.callOutNum), 2, RoundingMode.HALF_DOWN);
            this.callOutSuccessRate = new BigDecimal(this.callOutSuccessNum).divide(new BigDecimal(this.callOutNum), 2, RoundingMode.HALF_DOWN);
            callOutRecords.forEach(t1 -> this.callOutDurationTotal = this.callOutDurationTotal.add(new BigInteger(t1.get("duration").toString())));
        }
        //总计数据统计
        this.validCallSum = (int) callRecords.stream().filter(map -> VALID_CALL_Y.equals(map.get("isValidCall"))).count();
        callRecords.forEach(t1 -> this.durationTotal = this.durationTotal.add(new BigInteger(t1.get("duration").toString())));
        this.aggAvgData();
    }

    /**
     * 为员工话务统计添加员工基础信息
     *
     * @return void
     * @paramter eeInfo
     * <AUTHOR>
     * @Date 11:00 2021/12/7
     **/
    public void addEeBaseData(Map<String, Object> eeInfo) {
        this.name = Optional.ofNullable(eeInfo.get("callerName")).orElse("").toString();
        this.userId = Optional.ofNullable(eeInfo.get("callerUserId")).orElse("").toString();
        this.callAccount = Optional.ofNullable(eeInfo.get("callAccount")).orElse("").toString();
        this.dptId = Optional.ofNullable(eeInfo.get("dptId")).orElse("").toString();
        this.schoolId = Optional.ofNullable(eeInfo.get("schoolId")).orElse("").toString();
        this.channelType = Optional.ofNullable(eeInfo.get("channelType")).orElse("").toString();
    }

    /**
     * 转换器: 将 EeTrafficStatisticVo 转为 CcCountday
     *
     * @return com.niceloo.cmc.ex.entity.CcCountday
     * @paramter
     * <AUTHOR>
     * @Date 11:11 2021/12/7
     * @see CcCountday
     **/
    public CcCountday convertToCountDay() {
        CcCountday ccCountday = new CcCountday();
        // 基础数据
        ccCountday.setCountcallerName(this.name);
        ccCountday.setCountcallerUserId(this.userId);
        ccCountday.setCountcallAccount(this.callAccount);
        ccCountday.setCountdptId(this.dptId);
        ccCountday.setCountschoolId(this.schoolId);
        ccCountday.setCountchannelType(this.channelType);
        ccCountday.setCountmodifiedTime(DateUtils.getNowDString());

        // 呼入数据
        ccCountday.setCountcallinNum(this.callInNum);
        ccCountday.setCountcallinSuccessNum(this.callInSuccessNum);
        ccCountday.setCountinConnectNum(this.callInConnectNum);
        ccCountday.setCountinConnectSuccess(this.callInConnectSuccessRate);
        ccCountday.setCountinDurationTotal(this.callInDurationTotal);

        // 呼出数据
        ccCountday.setCountcalloutNum(this.callOutNum);
        ccCountday.setCountcalloutSuccessNum(this.callOutSuccessNum);
        ccCountday.setCountoutConnectNum(this.callOutConnectNum);
        ccCountday.setCountoutDurationTotal(this.callOutDurationTotal);
        ccCountday.setCountoutConnectSuccess(this.callOutConnectSuccessRate);
        ccCountday.setCountcalloutSuccess(this.callOutSuccessRate);

        // 总数据
        ccCountday.setCountvalidCallNum(this.validCallSum);
        ccCountday.setCountdurationTotal(this.durationTotal);
        ccCountday.setCountdurationAvg(this.durationAvg);
        ccCountday.setCountconnectSuccess(this.connectSuccessRate);
        ccCountday.setCountvalidCall(this.validCallRate);
        return ccCountday;
    }

    /**
     * 将日员工话务统计数据再次统计为月员工话务统计
     *
     * @return void
     * @paramter countDays
     * <AUTHOR>
     * @Date 14:45 2021/12/7
     **/
    public void monthTrafficStatistic(List<CcCountday> countDays) {
        //基础数据统计
        CcCountday ccCountday = countDays.get(0);
        this.name = ccCountday.getCountcallerName();
        this.userId = ccCountday.getCountcallerUserId();
        this.callAccount = ccCountday.getCountcallAccount();
        this.dptId = ccCountday.getCountdptId();
        this.schoolId = ccCountday.getCountschoolId();
        this.channelType = ccCountday.getCountchannelType();
        //呼入数据统计
        this.callInNum = countDays.stream().mapToInt(CcCountday::getCountcallinNum).sum();
        this.callInSuccessNum = countDays.stream().mapToInt(CcCountday::getCountcallinSuccessNum).sum();
        this.callInConnectNum = countDays.stream().mapToInt(CcCountday::getCountinConnectNum).sum();
        this.callInDurationTotal = countDays.stream().map(CcCountday::getCountinDurationTotal).reduce(BigInteger.ZERO, BigInteger::add);
        if (this.callInNum > 0) {
            this.callInConnectSuccessRate = new BigDecimal(this.callInConnectNum).divide(new BigDecimal(this.callInNum), 2, RoundingMode.HALF_DOWN);
        }
        //呼出数据统计
        this.callOutNum = countDays.stream().mapToInt(CcCountday::getCountcalloutNum).sum();
        this.callOutSuccessNum = countDays.stream().mapToInt(CcCountday::getCountcalloutSuccessNum).sum();
        this.callOutConnectNum = countDays.stream().mapToInt(CcCountday::getCountoutConnectNum).sum();
        this.callOutDurationTotal = countDays.stream().map(CcCountday::getCountoutDurationTotal).reduce(BigInteger.ZERO, BigInteger::add);
        if (this.callOutNum > 0) {
            this.callOutConnectSuccessRate = new BigDecimal(this.callOutConnectNum).divide(new BigDecimal(this.callOutNum), 2, RoundingMode.HALF_DOWN);
            this.callOutSuccessRate = new BigDecimal(this.callOutSuccessNum).divide(new BigDecimal(this.callOutNum), 2, RoundingMode.HALF_DOWN);
        }
        //总计数据统计
        this.validCallSum = countDays.stream().mapToInt(CcCountday::getCountvalidCallNum).sum();
        this.durationTotal = countDays.stream().map(CcCountday::getCountdurationTotal).reduce(BigInteger.ZERO, BigInteger::add);
        this.durationDayAvg = new BigDecimal(durationTotal).divide(new BigDecimal(countDays.size()), 2, RoundingMode.HALF_DOWN);
        this.aggAvgData();
    }


    /**
     * 总计汇总数据统计[日统计和月统计计算方式一致,避免重复代码抽取]
     *
     * @paramter
     * <AUTHOR>
     * @Date 15:16 2021/12/7
     **/
    private void aggAvgData() {
        int callSum = this.callInNum + this.callOutNum;
        int callConnectSum = this.callInConnectNum + this.callOutConnectNum;
        if (callSum > 0) {
            this.connectSuccessRate = new BigDecimal(callConnectSum).divide(new BigDecimal(callSum), 2, RoundingMode.HALF_DOWN);
            this.validCallRate = new BigDecimal(this.validCallSum).divide(new BigDecimal(callSum), 2, RoundingMode.HALF_DOWN);
        }
        if (callConnectSum > 0) {
            this.durationAvg = new BigDecimal(this.durationTotal).divide(new BigDecimal(callConnectSum), 2, RoundingMode.HALF_DOWN);
        }
    }

    /**
     * 转换器: 将 EeTrafficStatisticVo 转为 CcCountmonth
     *
     * @return com.niceloo.cmc.ex.entity.CcCountmonth
     * @paramter
     * <AUTHOR>
     * @Date 15:23 2021/12/7
     **/
    public CcCountmonth convertToCountMonth() {
        CcCountmonth ccCountmonth = new CcCountmonth();
        ccCountmonth.setCountcallerName(this.name);
        ccCountmonth.setCountcallerUserId(this.userId);
        ccCountmonth.setCountcallAccount(this.callAccount);
        ccCountmonth.setCountdptId(this.dptId);
        ccCountmonth.setCountschoolId(this.schoolId);
        ccCountmonth.setCountchannelType(this.channelType);
        ccCountmonth.setCountmodifiedTime(DateUtils.getNowDString());

        ccCountmonth.setCountcallinNum(this.callInNum);
        ccCountmonth.setCountcallinSuccessNum(this.callInSuccessNum);
        ccCountmonth.setCountinConnectNum(this.callInConnectNum);
        ccCountmonth.setCountinConnectSuccess(this.callInConnectSuccessRate);
        ccCountmonth.setCountinDurationTotal(this.callInDurationTotal);

        ccCountmonth.setCountcalloutNum(this.callOutNum);
        ccCountmonth.setCountcalloutSuccessNum(this.callOutSuccessNum);
        ccCountmonth.setCountoutConnectNum(this.callOutConnectNum);
        ccCountmonth.setCountoutDurationTotal(this.callOutDurationTotal);
        ccCountmonth.setCountoutConnectSuccess(this.callOutConnectSuccessRate);
        ccCountmonth.setCountcalloutSuccess(this.callOutSuccessRate);

        ccCountmonth.setCountvalidCallNum(this.validCallSum);
        ccCountmonth.setCountdurationTotal(this.durationTotal);
        ccCountmonth.setCountdurationAvg(this.durationAvg);
        ccCountmonth.setCountconnectSuccess(this.connectSuccessRate);
        ccCountmonth.setCountvalidCall(this.validCallRate);
        ccCountmonth.setCountdurationDayAvg(this.durationDayAvg);
        return ccCountmonth;
    }

    /**
     * 转换器: 将 EeTrafficStatisticVo 转为前端需要的Map
     * 呼出个数：向外拨打的电话总数；
     * 呼出通话个数：拨通并用户接听的电话总数；
     * 呼出成功率：呼出成功的电话数/外呼个数；
     * 呼入个数：外部拨打进来的电话总数；
     * 呼入通话个数：外部拨打进来并接通的电话总数；
     * 总有效通话个数：通话时长超过20s的电话总数（包括外呼和呼入）；
     * 通话总个数：外呼通话个数+呼入通话个数；
     * 通话总时长：当日通话的总时长（外呼通话时长+呼入通话时长）；
     * 平均通话时长：通话总时长/通话个数（包括外呼和呼入）
     * 总接通率：(外呼通话个数+呼入通话个数)/(呼出个数+呼入个数)；
     * 总有效沟通率：(外呼有效通话个数+呼入有效通话个数) / (外呼个数+ 呼入个数)；
     *
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @paramter
     * <AUTHOR>
     * @Date 17:36 2021/12/7
     **/
    public Map<String, Object> convertToMap() {
        Map<String, Object> statisticData = new HashMap<>();
        statisticData.put("callinNum", this.callInNum);
        statisticData.put("inConnectNum", this.callInConnectNum);
        statisticData.put("callinSuccessNum", this.callInSuccessNum);
        statisticData.put("inConnectSuccess", this.callInConnectSuccessRate);
        statisticData.put("inDurationTotal", this.callInDurationTotal);

        statisticData.put("calloutSuccessNum", this.callOutSuccessNum);
        statisticData.put("calloutNum", this.callOutNum);
        statisticData.put("outConnectNum", this.callOutConnectNum);
        statisticData.put("calloutSuccess", this.callOutSuccessRate);
        statisticData.put("outConnectSuccess", this.callOutConnectSuccessRate);
        statisticData.put("outDurationTotal", this.callOutDurationTotal);

        statisticData.put("validCallNum", this.validCallSum);
        statisticData.put("connectSuccess", this.connectSuccessRate);
        statisticData.put("validCall", this.validCallRate);
        statisticData.put("durationTotal", this.durationTotal);
        statisticData.put("durationAvg", this.durationAvg);
        return statisticData;
    }
}
