package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsRangeVal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 外呼账号批量解绑请求参数
 *
 * @author: WangChenyu
 * @create: 2022-04-27 16:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("外呼账号批量解绑请求参数")
public class CallAccountBatchUnbindRequest extends CallAccountCommonRequest {

    @ApiModelProperty("外呼通道所属类型(JLFY->巨量飞鱼,ZHZX->中弘智享)[作为权限校验使用]")
    @IsRangeVal({"JLFY", "ZHZX"})
    @NotBlank(message = "外呼通道所属类型不能为空")
    private String channelType;

    @ApiModelProperty("外呼账号ID列表")
    @NotNull(message = "外呼账号ID列表不能为空")
    private List<String> callAccountId;
}
