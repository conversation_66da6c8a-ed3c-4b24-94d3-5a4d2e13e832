package com.niceloo.cmc.ex.pojo.param;
import com.niceloo.framework.web.validation.anno.IsYMDHMS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @desc: 手动统计参数
 * @author: song
 * @date: 2022/3/1
 */

@ApiModel(value = "手动统计参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ManualCountParam {

    @ApiParam(value = "时间起始", required = true)
    @NotBlank(message = "时间起始必填")
    @IsYMDHMS
    private String timeStart;

    @ApiParam(value = "时间结束", required = true)
    @NotBlank(message = "时间结束必填")
    @IsYMDHMS
    private String timeEnd;

}
