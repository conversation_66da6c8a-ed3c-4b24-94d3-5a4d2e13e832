package com.niceloo.cmc.ex.pojo.dto.jdyx;

import com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 京东言犀接口<b>--通话记录回调--</b>返回值
 *
 * <AUTHOR>
 * @Date 2022-08-25 10:39
 */
@Data
public class JDCallRecordCallback {

    /**
     * 任务名称
     */
    private Integer jobId;

    /**
     * 任务id
     */
    private String jobName;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 通话时长（单位：秒）
     */
    private Long callDuration;

    /**
     * 重拨次数（当前任务中，当通会话当前用户）
     */
    private Integer redialTimes;

    /**
     * 对话接通时间（第一条消息发出时间）
     */
    private String answerTime;

    /**
     * 该条外呼对应的呼叫开始时间（调度发起时间）
     */
    private String ringTime;

    /**
     * 最后通话时间
     */
    private String lastCallTime;

    /**
     * session id
     */
    private String sid;

    /**
     * 通话状态（见：电话系统状态）
     */
    private Integer callStatus;

    /**
     * 对话轮次
     */
    private Long dialogCount;

    /**
     * 机器人ID
     */
    private Integer botId;

    /**
     * 该条明细所在任务对应的测试/正式任务属性（0-测试 1-正式）
     */
    private Integer jobType;

    /**
     * 话术模板ID
     */
    private Integer cid;

    /**
     * 意图标签列表,逗号分隔
     */
    private String intentLabels;

    /**
     * 外呼时使用的虚拟号码或线路对应的外显号码
     */
    private List<String> jobPhoneCalls;

    /**
     * 客户属性
     */
    private Map<String, String> customerVariables;

    /**
     * 录音地址
     */
    private String audioPath;

    /**
     * 录音文本
     */
    private String chatText;

    /**
     * 将回调参数转成实体
     *
     * @return com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo
     * <AUTHOR>
     * @Date 11:19 2022/8/25
     **/
    public AICallJobCustomerInfo convertToAIJobCustomerEntity() {
        if (this.customerVariables == null || !this.customerVariables.containsKey("uuid")) {
            return null;
        }
        AICallJobCustomerInfo jobCustomerInfo = BeanUtils.copyFromObjToClass(AICallJobCustomerInfo.class, this);

        // 获取 uuid 并处理，如果包含 @ 则只取 @ 前的部分
        String uuid = this.customerVariables.get("uuid");
        if (uuid.contains("@")) {
            uuid = uuid.substring(0, uuid.indexOf("@"));
        }
        jobCustomerInfo.setId(uuid);

        jobCustomerInfo.setJobId(null);
        jobCustomerInfo.setJobType(null);
        jobCustomerInfo.setCallDuration(Optional.ofNullable(this.callDuration).orElse(0L));
        jobCustomerInfo.setDialogCount(Optional.ofNullable(this.dialogCount).orElse(0L));
        jobCustomerInfo.setLastCallTime(Optional.ofNullable(this.lastCallTime).orElse(""));
        jobCustomerInfo.setAnswerTime(Optional.ofNullable(this.answerTime).orElse(""));
        jobCustomerInfo.setRingTime(Optional.ofNullable(this.ringTime).orElse(""));
        jobCustomerInfo.setVendorJobId(this.jobId);
        List<String> phoneCalls = this.getJobPhoneCalls();
        // 处理空值情况
        if (phoneCalls != null && !phoneCalls.isEmpty()) {
            jobCustomerInfo.setCallingPhone(phoneCalls.get(0));
        } else {
            jobCustomerInfo.setCallingPhone(null); // 或者设置为一个默认值
        }
        jobCustomerInfo.setIntentLabels(Optional.ofNullable(this.intentLabels).orElse(""));
        jobCustomerInfo.setSessionId(this.sid);
        jobCustomerInfo.setSyncStatus("Y");
        jobCustomerInfo.setCallbackStatus("Y");
        jobCustomerInfo.setJobEnv(this.jobType);
        jobCustomerInfo.setCallbackTime(new Date());
        jobCustomerInfo.setModifiedTime(jobCustomerInfo.getCallbackTime());
        jobCustomerInfo.setRecordingUrl(Optional.ofNullable(this.audioPath).orElse(""));
        jobCustomerInfo.setHasRecordingDownLoad("N");
        jobCustomerInfo.setRecordingDownLoadTimes(0);
        jobCustomerInfo.setFileKey("");
        jobCustomerInfo.setServerFolder("");
        if (StringUtils.isNotEmpty(this.audioPath) && this.callDuration > 0) {
            // todo 需要添加根据标签来校验是否下载录音逻辑
            jobCustomerInfo.setHasRecordingDownLoad("Y");
        }
        return jobCustomerInfo;
    }
}
