package com.niceloo.cmc.ex.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.niceloo.cmc.ex.pojo.vo.TrafficStatisticVO;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.usermodel.Font;

import java.util.ArrayList;
import java.util.List;

/**
 * 话务排行列表EXCEL下载DTO
 *
 * <AUTHOR>
 * @Date 2022-10-08 16:48
 */
@Getter
@Setter
@EqualsAndHashCode
@HeadRowHeight(value = 20)
@ContentRowHeight(value = 20)
@HeadFontStyle(bold = BooleanEnum.FALSE)
@ColumnWidth(value = 20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 22)
public class TrafficTopExcelDTO {

    @ExcelProperty("序号")
    private Integer IDX;

    @ExcelProperty("时间")
    private String date;

    @ExcelProperty("员工名称")
    @ContentFontStyle(color = Font.COLOR_RED)
    private String userName;

    @ExcelProperty("所属分校")
    private String schoolName;

    @ExcelProperty("所属部门")
    private String dptName;

    @ExcelProperty("通话总量-今日手机通话总量，呼出+呼入。")
    private Integer callNum;

    @ExcelProperty("呼出总量-拨出的所有通话个数，不管是否拨通接听。")
    private Integer callOutNum;

    @ExcelProperty("呼入总量-手机来电的所有通话个数，不管是否拨通接听")
    private Integer callInNum;

    @ExcelProperty("总接通量-接通总个数（呼出接通量+呼入接通量）")
    private Integer callConnectNum;

    @ExcelProperty("呼出接通量-拨出且接通的通话个数。")
    private Integer callOutConnectNum;

    @ExcelProperty("呼入接通量-手机来电且接通的通话个数。")
    private Integer callInConnectNum;

    @ExcelProperty("通话总时长(分秒)-今日手机接通的总通话时长（分秒）。")
    private String callDuration;

    @ExcelProperty("呼出总时长(分秒)-拨出且接通的总通话时长（分秒）。")
    private String callOutDuration;

    @ExcelProperty("呼入总时长(分秒)-手机来电且接通的总通话时长（分秒）")
    private String callInDuration;

    @ExcelProperty("平均通话时长(分秒)-通话总时长/总接通量（分秒）")
    private String callDurationAvg;

    @ExcelProperty("接通率-总接通量(呼出接通量+呼入接通量)/通话总量。")
    private String callConnectRate = "0%";

    /**
     * 统计信息转换为ExcelDTO
     *
     * @param statisticVOList 从ES内查询出来的话务统计信息
     * @return ExcelDTOList
     */
    public static List<TrafficTopExcelDTO> trafficAnalysisExcelConverter(List<TrafficStatisticVO> statisticVOList) {
        List<TrafficTopExcelDTO> trafficTopExcelDTOS = new ArrayList<>(statisticVOList.size());
        int i = 1;
        for (TrafficStatisticVO trafficStatisticVO : statisticVOList) {
            TrafficTopExcelDTO trafficTopExcelDTO = BeanUtils.copyFromObjToClass(TrafficTopExcelDTO.class, trafficStatisticVO);
            trafficTopExcelDTO.setDate(DateUtils.getNowDString(DateUtil.YMD));
            int callConnectRate = (int) (trafficStatisticVO.getCallConnectRate() * 100);
            trafficTopExcelDTO.setCallConnectRate(callConnectRate + "%");
            trafficTopExcelDTO.setCallDuration(DateUtil.secondConvertToMinutesSecond(trafficStatisticVO.getCallDuration()));
            trafficTopExcelDTO.setCallOutDuration(DateUtil.secondConvertToMinutesSecond(trafficStatisticVO.getCallOutDuration()));
            trafficTopExcelDTO.setCallInDuration(DateUtil.secondConvertToMinutesSecond(trafficStatisticVO.getCallInDuration()));
            trafficTopExcelDTO.setCallDurationAvg(DateUtil.secondConvertToMinutesSecond(trafficStatisticVO.getCallDurationAvg()));
            trafficTopExcelDTO.setIDX(i++);
            trafficTopExcelDTOS.add(trafficTopExcelDTO);
        }
        return trafficTopExcelDTOS;
    }
}
