package com.niceloo.cmc.ex.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 话务实时统计接口返回值
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-03-03 09:56
 */
@Data
public class RecordRealtimeStatisticsVO {
    /**
     * 呼出个数
     */
    @ApiModelProperty("呼出个数")
    private Integer calloutNum;

    /**
     * 呼出通话个数
     */
    @ApiModelProperty("呼出通话个数")
    private Integer outConnectNum;

    /**
     * 呼入个数
     */
    @ApiModelProperty("呼入个数")
    private Integer callinNum;

    /**
     * 呼入通话个数
     */
    @ApiModelProperty("呼入通话个数")
    private Integer inConnectNum;

    /**
     * 有效通话个数
     */
    @ApiModelProperty("有效通话个数")
    private Integer validCallNum;

    /**
     * 通话总时长(秒)
     */
    @ApiModelProperty("通话总时长(秒)")
    private Integer durationTotal;

    /**
     * 平均通话时长(秒)
     */
    @ApiModelProperty("平均通话时长(秒)")
    private Integer durationAvg;

    /**
     * 呼出成功率
     */
    @ApiModelProperty("呼出成功率")
    private BigDecimal calloutSuccess;

    /**
     * 接通率
     */
    @ApiModelProperty("接通率")
    private BigDecimal connectSuccess;

    /**
     * 有效沟通率
     */
    @ApiModelProperty("有效沟通率")
    private BigDecimal validCall;

    /**
     * 各项值初始化为0
     *
     * @return com.niceloo.cmc.ex.pojo.vo.RecordRealtimeStatisticsVO
     * @paramter
     * <AUTHOR>
     * @Date 10:12 2022/3/3
     **/
    public static RecordRealtimeStatisticsVO initialize() {
        RecordRealtimeStatisticsVO recordRealtimeStatisticsVO = new RecordRealtimeStatisticsVO();
        recordRealtimeStatisticsVO.calloutNum = 0;
        recordRealtimeStatisticsVO.outConnectNum = 0;
        recordRealtimeStatisticsVO.callinNum = 0;
        recordRealtimeStatisticsVO.inConnectNum = 0;
        recordRealtimeStatisticsVO.validCallNum = 0;
        recordRealtimeStatisticsVO.durationTotal = 0;
        recordRealtimeStatisticsVO.durationAvg = 0;
        recordRealtimeStatisticsVO.calloutSuccess = BigDecimal.valueOf(0);
        recordRealtimeStatisticsVO.connectSuccess = BigDecimal.valueOf(0);
        recordRealtimeStatisticsVO.validCall = BigDecimal.valueOf(0);
        return recordRealtimeStatisticsVO;
    }
}
