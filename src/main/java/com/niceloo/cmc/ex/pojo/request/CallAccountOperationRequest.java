package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsRangeVal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 外呼账号操作请求参数
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-27 16:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("外呼账号操作请求参数")
public class CallAccountOperationRequest extends CallAccountCommonRequest {

    @ApiModelProperty("外呼通道所属类型(JLFY->巨量飞鱼,ZHZX->中弘智享,JDYX->京东言犀AI外呼)")
    @IsRangeVal({"JLFY", "ZHZX", "JDYX"})
    @NotBlank(message = "外呼通道所属类型不能为空")
    private String channelType;

    @ApiModelProperty("外呼账号主键ID")
    @NotBlank(message = "外呼账号主键ID不能为空")
    private String callAccountId;

    @ApiModelProperty("操作类型(1->创建,2->解绑,3->换绑,4->绑定,7->启用,8->禁用)")
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;

    @ApiModelProperty(value = "操作后账号所属员工ID[绑定和换绑需要该参数]")
    private String afterEeUserId = "";

}
