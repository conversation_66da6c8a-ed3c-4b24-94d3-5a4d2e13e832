package com.niceloo.cmc.ex.pojo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDLineListDTO;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import static com.niceloo.cmc.ex.common.BizConst.AI_JOB_CUSTOMER_ES_PREFIX;

/**
 * 京东言犀-话术意向标签查询请求参数
 *
 * <AUTHOR>
 * @Since 2024-11-22 17:05:38
 */

@Data
@ApiModel("话术意向标签查询请求参数")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JDTagListRequest implements Serializable {

    private static final long serialVersionUID = 2842387965593970731L;

    @ApiModelProperty("业务参数；话术ID")
    @NotBlank(message = "话术ID不能为空")
    private String cid;

    @ApiModelProperty("业务参数；筛选查询具体某个分组ID")
    private Long groupId;

    @ApiModelProperty("业务参数；页面大小，默认10，最大100")
    private Long pageSize;

    @ApiModelProperty("业务参数；当前页数，默认1")
    private Long currentPage;
}
