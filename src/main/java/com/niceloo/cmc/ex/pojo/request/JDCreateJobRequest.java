package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDLineListDTO;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.ApiErrorCodes;
import com.niceloo.framework.web.model.UcUser;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import static com.niceloo.cmc.ex.common.BizConst.AI_JOB_CUSTOMER_ES_PREFIX;

/**
 * 京东言犀-创建外呼任务请求参数
 *
 * <AUTHOR>
 * @since 2022-08-02 15:47
 */
@Data
@ApiModel("创建外呼任务请求参数")
public class JDCreateJobRequest implements Serializable {

    private static final long serialVersionUID = -3554705538845133974L;

    /**
     * 外呼任务类型(1->分校任务,2->全国任务,3->学服任务)
     */
    @ApiModelProperty("外呼任务类型(1->分校任务,2->全国任务,3->学服任务)")
    @NotBlank(message = "外呼任务类型不能为空")
    private String callTaskType;

    @ApiModelProperty("外呼任务名称")
    @NotBlank(message = "外呼任务名称不能为空")
    @Length(max = 200, message = "外呼任务名称长度不得超过200位")
    private String name;

    @ApiModelProperty("外呼job任务描述")
    private String description;

    @ApiModelProperty("外呼时段星期列表(1-7),多个星期用逗号隔开")
    @NotBlank(message = "外呼时段星期列表不能为空")
    private String jobOfflineWeek;

    @ApiModelProperty("启动模式(Manual->手动、Timing->定时),不传默认手动")
    private String startupMode = "Manual";

    @ApiModelProperty("任务的启动时间,启动模式为定时时使用,百应精确到天(设置的time无效,只会取date)")
    private String startTime;

    @ApiModelProperty("时段_开始时分秒，例：6点整，对应06:00(京东言犀:08:00 - 20:00, 百应:09:00 - 20:00)")
    @NotBlank(message = "时段_开始时分秒不能为空")
    private String timeBegin;

    @ApiModelProperty("时段_结束时分秒，例：6点整，对应06:00(京东言犀:08:00 - 20:00, 百应:09:00 - 20:00)")
    @NotBlank(message = "时段_结束时分秒不能为空")
    private String timeEnd;

    @ApiModelProperty("外呼线路id")
    @NotNull(message = "外呼线路id不能为空")
    private Long lineId;

    @ApiModelProperty("任务维度-机器人线路并发数")
    @NotNull(message = "任务维度-机器人线路并发数不能为空")
    private Integer concurrentNumber;

    @ApiModelProperty("外呼话术模板id")
    @NotNull(message = "外呼话术模板id不能为空")
    private Integer contextId;

    @ApiModelProperty("外呼话术模板名称")
    @NotBlank(message = "外呼话术模板名称不能为空")
    private String contextName;

    @ApiModelProperty("是否是重拨任务,0->不重拨任务,1->重拨")
    @Max(value = 1, message = "是否是重拨任务为非法参数")
    @Min(value = 0, message = "是否是重拨任务为非法参数")
    private int isRedial = 0;

    @ApiModelProperty("自动重拨次数取值范围: [1~5]")
    private Integer redialTimes;

    @ApiModelProperty("重拨间隔（分钟）,京东言犀: [1~1440], 百应:[0-120]")
    private Integer redialInterval;

    /**
     * 重拨的原因,多个原因之间用竖线分隔,若是重拨任务的话,此项必填
     * 京东言犀：“NeedRedial”,“意向标签-需重拨”；“Closed”, “关机”；”Busy”, “占线”；”Missed”, “未接/无人接听”；”Stopped”, “停机”；”Unavailabe”, “无法接通”；”Dead”, “空号”；”ConcurrentCallLoss”, “并发呼损”；”NoAvailableLine”, “无可用线路”；”Reject”, “拒接”；”CallLimit”, “呼叫受限”；”CallerArrears”, “主叫欠费”；”OutboundFail”, “外呼失败”；”Intercepted”, “呼出拦截”
     * 百应: 1:拒接，2:无法接通，3:外呼失败，4:空号，5:关机，6:占线，7:停机，8:未接，9:主叫欠费，10:呼损，25:无可用线路
     */
    @ApiModelProperty("重拨的原因(多个原因之间用竖线分隔)")
    private String redialReason;

    /**
     * 学服无外呼项目和挖掘项目
     * 下面两个字段callProject 和 exploitProject设置为非空
     */
    @ApiModelProperty("外呼项目")
    private String callProject;

    @ApiModelProperty("挖掘项目")
    private String exploitProject;

    @ApiModelProperty(value = "当前13位毫秒时间戳", hidden = true)
    private Long timestamp = System.currentTimeMillis();

    @ApiModelProperty("客户的总数量")
    @NotNull(message = "客户的总数量不能为空")
    @Max(value = 500000, message = "客户数量最多不能超过500000")
    @Min(value = 1, message = "外呼任务最少有一个客户")
    private Integer customerNum;

    @ApiModelProperty("百应天盾策略组id")
    private Integer tiandunRuleStrategyGroupId;

    @ApiModelProperty("百应天盾策略组名")
    @Length(max = 100, message = "百应天盾策略组名称不得超过100个字符")
    private String tiandunRuleStrategyGroupName;

    @ApiModelProperty("任务优先级，数字越大任务优先级越高")
    @Range(max = 10, min = 1, message = "数字越大任务优先级越高(1-10)")
    private Integer priority;

    /**
     * 转换为外呼任务实体类(缺少主键id、任务分校信息、京东言犀任务id)
     *
     * @return com.niceloo.cmc.ex.entity.CcAiCallJob
     * <AUTHOR>
     * @Date 10:36 2022/8/23
     **/
    public CcAiCallJob convertToAiCallJobEntity(CallChannelEnum callChannelEnum, String parentJobId, List<JDLineListDTO> lineList, Integer customerSize, UcUser ucUser, CallProperties.YanxiProperty.Tenant tenant) {
        // 属性值copy
        CcAiCallJob ccAiCallJob = BeanUtils.copyFromObjToClass(CcAiCallJob.class, this);
        ccAiCallJob.setVendorJobId(null);
        ccAiCallJob.setStartupMode(this.startupMode);
        if (StringUtils.isEmpty(this.startupMode)) {
            ccAiCallJob.setStartupMode("Manual");
        }
        String date = DateUtils.toStr(new Date(this.timestamp), "yyyyMM");
        ccAiCallJob.setCustomerIndex(AI_JOB_CUSTOMER_ES_PREFIX + date);
        ccAiCallJob.setJobName(this.name);
        ccAiCallJob.setConcurrency(this.concurrentNumber);
        if (tenant != null) {
            ccAiCallJob.setTenantId(tenant.getTenantId());
            ccAiCallJob.setBotId(tenant.getBotId());
            ccAiCallJob.setUserPin(tenant.getUserPin());
        }
        ccAiCallJob.setJobSchoolId("");
        ccAiCallJob.setJobSchoolName("");
        ccAiCallJob.setParentJobId(parentJobId);
        ccAiCallJob.setJobLevel(StringUtils.isEmpty(parentJobId) ? 0 : 1);
        ccAiCallJob.setJobProgress(1);
        ccAiCallJob.setAiJobType(callChannelEnum.getType());
        ccAiCallJob.setOutboundProcess(0.00);
        ccAiCallJob.setConnectedTaskNum(0);
        ccAiCallJob.setTotalTaskNum(customerSize);
        ccAiCallJob.setDptId(ucUser.getDptId());
        ccAiCallJob.setSchoolId(ucUser.getSchoolId());
        ccAiCallJob.setSchoolName(ucUser.getSchoolName());
        ccAiCallJob.setCreator(ucUser.getUserId());
        ccAiCallJob.setCreatorName(ucUser.getUserName());
        ccAiCallJob.setCreateDate(DateUtils.getNowDString());
        ccAiCallJob.setModifier(ucUser.getUserId());
        ccAiCallJob.setModifyName(ccAiCallJob.getCreatorName());
        ccAiCallJob.setModifyDate(ccAiCallJob.getCreateDate());
        ccAiCallJob.setErrorLog("");
        ccAiCallJob.setIsRedial(this.isRedial);
        if (this.isRedial == 1) {
            ccAiCallJob.setRedialTimes(this.redialTimes);
            ccAiCallJob.setRedialInterval(this.redialInterval);
            ccAiCallJob.setRedialReason(this.redialReason);
        }
        // 线路属性赋值
        lineList.forEach((t) -> {
            if (this.lineId.equals(t.getLineId())) {
                ccAiCallJob.setLineName(t.getLineName());
                if (t.getDisplayNumbers() != null) {
                    ccAiCallJob.setDisplayNumber(t.getDisplayNumbers().get(0));
                }
            }
        });
        return ccAiCallJob;
    }

    /**
     * 获取星期列表内距离当天最近的时间
     *
     * @param jobOfflineWeek 星期列表
     * @param beginTime      任外呼时段起始时间
     * @return java.lang.String
     * <AUTHOR>
     * @Date 11:08 2022/8/12
     **/
    private String getStartTime(List<Integer> jobOfflineWeek, String beginTime) {
        // 当天星期几
        int weekValue = LocalDate.now().getDayOfWeek().getValue();
        int latestWeek = 0;
        // 进行由小到大排序
        jobOfflineWeek.sort(Integer::compareTo);
        boolean flag = false;
        // 得到最近的星期
        for (Integer week : jobOfflineWeek) {
            if (weekValue == week) {
                flag = true;
                latestWeek = week;
                break;
            }
            if (weekValue < week) {
                flag = true;
                latestWeek = week;
                break;
            }
        }
        if (!flag) {
            latestWeek = jobOfflineWeek.get(0);
        }
        int differenceValue = (weekValue - latestWeek);
        // 得到开始日期
        int plusDay = differenceValue <= 0 ? (Math.abs(differenceValue)) : (7 - differenceValue);
        LocalDate localDate = LocalDate.now().plusDays(plusDay);
        String date = localDate.format(DateTimeFormatter.ofPattern(DateUtil.YMD));
        // 如果开始时间是今天,延迟5分钟后开始
        if (0 == plusDay) {
            return LocalDateTime.now().plusMinutes(5).format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_FORMAT));
        }
        return date + " " + beginTime;
    }

    public JDCreateJobRequest() {
    }

    public void check(CcAiCallJobService ccAiCallJobService) {
        // 日期时间段校验
        this.checkJobOfflineWeek(this.getJobOfflineWeek());
        if (this.getIsRedial() == 1 && (this.getRedialTimes() == null || this.getRedialInterval() == null || StringUtils.isEmpty(this.getRedialReason()))) {
                throw new ApplicationException(ApiErrorCodes.argument_invalided, "请补充完整重试策略!!!");

        }
        if (!"Manual".equals(this.getStartupMode()) && !"Timing".equals(this.getStartupMode())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "非法参数!!!启动模式(Manual->手动、Timing->定时),不传默认手动，但不能传null!!!");
        }
        if ("Timing".equals(this.getStartupMode()) && StringUtils.isEmpty(this.getStartTime())) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "请补充任务启动时间!!!");
        }
        // 校验外呼名称是否重复
        String name = this.getName();
        boolean flag = ccAiCallJobService.existJobName(name);
        if (flag) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "外呼任务名称重复!!!");
        }
    }

    /**
     * 创建任务和编辑任务时对任务执行时间段进行校验
     *
     * @param jobOfflineWeek 外呼时段星期列表
     * <AUTHOR>
     * @since 15:27 2022/9/22
     **/
    public static void checkJobOfflineWeek(String jobOfflineWeek) {
        try {
            if (StringUtils.isEmpty(jobOfflineWeek)) {
                return;
            }
            String[] weekList = jobOfflineWeek.split(",");
            for (String week : weekList) {
                if (StringUtils.isEmpty(week)) {
                    throw new ApplicationException(ApiErrorCodes.argument_invalided, "外呼时段星期列表不能为空，确保使用英文逗号分隔");
                }
                int weekNumber = Integer.parseInt(week);
                if (weekNumber > 7 || weekNumber < 1) {
                    throw new ApplicationException(ApiErrorCodes.argument_invalided, "外呼时段星期列表必须在1到7之间: " + week);
                }
            }
        } catch (NumberFormatException e) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "外呼时段星期列表格式无效，必须为整数");
        } catch (Exception e) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "在外呼时段星期列表的校验时，发生未知错误: " + e.getMessage());
        }
    }

}
