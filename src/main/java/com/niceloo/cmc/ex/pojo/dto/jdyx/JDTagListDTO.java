package com.niceloo.cmc.ex.pojo.dto.jdyx;

import lombok.Data;

import java.util.List;

/**
 * * 京东言犀接口<b>--话术意向标签查询--</b>返回值
 *
 * <AUTHOR>
 * @since 2024-11-22 17:00:57
 */
@Data
public class JDTagListDTO {
    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 标签组名
     */
    private String name;

    /**
     * 组类型：1为系统标签组，2为自定义标签组
     */
    private Integer type;

    /**
     * 机器人编号
     */
    private Long botId;

    /**
     * 标签类型：answerLabel（答案标签），intentLabel（规则标签）
     */
    private String labelType;

    /**
     * 标签数组
     */
    private List<TagDTO> tagList;

    /**
     * 总页数
     */
    private Long pageCount;

    /**
     * 开始索引
     */
    private Long startIndex;

    /**
     * 上一页的页码
     */
    private Long previousPage;

    /**
     * 是否是首页
     */
    private Boolean firstPage;

    /**
     * 是否是最后一页
     */
    private Boolean lastPage;

    /**
     * 结束索引
     */
    private Long endIndex;

    /**
     * 下一页
     */
    private Long nextPage;

    /**
     * 页大小
     */
    private Long pageSize;

    /**
     * 当前页
     */
    private Long currentPage;

    /**
     * 总数据量
     */
    private Long totalCount;

    @Data
    public static class TagDTO {
        /**
         * 标签组名称
         */
        private String groupName;

        /**
         * 意向类型
         */
        private String intentType;

        /**
         * 标签组ID
         */
        private Long groupId;

        /**
         * 标签类型
         */
        private String labelType;

        /**
         * 标签名称
         */
        private String name;

        /**
         * 标签描述
         */
        private String description;

        /**
         * 机器人编号
         */
        private Long botId;

        /**
         * 详细信息
         */
        private String detail;

        /**
         * 标签ID
         */
        private Long id;

        /**
         * 标签内容
         */
        private String content;
    }
}
