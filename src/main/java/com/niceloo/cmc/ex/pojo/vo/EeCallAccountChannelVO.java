package com.niceloo.cmc.ex.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 员工外呼账号通道VO
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-29 09:43
 */
@Data
@NoArgsConstructor
public class EeCallAccountChannelVO {

    @ApiModelProperty("员工userid")
    private String eeUserId;

    @ApiModelProperty("该员工拥有的外呼通道列表[(JLFY->巨量飞鱼,ZHZX->中弘智享,JDYX->京东言犀AI外呼账号)]")
    private List<String> channelType;

    public EeCallAccountChannelVO(String eeUserId) {
        this.eeUserId = eeUserId;
        this.channelType = new ArrayList<>();
    }
}
