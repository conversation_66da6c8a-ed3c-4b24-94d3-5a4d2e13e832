package com.niceloo.cmc.ex.pojo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.cmc.ex.common.BizConst;
import lombok.Data;

/**
 * @description: 拉取TQ通话记录请求对象
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-12-27 11:25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PullTQRecordRequest {

    /**
     * 当前页数
     */
    private int pageNum;

    /**
     * 每页数量
     */
    private int pageSize = BizConst.PAGE_SIZE;

    /**
     * 通话时间的起始时间戳(十位)
     */
    private String start_time;

    /**
     * 通话时间的截止时间戳(十位)
     */
    private String end_time;


    /**
     * 外呼成功后返回的唯一id
     */
    private String client_id;

    /**
     * 排序字段
     */
    private String order_name = "insert_time";

    /**
     * 排序规则
     */
    private String order_rule = "asc";


    public PullTQRecordRequest(int pageNum, String start_time, String end_time) {
        this.pageNum = pageNum;
        this.start_time = start_time;
        this.end_time = end_time;
    }
}
