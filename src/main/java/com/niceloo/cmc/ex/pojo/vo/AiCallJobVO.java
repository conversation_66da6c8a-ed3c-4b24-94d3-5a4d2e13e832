package com.niceloo.cmc.ex.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * AI外呼任务列表VO
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Data
@ApiModel("AI外呼任务列表VO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiCallJobVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String jobId;

    @ApiModelProperty("父任务id(如果本身就是父任务则为空字符串)")
    private String parentJobId;

    @ApiModelProperty("任务等级(0->父任务,1->子任务)")
    private Integer jobLevel;

    @ApiModelProperty("外呼通道类型(京东言犀->JDYX,百应->BYAI)")
    private String aiJobType;

    @ApiModelProperty("第三方厂商 任务id")
    private Integer vendorJobId;

    @ApiModelProperty("任务名称")
    private String jobName;

    @ApiModelProperty("话术模板id")
    @TableField("contextId")
    private Integer contextId;

    @ApiModelProperty("话术模板名称")
    private String contextName;
    
    @ApiModelProperty("任务所属分校名称")
    private String jobSchoolName;

    @ApiModelProperty("任务进度(1->已创建,2->同步客户,3->创建失败,4->已完成)")
    private Integer jobProgress;

    @ApiModelProperty("任务状态（waiting 队列中, running 执行中, finished 已完成, paused 暂停中, canceled 已取消, stopped 已结束）")
    private String status;

    @ApiModelProperty("外呼项目")
    private String callProject;

    @ApiModelProperty("挖掘项目")
    private String exploitProject;

    @ApiModelProperty("执行进度")
    private Double outboundProcess;

    @ApiModelProperty("接通数量")
    private Integer connectedTaskNum;

    @ApiModelProperty("客户总数量")
    private Integer totalTaskNum;

    @ApiModelProperty("创建人名称")
    private String creatorName;

    @ApiModelProperty("创建人UserId")
    private String creator;

    @ApiModelProperty("创建时间")
    private String createDate;

    @ApiModelProperty("更新人名称")
    private String modifyName;

    @ApiModelProperty("更新时间")
    private String modifyDate;

    @ApiModelProperty("异常信息")
    private String errorLog;

    @ApiModelProperty("子任务列表")
    private List<AiCallJobVO> children;

}
