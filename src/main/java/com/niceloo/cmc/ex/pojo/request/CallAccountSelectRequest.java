package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("外呼账号查询参数")
public class CallAccountSelectRequest extends AuthBaseRequest{

    /**
     * 分页起始,即offset
     */
    @IsAvlStartindex
    @ApiModelProperty("分页起始")
    private Integer pageIndex = 0;

    /**
     * 当页显示数
     */
    @IsAvlCount
    @ApiModelProperty("当页显示数")
    private Integer pageSize = 10;

    @ApiModelProperty("外呼通道所属类型(JLFY->巨量飞鱼,ZHZX->中弘智享,JDYX->京东言犀AI外呼)")
    @IsRangeVal({"JLFY", "ZHZX", "JDYX"})
    @NotBlank(message = "外呼通道所属类型不能为空")
    private String channelType;

    @IsSafe
    @ApiModelProperty("员工编号")
    private String eeNo;
    
    @IsSafe
    @ApiModelProperty("主叫号码")
    private String account;

    @IsSafe
    @ApiModelProperty("绑定状态(N->未绑定，Y->已绑定)")
    private String bindStatus;

    @IsSafe
    @ApiModelProperty("禁用状态(Y->禁用,N->启用)")
    private String disableStatus;

    @IsYMD
    @ApiModelProperty("创建时间-开始时间(yyyy-MM-dd)")
    private String startTime;

    @IsYMD
    @ApiModelProperty("创建时间-结束时间(yyyy-MM-dd)")
    private String endTime;
}
