package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsAvlCount;
import com.niceloo.framework.web.validation.anno.IsAvlStartindex;
import com.niceloo.framework.web.validation.anno.IsRangeVal;
import com.niceloo.framework.web.validation.anno.IsYMDHMS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * AI外呼任务列表查询参数
 *
 * <AUTHOR>
 * @Date 2022-08-02 15:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("AI外呼任务列表参数")
public class AICallJobListRequest extends AuthBaseRequest implements Serializable {

    private static final long serialVersionUID = -8010438682756307468L;
    /**
     * 分页起始,即offset
     */
    @IsAvlStartindex
    @ApiModelProperty("分页起始")
    private Integer pageIndex = 0;

    /**
     * 当页显示数
     */
    @IsAvlCount
    @ApiModelProperty("当页显示数")
    private Integer pageSize = 10;

    @ApiModelProperty("外呼任务名称")
    private String name;

    @ApiModelProperty("外呼话术模板id")
    private Integer contextId;

    @ApiModelProperty("外呼状态（waiting 队列中, running 执行中, finished 已完成, paused 暂停中, canceled 已取消, stopped 已结束）")
    @IsRangeVal({"waiting", "running", "finished", "paused", "canceled", "stopped"})
    private String status;

    @ApiModelProperty("任务所属的分校id")
    private String jobSchoolId;

    @IsYMDHMS
    @ApiModelProperty("创建时间-开始时间(yyyy-MM-dd HH:mm:ss)")
    private String startTime;

    @IsYMDHMS
    @ApiModelProperty("创建时间-结束时间(yyyy-MM-dd HH:mm:ss)")
    private String endTime;

    @ApiModelProperty("外呼通道类型(京东言犀->JDYX,百应->BYAI)")
    private String aiJobType;
}
