package com.niceloo.cmc.ex.pojo.param;

import com.niceloo.framework.web.validation.anno.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @desc: 云客通话记录参数
 * @author: song
 * @date: 2022/2/28
 */
@ApiModel("云客通话记录参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class YXRecordsParam {

    @ApiParam(value = "呼叫通道(ZK:亿讯;YH:亿讯回拨;YXSIP:亿讯SIP)", required = true)
    @NotBlank(message = "呼叫通道必填")
    @IsRangeVal({"ZK","YH", "YXSIP"})
    private String channelType;

    @ApiParam(value = "账号Id(例:C20)", required = true)
    @NotBlank(message = "账号Id必填")
    private String accountId;

    @ApiParam(value = "起始时间", required = true)
    @NotBlank(message = "起始时间必填")
    @IsYMDHMS
    private String startDateStr;

    @ApiParam(value = "结束时间", required = true)
    @NotBlank(message = "结束时间必填")
    @IsYMDHMS
    private String endDateStr;

    @ApiParam(value = "执行步长(单位小时,默认值:1)")
    private Integer stepHour = 1;

    @ApiParam(value = "是否下载录音(Y:是;N:否)")
    @IsRangeVal({"Y","N"})
    private String downloadVoice = "Y";
}
