package com.niceloo.cmc.ex.pojo.dto.byai;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

/**
 * 百应ai接口<b>--查询指定任务ID执行状态--</b>返回值
 * 百应ai接口<b>--任务状态变更回调--</b>返回值
 *
 */
@Data
public class BYAIJobInfoDTO {

    /**
     * 任务id
     */
    @JsonAlias("callJobId")
    private Integer jobId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 话术模板id
     */
    private Integer contextId;

    /**
     * 任务状态（waiting 队列中, running 执行中, finished 已完成, paused 暂停中, canceled 已取消, stopped 已结束）
     */
    private String status;

    /**
     * 任务开始时间
     */
    private String startTime;

    /**
     * 任务结束时间
     */
    private String endTime;

    /**
     * 任务创建时间
     */
    private String createTime;

    /**
     * 拨打号码总数
     */
    private Integer totalCalledNum;

    /**
     * 已拨打号码数
     */
    private Integer calledTaskNum;

    /**
     * 接通号码数
     */
    private Integer connectedTaskNum;

    /**
     * 未拨打号码数
     */
    private Integer pendingTaskNum;

    /**
     * 租户标识
     */
    private Integer tenantId;

    /**
     * 机器人标识
     */
    private Integer botId;

    /**
     * 百应companyId
     */
    private Long companyId;
}
