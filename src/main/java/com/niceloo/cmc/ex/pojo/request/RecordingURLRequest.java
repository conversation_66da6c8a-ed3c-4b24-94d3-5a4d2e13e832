package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsYMD;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 通话录音转换参数
 *
 * <AUTHOR>
 * @Date 2022-11-21 09:49
 */
@Data
@ApiModel("通话录音转换参数")
public class RecordingURLRequest {

    @ApiModelProperty("当前录音所在的月份(格式: yyyy-MM-dd)")
    @IsYMD
    @NotBlank
    private String date;

    @ApiModelProperty("外呼使用的厂商类型(JDYX->京东言犀,P->人工外呼)")
//    @IsRangeVal(value = {"JDYX", "P"})
    @NotBlank
    private String channelType;

    @NotBlank
    @ApiModelProperty("录音源地址")
    private String sourceUrl;
}