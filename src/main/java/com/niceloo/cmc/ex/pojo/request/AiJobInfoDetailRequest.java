package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsAvlCount;
import com.niceloo.framework.web.validation.anno.IsAvlStartindex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * AI外呼任务话务数量统计参数
 *
 * <AUTHOR>
 * @Date 2022-11-23 14:13
 */
@Data
@ApiModel("AI外呼任务话务数量统计参数")
public class AiJobInfoDetailRequest {

    @IsAvlStartindex
    @ApiModelProperty("分页起始")
    private Integer pageIndex = 0;

    @IsAvlCount
    @ApiModelProperty("当页显示数")
    private Integer pageSize = 10;

    @ApiModelProperty("AI外呼任务主键id")
    @NotBlank(message = "AI外呼任务主键id不能为空")
    private String aiJobId;
}