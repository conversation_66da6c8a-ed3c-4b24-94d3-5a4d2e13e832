package com.niceloo.cmc.ex.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * AI外呼任务话务数量统计返回值
 *
 * <AUTHOR>
 * @Date 2022-11-22 17:31
 */
@Data
@ApiModel("AI外呼任务话务数量统计返回值")
@NoArgsConstructor
public class AiJobCallNumStateVO {

    @ApiModelProperty("任务id")
    private String jobId;

    @ApiModelProperty("总外呼数量")
    private long callNum;

    @ApiModelProperty("接通总数")
    private long connectNum;

    @ApiModelProperty("未接通数量")
    private long notConnectNum;

    @ApiModelProperty("电话接通率(0-1的小数形式)")
    private double connectRate;

    @ApiModelProperty("外呼总时长(秒数)")
    private long totalDuration;

    @ApiModelProperty("平均通话时长(秒数)")
    private long avgDuration;

    @ApiModelProperty("挂机率(0-1的小数形式)")
    private double onHookRate;

    public AiJobCallNumStateVO(String jobId, long callNum, long connectNum, long totalDuration, long onHookNum) {
        this.jobId = jobId;
        this.callNum = callNum;
        this.connectNum = connectNum;
        this.totalDuration = totalDuration;
        this.notConnectNum = callNum - connectNum;
        if (callNum != 0) {
            BigDecimal callNumBig = new BigDecimal(callNum);
            BigDecimal connectNumBig = new BigDecimal(connectNum);
            BigDecimal totalDurationBig = new BigDecimal(totalDuration);
            //电话接通率
            this.connectRate = connectNumBig.divide(callNumBig, 2, RoundingMode.HALF_DOWN).doubleValue();
            if (connectNum != 0) {
                // 平均通话时长
                this.avgDuration = totalDurationBig.divide(connectNumBig, 2, RoundingMode.HALF_DOWN).intValue();
                BigDecimal onHookNumBig = new BigDecimal(onHookNum);
                // 挂机率
                this.onHookRate = onHookNumBig.divide(connectNumBig, 2, RoundingMode.HALF_DOWN).doubleValue();
            }
        }
    }
}