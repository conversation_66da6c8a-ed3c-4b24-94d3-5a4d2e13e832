package com.niceloo.cmc.ex.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * 中弘智享回调DTO
 *
 * <AUTHOR>
 * @since 2023-08-09 15:33:19
 */
@Data
public class ZHZXCallRecordsDTO {
    /**
     * 通话唯一标识
     */
    @ApiModelProperty(value = "通话唯一标识")
    @JsonProperty("session_id")
    private String sessionId;
    /**
     * 随路数据
     */
    @ApiModelProperty(value = "随路数据")
    @JsonProperty("remark")
    private String remark;
    /**
     * 通话绑定描述
     */
    @ApiModelProperty(value = "通话绑定编码")
    @JsonProperty("code")
    private String code;
    /**
     * 通话绑定描述
     */
    @ApiModelProperty(value = "通话绑定描述")
    @JsonProperty("msg")
    private String msg;
    /**
     * A 号码
     */
    @ApiModelProperty(value = "A 号码 ")
    @JsonProperty("callee_a_num")
    private String calleeANum;
    /**
     * B 号码
     */
    @ApiModelProperty(value = "B 号码")
    @JsonProperty("callee_b_num")
    private String calleeBNum;
    /**
     * A 路振铃时间
     */
    @ApiModelProperty(value = "A 路振铃时间")
    @JsonProperty("caller_alert_time")
    private String callerAlertTime;
    /**
     * A 路接通时间
     */
    @ApiModelProperty(value = "A 路接通时间")
    @JsonProperty("caller_answer_time")
    private String callerAnswerTime;
    /**
     * B 路呼叫时间
     */
    @ApiModelProperty(value = "B 路呼叫时间")
    @JsonProperty("callee_start_time")
    private String calleeStartTime;
    /**
     * B 路振铃时间
     */
    @ApiModelProperty(value = "B 路振铃时间")
    @JsonProperty("callee_alert_time")
    private String calleeAlertTime;
    /**
     * B 路接通时间
     */
    @ApiModelProperty(value = "B 路接通时间")
    @JsonProperty("callee_answer_time")
    private String calleeAnswerTime;
    /**
     * A 路通话时长
     */
    @ApiModelProperty(value = "A 路通话时长")
    @JsonProperty("caller_call_duration")
    private int callerCallDuration;
    /**
     * B 路通话时长
     */
    @ApiModelProperty(value = "B 路通话时长")
    @JsonProperty("callee_call_duration")
    private int calleeCallDuration;
    /**
     * 挂机时间
     */
    @ApiModelProperty(value = "挂机时间")
    @JsonProperty("hangup_time")
    private String hangupTime;
    /**
     * 挂机原因
     */
    @ApiModelProperty(value = "挂机原因")
    @JsonProperty("hangup_reason")
    private String hangupReason;
    /**
     * 挂机原因描述
     */
    @ApiModelProperty(value = "挂机原因描述")
    @JsonProperty("hangup_description")
    private String hangupDescription;
    /**
     * 录音地址
     */
    @ApiModelProperty(value = "录音地址")
    @JsonProperty("record_url")
    private String recordUrl;

    /**
     * 将中弘智享厂商返回的通话信息转换为CallRecord
     *
     * @return com.niceloo.cmc.ex.entity.es.CallRecord
     * <AUTHOR>
     * @since 2023-08-10 14:52:48
     **/

    public CallRecord callRecordsConverter() {
        CallRecord callRecord = new CallRecord();
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setChannelType(CallChannelEnum.CALL_TYPE_ZHZX.getType());
        callRecord.setVoiceSourceId(this.sessionId);
        callRecord.setCallAccount(notEmpty(this.calleeANum));
        callRecord.setField2(notEmpty(this.sessionId));
        callRecord.setDataCompleteStatus("N");
        callRecord.setVoiceSyncStatus(0);
        callRecord.setVoiceSourceUrl(notEmpty(this.recordUrl));
        callRecord.setDuration(this.calleeCallDuration);
        callRecord.setCallType(BizConst.CALL_OUT);
        callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.calleeBNum));
        callRecord.setCallPhone(notEmpty(this.calleeANum));
        callRecord.setRemarks(notEmpty(this.remark));
        callRecord.setIntentionMsg(notEmpty(this.hangupReason));
        callRecord.setCallTime(
                DateUtil.getFirstNonNullDate(
                        this.callerAlertTime,
                        this.callerAnswerTime,
                        this.calleeStartTime,
                        this.calleeAlertTime,
                        this.calleeAnswerTime,
                        this.hangupTime
                )
        );
        callRecord.setDataSyncTime(new Date());
        callRecord.setCreatedTime(new Date());
        callRecord.setModifiedTime(callRecord.getCreatedTime());
        callRecord.setModifier("SYSTEM");
        callRecord.setVoiceStatus(BizConst.VOICE_NO_DEAL);
        callRecord.setIsConnectSuccess("N");
        callRecord.setIsValidCall("N");
        callRecord.setAgentId("");
        callRecord.setIsConnected("N");
        if (this.calleeCallDuration > 0) {
            callRecord.setIsConnectSuccess("Y");
            callRecord.setVoiceStatus(BizConst.VOICE_DEALING);
            callRecord.setIsConnected("Y");
            if (this.calleeCallDuration >= BizConst.EFFECTIVE_TIME) {
                callRecord.setIsValidCall("Y");
            }
        }
        //校验是否是该通话记录的录音
        callRecord.setServerFolder("");
        if (StringUtils.isEmpty(this.recordUrl) && this.calleeCallDuration < 20) {
            callRecord.setDataCompleteStatus("Y");
        } else {
            callRecord.setVoiceSyncStatus(1);
        }
        return callRecord;
    }

    /**
     * 补充没有数据的空值
     * @param callRecord 通话记录实体
     * <AUTHOR>
     * @since 10:17 2022/2/23
     **/
    public void replenishEmpty(CallRecord callRecord) {
        //客户信息
        callRecord.setReciverUserId("");
        callRecord.setReciverbdUserId("");
        callRecord.setReciverName("");
        callRecord.setAreaCode("");
        //员工信息
        callRecord.setCallerUserId("");
        callRecord.setCallerName("");
        callRecord.setCallerPhone("");
        callRecord.setSchoolId("");
        callRecord.setSchoolName("");
        callRecord.setDptId("");
        callRecord.setDptName("");
        //通话信息
        callRecord.setField1("");
        callRecord.setVoiceUrl("");
        callRecord.setAreaName("");
        callRecord.setAgentPhone("");
        callRecord.setReciverType("");
        callRecord.setSatisfaction("");
        callRecord.setCommuIntention("");
        callRecord.setProjectId("");
        callRecord.setProjectName("");
    }

    /**
     * 返回原字符串,除非字符串为null返回""
     */
    private static String notEmpty(String str) {
        return StringUtils.nullToEmpty(str);
    }
}
