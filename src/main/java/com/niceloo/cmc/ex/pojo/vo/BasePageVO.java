package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.framework.utils.CollectionUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 基础分页查询结果
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-03-03 13:50
 */
@Data
public class BasePageVO<T> {

    /**
     * 总条数
     */
    @ApiModelProperty("总条数")
    private Integer count;

    /**
     * 数据列表
     */
    @ApiModelProperty("数据列表")
    private List<T> data;

    public BasePageVO(Integer count, List<T> data) {
        this.count = count;
        this.data = data;
        if (CollectionUtils.isEmpty(data)) {
            this.data = new ArrayList<>();
        }
    }
}
