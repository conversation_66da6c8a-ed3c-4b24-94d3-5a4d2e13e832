package com.niceloo.cmc.ex.pojo.dto;

import lombok.Data;

import java.util.Date;

/**
 * @description: 用于更新通话录音信息到通话记录内时使用
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-17 14:58
 */
@Data
public class UpdateRecordingDTO {
    /**
     * 通话记录所属的索引名称
     */
    private final String index;
    /**
     * 外呼id
     */
    private final String callId;

    /**
     * 录音上传地址
     */
    private final String serverFolder;

    /**
     * 通话记录是否补充完成 Y->完成
     */
    private final String dataCompleteStatus;

    /**
     * 录音文件key
     */
    private final String field1;

    /**
     * 修改时间
     */
    private final Date modifiedTime;

    public UpdateRecordingDTO(String index, String callId, String serverFolder, String field1) {
        this.index = index;
        this.callId = callId;
        this.serverFolder = serverFolder;
        this.field1 = field1;
        this.dataCompleteStatus = "Y";
        this.modifiedTime = new Date();
    }
}
