package com.niceloo.cmc.ex.pojo.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI任务详情返回值
 *
 * <AUTHOR>
 * @Date 2022-08-31 10:27
 */
@Data
@ApiModel("AI任务详情返回值")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiCallJobInfoVO {

    @ApiModelProperty("主键ID")
    private String jobId;

    @ApiModelProperty("父任务id")
    private String parentJobId;

    @ApiModelProperty("任务等级(0->父任务,1->子任务)")
    private Integer jobLevel;

    @ApiModelProperty("任务进度(1->已创建,2->同步客户,3->创建失败,4->已完成)")
    private Integer jobProgress;

    @ApiModelProperty("任务所属分校id")
    private String jobSchoolId;

    @ApiModelProperty("任务所属分校名称")
    private String jobSchoolName;

    @ApiModelProperty("外呼通道类型(京东言犀->JDYX,百应->BYAI)")
    private String aiJobType;

    @ApiModelProperty("第三方厂商 任务id")
    private Integer vendorJobId;

    @ApiModelProperty("任务名称")
    private String jobName;

    @ApiModelProperty("外呼job任务描述")
    private String description;

    @ApiModelProperty("外呼时段星期列表[(1-7),多个星期用逗号隔开]")
    private String jobOfflineWeek;

    @ApiModelProperty("启动模式(Manual->手动、Timing->定时)")
    private String startupMode;

    @ApiModelProperty("任务的启动时间")
    private String startTime;

    @ApiModelProperty("时段_开始时分秒")
    private String timeBegin;

    @ApiModelProperty("时段_结束时分秒")
    private String timeEnd;

    @ApiModelProperty("坐席并发数量")
    private Integer concurrency;

    @ApiModelProperty("线路id")
    private Long lineId;

    @ApiModelProperty("线路名称")
    private String lineName;

    @ApiModelProperty("外显号码")
    private String displayNumber;

    @ApiModelProperty("话术模板id")
    private Integer contextId;

    @ApiModelProperty("话术模板名称")
    private String contextName;

    @ApiModelProperty("是否是重拨任务(0->不重拨,1->重播)")
    private Integer isRedial;

    @ApiModelProperty("自动重拨次数取值范围: [1~5]")
    private Integer redialTimes;

    @ApiModelProperty("重拨间隔（分钟）,京东言犀: [1~1440], 百应:[0-120]")
    private Integer redialInterval;

    @ApiModelProperty("重拨的原因(多个原因之间用竖线分隔)")
    private String redialReason;

    @ApiModelProperty("任务状态（waiting 队列中, running 执行中, finished 已完成, paused 暂停中, canceled 已取消, stopped 已结束）")
    private String status;

    @ApiModelProperty("外呼项目")
    private String callProject;

    @ApiModelProperty("挖掘项目")
    private String exploitProject;

    @ApiModelProperty("任务进度")
    private Double outboundProcess;

    @ApiModelProperty("接通数量")
    private Integer connectedTaskNum;

    @ApiModelProperty("客户总数量")
    private Integer totalTaskNum;

    @ApiModelProperty("创建人的部门id")
    private String dptId;

    @ApiModelProperty("创建人所属分校id")
    private String schoolId;

    @ApiModelProperty("创建人所属分校名称")
    private String schoolName;

    @ApiModelProperty("创建人userId")
    private String creator;

    @ApiModelProperty("创建人名称")
    private String creatorName;

    @ApiModelProperty("创建时间")
    private String createDate;

    @ApiModelProperty("更新人userId")
    private String modifier;

    @ApiModelProperty("更新人名称")
    private String modifyName;

    @ApiModelProperty("更新时间")
    private String modifyDate;

    @ApiModelProperty("异常信息")
    private String errorLog;

    @ApiModelProperty("tiandunRuleStrategyGroupId")
    private Integer tiandunRuleStrategyGroupId;

    @ApiModelProperty("tiandunRuleStrategyGroupName")
    private String tiandunRuleStrategyGroupName;

    @ApiModelProperty("priority")
    private Integer priority;
}
