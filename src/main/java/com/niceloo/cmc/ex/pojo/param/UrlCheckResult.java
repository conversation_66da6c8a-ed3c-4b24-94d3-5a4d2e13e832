package com.niceloo.cmc.ex.pojo.param;

/**
 * url路径有效性检测的返回对象
 *
 * <AUTHOR>
 * @since 2024/10/16 16:27
 */
public class UrlCheckResult {
    private boolean isUrlAccessible;
    private long fileSize;

    public UrlCheckResult(boolean isUrlAccessible, long fileSize) {
        this.isUrlAccessible = isUrlAccessible;
        this.fileSize = fileSize;
    }

    /**
     * 判断URL是否可访问
     *
     * @return 如果URL可访问，则返回true；否则返回false
     */
    public boolean isUrlAccessible() {
        return isUrlAccessible;
    }

    /**
     * 获取URL对应的文件大小
     *
     * @return 文件大小，单位为字节
     */
    public long getFileSize() {
        return fileSize;
    }
}
