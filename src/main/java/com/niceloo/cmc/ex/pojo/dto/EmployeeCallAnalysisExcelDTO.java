package com.niceloo.cmc.ex.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 员工通话分析EXCEL下载DTO
 * <AUTHOR>
 * @date 2024/9/18 17:45
 */
@Getter
@Setter
@EqualsAndHashCode
@HeadRowHeight(value = 20)
@ContentRowHeight(value = 20)
@HeadFontStyle(bold = BooleanEnum.FALSE)
@ColumnWidth(value = 20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 22)
public class EmployeeCallAnalysisExcelDTO {
    @ExcelProperty("callerUserId")
    private String callerUserId;

    @ExcelProperty("channelType")
    private String channelType;

    @ExcelProperty("通话量")
    private Long totalCount;

    @ExcelProperty("状态未更新通话量")
    private Long statelessRecordCount;

    @ExcelProperty("状态未更新占比")
    private String statelessRecordRatio;
}
