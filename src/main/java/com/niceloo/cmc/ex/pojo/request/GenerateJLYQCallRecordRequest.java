package com.niceloo.cmc.ex.pojo.request;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 巨量引擎外呼请求成功生成通话记录参数
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022年5月26日15:40:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GenerateJLYQCallRecordRequest extends GenerateCallRecordRequest {

    private static final long serialVersionUID = 2476442106789883724L;
    /**
     * 广告主ID
     */
    private Long adId;

    /**
     * 线索ID
     */
    private Long clueId;
}
