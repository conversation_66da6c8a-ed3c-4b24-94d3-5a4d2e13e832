package com.niceloo.cmc.ex.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * AI外呼任务话务数据聚合统计返回值
 *
 * <AUTHOR>
 * @Date 2022-11-22 17:31
 */
@Data
@ApiModel("AI外呼任务话务数据聚合统计返回值")
@NoArgsConstructor
public class AiJobAggsStateVO {

    @ApiModelProperty("任务id")
    private String jobId;

    @ApiModelProperty("总外呼数量")
    private long callNum;

    @ApiModelProperty("接通总数")
    private long connectNum;

    @ApiModelProperty("A意向客户数量")
    private long intentionA;

    @ApiModelProperty("B意向客户数量")
    private long intentionB;

    @ApiModelProperty("C意向客户数量")
    private long intentionC;

    @ApiModelProperty("D意向客户数量")
    private long intentionD;

    @ApiModelProperty("E意向客户数量")
    private long intentionE;

    @ApiModelProperty("F意向客户数量")
    private long intentionF;

    @ApiModelProperty("未接通客户数量")
    private long notConnectNum;

    @ApiModelProperty("未呼出客户数量")
    private long notCalledOut;

    @ApiModelProperty("0-1轮对话伦次")
    private long dialogCountGe0AndLe1;

    @ApiModelProperty("2-5轮对话伦次")
    private long dialogCountGe2AndLe5;

    @ApiModelProperty("6-9轮对话伦次")
    private long dialogCountGe6AndLe9;

    @ApiModelProperty("10轮及以上对话伦次")
    private long dialogCountGe10;
}