package com.niceloo.cmc.ex.pojo.dto;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.EasyMap;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.UUID;

/**
 * @desc: 一号互联通话记录
 * @author: song
 * @date: 2022/2/24
 */
public class OICallRecordDto {

    /**
     * @desc:
     * @author: song
     * @date: 2022/2/24
     */
    public static CallRecord callRecordCovert(EasyMap map) {
        CallRecord callRecord = getCallRecord();
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setChannelType(CallChannelEnum.CALL_TYPE_OI.getType());
        callRecord.setVoiceSourceId(map.getStr("id"));
        callRecord.setCallerName(map.getStr("username"));
        String callPhone = map.getStr("mobile");
        callRecord.setCallAccount(callPhone);
        callRecord.setCallPhone(callPhone);
        callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(map.getStr("callphone")));
        //录音判断
        long beginTime = map.getLong("begin_time");
        long endTime = map.getLong("stop_time");
        callRecord.setCallTime(new Date(beginTime * 1000));
        callRecord.setDataSyncTime(new Date());
        int duration = (int)(endTime - beginTime);
        callRecord.setDuration(duration);
        if (duration > 0) {
            callRecord.setVoiceStatus(BizConst.VOICE_DEALING);
            callRecord.setIsConnected(BizConst.CONNECTED_SUCCESS);
            callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_Y);
            if (duration >= BizConst.EFFECTIVE_TIME) {
                callRecord.setIsValidCall(BizConst.VALID_CALL_Y);
            }
        }
        //录音地址
        if (StringUtils.isNotBlank(map.getStr("address"))) {
            callRecord.setVoiceSourceUrl(map.getStr("address"));
            callRecord.setDataCompleteStatus("N");
            callRecord.setVoiceSyncStatus(1);
        }
        callRecord.setField5(map.getStr("task_tag"));
        callRecord.setCreatedTime(new Date());
        callRecord.setModifier("SYSTEM");
        callRecord.setModifiedTime(callRecord.getCreatedTime());
        return callRecord;
    }

    /**
     * @desc: 获取通话记录对象
     * @author: song
     * @date: 2022/2/24
     */
    private static CallRecord getCallRecord() {
        CallRecord callRecord = new CallRecord();
        callRecord.setCallId("");
        callRecord.setCallerUserId("");
        callRecord.setCallerName("");
        callRecord.setCallerPhone("");
        callRecord.setCallAccount("");
        callRecord.setCallPhone("");
        callRecord.setDptId("");
        callRecord.setDptName("");
        callRecord.setAgentId("");
        callRecord.setAgentPhone("");
        callRecord.setReciverUserId("");
        callRecord.setReciverbdUserId("");
        callRecord.setReciverName("");
        callRecord.setReciverPhone("");
        callRecord.setAreaCode("");
        callRecord.setAreaName("");
        callRecord.setReciverType("");
        callRecord.setSchoolId("");
        callRecord.setSchoolName("");
        callRecord.setProjectId("");
        callRecord.setProjectName("");
        callRecord.setCallType(0);
        callRecord.setCallTime(null);
        callRecord.setDuration(0);
        callRecord.setSatisfaction("");
        callRecord.setIsConnected(BizConst.CONNECTED_FAIL);
        callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_N);
        callRecord.setIsValidCall(BizConst.VALID_CALL_N);
        callRecord.setVoiceSourceUrl("");
        callRecord.setVoiceUrl("");
        callRecord.setVoiceSourceId("");
        callRecord.setVoiceSyncStatus(0);
        callRecord.setVoiceStatus("N");
        callRecord.setRemarks("");
        callRecord.setCommuIntention("");
        callRecord.setIntentionMsg("");
        callRecord.setHangUp(null);
        callRecord.setChannelType("");
        callRecord.setDataCompleteStatus("Y");
        callRecord.setServerFolder("");
        callRecord.setCreatedTime(null);
        callRecord.setModifier("");
        callRecord.setModifiedTime(null);
        callRecord.setField1("");
        callRecord.setField2("");
        callRecord.setField3("");
        callRecord.setField4("");
        callRecord.setField5("");
        callRecord.setField6("");
        return callRecord;
    }
}
