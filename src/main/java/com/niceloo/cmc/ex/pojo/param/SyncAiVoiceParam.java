package com.niceloo.cmc.ex.pojo.param;

import com.niceloo.framework.web.validation.anno.IsGTE0;
import com.niceloo.framework.web.validation.anno.IsRangeVal;
import com.niceloo.framework.web.validation.anno.IsYMDHMS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 同步AI录音参数
 * <AUTHOR>
 * @since 2024-12-03 10:47:32
 */
@ApiModel("同步AI录音参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncAiVoiceParam {

    @ApiParam(value = "索引名(一次仅支持输入一个索引名)", required = true)
    @NotBlank(message = "索引必填")
    private String index;

    @ApiParam(value = "时间起始")
    @IsYMDHMS
    @NotBlank(message = "时间起始必填")
    private String ringTimeStart;

    @ApiParam(value = "时间结束")
    @IsYMDHMS
    @NotNull(message = "时间时间结束必填")
    private String ringTimeEnd;

    @ApiParam(value = "已下载次数下限")
    @IsGTE0
    private Integer downloadTimesStart;

    @ApiParam(value = "已下载次数上限")
    @IsGTE0
    private Integer downloadTimesEnd;

    @ApiParam(value = "外呼通道类型(JDYX:京东言犀AI;BYAI:百应AI)")
    @IsRangeVal({"JDYX","BYAI"})
    private String jobType;
}
