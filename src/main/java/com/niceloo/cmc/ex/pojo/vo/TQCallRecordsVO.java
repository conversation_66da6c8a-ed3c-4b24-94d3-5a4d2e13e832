package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.StringUtils;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * @description: TQ通话记录业务查询返回值
 * @author: WangChenyu
 * @create: 2022-03-01 10:37
 */
@Data
public class TQCallRecordsVO {
    /**
     * 座席账号
     */
    private Integer uin;
    /**
     * 座席电话
     */
    private String called_id;
    /**
     * 客户电话
     */
    private String caller_id;
    /**
     * 电话开始呼叫时间；utc10位
     */
    private Integer insert_time;
    /**
     * 通话时长（秒）；long型
     */
    private Integer duration;
    /**
     * 录音URL链接
     */
    private String recordfile;
    /**
     * 电话记录唯一事件标识
     */
    private String fsunique_id;
    /**
     * 是使用接口外呼时返回的id，用来临时标记一次外呼，用来挂断电话或者与通话记录关联
     */
    private String client_id;
    /**
     * 号码归属地，格式为86751755，86为国际码
     */
    private Integer area_id;
    /**
     * 客户归属地；如：浙江杭州市固定电话
     */
    private String callerArea;
    /**
     * 坐席工号
     */
    private String seatid;
    /**
     * 呼叫方式 3:外呼电话 4:直线呼入
     */
    private Integer call_style;
    /**
     * 是否接通  1:接通  0、2、其他:未接通
     */
    private Integer is_called_phone;
    /**
     * 该次电话备注
     */
    private String resume;
    /**
     * 挂机方 1：座席侧  2：客户侧
     */
    private Integer hangup_side;
    /**
     * 呼叫途径 1：呼叫中心 2：工作手机 3：免费电话 4：400 电话 5：SIP电话
     */
    private Integer pathway;

    /**
     * 补充通话信息
     *
     * @return com.niceloo.cmc.ex.entity.es.CallRecord
     * <AUTHOR>
     * @Date 11:45 2022/3/1
     **/
    public CallRecord callRecordsConverter() {
        if(null == this.duration){
            this.duration = 0;
        }
        CallRecord callRecord = new CallRecord();
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setCallAccount(String.valueOf(this.uin));
        callRecord.setChannelType(CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType());
        callRecord.setVoiceSourceId(notEmpty(this.fsunique_id));
        callRecord.setCallTime(new Date(Long.parseLong(this.insert_time.toString()) * 1000));
        callRecord.setDataSyncTime(new Date());
        callRecord.setCreatedTime(new Date());
        callRecord.setDuration(this.duration);
        // 先替换路径 (mdb.tq.cn -> mdbnl.tq.cn)
        callRecord.setVoiceSourceUrl(this.recordfile.replace("mdb.tq.cn",CallProperties.TQProperty.newHost));
        callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_N);
        callRecord.setVoiceStatus(BizConst.VOICE_NO_DEAL);
        callRecord.setIsValidCall(BizConst.VALID_CALL_N);
        callRecord.setIsConnected(BizConst.CONNECTED_FAIL);
        callRecord.setVoiceSyncStatus(0);
        callRecord.setDataCompleteStatus("Y");
        callRecord.setCallPhone(notEmpty(this.called_id));
        callRecord.setAgentPhone(notEmpty(this.called_id));
        callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.caller_id));
        if (duration > 0) {
            callRecord.setVoiceStatus(BizConst.VOICE_DEALING);
        }
        if (duration >= BizConst.EFFECTIVE_TIME) {
            callRecord.setIsValidCall(BizConst.VALID_CALL_Y);
        }
        callRecord.setField2(notEmpty(this.client_id));
        callRecord.setField3(String.valueOf(this.pathway));
        if (null != this.call_style){
            if (call_style == 3) {
                callRecord.setCallType(BizConst.CALL_OUT);
            }
            if (call_style == 4) {
                callRecord.setCallType(BizConst.CALL_IN);
            }
        }
        callRecord.setRemarks(notEmpty(this.resume));
        callRecord.setHangUp(this.hangup_side);
        if(null != this.is_called_phone && this.is_called_phone == 1){
            callRecord.setIsConnected(BizConst.CONNECTED_SUCCESS);
            callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_Y);
        }
        callRecord.setAgentId(notEmpty(this.seatid));
        callRecord.setAreaCode(String.valueOf(this.area_id));
        callRecord.setAreaName(notEmpty(this.callerArea));
        if (StringUtils.isNotEmpty(this.recordfile)){
            callRecord.setDataCompleteStatus("N");
            callRecord.setVoiceSyncStatus(1);
        }
        return callRecord;
    }

    /**
     * 返回原字符串,除非字符串为null返回""
     */
    private static String notEmpty(String str) {
        return StringUtils.nullToEmpty(str);
    }


    /**
     * 补充没有数据的空值
     *
     * @paramter callRecord
     * <AUTHOR>
     * @Date 10:17 2022/2/23
     **/
    public void replenishEmpty(CallRecord callRecord) {
        //通话信息
        callRecord.setField1("");
        callRecord.setVoiceUrl("");
        callRecord.setReciverType("");
        callRecord.setSatisfaction("");
        callRecord.setCommuIntention("");
        callRecord.setProjectId("");
        callRecord.setProjectName("");
        callRecord.setIntentionMsg("");
        callRecord.setModifier("SYSTEM");
        callRecord.setModifiedTime(new Date());
    }
}
