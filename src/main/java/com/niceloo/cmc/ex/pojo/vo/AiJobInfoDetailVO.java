package com.niceloo.cmc.ex.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.Font;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * AI外呼任务明细返回值
 *
 * <AUTHOR>
 * @Date 2022-11-23 13:49
 */

@Data
@ApiModel("AI外呼任务话务数量统计返回值")
@NoArgsConstructor
@EqualsAndHashCode
@HeadRowHeight(value = 20)
@ContentRowHeight(value = 20)
@HeadFontStyle(bold = BooleanEnum.FALSE)
@ColumnWidth(value = 20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 22)
public class AiJobInfoDetailVO {

    @ExcelProperty("序号")
    @JsonIgnore
    private Integer IDX;

    @ExcelProperty("客户的主键id")
    @ExcelIgnore
    private String id;

    @ExcelProperty("姓名")
    @ContentFontStyle(color = Font.COLOR_RED)
    @ApiModelProperty("姓名")
    private String customerName;

    @ExcelProperty("客户ID")
    @ApiModelProperty("客户ID")
    private String customerId;

    @ExcelProperty("手机号")
    @ApiModelProperty("手机号")
    private String customerPhone;

    @ExcelProperty("所属分校")
    @ApiModelProperty("所属分校")
    private String schoolName;

    @ExcelProperty("通话状态")
    @ApiModelProperty("通话状态")
    private String callStatus = "-";

    @ExcelProperty("意向")
    @ApiModelProperty("意向")
    private String intentLabels = "-";

    @ExcelProperty("通话时长")
    @ApiModelProperty("通话时长")
    private String callDuration = "-";

    @ExcelProperty("对话伦次")
    @ApiModelProperty("对话伦次")
    private String dialogCount = "-";

    @ExcelProperty("重呼伦次")
    @ApiModelProperty("重呼伦次")
    private String redialTimes = "-";

    @ExcelProperty("开始时间")
    @ApiModelProperty("开始时间")
    private String ringTime = "-";

    @ExcelProperty("最后通话时间")
    @ApiModelProperty("最后通话时间")
    private String lastCallTime = "-";

    @ExcelProperty("更新时间")
    @ApiModelProperty("更新时间")
    private String modifiedTime = "-";

    @ExcelIgnore
    @ApiModelProperty("是否有录音(Y->有,N->没有)")
    private String hasRecording = "N";

    @ExcelIgnore
    @ApiModelProperty("录音地址")
    private String serverFolder = "";

    /**
     * AI外呼任务客户信息详情列表批量转换为VO
     *
     * @param aiCallJobCustomerInfos 客户详情实体列表
     * @return java.util.List<com.niceloo.cmc.ex.pojo.vo.AiJobInfoDetailVO>
     * <AUTHOR>
     * @Date 14:59 2022/11/23
     **/
    public static List<AiJobInfoDetailVO> bathConvertToAiJobInfoDetailVO(List<AICallJobCustomerInfo> aiCallJobCustomerInfos) {
        if (CollectionUtils.isEmpty(aiCallJobCustomerInfos)) {
            return new ArrayList<>();
        }
        List<AiJobInfoDetailVO> aiJobInfoDetailVOList = new ArrayList<>(aiCallJobCustomerInfos.size());
        int i = 1;
        for (AICallJobCustomerInfo customerInfo : aiCallJobCustomerInfos) {
            AiJobInfoDetailVO aiJobInfoDetailVO = new AiJobInfoDetailVO();
            aiJobInfoDetailVO.setIDX(i++);
            aiJobInfoDetailVO.setId(customerInfo.getId());
            aiJobInfoDetailVO.setCustomerId(customerInfo.getCustomerId());
            aiJobInfoDetailVO.setCustomerName(FieldCipherUtil.decrypt(customerInfo.getCustomerName()));
            aiJobInfoDetailVO.setSchoolName(customerInfo.getSchoolName());
            String phone = FieldCipherUtil.decrypt(customerInfo.getCustomerPhone());
            aiJobInfoDetailVO.setCustomerPhone(phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
            if ("Y".equals(customerInfo.getCallbackStatus())) {
                aiJobInfoDetailVO.setIntentLabels(Optional.of(customerInfo.getIntentLabels()).orElse("-"));
                aiJobInfoDetailVO.setDialogCount(customerInfo.getDialogCount() + "轮");
                aiJobInfoDetailVO.setRedialTimes(customerInfo.getRedialTimes() + "轮");
                aiJobInfoDetailVO.setModifiedTime(DateUtils.toStr(customerInfo.getModifiedTime()));
                aiJobInfoDetailVO.setRingTime(Optional.of(customerInfo.getRingTime()).orElse("-"));
                aiJobInfoDetailVO.setLastCallTime(Optional.of(customerInfo.getLastCallTime()).orElse("-"));
                aiJobInfoDetailVO.setCallDuration(DateUtil.secondConvertToMinutesSecond(Math.toIntExact(customerInfo.getCallDuration())));
                aiJobInfoDetailVO.setCallStatus(AICallJobCustomerInfo.getCallStatusDesc(customerInfo.getCallStatus()));
                if (StringUtils.isNotEmpty(customerInfo.getServerFolder())) {
                    aiJobInfoDetailVO.setHasRecording("Y");
                    aiJobInfoDetailVO.setServerFolder(customerInfo.getServerFolder());
                }
            }
            aiJobInfoDetailVOList.add(aiJobInfoDetailVO);
        }
        return aiJobInfoDetailVOList;
    }

}