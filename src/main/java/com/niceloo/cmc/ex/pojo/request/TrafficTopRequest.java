package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsAvlCount;
import com.niceloo.framework.web.validation.anno.IsAvlStartindex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 话务排行参数
 *
 * <AUTHOR>
 * @Date 2022-10-08 16:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("话务排行参数")
public class TrafficTopRequest extends AuthBaseRequest {

    @IsAvlStartindex
    @ApiModelProperty("分页起始,即offset")
    private Integer pageIndex = 0;

    @IsAvlCount
    @ApiModelProperty("当页显示数")
    private Integer pageSize = 10;

    @Override
    public String toString() {
        return "TrafficTopRequest{" +
                "pageIndex=" + pageIndex +
                ", pageSize=" + pageSize +
                "} " + super.toString();
    }
}
