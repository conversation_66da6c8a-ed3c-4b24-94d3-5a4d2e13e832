package com.niceloo.cmc.ex.pojo.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.framework.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.DayOfWeek;
import java.util.ArrayList;
import java.util.List;

/**
 * 京东言犀AI外呼时段参数
 *
 * <AUTHOR>
 * @Date 2022-08-02 11:10
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel("外呼时段参数")
public class JDCallPeriodParam {

    /**
     * 星期几，例：SUN, 周日；MON，周一；TUE，周二；WED，周三；THU，周四；FRI，周五；SAT，周六
     */
    @ApiModelProperty("星期几，例：SUN, 周日；MON，周一；TUE，周二；WED，周三；THU，周四；FRI，周五；SAT，周六")
    @NotBlank(message = "星期不能为空")
    private String weekday;

    /**
     * true为不拨打时段，false为拨打时段
     */
    @ApiModelProperty("true为不拨打时段，false为拨打时段")
    private boolean isOffline = false;


    @ApiModelProperty("时段_开始时分秒，例：6点整，对应06:00:00(timeBegin起始时间范围为:08:00 - 20:00)")
    @NotBlank(message = "时段_开始时分秒不能为空")
    private String timeBegin;


    @ApiModelProperty("时段_结束时分秒，例：6点整，对应06:00:00(timeBegin起始时间范围为:08:00 - 20:00)")
    @NotBlank(message = "时段_结束时分秒不能为空")
    private String timeEnd;


    public JDCallPeriodParam(String weekday, boolean isOffline, String timeBegin, String timeEnd) {
        if (StringUtils.isNotEmpty(weekday)) {
            this.weekday = weekday;
        }
        this.isOffline = isOffline;
        this.timeBegin = timeBegin;
        this.timeEnd = timeEnd;
    }

    /**
     * 不要删除,用于转为JSON时使用,让属性名展示为:isOffline
     */
    public boolean getIsOffline() {
        return isOffline;
    }

    public void setIsOffline(boolean offline) {
        isOffline = offline;
    }

    /**
     * 获取京东言犀AI外呼时段参数
     *
     * @param weekListStr 星期列表字符串,中间使用逗号隔开
     * @param timeBegin   开始时间
     * @param timeEnd     结束时间
     * @return java.util.List<com.niceloo.cmc.ex.pojo.param.JDCallPeriodParam>
     * <AUTHOR>
     * @Date 11:06 2022/8/12
     **/
    public static List<JDCallPeriodParam> getPeriodList(String weekListStr, String timeBegin, String timeEnd) {
        String[] weekList = weekListStr.split(",");
        List<JDCallPeriodParam> periodParamList = new ArrayList<>(weekList.length);
        for (String week : weekList) {
            DayOfWeek dayOfWeek = DayOfWeek.of(Integer.parseInt(week));
            JDCallPeriodParam periodParam = new JDCallPeriodParam(dayOfWeek.name().substring(0, 3), false, timeBegin, timeEnd);
            periodParamList.add(periodParam);
        }
        return periodParamList;
    }
}
