package com.niceloo.cmc.ex.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 话务统计返回结果
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-03-03 11:50
 */
@Data
public class RecordStatisticsBaseDTO {

    /**
     * 呼入个数
     */

    @ApiModelProperty("呼入个数")
    private Integer callinNum;

    /**
     * 呼入通话个数
     */
    @ApiModelProperty("呼入通话个数")
    private Integer inConnectNum;

    /**
     * 呼出个数
     */
    @ApiModelProperty("呼出个数")
    private Integer calloutNum;

    /**
     * 呼出通话个数
     */
    @ApiModelProperty("呼出通话个数")
    private Integer outConnectNum;

    /**
     * 呼出成功个数
     */
    @ApiModelProperty("呼出成功个数")
    private Integer outSuccessNum;

    /**
     * 总有效通话个数
     */
    @ApiModelProperty("总有效通话个数")
    private Integer validCallNum;

    /**
     * 通话总时长(秒)
     */
    @ApiModelProperty("通话总时长(秒)")
    private Integer durationTotal;

    /**
     * 平均通话时长(秒)
     */
    @ApiModelProperty("平均通话时长(秒)")
    private Double durationAvg;

    /**
     * 呼出成功率
     */
    @ApiModelProperty("呼出成功率")
    private Double calloutSuccess;

    /**
     * 总接通率
     */
    @ApiModelProperty("总接通率")
    private Double connectSuccess;

    /**
     * 总有效沟通率
     */
    @ApiModelProperty("总有效沟通率")
    private Double validCall;
    
}
