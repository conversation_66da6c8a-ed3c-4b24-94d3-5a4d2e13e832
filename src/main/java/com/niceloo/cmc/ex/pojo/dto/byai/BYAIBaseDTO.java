package com.niceloo.cmc.ex.pojo.dto.byai;

import lombok.Data;

/**
 * 百应ai基本返回值
 *
 */
@Data
public class BYAIBaseDTO {

    /**
     * 返回报文状态码“200”表示成功，其它为失败。详见后文“错误码示例”
     */
    private Integer code;

    /**
     * 返回结果数据
     */
    private Object data;

    /**
     * 返回结果描述信息
     */
    private String resultMsg;

    /**
     * 请求id
     */
    private String requestId;

    /*
     * 错误码集       错误说明                     解决方案
     * 40000001	    未指定clientId	            请求时传入 clientId
     * 40000002	    未找到 clientId 对应的 APP	请确定 AppId 的正确性
     * 40000009	    未指定 AccessToken	        请求时传入 AcessToken
     * 40000010	    AccessToken 不存在或者过期	    请传入有效 AccessToken
     * 40000011	    无效的 AccessToken	        请传入有效 AccessToken
     */

}
