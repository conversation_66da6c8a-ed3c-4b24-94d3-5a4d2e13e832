package com.niceloo.cmc.ex.pojo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 京东言犀-话术意向标签查询请求参数
 *
 * <AUTHOR>
 * @Since 2024-11-25 16:53:37
 */
@Data
@ApiModel("意向标签分组查询请求参数")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JDGroupListRequest implements Serializable {

    private static final long serialVersionUID = -4807044910116943259L;

    @ApiModelProperty("业务参数；话术ID")
    @NotBlank(message = "话术ID不能为空")
    private String cid;
}
