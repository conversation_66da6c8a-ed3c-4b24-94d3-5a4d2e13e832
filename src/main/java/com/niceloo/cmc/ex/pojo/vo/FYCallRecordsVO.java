package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * @description: 风云通话记录业务查询返回值
 * @author: WangChenyu
 * @create: 2022-02-26 14:00
 */
@Data
public class FYCallRecordsVO {

    /**
     * 本条通话记录的唯一ID
     */
    private String _id;

    /**
     * 主叫号码
     */
    private String CALL_NO;

    /**
     * 被叫号码
     */
    private String CALLED_NO;

    /**
     * 坐席工号
     */
    private String EXTEN;

    /**
     * 呼叫类型，值为 normal（普通来电）、dialout（外呼去电）、transfer（来电转接）、dialTransfer（外呼转接）
     */
    private String CONNECT_TYPE;

    /**
     * 处理状态，值为dealing（已接听）、notDeal（振铃未接听）、queueLeak（排队放弃）、voicemail（已留言）、 leak（IVR放弃） 、blackList（黑名单）
     */
    private String STATUS;

    /**
     * 呼叫发起时间
     */
    private String OFFERING_TIME;

    /**
     * 录音文件名
     */
    private String RECORD_FILE_NAME;

    /**
     * 录音服务器地址
     */
    private String FILE_SERVER;

    /**
     * 省
     */
    private String PROVINCE;

    /**
     * 市
     */
    private String DISTRICT;

    /**
     * 通话时长（未接通为0)
     */
    private Integer CALL_TIME_LENGTH;

    /**
     * 是使用接口外呼时返回的id，用来临时标记一次外呼，用来挂断电话或者与通话记录关联
     */
    private String ACTION_ID;

    /**
     * 客户手机号号码所属地区域
     */
    private String DISTRICT_CODE;

    /**
     * 挂机方（1：座席侧  2：客户侧  3：系统挂机）
     */
    private String HANGUP_USER;

    public CallRecord callRecordsConverter() {
        CallRecord callRecord = new CallRecord();
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setChannelType(CallChannelEnum.CALL_TYPE_FY.getType());
        callRecord.setVoiceSourceId(notEmpty(this._id));
        callRecord.setCallTime(DateUtils.toDate(this.OFFERING_TIME));
        callRecord.setDataSyncTime(new Date());
        callRecord.setCreatedTime(new Date());
        callRecord.setDuration(this.CALL_TIME_LENGTH);
        callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_N);
        callRecord.setVoiceStatus(BizConst.VOICE_NO_DEAL);
        callRecord.setIsValidCall(BizConst.VALID_CALL_N);
        callRecord.setIsConnected(BizConst.CONNECTED_FAIL);
        callRecord.setVoiceSyncStatus(0);
        callRecord.setDataCompleteStatus("Y");
        callRecord.setAgentId(notEmpty(this.EXTEN));
        if (this.CALL_TIME_LENGTH >= BizConst.EFFECTIVE_TIME) {
            callRecord.setIsValidCall(BizConst.VALID_CALL_Y);
        }
        // 外呼方式
        int callType = 0;
        if (this.CONNECT_TYPE.contains("normal") || this.CONNECT_TYPE.contains("transfer")) {
            callType = 1;
        }
        if (this.CONNECT_TYPE.contains("dialout") || this.CONNECT_TYPE.contains("dialTransfer")) {
            callType = 0;
        }
        callRecord.setCallType(callType);
        // 主叫和被叫 0->呼出,1->呼入
        if (callType == 0) {
            callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.CALLED_NO));
            callRecord.setAgentPhone(notEmpty(this.CALL_NO));
            callRecord.setCallPhone("");
        } else {
            callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.CALL_NO));
            callRecord.setCallPhone(notEmpty(this.CALLED_NO));
            callRecord.setAgentPhone("");
        }
        // 通话处理状态
        String status = this.STATUS.substring(0, 1).toUpperCase();
        if ("V".equalsIgnoreCase(status)) {
            status = BizConst.VOICE_EMAIL;
        }
        if (BizConst.VOICE_BLACK.equals(status) && this.STATUS.contains("blank")) {
            status = BizConst.VOICE_BLANK;
        }
        callRecord.setVoiceStatus(status);
        if (BizConst.VOICE_DEALING.equals(status)) {
            callRecord.setIsConnected(BizConst.CONNECTED_SUCCESS);
            callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_Y);
        }
        // 录音地址
        if ((BizConst.VOICE_DEALING.equals(status) || callRecord.getDuration() > 0)
                && !StringUtils.isEmpty(FILE_SERVER) && !StringUtils.isEmpty(RECORD_FILE_NAME)) {
            callRecord.setVoiceSourceUrl(FILE_SERVER + "/" + RECORD_FILE_NAME);
            callRecord.setVoiceSyncStatus(1);
            callRecord.setDataCompleteStatus("N");
        } else {
            callRecord.setVoiceUrl("");
            callRecord.setField1("");
            callRecord.setDataCompleteStatus("Y");
        }
        callRecord.setAreaCode(notEmpty(DISTRICT_CODE));
        callRecord.setField2(notEmpty(ACTION_ID));
        callRecord.setAreaName(notEmpty(PROVINCE) + notEmpty(DISTRICT));
        callRecord.setModifiedTime(callRecord.getCreatedTime());
        callRecord.setModifier("SYSTEM");
        callRecord.setRemarks("");
        callRecord.setIntentionMsg("");
        callRecord.setServerFolder("");
        String hangUp = notEmpty(HANGUP_USER);
        if (hangUp.contains("agent")) {
            callRecord.setHangUp(1);
        }
        if (hangUp.contains("customer")) {
            callRecord.setHangUp(2);
        }
        if (hangUp.contains("system")) {
            callRecord.setHangUp(3);
        }
        return callRecord;
    }


    /**
     * 补充没有数据的空值
     *
     * @paramter callRecord
     * <AUTHOR>
     * @Date 10:17 2022/2/23
     **/
    public void replenishEmpty(CallRecord callRecord) {
        //通话信息
        callRecord.setField1("");
        callRecord.setVoiceUrl("");
        callRecord.setReciverType("");
        callRecord.setSatisfaction("");
        callRecord.setCommuIntention("");
        callRecord.setProjectId("");
        callRecord.setProjectName("");
    }

    /**
     * 返回原字符串,除非字符串为null返回""
     */
    private static String notEmpty(String str) {
        return StringUtils.nullToEmpty(str);
    }
}
