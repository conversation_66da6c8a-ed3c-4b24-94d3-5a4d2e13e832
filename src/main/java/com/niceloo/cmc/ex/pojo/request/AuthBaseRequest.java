package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsSafe;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求接口需要数据权限限制的基本参数
 *
 * @author: <PERSON><PERSON><PERSON>yu
 * @create: 2022-04-27 16:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuthBaseRequest {
    @IsSafe
    @ApiModelProperty(value = "员工用户id", hidden = true)
    private String eeUserId;

    @IsSafe
    @ApiModelProperty(value = "分校ID,多个使用逗号分割", hidden = true)
    private String schoolId;

    @IsSafe
    @ApiModelProperty(value = "所属部门的ID,多个使用逗号分割", hidden = true)
    private String dptId;

}
