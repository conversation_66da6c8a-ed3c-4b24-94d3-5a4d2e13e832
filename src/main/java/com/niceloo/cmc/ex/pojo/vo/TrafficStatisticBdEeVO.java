package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.cmc.ex.pojo.dto.BdEeDptDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 话务统计员工属性
 *
 * <AUTHOR>
 * @Date 2022-10-08 16:07
 */
@Data
@ApiModel("话务统计员工属性")
public class TrafficStatisticBdEeVO {

    @ApiModelProperty("员工id")
    private String eeId;

    @ApiModelProperty("员工的userId")
    private String userId;

    @ApiModelProperty("员工名称")
    private String userName;

    @ApiModelProperty("员工分校ID")
    private String schoolId;

    @ApiModelProperty("员工分校名称")
    private String schoolName;

    @ApiModelProperty("员工部门ID")
    private String dptId;

    @ApiModelProperty("员工部门名称")
    private String dptName;


    /**
     * 填充值
     *
     * @param bdEeDptDTO 员工的组织架构
     * <AUTHOR>
     * @Date 10:39 2022/10/10
     **/
    public void fillValue(BdEeDptDTO bdEeDptDTO) {
        BeanUtils.copyProperties(bdEeDptDTO, this);
    }
}
