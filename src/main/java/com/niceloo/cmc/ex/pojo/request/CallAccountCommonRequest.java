package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.entity.CcCallAccountModifyLog;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 外呼账号创建、操作公共请求参数[不需要前端传，主要为了参数处理方便]
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-27 16:06
 */
@Data
public class CallAccountCommonRequest {

    @ApiModelProperty(value = "分校ID", hidden = true)
    private String schoolId;

    @ApiModelProperty(value = "分校Name", hidden = true)
    private String schoolName = "";

    @ApiModelProperty(value = "部门ID", hidden = true)
    private String dptId;

    @ApiModelProperty(value = "员工编号", hidden = true)
    private String eeNo = "";

    @ApiModelProperty(value = "员工名称", hidden = true)
    private String eeUserName = "";

    @ApiModelProperty(value = "员工ID", hidden = true)
    private String eeUserId = "";

    @ApiModelProperty(value = "操作IP(前端忽略)", hidden = true)
    protected String operationIp;

    @ApiModelProperty(value = "操作人userId(前端忽略)", hidden = true)
    protected String accountCreator;

    @ApiModelProperty(value = "操作人userName(前端忽略)", hidden = true)
    protected String accountCreatorName;


    /**
     * 添加操作人信息
     *
     * @param ngExpand 网关添加的扩展信息
     * <AUTHOR>
     * @Date 13:38 2022/4/29
     **/
    public void addOperatorInfo(NgExpand ngExpand) {
        String ip = ngExpand.getIp();
        UcUser ucUser = ngExpand.getUcUser();
        String userId = ucUser.getUserId();
        String userName = ucUser.getUserName();
        this.operationIp = ip;
        this.accountCreator = userId;
        this.accountCreatorName = userName;
    }

    /**
     * 添加外呼账号操作所属的员工其他信息.如:分校、部门、优路号
     *
     * @param userInfo 员工的其他信息MAP
     * <AUTHOR>
     * @Date 10:40 2022/5/27
     **/
    public void addEeOtherInfo(Map<String, Object> userInfo) {
        // 如果不需要数据权限校验userinfo为空
        if (MapUtils.isEmpty(userInfo)) {
            return;
        }
        this.schoolId = userInfo.get("schoolId") == null ? "" : userInfo.get("schoolId").toString();
        this.schoolName = userInfo.get("schoolName") == null ? "" : userInfo.get("schoolName").toString();
        this.dptId = userInfo.get("dptId") == null ? "" : userInfo.get("dptId").toString();
        this.eeNo = userInfo.get("eeNo") == null ? "" : userInfo.get("eeNo").toString();
        this.eeUserName = userInfo.get("userName") == null ? "" : userInfo.get("userName").toString();
        this.eeUserId = userInfo.get("userId") == null ? "" : userInfo.get("userId").toString();
    }


    /**
     * 根据账号配置信息生成操作记录
     * @param operationType 操作类型
     * @param beforeCallAccountConfig 操作前的账号信息
     * @return com.niceloo.cmc.ex.entity.CcCallAccountModifyLog
     * <AUTHOR>
     * @Date 11:12 2022/5/27
     **/
    public CcCallAccountModifyLog generateOperationLog(Integer operationType, CcCallAccountConfig beforeCallAccountConfig) {
        CcCallAccountModifyLog ccCallAccountModifyLog = new CcCallAccountModifyLog();
        ccCallAccountModifyLog.setOperationType(operationType);
        ccCallAccountModifyLog.setOperationIp(this.operationIp);
        ccCallAccountModifyLog.setAccountLogCreator(this.accountCreator);
        ccCallAccountModifyLog.setAccountLogCreatorName(this.accountCreatorName);

        ccCallAccountModifyLog.setAfterEeUserId(this.eeUserId);
        ccCallAccountModifyLog.setAfterEeUserName(this.eeUserName);
        ccCallAccountModifyLog.setAfterEeSchoolId(this.schoolId);
        ccCallAccountModifyLog.setAfterEeSchoolName(this.schoolName);

        ccCallAccountModifyLog.setBeforeEeUserId(beforeCallAccountConfig.getEeUserId());
        ccCallAccountModifyLog.setBeforeEeUserName(beforeCallAccountConfig.getEeUserName());
        ccCallAccountModifyLog.setBeforeEeSchoolId(beforeCallAccountConfig.getSchoolId());
        ccCallAccountModifyLog.setBeforeEeSchoolName(beforeCallAccountConfig.getSchoolName());
        switch (operationType) {
            case 2:
            case 6: { // 解绑、批量解绑
                ccCallAccountModifyLog.setAfterEeUserId("");
                ccCallAccountModifyLog.setAfterEeUserName("");
                ccCallAccountModifyLog.setAfterEeSchoolId("");
                ccCallAccountModifyLog.setAfterEeSchoolName("");
                break;
            }
            case 1:
            case 4: { // 新增、绑定
                ccCallAccountModifyLog.setBeforeEeUserId("");
                ccCallAccountModifyLog.setBeforeEeUserName("");
                ccCallAccountModifyLog.setBeforeEeSchoolId("");
                ccCallAccountModifyLog.setBeforeEeSchoolName("");
                break;
            }
            case 7:
            case 8: { // 启用、禁用(前后所属员工信息不发生改变)
                ccCallAccountModifyLog.setAfterEeUserId(beforeCallAccountConfig.getEeUserId());
                ccCallAccountModifyLog.setAfterEeUserName(beforeCallAccountConfig.getEeUserName());
                ccCallAccountModifyLog.setAfterEeSchoolId(beforeCallAccountConfig.getSchoolId());
                ccCallAccountModifyLog.setAfterEeSchoolName(beforeCallAccountConfig.getSchoolName());
                break;
            }
            default:
                break;
        }
        return ccCallAccountModifyLog;
    }
}
