package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.cmc.ex.pojo.dto.RecordStatisticsBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 员工维度_日话务统计
 * @author: WangChenyu
 * @create: 2022-03-03 13:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecordEeStatisticsVO extends RecordStatisticsBaseDTO {

    /**
     * 外呼账户
     */
    @ApiModelProperty("外呼账户")
    private String callAccount;

    /**
     * 员工的userid
     */
    @ApiModelProperty("员工的userid")
    private String callerUserId;


    /**
     * 外呼通道类型
     */
    @ApiModelProperty("外呼通道类型")
    private String channelType;

    /**
     * 所属员工姓名
     */
    @ApiModelProperty("所属员工姓名")
    private String callerName;

    /**
     * 员工所属分校标识
     */
    @ApiModelProperty("员工所属分校标识")
    private String schoolId;

    /**
     * 员工所属分校名称
     */
    @ApiModelProperty("员工所属分校名称")
    private String schoolName;

    /**
     * 添加员工的基础信息到话务统计
     *
     * @paramter eeStatisticsVO
     * <AUTHOR>
     * @Date 14:50 2022/3/4
     **/
    public void addEeBaeData(RecordEeStatisticsVO eeStatisticsVO) {
        this.callerName = eeStatisticsVO.getCallerName();
        this.callerUserId = eeStatisticsVO.getCallerUserId();
        this.callAccount = eeStatisticsVO.getCallAccount();
        this.channelType = eeStatisticsVO.getChannelType();
        this.schoolId = eeStatisticsVO.getSchoolId();
        this.schoolName = "";
    }
}


