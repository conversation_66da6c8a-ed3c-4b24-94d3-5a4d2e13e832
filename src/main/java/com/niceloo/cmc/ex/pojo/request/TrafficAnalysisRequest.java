package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsYMDHMS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 话务分析查询参数
 *
 * <AUTHOR>
 * @Date 2022-10-08 16:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("话务分析查询参数")
public class TrafficAnalysisRequest extends TrafficTopRequest {

    @NotBlank(message = "呼叫时间开始时间不能为空")
    @ApiModelProperty("呼叫时间开始时间")
    @IsYMDHMS
    private String startTime;

    @NotBlank(message = "呼叫时间结束时间不能为空")
    @ApiModelProperty("呼叫时间结束时间")
    @IsYMDHMS
    private String endTime;

    @Override
    public String toString() {
        return "TrafficAnalysisRequest{" +
                "startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                "} " + super.toString();
    }
}
