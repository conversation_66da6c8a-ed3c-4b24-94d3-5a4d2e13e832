package com.niceloo.cmc.ex.pojo.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 京东言犀AI外呼任务详情VO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@ApiModel("京东言犀AI外呼任务详情VO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JDYXJobDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("任务ID")
    private String jobId;

    @ApiModelProperty("任务名称")
    private String jobName;

    @ApiModelProperty("任务状态")
    private String status;

    @ApiModelProperty("任务状态描述")
    private String statusDesc;

    @ApiModelProperty("已选的外呼线路")
    private LineInfo lineInfo;

    @ApiModelProperty("AI坐席数量")
    private Integer concurrency;

    @ApiModelProperty("外呼时段")
    private List<CallPeriod> callPeriods;

    @ApiModelProperty("外呼时间")
    private CallTime callTime;

    @ApiModelProperty("自动重播开启情况")
    private RedialConfig redialConfig;

    @ApiModelProperty("重播原因勾选情况")
    private List<String> redialReasons;

    @ApiModelProperty("重播次数")
    private Integer redialTimes;

    @ApiModelProperty("间隔时间（分钟）")
    private Integer redialInterval;

    @ApiModelProperty("任务进度信息")
    private TaskProgress taskProgress;

    /**
     * 外呼线路信息
     */
    @Data
    @ApiModel("外呼线路信息")
    public static class LineInfo {
        @ApiModelProperty("线路ID")
        private Long lineId;

        @ApiModelProperty("线路名称")
        private String lineName;

        @ApiModelProperty("外显号码")
        private String displayNumber;
    }

    /**
     * 外呼时段信息
     */
    @Data
    @ApiModel("外呼时段信息")
    public static class CallPeriod {
        @ApiModelProperty("星期几（1-7）")
        private String weekDay;

        @ApiModelProperty("星期几描述")
        private String weekDayDesc;

        @ApiModelProperty("开始时间")
        private String timeBegin;

        @ApiModelProperty("结束时间")
        private String timeEnd;

        @ApiModelProperty("是否启用")
        private Boolean enabled;
    }

    /**
     * 外呼时间信息
     */
    @Data
    @ApiModel("外呼时间信息")
    public static class CallTime {
        @ApiModelProperty("启动模式")
        private String startupMode;

        @ApiModelProperty("启动模式描述")
        private String startupModeDesc;

        @ApiModelProperty("任务启动时间")
        private String startTime;

        @ApiModelProperty("任务创建时间")
        private String createTime;

        @ApiModelProperty("任务开始时间")
        private String actualStartTime;

        @ApiModelProperty("任务结束时间")
        private String endTime;
    }

    /**
     * 重播配置信息
     */
    @Data
    @ApiModel("重播配置信息")
    public static class RedialConfig {
        @ApiModelProperty("是否开启重播")
        private Boolean enabled;

        @ApiModelProperty("重播开启状态描述")
        private String enabledDesc;
    }

    /**
     * 任务进度信息
     */
    @Data
    @ApiModel("任务进度信息")
    public static class TaskProgress {
        @ApiModelProperty("拨打号码总数")
        private Integer totalCalledNum;

        @ApiModelProperty("已拨打号码数")
        private Integer calledTaskNum;

        @ApiModelProperty("接通号码数")
        private Integer connectedTaskNum;

        @ApiModelProperty("未拨打号码数")
        private Integer pendingTaskNum;

        @ApiModelProperty("外呼进度百分比")
        private Double outboundProcess;

        @ApiModelProperty("接通率")
        private Double connectedRatio;
    }
}
