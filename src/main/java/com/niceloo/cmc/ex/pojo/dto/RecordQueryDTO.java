package com.niceloo.cmc.ex.pojo.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description: RecordQueryDTO
 * @Date 2021/12/8 16:18
 */
@Data
public class RecordQueryDTO {
    public static final int QUERY_ERROR_COUNT_THRESHOLD = 300;
    public static final double MAX_QUERY_ERROR_RATE = 0.9;

    private String indexes;
    private String callTimeStart;
    private String callTimeEnd;
    private String channelType;
    private Integer queryCount;
    private Integer queryErrorCount;

    public RecordQueryDTO() {
        this.queryCount = 0;
        this.queryErrorCount = 0;
    }

    public RecordQueryDTO(String indexes) {
        this.indexes = indexes;
        this.queryCount = 0;
        this.queryErrorCount = 0;
    }

    public boolean reachMaxQueryErrorRate() {
        if (queryCount <= 0) {
            return false;
        }
        return ((double) this.queryErrorCount / (double) this.queryCount) >= MAX_QUERY_ERROR_RATE;
    }

    public void incrementQueryCount() {
        this.queryCount++;
    }

    public void incrementQueryErrorCount() {
        this.queryErrorCount++;
    }

    public boolean reachQueryErrorCountThreshold() {
        return queryErrorCount >= QUERY_ERROR_COUNT_THRESHOLD;
    }

}
