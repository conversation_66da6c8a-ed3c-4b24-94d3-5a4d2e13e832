package com.niceloo.cmc.ex.pojo.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 话务统计返回值
 *
 * <AUTHOR>
 * @Date 2022-10-08 16:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("话务统计返回值")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TrafficStatisticVO extends TrafficStatisticBdEeVO {

    @ApiModelProperty("呼出总量")
    private Integer callOutNum;

    @ApiModelProperty("呼出接通量")
    private Integer callOutConnectNum;

    @ApiModelProperty("呼出总时长")
    private Integer callOutDuration;

    @ApiModelProperty("呼入总量")
    private Integer callInNum;

    @ApiModelProperty("呼入接通量")
    private Integer callInConnectNum;

    @ApiModelProperty("呼入总时长")
    private Integer callInDuration;

    @ApiModelProperty("通话总量")
    private Integer callNum = 0;

    @ApiModelProperty("接通率")
    private Double callConnectRate = 0.0;

    @ApiModelProperty("通话总时长")
    private Integer callDuration = 0;

    @ApiModelProperty("总接通量")
    private Integer callConnectNum = 0;

    @ApiModelProperty("平均通话时长")
    private Integer callDurationAvg = 0;

    @ApiModelProperty("呼出接通率")
    private Double callOutConnectRate = 0.0;

    @ApiModelProperty("呼出平均通话时长")
    private Integer callOutDurationAvg = 0;

    @ApiModelProperty("呼入接通率")
    private Double callInConnectRate = 0.0;

    @ApiModelProperty("呼入平均通话时长")
    private Integer callInDurationAvg = 0;

}
