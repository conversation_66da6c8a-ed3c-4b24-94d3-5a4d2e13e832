package com.niceloo.cmc.ex.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 外呼账号操作记录列表返回值
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-27 15:45
 */
@Data
@ApiModel("外呼账号操作记录列表返回值")
public class CallAccountOperationListVO {

    @ApiModelProperty("外呼账号操作记录主键ID")
    private String callAccountLogId;

    @ApiModelProperty("操作类型(1->创建,2->解绑,3->换绑,4->绑定,6->批量解绑,7->启用,8->禁用)")
    private Integer operationType;

    @ApiModelProperty("操作人")
    private String operatorName;

    @ApiModelProperty("操作时间(yyyy-MM-dd HH:mm:ss)")
    private String operationTime;

    @ApiModelProperty("操作IP")
    private String operationIp;

    @ApiModelProperty("操作前账号所属员工姓名,没有返回空字符串")
    private String beforeEeUserName = "";

    @ApiModelProperty("操作后账号所属员工姓名,没有返回空字符串")
    private String afterEeUserName = "";

    @ApiModelProperty("操作前账号所属员工分校id,没有返回空字符串")
    private String beforeEeSchoolId = "";

    @ApiModelProperty("操作后账号所属员工分校id,没有返回空字符串")
    private String afterEeSchoolId = "";

    @ApiModelProperty("操作前账号所属员工分校名称,没有返回空字符串")
    private String beforeEeSchoolName = "";

    @ApiModelProperty("操作后账号所属员工分校名称,没有返回空字符串")
    private String afterEeSchoolName = "";
}
