package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.log.Logger;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2023-07-21 18:20
 */
@Data
public class SchoolAuthVO {
    private static final Logger logger = LoggerFactory.getLogger(SchoolAuthVO.class);
    /**
     * 分校可用标识
     */
    @ApiModelProperty(value ="分校可用标识", example = "Y")
    private String schoolAvlstatus;

    /**
     * 分校别名
     */
    @ApiModelProperty(value = "分校别名", example = "宿迁")
    private String schoolAlias;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID", example = "DPT20200409010000000825")
    private String dptId;

    /**
     * 分校标识
     */
    @ApiModelProperty(value = "分校标识", example = "SCHOOL20190411010000000033")
    private String schoolId;

    /**
     * 部门类型
     */
    @ApiModelProperty(value = "部门类型", example = "S")
    private String dptType;

    /**
     * 分校名称
     */
    @ApiModelProperty(value = "分校名称", example = "宿迁")
    private String schoolName;

    /**
     * 分校别名
     */
    @ApiModelProperty(value = "分校别名", example = "null")
    private String schoolNameAlias;
}
