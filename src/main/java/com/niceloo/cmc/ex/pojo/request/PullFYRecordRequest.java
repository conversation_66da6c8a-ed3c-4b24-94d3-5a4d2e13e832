package com.niceloo.cmc.ex.pojo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.cmc.ex.common.BizConst;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 拉取风云通话记录请求对象
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-26 13:41
 */
@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PullFYRecordRequest {

    /**
     * 账号信息
     */
    private String accountId;

    /**
     * 秘钥
     */
    private String apiSecret;

    /**
     * 当前页
     */
    private int page;

    /**
     * 当前页
     */
    private int pageSize = BizConst.PAGE_SIZE;

    /**
     * 通话时间的起始时间(yyyy-MM-dd HH:mm:ss)
     */
    private String beginTime;

    /**
     * 通话时间的截止时间(yyyy-MM-dd HH:mm:ss)
     */
    private String endTime;

    /**
     * 被叫号码
     */
    private String calledNo;

    public PullFYRecordRequest(String accountId, String apiSecret, String beginTime, String endTime) {
        this.accountId = accountId;
        this.apiSecret = apiSecret;
        this.beginTime = beginTime;
        this.endTime = endTime;
    }
}

