package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsSafe;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 根据业务id查询录音地址参数
 * @author: <PERSON><PERSON><PERSON>yu
 * @create: 2022-03-08 10:25
 */
@Data
@ApiModel("根据业务id查询录音地址参数")
public class BizRecordRequest {
    
    @IsSafe
    @ApiModelProperty("业务ids")
    @NotNull
    private String bizIds;

    @IsSafe
    @ApiModelProperty("bizIds对应业务数据创建时间的年月值(格式yyyyMM)")
    private String bizCreatedYMs;

    @IsSafe
    @ApiModelProperty("指定返回的字段,最多返回默认值所示的字段[callTime,duration,bizId,recordUrl,voiceStatus(是否接听[D为已接通,其他值为未接通])]")
    private String fields = "callTime,duration,bizId,recordUrl,voiceStatus";


}
