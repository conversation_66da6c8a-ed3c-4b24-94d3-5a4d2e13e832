package com.niceloo.cmc.ex.pojo.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecordDayStatisticsData extends RecordStatisticsBaseDTO {

    /**
     * 统计的日期
     */
    @ApiModelProperty("统计的日期")
    private String date;

    /**
     * 呼入通话总时长(秒)
     */
    @ApiModelProperty("呼入通话总时长(秒)")
    private Integer inDurationTotal;

    /**
     * 呼出通话总时长(秒)
     */
    @ApiModelProperty("呼出通话总时长(秒)")
    private Integer outDurationTotal;

    /**
     * 呼入接通率
     */
    @ApiModelProperty("呼入接通率")
    private Double inConnectSuccess;

    /**
     * 呼出接通率
     */
    @ApiModelProperty("呼出接通率")
    private Double outConnectSuccess;
    
}