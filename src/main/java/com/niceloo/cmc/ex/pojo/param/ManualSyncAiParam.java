package com.niceloo.cmc.ex.pojo.param;

import com.niceloo.framework.web.validation.anno.IsRangeVal;
import com.niceloo.framework.web.validation.anno.IsSafe;
import com.niceloo.framework.web.validation.anno.IsYMDHMS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * AI外呼手动同步参数
 *
 * <AUTHOR>
 * @since 2024-12-23 15:21:20
 */
@ApiModel("AI外呼手动同步参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ManualSyncAiParam {

    @ApiParam(value = "外呼通道名称(JDYX:京东言犀;BYAI:百应)", required = true)
    @NotBlank(message = "外呼通道名称不可为空")
    @IsRangeVal({"JDYX", "BYAI"})
    private String jobType;

    @ApiParam(value = "任务ID，多个用逗号隔开", required = true)
    @IsSafe
    private String jobIds;

    @ApiParam(value = "时间起始，格式为yyyy-mm-dd hh:mm:ss", required = true)
    @NotBlank(message = "时间起始必填")
    @IsYMDHMS
    private String startDate;

    @ApiParam(value = "时间结束，格式为yyyy-mm-dd hh:mm:ss", required = true)
    @NotBlank(message = "时间结束必填")
    @IsYMDHMS
    private String endDate;
}
