package com.niceloo.cmc.ex.pojo.dto;

import com.niceloo.cmc.ex.pojo.request.CountDayRequest;
import com.niceloo.cmc.ex.pojo.request.CountEeRequest;
import lombok.Data;

/**
 * 经过权限验证的查询参数类
 * 用于封装经过权限验证后的查询参数，保持不可变性
 * 
 * <AUTHOR>
 * @since 2024-10-28
 */
@Data
public class ValidatedQueryParams {
    
    /**
     * 验证后的员工用户ID
     */
    private final String eeUserId;
    
    /**
     * 验证后的部门ID列表（逗号分割）
     */
    private final String dptIds;
    
    /**
     * 验证后的分校ID列表（逗号分割）
     */
    private final String schoolIds;
    
    /**
     * 呼叫通道类型
     */
    private final String channelType;
    
    /**
     * 查询开始时间
     */
    private final String createdTimeStart;
    
    /**
     * 查询结束时间
     */
    private final String createdTimeEnd;
    
    /**
     * 查询统计类型
     */
    private final String countType;
    
    /**
     * 分页起始位置
     */
    private final Integer pageIndex;
    
    /**
     * 分页大小
     */
    private final Integer pageSize;
    
    /**
     * 构造函数
     */
    public ValidatedQueryParams(String eeUserId, String dptIds, String schoolIds, 
                               String channelType, String createdTimeStart, String createdTimeEnd,
                               String countType, Integer pageIndex, Integer pageSize) {
        this.eeUserId = eeUserId;
        this.dptIds = dptIds;
        this.schoolIds = schoolIds;
        this.channelType = channelType;
        this.createdTimeStart = createdTimeStart;
        this.createdTimeEnd = createdTimeEnd;
        this.countType = countType;
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
    }
    
    /**
     * 从CountEeRequest创建ValidatedQueryParams
     */
    public static ValidatedQueryParams fromCountEeRequest(CountEeRequest request, AuthValidationResult authResult) {
        return new ValidatedQueryParams(
            authResult.getValidatedEeUserId(),
            authResult.getValidatedDptIds(),
            authResult.getValidatedSchoolIds(),
            request.getChannelType(),
            request.getCreatedTimeStart(),
            request.getCreatedTimeEnd(),
            request.getCountType(),
            request.getPageIndex(),
            request.getPageSize()
        );
    }
    
    /**
     * 从CountDayRequest创建ValidatedQueryParams
     */
    public static ValidatedQueryParams fromCountDayRequest(CountDayRequest request, AuthValidationResult authResult) {
        return new ValidatedQueryParams(
            authResult.getValidatedEeUserId(),
            authResult.getValidatedDptIds(),
            authResult.getValidatedSchoolIds(),
            null, // CountDayRequest没有channelType
            request.getCreatedTimeStart(),
            request.getCreatedTimeEnd(),
            request.getCountType(),
            null, // CountDayRequest没有分页参数
            null
        );
    }
}
