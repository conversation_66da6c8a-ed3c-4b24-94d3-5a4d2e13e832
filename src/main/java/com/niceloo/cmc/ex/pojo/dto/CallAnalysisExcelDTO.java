package com.niceloo.cmc.ex.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.niceloo.cmc.ex.pojo.vo.TrafficStatisticVO;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.usermodel.Font;

import java.util.ArrayList;
import java.util.List;

/**
 * 通话分析EXCEL下载DTO
 *
 * <AUTHOR>
 * @Date 2022-10-08 16:48
 */
@Getter
@Setter
@EqualsAndHashCode
@HeadRowHeight(value = 20)
@ContentRowHeight(value = 20)
@HeadFontStyle(bold = BooleanEnum.FALSE)
@ColumnWidth(value = 20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 22)
public class CallAnalysisExcelDTO {

    @ExcelProperty("序号")
    private Integer IDX;

    @ExcelProperty("统计时间")
    private String date;

    @ExcelProperty("员工名称")
    @ContentFontStyle(color = Font.COLOR_RED)
    private String userName;

    @ExcelProperty("所属分校")
    private String schoolName;

    @ExcelProperty("所属部门")
    private String dptName;

    @ExcelProperty("呼出总量-员工拨出的所有通话个数，不管是否拨通接听。")
    private Integer callOutNum;

    @ExcelProperty("呼出接通量-拨出且接通的通话个数。")
    private Integer callOutConnectNum;

    @ExcelProperty("呼出总时长(分秒)-拨出且接通的总通话时长（分秒）")
    private String callOutDuration;

    @ExcelProperty("呼出接通率-呼出接通量/呼出总量")
    private String callOutConnectRate = "0%";

    @ExcelProperty("呼出平均通话时长(分秒)-拨出且接通的总通话时长/呼出接通量")
    private String callOutDurationAvg;

    @ExcelProperty("呼入总量-手机来电的所有通话个数，不管是否拨通接听。")
    private Integer callInNum;

    @ExcelProperty("呼入接通量-手机来电且接通的通话个数")
    private Integer callInConnectNum;

    @ExcelProperty("呼入总时长(分秒)-手机来电且接通的总通话时长（分秒）")
    private String callInDuration;

    @ExcelProperty("呼入接通率-呼入接通量/呼入总量")
    private String callInConnectRate = "0%";

    @ExcelProperty("呼入平均通话时长(分秒)-来电且接通的总通话时长/呼入接通量（分秒）")
    private String callInDurationAvg;

    /**
     * 通话分析数据转为ExcelDTO
     *
     * @param statisticVOList 在ES内查询出来的通话分析数据
     * @param date            查询的日期范围字符串
     * @return ExcelDTOList
     */
    public static List<CallAnalysisExcelDTO> trafficStatisticVOConverter(List<TrafficStatisticVO> statisticVOList, String date) {
        List<CallAnalysisExcelDTO> callAnalysisExcelDTOArrayList = new ArrayList<>(statisticVOList.size());
        int i = 1;
        // 通话分析数据转为ExcelDTO
        for (TrafficStatisticVO trafficStatisticVO : statisticVOList) {
            CallAnalysisExcelDTO callAnalysisExcelDTO = BeanUtils.copyFromObjToClass(CallAnalysisExcelDTO.class, trafficStatisticVO);
            int callOutConnectRate = (int) (trafficStatisticVO.getCallOutConnectRate() * 100);
            callAnalysisExcelDTO.setCallOutConnectRate(callOutConnectRate + "%");
            int callInConnectRate = (int) (trafficStatisticVO.getCallInConnectRate() * 100);
            callAnalysisExcelDTO.setCallInConnectRate(callInConnectRate + "%");
            callAnalysisExcelDTO.setCallOutDuration(DateUtil.secondConvertToMinutesSecond(trafficStatisticVO.getCallOutDuration()));
            callAnalysisExcelDTO.setCallOutDurationAvg(DateUtil.secondConvertToMinutesSecond(trafficStatisticVO.getCallOutDurationAvg()));
            callAnalysisExcelDTO.setCallInDuration(DateUtil.secondConvertToMinutesSecond(trafficStatisticVO.getCallInDuration()));
            callAnalysisExcelDTO.setCallInDurationAvg(DateUtil.secondConvertToMinutesSecond(trafficStatisticVO.getCallInDurationAvg()));
            callAnalysisExcelDTO.setIDX(i++);
            callAnalysisExcelDTO.setDate(date);
            callAnalysisExcelDTOArrayList.add(callAnalysisExcelDTO);
        }
        return callAnalysisExcelDTOArrayList;
    }
}
