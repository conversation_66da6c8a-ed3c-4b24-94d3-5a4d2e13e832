package com.niceloo.cmc.ex.pojo.request;

import com.byai.util.hash.MD5Utils;
import com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.json.JSONUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 京东言犀外呼客户参数
 *
 * <AUTHOR>
 * @since 2022-08-02 14:06
 */
@Data
@ApiModel("外呼客户参数")
@NoArgsConstructor
public class JDCallCustomerRequest implements Serializable {

    private static final long serialVersionUID = -8410513751194446525L;
    @ApiModelProperty("客户名称")
    @NotBlank(message = "客户名称不能为空")
    @Length(max = 36, message = "客户名称最大不能超过36个字符")
    private String name;

    @ApiModelProperty("客户电话号码")
    @NotBlank(message = "客户电话不能为空")
    @Length(max = 36, message = "客户电话号码最大不能超过36个字符")
    private String phone;

    @ApiModelProperty("客户id")
    @Length(max = 36, message = "客户id最大不能超过36个字符")
    private String custId;

    @ApiModelProperty("客户所属分校id")
    @Length(max = 36, message = "客户所属分校id最大不能超过36个字符")
    private String schoolId;

    @ApiModelProperty("客户所属分校名称")
    private String schoolName;

    @ApiModelProperty("客户唯一id[不需要传]")
    private String uuId;

    @ApiModelProperty("客户变量，调用方自定义参数")
    private Map<String, Object> variable;

    /**
     * 批量更新客户的同步状态(追加客户到AI外呼任务之后调用)
     *
     * @param jobId               任务id
     * @param customerRequestList 客户列表
     * @param invalidPhoneMap     追加失败的客户列表
     * @return java.util.List<com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo>
     * <AUTHOR>
     * @since 10:58 2022/8/24
     **/
    public static List<AICallJobCustomerInfo> bulkUpdateCustomerSyncStatus(Integer jobId, String jobName, List<JDCallCustomerRequest> customerRequestList, Map<String, String> invalidPhoneMap) {
        List<AICallJobCustomerInfo> aiCallJobCustomerInfoList = new ArrayList<>(customerRequestList.size());
        for (JDCallCustomerRequest customerRequest : customerRequestList) {
            AICallJobCustomerInfo aiCallJobCustomerInfo = new AICallJobCustomerInfo();
            aiCallJobCustomerInfo.setId(RecordUtil.extractId(customerRequest.getUuId()));
            aiCallJobCustomerInfo.setVendorJobId(jobId);
            aiCallJobCustomerInfo.setJobName(jobName);
            aiCallJobCustomerInfo.setSyncStatus("Y");
            aiCallJobCustomerInfo.setModifiedTime(new Date());
            // 同步失败的客户
            if (invalidPhoneMap.containsKey(customerRequest.getPhone()) || invalidPhoneMap.containsKey(MD5Utils.MD5(customerRequest.getPhone()))) {
                aiCallJobCustomerInfo.setSyncStatus("E");
                aiCallJobCustomerInfo.setErrorLog(invalidPhoneMap.get(customerRequest.getPhone()) != null ? invalidPhoneMap.get(customerRequest.getPhone()) : invalidPhoneMap.get(MD5Utils.MD5(customerRequest.getPhone())));
            }
            aiCallJobCustomerInfoList.add(aiCallJobCustomerInfo);
        }
        return aiCallJobCustomerInfoList;
    }

    /**
     * 将客户实体列表转换为京东言犀平台的客户请求参数列表。
     *
     * 该方法遍历传入的客户实体列表，将每个客户实体转换为对应的京东言犀平台客户请求参数对象，并返回这些对象的列表。
     *
     * @param indexName Elasticsearch索引的名称，用于生成客户变量的UUID。
     * @param jobCustomerInfoList 包含客户实体信息的列表。
     * @return 转换后的京东言犀平台客户请求参数列表。
     * <AUTHOR>
     * @since 2022/8/24 16:51
     */
    public static List<JDCallCustomerRequest> bulkConvertToCustomerRequest(String indexName, List<AICallJobCustomerInfo> jobCustomerInfoList) {
        List<JDCallCustomerRequest> customerRequestList = new ArrayList<>(jobCustomerInfoList.size());
        for (AICallJobCustomerInfo aiCallJobCustomerInfo : jobCustomerInfoList) {
            JDCallCustomerRequest customerRequest = new JDCallCustomerRequest();
            customerRequest.setUuId(RecordUtil.getCustomerVariablesUUID(aiCallJobCustomerInfo.getId(),indexName));
            customerRequest.setCustId(aiCallJobCustomerInfo.getCustomerId());
            // 客户的名称、客户的手机号解密
            customerRequest.setName(FieldCipherUtil.decrypt(aiCallJobCustomerInfo.getCustomerName()));
            customerRequest.setPhone(FieldCipherUtil.decrypt(aiCallJobCustomerInfo.getCustomerPhone()));
            customerRequest.setSchoolId(aiCallJobCustomerInfo.getSchoolId());
            customerRequest.setSchoolName(aiCallJobCustomerInfo.getSchoolName());
            if (StringUtils.isNotEmpty(aiCallJobCustomerInfo.getExt1())) {
                customerRequest.setVariable(JSONUtils.toMap(aiCallJobCustomerInfo.getExt1()));
            }
            customerRequestList.add(customerRequest);
        }
        return customerRequestList;
    }
}

