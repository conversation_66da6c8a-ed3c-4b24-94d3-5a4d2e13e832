package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.cmc.ex.feign.FileFeignClient;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.MapUtils;
import com.niceloo.framework.utils.StringUtils;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 提供给上游业务方的通话记录数据
 * @Date 2021/8/3 11:20
 */
@Data
public class BizRecordVO {
    private static final Logger logger = LoggerFactory.getLogger(BizRecordVO.class);
    /**
     * 与通话记录绑定的业务id
     * 即VoiceVo.field2
     */
    private String field2;
    /**
     * 呼叫时间(yyyy-MM-dd HH:mm:ss)
     */
    private String callTime;
    /**
     * 通话时长(秒)
     */
    private String duration;
    /**
     * 存放在文件服务上的录音路径,该路径不可直接访问
     */
    private String serverFolder;

    /**
     * 接听状态,(D为已接通,其他值为未接通)
     */
    private String voiceStatus;
    /**
     * 可访问的临时url(有效期30分钟), 拿serverFolder调用文件服务接口获取
     */
    private String recordUrl;

    /**
     * 业务Id
     */
    private String bizId;

    /**
     * @param bizRecordVos
     * @Description: 用ServerFolder从文件服务获取临时可访问的url, 设置到RecordUrl字段; 获取url失败时, 什么也不做
     * <AUTHOR>
     * @Date 2021/8/3 14:43
     */
    public static void fillTemporaryRecordUrl(List<BizRecordVO> bizRecordVos) {
        Set<String> filePaths = bizRecordVos.stream()
                .map(BizRecordVO::getServerFolder)
                .filter(folder -> !StringUtils.isEmpty(folder))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(filePaths)) {
            return;
        }
        // 请求文件服务获取临时访问路径
        FileFeignClient fileFeignClient = SpringUtils.getBean(FileFeignClient.class);
        String expire = SpringUtils.getProperty("zhijian.recordUrl.expire", 1000 * 60 * 30 + "");
        Map<String, Object> request = new HashMap<>(2);
        request.put("filePaths", filePaths);
        request.put("expire", expire);
        try {
            Map<String, Object> result = fileFeignClient.queryTemporaryURL(JSONUtils.toJSONString(request));
            if (result != null) {
                List<Map<String, String>> mapList = (List<Map<String, String>>) result.get("data");
                if (!CollectionUtils.isEmpty(mapList)) {
                    Map<String, String> filePath_url_map = mapList.stream().collect(Collectors.toMap(map -> map.get("filePath"), map -> map.get("url")));
                    if (!MapUtils.isEmpty(filePath_url_map)){
                        bizRecordVos.forEach(bizRecordVo -> bizRecordVo.setRecordUrl(filePath_url_map.get(bizRecordVo.getServerFolder())));
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e, e.getMessage());
        }
    }
}
