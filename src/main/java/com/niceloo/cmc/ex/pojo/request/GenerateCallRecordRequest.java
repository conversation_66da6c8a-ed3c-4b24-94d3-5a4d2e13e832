package com.niceloo.cmc.ex.pojo.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.niceloo.cmc.ex.config.CustProperties;
import com.niceloo.cmc.ex.entity.BdDpt;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.BdEeDTO;
import com.niceloo.cmc.ex.service.BdDptService;
import com.niceloo.cmc.ex.service.BdEeService;
import com.niceloo.cmc.ex.service.CcCallAccountConfigService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.cmc.ex.utils.OkHttpUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.StringUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

import static com.niceloo.cmc.ex.common.CallChannelEnum.*;


/**
 * 通用外呼请求成功生成通话记录参数
 *
 * <AUTHOR>
 * @since 2022年5月26日15:40:29
 */
@Data
// 忽略不需要序列化的字段
@JsonIgnoreProperties({"okHttpUtil", "bdDptService", "bdEeService", "callAccountConfigService"})
public class GenerateCallRecordRequest implements Serializable {

    private static final long serialVersionUID = 933238867097057571L;
    /**
     * 被叫号码
     */
    private String callee;

    /**
     * 员工Id
     */
    private String callerUserId;

    /**
     * 客户Id[custId](可能为空)
     */
    private String calleeUserId;

    /**
     * 外呼通话唯一id
     */
    private String contactId;

    /**
     * 外呼类型
     */
    private String channelType;

    /**
     * 打电话的创建时间[用来确定放在哪个索引]yyyyMM
     */
    private String createDate;

    /**
     * 主叫账号
     */
    private String callAccount;

    /**
     * 生成的通话记录实体，只在该类内使用
     */
    protected CallRecord callRecord;
    private final OkHttpUtil okHttpUtil = SpringUtils.getBean(OkHttpUtil.class);
    private final BdDptService bdDptService = SpringUtils.getBean(BdDptService.class);
    private final BdEeService bdEeService = SpringUtils.getBean(BdEeService.class);
    private final CcCallAccountConfigService callAccountConfigService = SpringUtils.getBean(CcCallAccountConfigService.class);


    public CallRecord getCallRecord() {
        return callRecord;
    }

    /**
     * 生成一个空的通话记录[一个实体只能生成一个]
     *
     * <AUTHOR>
     * @Date 15:04 2022/5/28
     **/
    private void generateEmptyCallRecord() {
        if (this.callRecord == null) {
            this.callRecord = new CallRecord();
            callRecord.setChannelType(this.channelType);
            callRecord.setCallerUserId(callerUserId);
            callRecord.setReciverUserId(calleeUserId);
            callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(callee));
            callRecord.setCallAccount(this.callAccount);
            if (CALL_TYPE_ZHZX.getType().equals(this.channelType)) {
                String[] split = callAccount.split("_");
                callRecord.setField4(split[0]);
                callRecord.setCallAccount(split[1]);
                this.channelType = CALL_TYPE_ZHZX.getType();
            }
            if (CALL_TYPE_YH.getType().equals(this.channelType)) {
                callRecord.setField3(CallRecord.CALL_TYPE_YH_FIELD3);
            }
            if (CALL_TYPE_TQ_MOBILE.getType().equals(this.channelType)) {
                callRecord.setField3(CallRecord.CALL_TYPE_TQ_MOBILE_FIELD3);
            }
            if (CALL_TYPE_TQ_FIX.getType().equals(this.channelType)) {
                callRecord.setField3(CallRecord.CALL_TYPE_TQ_FIX_FIELD3);
                this.channelType = CALL_TYPE_TQ_MOBILE.getType();
                callRecord.setChannelType(this.channelType);
            }
            if (CALL_TYPE_ZK.getType().equals(this.channelType) || CALL_TYPE_YH.getType().equals(this.channelType) || CALL_TYPE_YX_SIP.getType().equals(this.channelType)) {
                // 亿迅callAccount->	C146_2483@zhangchun
                String[] split = callAccount.split("_");
                callRecord.setField4(split[0]);
                callRecord.setCallAccount(split[1]);
                this.channelType = CALL_TYPE_ZK.getType();
            }
        }
    }

    /**
     * 在通话记录实体内加入员工信息
     *
     * <AUTHOR>
     * @Date 14:56 2022/5/28
     **/
    public void addEeInfo() {
        this.generateEmptyCallRecord();
        // 巨量飞鱼和中弘智享的账号在通讯中心数据库CcCallAccountConfig表内
        if (this.channelType.equals(CALL_TYPE_JLFY.getType()) || this.channelType.equals(CALL_TYPE_ZHZX.getType())) {
            this.addEeInfoFromCmc();
        } else {
            // 员工基本信息
            BdEeDTO bdEeDTO = bdEeService.findBdEeDTOAndSchoolNameByUserId(this.callerUserId);
            callRecord.setCallerName(bdEeDTO.getUserName());
            callRecord.setCallerPhone(bdEeDTO.getEeInnerphone());
            callRecord.setSchoolId(bdEeDTO.getSchoolId());
            callRecord.setSchoolName(bdEeDTO.getSchoolName());
            callRecord.setDptId("");
            callRecord.setDptName("");
            BdDpt bdDpt = bdDptService.findDptIdAndNameByEeId(bdEeDTO.getEeId());
            if (null != bdDpt) {
                callRecord.setDptId(bdDpt.getDptId());
                callRecord.setDptName(bdDpt.getDptName());
            }
        }
    }

    private void addEeInfoFromCmc() {
        // 员工基本信息
        CcCallAccountConfig ccCallAccountConfig = callAccountConfigService.searchCallAccountInfoByUserId(this.callerUserId, this.channelType);
        callRecord.setCallAccount(ccCallAccountConfig.getAccount());
        BdEeDTO bdEeDTO = bdEeService.findBdEeDTOAndSchoolNameByUserId(this.callerUserId);
        callRecord.setCallerName(bdEeDTO.getUserName());
        callRecord.setCallerPhone(bdEeDTO.getEeInnerphone());
        callRecord.setSchoolId(bdEeDTO.getSchoolId());
        callRecord.setSchoolName(bdEeDTO.getSchoolName());
        callRecord.setDptId("");
        callRecord.setDptName("");
        BdDpt bdDpt = bdDptService.findDptIdAndNameByEeId(bdEeDTO.getEeId());
        if (null != bdDpt) {
            callRecord.setDptId(bdDpt.getDptId());
            callRecord.setDptName(bdDpt.getDptName());
        }
    }

    /**
     * 补充客户的信息到话单[调用客户中心接口:/api/customers/${calleeUserId}]
     *
     * <AUTHOR>
     * @since  16:07 2022/5/28
     **/
    public void addCustInfo() {
        this.generateEmptyCallRecord();
        callRecord.setReciverbdUserId("");
        callRecord.setReciverName("");
        callRecord.setAreaCode("");
        callRecord.setAreaName("");

        if (StringUtils.isEmpty(this.calleeUserId)) {
            this.addCustInfoByMobile();
            return;
        }

        String response = okHttpUtil.get(CustProperties.crmUrl + "/api/customers/" + this.calleeUserId, null, null);
        if (StringUtils.isEmpty(response)) {
            return;
        }

        Map<String, Object> responseMap = JSONUtils.toMap(response);
        Map<String, Object> data = (Map<String, Object>) responseMap.getOrDefault("data", Collections.emptyMap());
        Object userId = data.get("userId");
        Object custName = data.get("custName");
        Object custMobileAreaCode = data.get("custMobileareacode");
        Object custMobileAreaName = data.get("custMobileareaname");
        callRecord.setReciverbdUserId(userId != null ? userId.toString() : "");
        callRecord.setReciverName(custName != null ? FieldCipherUtil.oneEncrypt(custName.toString()) : "");
        callRecord.setAreaCode(custMobileAreaCode != null ? custMobileAreaCode.toString() : "");
        callRecord.setAreaName(custMobileAreaName != null ? custMobileAreaName.toString() : "");
    }

    /**
     * 根据客户的手机号调用客户中心获取客户信息添加到通话记录
     *
     * <AUTHOR>
     * @since 9:39 2022/6/30
     **/
    private void addCustInfoByMobile() {
        callRecord.setReciverUserId("");
        Map<String, String> queries = new HashMap<>(2);
        queries.put("BrandId", "YOULU");
        queries.put("Mobile", FieldCipherUtil.decrypt(callRecord.getReciverPhone()));
        String response = okHttpUtil.get(CustProperties.crmUrl + "/api/customers/mobile", queries, null);
        Map<String, Object> responseMap = JSONUtils.toMap(response);
        if (responseMap.containsKey("data")) {
            Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
            Object custId = data.get("custId");
            Object userId = data.get("userId");
            Object custName = data.get("custName");
            Object custMobileAreaCode = data.get("custMobileareacode");
            Object custMobileAreaName = data.get("custMobileareaname");
            callRecord.setReciverbdUserId(null == userId ? "" : userId.toString());
            callRecord.setReciverUserId(null == custId ? "" : custId.toString());
            callRecord.setReciverName(null == custName ? "" : FieldCipherUtil.oneEncrypt(custName.toString()));
            callRecord.setAreaCode(null == custMobileAreaCode ? "" : custMobileAreaCode.toString());
            callRecord.setAreaName(null == custMobileAreaName ? "" : custMobileAreaName.toString());
        }
    }

    /**
     * 在通话记录实体内添加一些没有用的默认值属性,可以在子类中实现，重写此方法
     *
     * <AUTHOR>
     * @since  15:57 2022/5/28
     **/
    public void addOtherInfo() {
        this.generateEmptyCallRecord();
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setCreatedTime(new Date());
        callRecord.setModifiedTime(callRecord.getCreatedTime());
        // 为了话务查询的时候能够查到
        callRecord.setCallTime(callRecord.getCreatedTime());
        callRecord.setModifier("SYSTEM");
        callRecord.setRemarks("");
        callRecord.setIntentionMsg("");
        callRecord.setServerFolder("");
        callRecord.setField2(contactId);
        callRecord.setField1("");
        callRecord.setServerFolder("");
        callRecord.setDataCompleteStatus("N");
        callRecord.setCommuIntention("");
        callRecord.setSatisfaction("");
        callRecord.setProjectId("");
        callRecord.setProjectName("");
        callRecord.setReciverType("");
        callRecord.setAgentId("");
        if (CALL_TYPE_ZK.getType().equals(this.channelType)) {
            String[] agent = callRecord.getCallAccount().split("@");
            if (agent.length > 0) {
                callRecord.setAgentId(agent[0]);
            }
        }
        callRecord.setAgentPhone("");
        callRecord.setVoiceSyncStatus(0);
        //------------------------加上通话信息默认值--------------
        callRecord.setCallType(0);
        // -1表示通话信息还没有同步
        callRecord.setDuration(-1);
        callRecord.setIsConnected("");
        callRecord.setIsConnectSuccess("");
        callRecord.setIsValidCall("");
        callRecord.setVoiceSourceUrl("");
        callRecord.setVoiceUrl("");
        callRecord.setVoiceStatus("");
        callRecord.setServerFolder("");
        callRecord.setField1("");
    }

}
