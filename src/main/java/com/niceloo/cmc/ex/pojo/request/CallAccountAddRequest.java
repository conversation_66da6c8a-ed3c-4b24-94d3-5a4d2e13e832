package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.pojo.dto.ZHZXAccountInfoDTO;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.framework.web.validation.anno.IsRangeVal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotBlank;

/**
 * 外呼账号创建请求参数
 *
 * @author: <PERSON><PERSON>henyu
 * @create: 2022-04-27 16:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("外呼账号创建请求参数")
public class CallAccountAddRequest extends CallAccountCommonRequest {

    @ApiModelProperty("外呼通道所属类型(JLFY->巨量飞鱼,ZHZX->中弘智享,JDYX->京东言犀AI外呼)")
    @IsRangeVal({"JLFY", "ZHZX", "JDYX"})
    @NotBlank(message = "外呼通道所属类型不能为空")
    private String channelType;

    @ApiModelProperty("所属员工ID")
    @NotBlank(message = "所属员工ID不能为空")
    private String eeUserId;

    @ApiModelProperty("外呼账号")
    // @NotBlank(message = "必传参数不能为空")
    @Length(max = 36, message = "外呼账号长度不得超过36位")
    private String account;

    @ApiModelProperty("外呼账号密码,不需要的外呼通道可不传")
    private String password = "";

    @ApiModelProperty(value = "不同外部厂商账号信息(没有可不传)", example= "{\"userName\":\"王飞\",\"idNO\":\"522530199208180048\",\"mobile\":\"***********\"}")
    private String vendorAccountInfo = "";

    @ApiModelProperty("账号状态(Y->禁用,N->启用)")
    private String disableStatus = "N";

    @ApiModelProperty("租户ID=商户ID，AI账号配置专用")
    private String tenantId;

    /**
     * 外呼账号创建类型转换为entity
     *
     * @return com.niceloo.cmc.ex.entity.CcCallAccountConfig
     * <AUTHOR>
     * @Date 13:56 2022/4/28
     **/
    public CcCallAccountConfig typeConverter() {
        CcCallAccountConfig callAccountConfig = new CcCallAccountConfig();
        BeanUtils.copyProperties(this, callAccountConfig);
        callAccountConfig.setBindStatus("Y");
        callAccountConfig.setVendorAccountStatus("Y");
        callAccountConfig.setVendorAccountInfo("");
        callAccountConfig.setCallDelstatus("N");
        callAccountConfig.setAccountCreateddate(DateUtils.getNowDString());
        callAccountConfig.setAccountModifier(this.accountCreator);
        callAccountConfig.setAccountModifierName(this.accountCreatorName);
        callAccountConfig.setAccountModifieddate(callAccountConfig.getAccountCreateddate());
        //默认启用
        if (StringUtils.isEmpty(this.disableStatus)) {
            callAccountConfig.setDisableStatus("N");
        }
        if (CallChannelEnum.CALL_TYPE_ZHZX.getType().equals(this.channelType)) {
            ZHZXAccountInfoDTO zhzxAccountInfoDTO = JSONUtils.toObject(this.vendorAccountInfo, ZHZXAccountInfoDTO.class);
            zhzxAccountInfoDTO.setMobile(this.account);
            zhzxAccountInfoDTO.setUserName(this.getEeUserName());
            zhzxAccountInfoDTO.setIdNO(FieldCipherUtil.oneEncrypt(zhzxAccountInfoDTO.getIdNO()));
            callAccountConfig.setVendorAccountInfo(JSONUtils.toJSONString(zhzxAccountInfoDTO));
        }
        return callAccountConfig;
    }
}
