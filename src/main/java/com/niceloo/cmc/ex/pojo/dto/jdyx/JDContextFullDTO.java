package com.niceloo.cmc.ex.pojo.dto.jdyx;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.List;

@Data
public class JDContextFullDTO {
    /**
     * 话术模板ID
     */
    private Long id;

    /**
     * 话术模板名称
     */
    private String name;

    /**
     * 应用/机器人ID
     */
    private Long botId;

    /**
     * 创建数据言犀用户名
     */
    private String createdUserPin;

    /**
     * 创建时间，毫秒值
     */
    private Long createdTime;

    /**
     * 更新时间，毫秒值（冗余字段）
     */
    private Long updatedTime;

    /**
     * 格式化后的创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String formattedCreatedTime;

    /**
     * 格式化后的更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String formattedUpdatedTime;

    /**
     * 话术中引用的变量列表
     */
    private List<String> cidVariables;

    /**
     * 版本号（冗余字段）
     */
    private String version;

    /**
     * 是否可见（冗余字段）
     */
    private Boolean visible;

    /**
     * 是否可编辑（冗余字段）
     */
    private Boolean editable;

    /**
     * 是否可用（冗余字段）
     */
    private Boolean isValid;

    /**
     * vid（冗余字段）
     */
    private String venderId;

    /**
     * dataid（冗余字段）
     */
    private String dataId;
}
