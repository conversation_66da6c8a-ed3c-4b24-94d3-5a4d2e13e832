package com.niceloo.cmc.ex.pojo.vo;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.CcCallAccountConfig;
import com.niceloo.cmc.ex.pojo.dto.ZHZXAccountInfoDTO;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.cmc.ex.utils.SensitiveInfoUtils;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;

import static com.niceloo.cmc.ex.utils.EncryptDealUtil.decryptFields;

/**
 * 外呼账号VO
 *
 * @author: <PERSON><PERSON><PERSON>yu
 * @create: 2022-04-27 15:34
 */
@Data
@ApiModel("外呼账号VO")
public class CallAccountVO {

    @ApiModelProperty("外呼账号主键ID")
    private String callAccountId;

    @ApiModelProperty("分校ID")
    private String schoolId;

    @ApiModelProperty("分校名称")
    private String schoolName;

    @ApiModelProperty("员工ID")
    private String eeUserId;

    @ApiModelProperty("员工姓名")
    private String eeUserName;

    @ApiModelProperty("员工编号")
    private String eeNo;

    @ApiModelProperty("外呼通道所属类型(JLFY->巨量飞鱼,ZHZX->中弘智享,JDYX->京东言犀AI外呼)")
    private String channelType;

    @ApiModelProperty("主叫账号(中间4位脱敏,不是11位不进行脱敏)")
    private String account;

    @ApiModelProperty("绑定状态(N->未绑定，Y->已绑定)")
    private String bindStatus;

    @ApiModelProperty("禁用状态(Y->禁用,N->启用)")
    private String disableStatus;

    @ApiModelProperty("创建时间(yyyy-MM-dd HH:mm:ss)")
    private String createdTime;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("不同外部厂商账号信息(用于查看详情时使用,不需要时可忽略)")
    private Object vendorAccountInfo;

    @ApiModelProperty("租户ID=商户ID，是确定企业身份账号的唯一标识)")
    private Object tenantId;

    /**
     * 实体对应装换器
     *
     * @param callAccountConfig 数据库实体
     * @return com.niceloo.cmc.ex.pojo.vo.CallAccountListVO
     * <AUTHOR>
     * @Date 11:24 2022/4/28
     **/
    public static CallAccountVO typeConvertor(@NotNull CcCallAccountConfig callAccountConfig) {
        CallAccountVO callAccountVO = new CallAccountVO();

        // 库表字段解密
        decryptFields(callAccountConfig);

        BeanUtils.copyProperties(callAccountConfig, callAccountVO);
        callAccountVO.setCreator(callAccountConfig.getAccountCreatorName());
        callAccountVO.setCreatedTime(callAccountConfig.getAccountCreateddate());
        callAccountVO.setVendorAccountInfo(null);
        // 解析外部厂商需要的信息
        if (StringUtils.isNotEmpty(callAccountConfig.getVendorAccountInfo())) {
            CallChannelEnum callChannel = CallChannelEnum.getCallChannel(callAccountConfig.getChannelType());
            if (null != callChannel && StringUtils.isNotEmpty(callAccountConfig.getVendorAccountInfo()) && callChannel == CallChannelEnum.CALL_TYPE_ZHZX) {

                String vendorAccountInfo = callAccountConfig.getVendorAccountInfo();
                ZHZXAccountInfoDTO zhzxAccountInfoDTO = JSONUtils.toObject(vendorAccountInfo, ZHZXAccountInfoDTO.class);
                // 身份证号解密并脱敏
                String idNO = FieldCipherUtil.decrypt(zhzxAccountInfoDTO.getIdNO());
                idNO = SensitiveInfoUtils.maskIDNumber(idNO);
                zhzxAccountInfoDTO.setIdNO(idNO);
                callAccountVO.setVendorAccountInfo(zhzxAccountInfoDTO);
            }
        }
        // 手机号脱敏
        if (CallChannelEnum.CALL_TYPE_JLFY.getType().equals(callAccountConfig.getChannelType()) ||
                CallChannelEnum.CALL_TYPE_ZHZX.getType().equals(callAccountConfig.getChannelType()) ||
                CallChannelEnum.CALL_TYPE_JDYX.getType().equals(callAccountConfig.getChannelType())) {
            callAccountVO.setAccount(SensitiveInfoUtils.maskPhoneNumber(callAccountConfig.getAccount()));
        }
        return callAccountVO;
    }
}
