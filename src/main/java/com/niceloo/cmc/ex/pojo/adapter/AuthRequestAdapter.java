package com.niceloo.cmc.ex.pojo.adapter;

import com.niceloo.cmc.ex.pojo.request.AuthBaseRequest;
import com.niceloo.cmc.ex.pojo.request.CountDayRequest;
import com.niceloo.cmc.ex.pojo.request.CountEeRequest;

/**
 * 权限请求适配器
 * 用于将具体的请求对象适配为AuthBaseRequest，避免在原始类中重写方法
 * 
 * <AUTHOR>
 * @since 2024-10-28
 */
public class AuthRequestAdapter extends AuthBaseRequest {
    
    private final Object originalRequest;
    
    private AuthRequestAdapter(Object originalRequest) {
        this.originalRequest = originalRequest;
    }
    
    /**
     * 从CountDayRequest创建适配器
     */
    public static AuthRequestAdapter from(CountDayRequest request) {
        AuthRequestAdapter adapter = new AuthRequestAdapter(request);
        adapter.setEeUserId(request.getCallerUserId());
        adapter.setDptId(request.getDptIds());
        adapter.setSchoolId(request.getSchoolId());
        return adapter;
    }
    
    /**
     * 从CountEeRequest创建适配器
     */
    public static AuthRequestAdapter from(CountEeRequest request) {
        AuthRequestAdapter adapter = new AuthRequestAdapter(request);
        adapter.setEeUserId(request.getCallerUserId());
        adapter.setDptId(request.getDptIds());
        adapter.setSchoolId(request.getSchoolId());
        return adapter;
    }
    
    /**
     * 获取原始请求对象
     */
    @SuppressWarnings("unchecked")
    public <T> T getOriginalRequest() {
        return (T) originalRequest;
    }
    
    /**
     * 将适配器的值应用回CountDayRequest
     */
    public CountDayRequest applyToCountDayRequest() {
        if (!(originalRequest instanceof CountDayRequest)) {
            throw new IllegalStateException("原始请求不是CountDayRequest类型");
        }
        CountDayRequest request = (CountDayRequest) originalRequest;
        // 这里可以选择是否要将修改应用回原始对象
        // 为了保持不可变性，我们不修改原始对象
        return request;
    }
    
    /**
     * 将适配器的值应用回CountEeRequest
     */
    public CountEeRequest applyToCountEeRequest() {
        if (!(originalRequest instanceof CountEeRequest)) {
            throw new IllegalStateException("原始请求不是CountEeRequest类型");
        }
        CountEeRequest request = (CountEeRequest) originalRequest;
        // 这里可以选择是否要将修改应用回原始对象
        // 为了保持不可变性，我们不修改原始对象
        return request;
    }
}
