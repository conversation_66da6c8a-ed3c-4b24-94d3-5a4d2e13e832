package com.niceloo.cmc.ex.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.niceloo.cmc.ex.pojo.vo.ValidCallAnalysisVO;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.usermodel.Font;

import java.util.ArrayList;
import java.util.List;

/**
 * 有效通话分析EXCEL下载DTO
 *
 * <AUTHOR>
 * @Date 2022-10-08 16:48
 */
@Getter
@Setter
@EqualsAndHashCode
@HeadRowHeight(value = 20)
@ContentRowHeight(value = 20)
@HeadFontStyle(bold = BooleanEnum.FALSE)
@ColumnWidth(value = 20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 22)
public class ValidCallAnalysisExcelDTO {

    @ExcelProperty("序号")
    private Integer IDX;

    @ExcelProperty("统计时间")
    private String date;

    @ExcelProperty("员工名称")
    @ContentFontStyle(color = Font.COLOR_RED)
    private String userName;

    @ExcelProperty("所属分校")
    private String schoolName;

    @ExcelProperty("所属部门")
    private String dptName;

    @ExcelProperty("10秒有效呼出量-呼出接通，通话时长大于等于10秒的数量。")
    private Integer callOutNumGe10;

    @ExcelProperty("10秒有效呼入量-呼入接通，通话时长大于等于10秒的数量。")
    private Integer callInNumGe10;

    @ExcelProperty("20秒有效呼出量-呼出接通，通话时长大于等于20秒的数量。")
    private Integer callOutNumGe20;

    @ExcelProperty("20秒有效呼入量-呼入接通，通话时长大于等于20秒的数量。")
    private Integer callInNumGe20;

    @ExcelProperty("30秒有效呼出量-呼出接通，通话时长大于等于30秒的数量。")
    private Integer callOutNumGe30;

    @ExcelProperty("30秒有效呼入量-呼入接通，通话时长大于等于30秒的数量。")
    private Integer callInNumGe30;

    @ExcelProperty("60秒有效呼出量-呼出接通，通话时长大于等于60秒的数量。")
    private Integer callOutNumGe60;

    @ExcelProperty("60秒有效呼入量-呼入接通，通话时长大于等于60秒的数量。")
    private Integer callInNumGe60;

    /**
     * 有效通话分析数据转为ExcelDTO
     *
     * @param validCallAnalysisVOList 从ES内查询出来的有效通话分析数据
     * @param date                    查询的日期范围字符串
     * @return ExcelDTOList
     */
    public static List<ValidCallAnalysisExcelDTO> trafficStatisticVOConverter(List<ValidCallAnalysisVO> validCallAnalysisVOList, String date) {
        int i = 1;
        List<ValidCallAnalysisExcelDTO> validCallAnalysisExcelDTOList = new ArrayList<>(validCallAnalysisVOList.size());
        for (ValidCallAnalysisVO validCallAnalysisVO : validCallAnalysisVOList) {
            ValidCallAnalysisExcelDTO analysisExcelDTO = BeanUtils.copyFromObjToClass(ValidCallAnalysisExcelDTO.class, validCallAnalysisVO);
            analysisExcelDTO.setIDX(i++);
            analysisExcelDTO.setDate(date);
            validCallAnalysisExcelDTOList.add(analysisExcelDTO);
        }
        return validCallAnalysisExcelDTOList;
    }
}
