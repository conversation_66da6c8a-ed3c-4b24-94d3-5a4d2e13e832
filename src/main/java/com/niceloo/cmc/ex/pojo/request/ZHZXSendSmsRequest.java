package com.niceloo.cmc.ex.pojo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.framework.web.validation.anno.IsIdcard;
import com.niceloo.framework.web.validation.anno.IsPhone;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 中弘智享短信验证码发送请求对象
 * <AUTHOR>
 * @since 2023-07-22 14:51:03
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ZHZXSendSmsRequest {

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @IsPhone
    private String phone;

    /**
     * 手机号所属人姓名
     */
    @ApiModelProperty(value = "手机号所属人姓名", required = true)
    @NotBlank(message = "手机号所属人姓名不能为空")
    private String name;

    /**
     * 手机号所属人的身份证号码
     */
    @ApiModelProperty(value = "手机号所属人的身份证号码", required = true)
    @NotBlank(message = "手机号所属人的身份证号不能为空")
    @IsIdcard
    private String idCard;

    public ZHZXSendSmsRequest(String phone, String name, String idCard) {
        this.phone = phone;
        this.name = name;
        this.idCard = idCard;
    }
}
