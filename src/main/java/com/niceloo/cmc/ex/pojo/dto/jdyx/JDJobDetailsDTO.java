package com.niceloo.cmc.ex.pojo.dto.jdyx;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * JDJobDetailsDTO类
 * 用于处理接口返回中的data元素
 */
@Data
public class JDJobDetailsDTO {

    /**
     * 应用/机器人ID
     */
    private Long botId;

    /**
     * 任务话术ID
     */
    private Long contextId;

    /**
     * 下一次查询起点
     */
    private Long nextStartIndex;

    /**
     * 明细信息集合
     */
    private List<JobDetail> jobDetails;

    /**
     * JobDetail类，用于处理明细信息
     */
    @Data
    public static class JobDetail {

        /**
         * 租户ID
         */
        private Long tenantId;

        /**
         * 机器人ID
         */
        private Long botId;

        /**
         * 任务ID
         */
        private Integer jobId;

        /**
         * 话单执行状态
         */
        private String execStatus;

        /**
         * 客户信息
         */
        private String customerName;

        /**
         * 客户电话号码
         */
        private String customerPhone;

        /**
         * 客户变量
         */
        private Map<String, String> customerVariables;

        /**
         * 客户电话拨打状态
         */
        private String customerPhoneStatus;

        /**
         * 通话时长（秒）
         */
        private String duration;

        /**
         * 对话轮次
         */
        private String chatTurnCount;

        /**
         * 会话拨打时间
         */
        private String beginTime;

        /**
         * 会话挂断时间
         */
        private String endTime;

        /**
         * 结束节点
         */
        private String hangupNode;

        /**
         * 唯一标识
         */
        private String sessionId;

        /**
         * 重拨次数
         */
        private Integer redialTimes;

        /**
         * 最近的通话时间
         */
        private String latestCallTime;

        /**
         * 意图标签列表
         */
        private List<IntentLabel> intentLabels;

        /**
         * 是否转人工
         */
        private Boolean turnedManual;

        /**
         * 通话明细
         */
        private String chatText;

        /**
         * 音频路径
         */
        private String audioPath;

        /**
         * IntentLabel类，用于处理意图标签
         */
        @Data
        public static class IntentLabel {

            /**
             * 意向标签名称
             */
            private String intentName;

            /**
             * 意向标签类型
             */
            private String intentType;

            /**
             * 聚合字段
             */
            private String uniqueString;
        }
    }
}