package com.niceloo.cmc.ex.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通话时长和通话量统计VO对象
 *
 * <AUTHOR>
 * @since 2023/12/13 15:53 2023-12-13
 */
@Data
@ApiModel("通话时长和通话量统计VO")
public class CallStatsVO {

    @ApiModelProperty("用户标识")
    private String callerUserId;

    @ApiModelProperty("通话总量")
    private Integer validCallNum;

    @ApiModelProperty("通话总时长")
    private Integer durationTotal;
}
