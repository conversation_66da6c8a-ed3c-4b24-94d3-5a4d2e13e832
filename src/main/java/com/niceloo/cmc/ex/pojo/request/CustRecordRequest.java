package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.IsAvlCount;
import com.niceloo.framework.web.validation.anno.IsAvlStartindex;
import com.niceloo.framework.web.validation.anno.IsSafe;
import com.niceloo.framework.web.validation.anno.IsYMDHMS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @description: 根据客户id查询通话记录参数
 * @author: Wang<PERSON>henyu
 * @create: 2022年8月27日10:57:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("根据客户id查询通话记录参数")
public class CustRecordRequest extends AuthBaseRequest {

    @IsSafe
    @ApiModelProperty("客户id")
    @NotNull
    private String custId;

    @IsSafe
    @ApiModelProperty("外呼类型(JLFY->巨量飞鱼)")
    private String channelType;

    @IsAvlStartindex
    @ApiModelProperty("分页起始，即offset(-1 ->不分页)")
    private Integer pageIndex = 0;

    @IsAvlCount
    @ApiModelProperty("当页显示数(不分页时无效,最多返回前100条信息)")
    private Integer pageSize = 10;

    @IsSafe
    @ApiModelProperty("指定返回的字段,最多返回默认值所示的字段[callId,callTime,callerName,callerUserId,serverFolder,field1,duration,voiceSourceId,voiceStatus(是否接听[D为已接通,其他值为未接通])]")
    private String fields = "callId,callTime,callerName,callerUserId,serverFolder,field1,duration,voiceSourceId,voiceStatus";

    @IsYMDHMS
    @ApiModelProperty("查询时间起始(默认查询一年内)")
    private String createdTimeStart;

    @IsYMDHMS
    @ApiModelProperty("查询时间结束(默认查询一年内)")
    private String createdTimeEnd;

    @IsSafe
    @ApiModelProperty("是否只查询有录音文件的通话记录(Y->是,N->否)")
    private String hasRecording;
}
