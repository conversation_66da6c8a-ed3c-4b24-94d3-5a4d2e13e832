package com.niceloo.cmc.ex.pojo.response;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 中弘智享 API 接口返回数据封装
 *
 * <AUTHOR>
 * @since 2023/8/21 19:14 2023-08-21
 */
public class ZhongHongApiResponse {
    private int statusCode;
    private String message;
    private List<Object> data;

    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public int getStatusCode() {
        return statusCode;
    }

    /**
     * 获取消息
     *
     * @return 消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取数据列表
     *
     * @return 数据列表
     */
    public List<Object> getData() {
        return data;
    }

    /**
     * 从JSON字符串解析得到ZhongHongApiResponse对象
     *
     * @param json JSON字符串
     * @return 解析后的ZhongHongApiResponse对象
     */
    public static ZhongHongApiResponse fromJson(String json) {
        ZhongHongApiResponse response = new ZhongHongApiResponse();
        JSONObject jsonObject = JSON.parseObject(json);
        response.statusCode = jsonObject.getIntValue("status_code");
        response.message = jsonObject.getString("message");

        JSONArray dataArray = jsonObject.getJSONArray("data");
        if (dataArray != null) {
            response.data = new ArrayList<>(dataArray.size());
            response.data.addAll(dataArray);
        }

        return response;
    }
}
