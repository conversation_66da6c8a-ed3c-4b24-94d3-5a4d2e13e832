package com.niceloo.cmc.ex.pojo.dto;


import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

import static com.niceloo.cmc.ex.common.BizConst.EFFECTIVE_TIME;

/**
 * 巨量飞鱼通话记录业务传输类
 *
 * <AUTHOR>
 * @Date 2022年5月30日10:11:31
 */
@Data
@ApiModel("巨量飞鱼通话记录业务传输类")
@NoArgsConstructor
public class JLFYCallRecordsDTO {

    /**
     * 线索id
     */
    private String clueId;

    /**
     * 呼叫方，0:呼出、1：呼入
     */
    private Integer callDirection;

    /**
     * 通话结束状态,0->已接通，其他均为未接通
     */
    private Integer endStateShowCode;

    /**
     * 通话状态文本
     */
    private String endStateShowMsg;

    /**
     * 通话时长
     */
    private Integer duration;

    /**
     * 呼叫时长
     */
    private Integer callDuration;

    /**
     * 开始时间[时间戳:毫秒级]
     */
    private Long startTime;

    /**
     * 结束时间[时间戳:毫秒级]
     */
    private Long endTime;

    /**
     * 主叫号码
     */
    private String callerNumber;

    /**
     * 被叫号码
     */
    private String calleeNumber;

    /**
     * 中间号
     */
    private String virtualNumber;

    /**
     * 通话记录ID
     */
    private String contactId;

    /**
     * 广告主ID
     */
    private String adId;
    
    
    public JLFYCallRecordsDTO(Map<String, Object> map) {
        String call_direction = map.get("call_direction").toString();
        this.callDirection = "CALL_OUT".equals(call_direction) ? 0 : 1;
        this.duration = Integer.parseInt(map.get("duration").toString());
        this.callDuration = Integer.parseInt(map.get("call_duration").toString());
        this.calleeNumber = map.get("callee_number").toString();
        this.callerNumber = map.get("caller_number").toString();
        this.endStateShowCode = Integer.parseInt(map.get("end_state_show_code").toString());
        Date end_time = DateUtils.toDate(map.get("end_time").toString());
        this.endTime = end_time.getTime();
        Date start_time = DateUtils.toDate(map.get("start_time").toString());
        this.startTime = start_time.getTime();
        this.virtualNumber = map.get("virtual_number").toString();
        this.clueId = map.get("clue_id").toString();
        this.contactId = map.get("contact_id").toString();
    }

    /**
     * 将巨量飞鱼厂商返回的通话信息转换为CallRecord
     *
     * @return com.niceloo.cmc.ex.entity.es.CallRecord
     * <AUTHOR>
     * @Date 10:25 2022/5/30
     **/
    public CallRecord callRecordsConverter() {
        CallRecord callRecord = new CallRecord();
        callRecord.setChannelType(CallChannelEnum.CALL_TYPE_JLFY.getType());
        callRecord.setVoiceSourceId(this.contactId);
        callRecord.setField2(notEmpty(this.contactId));
        callRecord.setDataCompleteStatus("Y");
        callRecord.setVoiceSyncStatus(0);
        callRecord.setDuration(this.duration);
        callRecord.setCallType(this.callDirection);
        if (this.callDirection == 1) {
            callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.callerNumber));
            callRecord.setCallPhone(notEmpty(this.calleeNumber));
        } else {
            callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.calleeNumber));
            callRecord.setCallPhone(notEmpty(this.callerNumber));
        }
        callRecord.setCallTime(new Date(startTime));
        callRecord.setDataSyncTime(new Date());
        callRecord.setModifiedTime(new Date());
        callRecord.setVoiceStatus(BizConst.VOICE_NO_DEAL);
        callRecord.setIsConnectSuccess("N");
        callRecord.setIsValidCall("N");
        callRecord.setIsConnected("N");
        if (this.endStateShowCode == 0) {
            callRecord.setVoiceSyncStatus(1);
            callRecord.setIsConnected("Y");
        }
        if (this.duration > 0) {
            callRecord.setDataCompleteStatus("N");
            callRecord.setIsConnectSuccess("Y");
            callRecord.setVoiceStatus(BizConst.VOICE_DEALING);
            if (this.duration >= BizConst.EFFECTIVE_TIME) {
                callRecord.setIsValidCall("Y");
            }
        }
        return callRecord;
    }


    /**
     * 返回原字符串,除非字符串为null返回""
     */
    private static String notEmpty(String str) {
        return StringUtils.nullToEmpty(str);
    }

}
