package com.niceloo.cmc.ex.pojo.dto.byai;

import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.framework.utils.DateUtils;
import lombok.Data;

import java.util.Optional;

/**
 * 百应ai接口<b>--批量任务详情查询接口--</b>返回值
 *
 */
@Data
public class BYAIJobReportDTO {

    /**
     * 任务id
     */
    private Integer id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务状态（waiting 队列中, running 执行中, finished 已完成, paused 暂停中, canceled 已取消, stopped 已结束）
     */
    private String status;

    /**
     * 任务开始时间(毫秒级时间戳)
     */
    private Long startTime;

    /**
     * 任务结束时间(毫秒级时间戳)
     */
    private Long endTime;

    /**
     * 任务创建时间(毫秒级时间戳)
     */
    private Long createTime;

    /**
     * 外呼进度
     */
    private Double outboundProcess;

    /**
     * 总电话号码数
     */
    private Integer totalTaskNum;

    /**
     * 接通号码数
     */
    private Integer connectedTaskNum;

    /**
     * 更新外呼任务的信息
     *
     * @param aiCallJob 数据库的外呼任务的信息
     * <AUTHOR>
     * @Date 16:41 2022/8/16
     **/
    public void updateCallInfo(CcAiCallJob aiCallJob) {
        aiCallJob.setJobName(this.getName());
        aiCallJob.setStatus(this.getStatus());
        aiCallJob.setOutboundProcess(this.getOutboundProcess());
        aiCallJob.setConnectedTaskNum(Optional.ofNullable(this.getConnectedTaskNum()).orElse(0));
        aiCallJob.setTotalTaskNum(this.getTotalTaskNum());
        aiCallJob.setModifyDate(DateUtils.getNowDString());
    }


//    /**
//     * 未拨打号码数
//     */
//    private Integer pendingTaskNum;
//    
//    /**
//     * 已拨打号码数
//     */
//    private Integer calledTaskNum;
//
//    /**
//     * 接通率
//     */
//    private Double connectedRatio;
//
//    /**
//     * 接通成功号码数
//     */
//    private String successTaskNum;
//
//    /**
//     * 接通成功率
//     */
//    private Double connectedSuccessRatio;
}
