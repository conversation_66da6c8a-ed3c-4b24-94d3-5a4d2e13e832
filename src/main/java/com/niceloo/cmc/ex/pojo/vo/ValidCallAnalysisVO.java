package com.niceloo.cmc.ex.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 有效通话分析返回结果
 *
 * <AUTHOR>
 * @Date 2022-10-08 16:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("有效通话分析返回结果")
public class ValidCallAnalysisVO extends TrafficStatisticBdEeVO {

    @ApiModelProperty("10秒有效呼出量")
    private Integer callOutNumGe10;

    @ApiModelProperty("10秒有效呼入量")
    private Integer callInNumGe10;

    @ApiModelProperty("20秒有效呼出量")
    private Integer callOutNumGe20;

    @ApiModelProperty("20秒有效呼入量")
    private Integer callInNumGe20;

    @ApiModelProperty("30秒有效呼出量")
    private Integer callOutNumGe30;

    @ApiModelProperty("30秒有效呼入量")
    private Integer callInNumGe30;

    @ApiModelProperty("60秒有效呼出量")
    private Integer callOutNumGe60;

    @ApiModelProperty("60秒有效呼入量")
    private Integer callInNumGe60;

}
