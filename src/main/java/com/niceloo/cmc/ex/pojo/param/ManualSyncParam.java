package com.niceloo.cmc.ex.pojo.param;
import com.niceloo.framework.web.validation.anno.IsRangeVal;
import com.niceloo.framework.web.validation.anno.IsYMDHMS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @desc: 手动同步参数
 * @author: song
 * @date: 2022/2/28
 */
@ApiModel("手动同步参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ManualSyncParam {

    @ApiParam(value = "索引名(TQ:TQ手机;FS:风云;YP:云客手机;ZK:亿讯;YH:亿讯回拨;YXSIP:亿讯SIP;OI:一号互联)", required = true)
    @NotBlank(message = "任务厂商不可为空")
    @IsRangeVal({"TQ","FS","YP","ZK","YH","YXSIP","OI"})
    private String channelType;

    @ApiParam(value = "时间起始", required = true)
    @NotBlank(message = "时间起始必填")
    @IsYMDHMS
    private String startDate;

    @ApiParam(value = "时间结束", required = true)
    @NotBlank(message = "时间结束必填")
    @IsYMDHMS
    private String endDate;

    @ApiParam(value = "是否补充操作(Y:是;N:否)")
    @IsRangeVal({"Y","N"})
    private String replenish = "Y";

    @ApiParam(value = "操作通话记录类型(C:创建;U:更新)")
    @IsRangeVal({"C","U"})
    private String actionType = "C";


}
