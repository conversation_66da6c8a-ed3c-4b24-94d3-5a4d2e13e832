package com.niceloo.cmc.ex.pojo.request;

import com.niceloo.framework.web.validation.anno.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 *  通话记录列表查询参数
 * @author: WangChenyu
 * @create: 2022-03-03 13:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CallRecordsSelectRequest extends AuthBaseRequest{

    @IsAvlStartindex
    @ApiModelProperty("分页起始")
    private Integer pageIndex = 0;

    @IsAvlCount
    @ApiModelProperty("当页显示数")
    private Integer pageSize = 10;

    @ApiModelProperty("客户姓名")
    private String reciverName;

    @ApiModelProperty("客户电话")
    private String reciverPhone;

    @IsSafe
    @ApiModelProperty("用户优路号")
    private String youluNum;

    @IsRangeVal(value = {"0", "1"})
    @ApiModelProperty("呼叫类型,[0 呼出;1 呼入]")
    private String callType;

    @IsId
    @ApiModelProperty("客戶Id")
    private String custId;

    @IsSafe
    @ApiModelProperty("员工呼叫账号")
    private String callAccount;

    @IsSafe
    @ApiModelProperty("外呼手机号（主叫）")
    private String callPhone;

    /**
     * D:已接听;N:振铃未接听/挂断;Q:排队放弃;E:已留言;L:IVR放弃;B:黑名单;K:空号;O:其他;R:未响应(即还没有收到厂商反馈的通话记录)
     */
    @IsRangeVal(value = {"D", "N", "Q", "E", "L", "B", "K", "O", "R"})
    @ApiModelProperty("呼叫状态")
    private String voiceStatus;

    /**
     * 呼叫通道 TQ:TQ手机;TF:TQ固话;FS:风云;YP:云客手机;YS:云客软电话;ZK:亿讯双呼;YH:亿讯回拨;YXSIP:亿讯SIP;OI:一号互联;JLFY: 巨量飞鱼;ZHZX:中弘智享
     */
    @IsRangeVal(value = {"TQ", "TF", "FS", "YP", "YS", "ZK", "YH", "YXSIP", "OI", "JLFY", "ZHZX"})
    @ApiModelProperty("呼叫通道")
    private String channelType;

    @IsYMDHMS
    @NotNull
    @ApiModelProperty("查询时间起始")
    private String createdTimeStart;

    @IsYMDHMS
    @NotNull
    @ApiModelProperty("查询时间结束")
    private String createdTimeEnd;

    @ApiModelProperty("通话时长最小值")
    private Integer durationMin;

    @ApiModelProperty("通话时长最大值")
    private Integer durationMax;

    @ApiModelProperty("录音状态(Y->已上传,N->未上传)")
    @IsRangeVal(value = {"Y", "N"})
    private String recordingStatus;
}
