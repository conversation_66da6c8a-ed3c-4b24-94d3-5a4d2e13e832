package com.niceloo.cmc.ex.pojo.dto;


import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.service.call.YKService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * 云客通话记录业务传输类
 * <AUTHOR>
 * @since 2021-12-17 14:12
 */
@Data
@ApiModel("云客通话记录业务传输类")
public class YKCallRecordsDTO {

    /**
     * 通话记录id 主键[根据此ID来去重]
     */
    @ApiModelProperty("通话记录id")
    private String id;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private String userId;

    /**
     * 通话开始时间
     */
    @ApiModelProperty("通话开始时间")
    private String createTime;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private String customerId;

    /**
     * 被叫手机号
     */
    @ApiModelProperty("被叫手机号")
    private String phone;

    /**
     * 录音url
     */
    @ApiModelProperty("录音url")
    private String recordUrl;

    /**
     * 时长 单位秒
     */
    @ApiModelProperty("时长 单位秒")
    private int duration;

    /**
     * 0呼出 1呼入
     */
    @ApiModelProperty("0呼出 1呼入")
    private int direction;

    /**
     * 外呼id 使用接口外呼时返回的id
     */
    @ApiModelProperty("外呼id 使用接口外呼时返回的id")
    private String callId;

    /**
     * 主叫号码
     */
    @ApiModelProperty("主叫号码")
    private String userPhone;

    /**
     * 通话备注
     */
    @ApiModelProperty("通话备注")
    private String remark;

    /**
     * 二级标签[意向描述]
     */
    @ApiModelProperty("二级标签")
    private String tipName;

    /**
     * 员工外呼SIM卡号码
     */
    @ApiModelProperty("员工外呼SIM卡号码")
    private String simPhone;

    /**
     * 二级标签id
     */
    @ApiModelProperty("二级标签id")
    private Integer tipType;

    /**
     * 一级标签文字
     */
    @ApiModelProperty("一级标签文字")
    private String tipTypeString;

    /**
     * 开始通话的时间
     */
    @ApiModelProperty("开始通话的时间")
    private String startCallTime;

    /**
     * 开始响铃时间
     */
    @ApiModelProperty("开始响铃时间")
    private String ringTime;

    /**
     * 响铃时长
     */
    @ApiModelProperty("响铃时长")
    private Integer ringSecond;

    /**
     * 云客的用户id
     */
    @ApiModelProperty("云客的用户id")
    private String yunkeUserId;

    /**
     * 是否接通
     */
    @ApiModelProperty("是否接通")
    private Boolean through;

    /**
     * 	销售进度类型id（销售进度描述可以通过/open/customer/getSalesProgresses接口获取）
     */
    @ApiModelProperty("销售进度类型id")
    private Integer progress;

    /**
     * 将云客厂商返回的通话信息转换为CallRecord
     *
     * @param 无
     * @return com.niceloo.cmc.ex.entity.es.CallRecord
     * <AUTHOR>
     * @since 10:17 2022/2/23
     **/
    public CallRecord callRecordsConverter() {
        CallRecord callRecord = new CallRecord();
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setChannelType(CallChannelEnum.CALL_TYPE_YPHONE.getType());
        callRecord.setVoiceSourceId(this.id);
        callRecord.setCallAccount(notEmpty(this.userId));
        callRecord.setField2(notEmpty(this.callId));
        callRecord.setDataCompleteStatus("N");
        callRecord.setVoiceSyncStatus(0);
        callRecord.setVoiceSourceUrl(notEmpty(this.recordUrl));
        callRecord.setDuration(this.duration);
        callRecord.setCallType(this.direction);
        callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.phone));
        callRecord.setCallPhone(notEmpty(this.simPhone));
        callRecord.setRemarks(notEmpty(this.remark));
        callRecord.setIntentionMsg(notEmpty(this.tipName));
        callRecord.setCallTime(DateUtils.toDate(this.createTime));
        callRecord.setDataSyncTime(new Date());
        callRecord.setCreatedTime(new Date());
        callRecord.setModifiedTime(callRecord.getCreatedTime());
        callRecord.setModifier("SYSTEM");
        callRecord.setVoiceStatus(BizConst.VOICE_NO_DEAL);
        callRecord.setIsConnectSuccess("N");
        callRecord.setIsValidCall("N");
        callRecord.setAgentId("");
        callRecord.setIsConnected("N");
        if (this.duration > 0) {
            callRecord.setIsConnectSuccess("Y");
            callRecord.setVoiceStatus(BizConst.VOICE_DEALING);
            callRecord.setIsConnected("Y");
            if (this.duration >= BizConst.EFFECTIVE_TIME) {
                callRecord.setIsValidCall("Y");
            }
        }
        //校验是否是该通话记录的录音
        callRecord.setServerFolder("");
        if (StringUtils.isEmpty(this.recordUrl) && this.duration < BizConst.EFFECTIVE_TIME) {
            callRecord.setDataCompleteStatus("Y");
        } else {
            callRecord.setVoiceSyncStatus(1);
            // 云客有可能通话录音和通话记录不匹配
            boolean urlIsCorrect = YKService.voiceSourceUrlIsCorrect(this.recordUrl, DateUtils.toDate(this.createTime), this.phone);
            if (!StringUtils.isEmpty(this.recordUrl) && !urlIsCorrect) {
                callRecord.setServerFolder("404");
                callRecord.setDataCompleteStatus("Y");
            }
        }
        return callRecord;
    }

    /**
     * 补充没有数据的空值
     * @param callRecord 通话记录实体
     * <AUTHOR>
     * @since 10:17 2022/2/23
     **/
    public void replenishEmpty(CallRecord callRecord) {
        //客户信息
        callRecord.setReciverUserId("");
        callRecord.setReciverbdUserId("");
        callRecord.setReciverName("");
        callRecord.setAreaCode("");
        //员工信息
        callRecord.setCallerUserId("");
        callRecord.setCallerName("");
        callRecord.setCallerPhone("");
        callRecord.setSchoolId("");
        callRecord.setSchoolName("");
        callRecord.setDptId("");
        callRecord.setDptName("");
        //通话信息
        callRecord.setField1("");
        callRecord.setVoiceUrl("");
        callRecord.setAreaName("");
        callRecord.setAgentPhone("");
        callRecord.setReciverType("");
        callRecord.setSatisfaction("");
        callRecord.setCommuIntention("");
        callRecord.setProjectId("");
        callRecord.setProjectName("");
    }

    /**
     * 返回原字符串,除非字符串为null返回""
     */
    private static String notEmpty(String str) {
        return StringUtils.nullToEmpty(str);
    }
}
