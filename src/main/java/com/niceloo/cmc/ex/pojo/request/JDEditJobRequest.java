package com.niceloo.cmc.ex.pojo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.niceloo.cmc.ex.entity.CcAiCallJob;
import com.niceloo.cmc.ex.pojo.vo.AiCallJobVO;
import com.niceloo.cmc.ex.service.CcAiCallJobService;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.web.ApiErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 京东言犀-更新外呼任务请求参数
 *
 * <AUTHOR>
 * @Date 2022-08-02 15:47
 */
@Data
@ApiModel("更新外呼任务请求参数")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JDEditJobRequest implements Serializable {

    private static final long serialVersionUID = 1136457152261109198L;
    @ApiModelProperty("主键id")
    @NotBlank(message = "主键id不能为空")
    private String id;

    @ApiModelProperty("外呼任务名称")
    @Length(max = 40, message = "外呼任务名称长度不得超过40位")
    private String name;

    @ApiModelProperty("外呼job任务描述")
    private String description;

    @ApiModelProperty("外呼时段星期列表(1-7),多个星期用逗号隔开")
    private String jobOfflineWeek;

    @ApiModelProperty("启动模式(Manual->手动、Timing->定时),不传默认手动")
    private String startupMode = "Manual";

    @ApiModelProperty("任务的启动时间,启动模式为定时时使用")
    private String startTime;

    @ApiModelProperty("时段_开始时分秒，例：6点整，对应06:00:00(京东言犀:08:00 - 20:00, 百应:09:00 - 20:00)")
    private String timeBegin;

    @ApiModelProperty("时段_结束时分秒，例：6点整，对应06:00:00(京东言犀:08:00 - 20:00, 百应:09:00 - 20:00)")
    private String timeEnd;

    @ApiModelProperty("外呼线路id")
    private Long lineId;

    @ApiModelProperty("任务维度-机器人线路并发数")
    private Integer concurrentNumber;

    @ApiModelProperty("外呼话术模板id")
    private Integer contextId;

    @ApiModelProperty("话术模板名称")
    private String contextName;

    @ApiModelProperty("是否是重拨任务,0->不重拨任务,1->重拨")
    private Integer isRedial;

    @ApiModelProperty("自动重拨次数取值范围: [1~5]")
    private Integer redialTimes;

    @ApiModelProperty("重拨间隔（分钟）,京东言犀: [1~1440], 百应:[0-120]")
    private Integer redialInterval;

    /**
     * 重拨的原因,多个原因之间用竖线分隔,若是重拨任务的话,此项必填
     * 原因说明：<p>“NeedRedial”,“意向标签-需重拨”；“Closed”, “关机”；”Busy”, “占线”；”Missed”, “未接/无人接听”；”Stopped”, “停机”；”Unavailabe”, “无法接通”；”Dead”, “空号”；”ConcurrentCallLoss”, “并发呼损”；”NoAvailableLine”, “无可用线路”；”Reject”, “拒接”；”CallLimit”, “呼叫受限”；”CallerArrears”, “主叫欠费”；”OutboundFail”, “外呼失败”；”Intercepted”, “呼出拦截”</p>
     */
    @ApiModelProperty("重拨的原因(多个原因之间用竖线分隔)")
    private String redialReason;

    @ApiModelProperty("百应天盾策略组id")
    private Integer tiandunRuleStrategyGroupId;

    @ApiModelProperty("百应天盾策略组名")
    @Length(max = 36, message = "百应天盾策略组名称不得超过36个字符")
    private String tiandunRuleStrategyGroupName;

    @ApiModelProperty("任务优先级，数字越大任务优先级越高")
    @Range(max = 10, min = 1, message = "数字越大任务优先级越高(1-10)")
    private Integer priority;

    public AiCallJobVO check(CcAiCallJob aiCallJob, CcAiCallJobService ccAiCallJobService) {
        JDCreateJobRequest.checkJobOfflineWeek(this.getJobOfflineWeek());

        if (null == aiCallJob || aiCallJob.getJobProgress() != 3) {
            return new AiCallJobVO();
        }
        if (!aiCallJob.getCustomerAddStatus().equals("Y")) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "外呼客户追加中，待客户追加完成之后，再编辑。");
        }
        // 任务名称重复检验
        String name = this.getName();
        boolean flag = ccAiCallJobService.existJobName(name);
        if (flag && !aiCallJob.getJobName().equals(name)) {
            throw new ApplicationException(ApiErrorCodes.argument_invalided, "外呼任务名称重复!!!");
        }
        return null;
    }

}
