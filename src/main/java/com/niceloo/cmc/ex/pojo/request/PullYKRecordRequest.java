package com.niceloo.cmc.ex.pojo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * @description: 拉取云客通话记录请求对象
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-12-27 11:25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PullYKRecordRequest {

    /**
     * 当前页数
     */
    private int page;

    /**
     * 每页数量
     */
    private int size;

    /**
     * 通话时间的起始时间(yyyy-MM-dd HH:mm:ss)
     */
    private String beginTime;

    /**
     * 通话时间的截止时间(yyyy-MM-dd HH:mm:ss)
     */
    private String endTime;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 客户手机号
     */
    private String phone;

    public PullYKRecordRequest(int page, int size, String beginTime, String endTime) {
        this.page = page;
        this.size = size;
        this.beginTime = beginTime;
        this.endTime = endTime;
    }
}
