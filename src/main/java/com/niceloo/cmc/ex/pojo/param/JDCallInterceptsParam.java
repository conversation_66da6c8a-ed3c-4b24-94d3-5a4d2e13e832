package com.niceloo.cmc.ex.pojo.param;

import lombok.Data;

/**
 * 京东言犀外呼任务防骚扰拦截参数
 *
 * <AUTHOR>
 * @Date 2022-08-02 14:33
 */
@Data
public class JDCallInterceptsParam {

    /**
     * 类型（call：限制外呼次数、connected: 限制用户接听次数）
     */
    private String type;

    /**
     * 是否开启（true：开启、false：关闭）
     */
    private boolean isOpen;

    /**
     * 策略天数（防骚扰拦截生效的天数）
     */
    private Integer days;

    /**
     * 策略数量（在策略天数之内，对同一用户最多能外呼多少次/同一用户最多能接听多少次）
     */
    private Integer maxNum;

    public boolean getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(boolean open) {
        isOpen = open;
    }

    public JDCallInterceptsParam(String type, boolean isOpen, Integer days, Integer maxNum) {
        this.type = type;
        this.isOpen = isOpen;
        this.days = days;
        this.maxNum = maxNum;
    }
}
