package com.niceloo.cmc.ex.pojo.dto;


import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

import static com.niceloo.cmc.ex.utils.AudioAddressCheckerUtil.isAddressEncrypted;

/**
 * 亿迅通话记录业务传输类
 * <AUTHOR>
 * @since 2021-12-17 14:12
 */
@Data
@ApiModel("亿迅通话记录业务传输类")
public class YXCallRecordsDTO {

    /**
     * callApiBilling: 话单推送
     */
    @ApiModelProperty("回调类型 callApiBilling:话单推送")
    private String type;

    /**
     * 主叫号码
     */
    @ApiModelProperty("主叫号码")
    private String caller;

    /**
     * 主叫来电显示 [第三方厂商的提供的随机号码]
     */
    @ApiModelProperty("主叫来电显示")
    private String callerDisplay;

    /**
     * 被叫号码
     */
    @ApiModelProperty("被叫号码")
    private String callee;

    /**
     * 被叫来电显示[呼入的方式就和caller数据一样]
     */
    @ApiModelProperty("被叫来电显示")
    private String calleeDisplay;

    /**
     * 呼叫主叫开始的时间[外呼时间]
     */
    @ApiModelProperty("呼叫主叫开始的时间")
    private String callerInviteTime;

    /**
     * 录音文件 [如果用户开启了号码隐藏，文件路径会用一段密文字符串推送]
     */
    @ApiModelProperty("录音文件")
    private String recordFile;

    /**
     * 用户回传数据
     */
    @ApiModelProperty("用户回传数据")
    private String userData;

    /**
     * 有效通话时长
     */
    @ApiModelProperty("有效通话时长")
    private String timeLength;

    /**
     * 挂断原因[0、200 正常释放]
     */
    @ApiModelProperty("挂断原因")
    private String releaseCause;

    /**
     * 账号标识
     */
    private String account;

    /**
     * 账号标识
     */
    private String accountName;

    /**
     * 通话标识
     */
    @ApiModelProperty("通话标识")
    private String session;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private String service;

    /**
     * 是否是回拨服务器 true->中科云(78),false->回拨(175)
     */
    private boolean sign;

    /**
     * 呼叫人座席号
     */
    @ApiModelProperty("呼叫人座席号")
    private String agent;

    // 2024-09-26 11:54:24
    // 增加未处理的回执字段

    /**
     * 主叫响铃开始时间
     */
    @ApiModelProperty("主叫响铃开始时间")
    private String callerRingingBeginTime;

    /**
     * 主叫应答时间
     */
    @ApiModelProperty("主叫应答时间")
    private String callerAnswerTime;

    /**
     * 主叫挂断时间
     */
    @ApiModelProperty("主叫挂断时间")
    private String callerHangupTime;

    /**
     * 被叫邀请时间
     */
    @ApiModelProperty("被叫邀请时间")
    private String calleeInviteTime;

    /**
     * 被叫响铃开始时间
     */
    @ApiModelProperty("被叫响铃开始时间")
    private String calleeRingingBeginTime;

    /**
     * 被叫应答时间
     */
    @ApiModelProperty("被叫应答时间")
    private String calleeAnswerTime;

    /**
     * 被叫挂断时间
     */
    @ApiModelProperty("被叫挂断时间")
    private String calleeHangupTime;

    /**
     * 将亿迅厂商返回的通话信息转换为VoiceVo
     *
     * @return com.niceloo.cmc.ex.entity.es.CallRecord
     * @paramter
     * <AUTHOR>
     * @Date 14:14 2021/12/23
     **/
    public CallRecord callRecordsConverter() {
        CallRecord callRecord = new CallRecord();
        callRecord.setCallId(UUID.randomUUID().toString());
        callRecord.setChannelType(CallChannelEnum.CALL_TYPE_ZK.getType());
        callRecord.setField4(this.account);
        callRecord.setField2(this.userData);
        callRecord.setCallPhone(notEmpty(this.caller));
        callRecord.setAgentId(notEmpty(this.agent));
        callRecord.setCallAccount("");
        callRecord.setVoiceSourceId("");
        callRecord.setField6(notEmpty(this.session));
        callRecord.setReciverPhone(FieldCipherUtil.oneEncrypt(this.callee));
        callRecord.setCallTime(DateUtils.toDate(this.callerInviteTime));
        callRecord.setDataSyncTime(new Date());
        callRecord.setCreatedTime(new Date());
        int duration = Integer.parseInt(this.timeLength);
        callRecord.setDuration(duration);
        callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_N);
        callRecord.setVoiceStatus(BizConst.VOICE_NO_DEAL);
        callRecord.setIsValidCall(BizConst.VALID_CALL_N);
        callRecord.setIsConnected(BizConst.CONNECTED_FAIL);
        callRecord.setVoiceSyncStatus(0);
        callRecord.setDataCompleteStatus("Y");
        // 服务器标志
        if (this.sign) {
            if (!ObjectUtils.isEmpty(callRecord.getAgentId())) {
                callRecord.setCallAccount(callRecord.getAgentId() + "@" + this.accountName);
            }
        } else {
            callRecord.setCallAccount(notEmpty(this.caller));
            callRecord.setField3(CallRecord.CALL_TYPE_YH_FIELD3);
        }
        // 呼叫类型[5->回拨]
        if (BizConst.SERVICE_CLICK_TO_CALL.equals(service) || BizConst.SERVICE_MANUAL_CALL.equals(this.service) || BizConst.SERVICE_INTERNAL_CALL.equals(service) || BizConst.SERVICE_AUTO_OUTBOUND.equals(service)) {
            callRecord.setCallType(BizConst.CALL_OUT);
        } else if (BizConst.SERVICE_INBOUND_CALL.equals(service)) {
            callRecord.setCallType(BizConst.CALL_IN);
        } else {
            callRecord.setCallType(Integer.valueOf(service));
        }
        if (duration > 0) {
            callRecord.setIsConnectSuccess(BizConst.CONNECT_SUCCESS_Y);
            callRecord.setIsConnected(BizConst.CONNECTED_SUCCESS);
            callRecord.setVoiceStatus(BizConst.VOICE_DEALING);
        }
        if (duration >= BizConst.EFFECTIVE_TIME) {
            callRecord.setIsValidCall(BizConst.VALID_CALL_Y);
        }
        if (!StringUtils.isEmpty(this.recordFile)) {
            String host;
            if (this.sign) {
                host = CallProperties.YXProperty.ZK_HOST;
            } else {
                host = CallProperties.YXProperty.YH_HOST;
            }
            // 判断录音地址是否为密文
            if (isAddressEncrypted(this.recordFile)) {
                // 如果录音地址是密文，则使用密文下载API来设置通话录音的声音源URL
                callRecord.setVoiceSourceUrl(host + CallProperties.YXProperty.RECORD_DOWNLOAD_API_CIPHERTEXT + this.recordFile);
            } else {
                // 如果录音地址不是密文，则使用普通下载API来设置通话录音的声音源URL
                callRecord.setVoiceSourceUrl(host + CallProperties.YXProperty.RECORD_DOWNLOAD_API + this.recordFile);
            }
            callRecord.setVoiceSyncStatus(1);
            callRecord.setDataCompleteStatus("N");
        } else {
            if (duration >= BizConst.EFFECTIVE_TIME) {
                callRecord.setDataCompleteStatus("N");
            }
        }
        callRecord.setModifiedTime(callRecord.getCreatedTime());
        callRecord.setModifier("SYSTEM");
        callRecord.setRemarks("");
        callRecord.setIntentionMsg("");
        callRecord.setServerFolder("");
        callRecord.setVoiceSourceUrl(notEmpty(callRecord.getVoiceSourceUrl()));
        return callRecord;
    }

    /**
     * 返回原字符串,除非字符串为null返回""
     */
    private static String notEmpty(String str) {
        return StringUtils.nullToEmpty(str);
    }

    public YXCallRecordsDTO oneCdrResultConverter(Map<String, String> map, String userData) {
        if (null == map) {
            return null;
        }
        this.caller = map.get("caller");
        this.callee = map.get("callee");
        this.callerInviteTime = map.get("startTime");
        this.recordFile = map.get("filename");
        this.timeLength = map.get("timeLen");
        this.session = map.get("session");
        this.agent = map.get("agent");
        // 到现在亿迅都是呼出
        this.service = "5";
        this.userData = userData;
        this.sign = true;
        return this;
    }
}
