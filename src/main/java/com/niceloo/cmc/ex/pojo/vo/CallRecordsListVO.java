package com.niceloo.cmc.ex.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 通话记录列表查询返回值
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-03-07 15:08
 */
@Data
@ApiModel("话务查询列表返回值")
public class CallRecordsListVO {

    @ApiModelProperty("客户标识")
    private String reciverUserId;

    @ApiModelProperty("客户姓名")
    private String reciverName;

    @ApiModelProperty("客户电话")
    private String reciverPhone;

    @ApiModelProperty("客户优路号")
    private String youluNum;

    @ApiModelProperty("所属员工姓名")
    private String callerName;

    @ApiModelProperty("呼叫账号")
    private String callAccount;

    @ApiModelProperty("外呼手机号（主叫）")
    private String callPhone;

    @ApiModelProperty("员工所属分校")
    private String schoolId;

    @ApiModelProperty("员工所属分校")
    private String schoolName;

    @ApiModelProperty("员工所属部门")
    private String dptId;

    @ApiModelProperty("员工所属部门")
    private String dptName;

    @ApiModelProperty("呼叫状态(D:已接听;N:振铃未接听/挂断;Q:排队放弃;E:已留言;L:IVR放弃;B:黑名单;K:空号;O:其他)")
    private String voiceStatus;

    @ApiModelProperty("呼叫通道(TQ:TQ手机;TF:TQ固话;FS:风云;YP:云客手机;YS:云客软电话;ZK:中科云软电话;OI:一号互联;JLFY:巨量飞鱼)")
    private String channelType;

    @ApiModelProperty("呼叫类型(0 呼出;1 呼入)")
    private String callType;

    @ApiModelProperty("呼叫时间")
    private String callTime;

    @ApiModelProperty("通话时长(秒),-1表示通话信息还没有同步,缺少通话时长")
    private String duration;

    @ApiModelProperty("录音地址")
    private String serverFolder;

    @ApiModelProperty("录音key")
    private String field1;

    @ApiModelProperty("录音下载 token")
    private String downloadToken;
    /**
     * 校验外呼通道
     */
    private String field3;
}
