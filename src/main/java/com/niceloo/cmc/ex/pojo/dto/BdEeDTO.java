package com.niceloo.cmc.ex.pojo.dto;

import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.framework.utils.StringUtils;
import lombok.Data;

/**
 * @description: 通话记录存储的员工信息数据传输类
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-26 10:02
 */
@Data
public class BdEeDTO {
    String eeId;
    String userId;
    String userName;
    String schoolId;
    String eeNo;
    String eeInnerphone;
    String eeWorkstatus;
    /**
     * 员工入职日期
     */
    String eeHiredate;
    /**
     * 员工离职时间
     */
    String eeTermdate;
    /**
     * 员工个人信息修改时间(BdEe)
     */
    String eeModifieddate;
    /**
     * 员工外呼账号表修改时间(BdPersonalinfo)
     */
    String infoModifieddate;
    String zkyAccount;
    /**
     * 分校名称 [2022年5月28日15:25:41新增]
     */
    String schoolName;

    /**
     * 将员工的信息添加到通话记录内
     * @paramter callRecord
     * <AUTHOR>
     * @Date 10:25 2022/3/16
     **/
    public void addEeToCallRecord(CallRecord callRecord) {
        callRecord.setCallerUserId(notEmpty(this.userId));
        callRecord.setCallerName(notEmpty(this.userName));
        callRecord.setCallerPhone(notEmpty(this.eeInnerphone));
        callRecord.setSchoolId(notEmpty(this.schoolId));

    }

    /**
     * 返回原字符串,除非字符串为null返回""
     */
    private static String notEmpty(String str) {
        return StringUtils.nullToEmpty(str);
    }
}
