package com.niceloo.cmc.ex.pojo.param;

import com.niceloo.framework.web.validation.anno.IsDateTime;
import com.niceloo.framework.web.validation.anno.IsGTE0;
import com.niceloo.framework.web.validation.anno.IsRangeVal;
import com.niceloo.framework.web.validation.anno.IsYMDHMS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @desc: 同步录音参数
 * @author: song
 * @date: 2022/2/28
 */
@ApiModel("同步录音参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncVoiceParam {

    @ApiParam(value = "索引名(多个索引用逗号拼接,call_record_*为全部索引)", required = true)
    @NotBlank(message = "索引必填")
    private String indexes;

    @ApiParam(value = "时间起始")
    @IsYMDHMS
    private String callTimeStart;

    @ApiParam(value = "时间结束")
    @IsYMDHMS
    private String callTimeEnd;

    @ApiParam(value = "已同步次数(大于等于)")
    @IsGTE0
    private Integer voiceSyncStatusStart;

    @ApiParam(value = "已同步次数(小于等于)")
    @IsGTE0
    private Integer voiceSyncStatusEnd;

    @ApiParam(value = "索引名(TQ:TQ手机;FS:风云;YP:云客手机;ZK:亿讯;YH:亿讯回拨;亿讯SIP:YXSIP;中弘智享:ZHZX)")
    @IsRangeVal({"TQ","FS","YP","ZK","YH","YXSIP","ZHZX"})
    private String channelType;
}
