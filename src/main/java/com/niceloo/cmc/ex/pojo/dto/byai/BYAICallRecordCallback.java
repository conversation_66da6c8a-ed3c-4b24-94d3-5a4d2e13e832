package com.niceloo.cmc.ex.pojo.dto.byai;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.niceloo.cmc.ex.common.aicall.AiCallStatusEnum;
import com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.plugin.sdk.lang.reflect.bean.BeanUtils;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 百应ai接口<b>--通话记录回调--</b>返回值
 *
 */
@Data
public class BYAICallRecordCallback {

    /**
     * 任务id
     */
    @JsonAlias("callJobId")
    private Integer jobId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 姓名
     */
//    @JsonAlias("customerName")
//    private String userName;

    /**
     * 手机号码
     */
//    @JsonAlias("customerTelephone")
//    private String phone;

    /**
     * 通话时长（单位：秒）
     */
    @JsonAlias("duration")
    private Long callDuration;

    /**
     * 重拨次数（当前任务中，当通会话当前用户）
     */
    @JsonAlias("calledTimes")
    private Integer redialTimes;

    /**
     * 对话接通时间（第一条消息发出时间）
     */
    private String answerTime;
    public String getAnswerTime() {
        return StringUtils.isEmpty(this.lastCallTime) ? null : this.ringTime;
    }

    /**
     * 该条外呼对应的呼叫开始时间（调度发起时间）
     * 百应通话开始时间，定义：
     * 1. 未接通的通话：AI开始拨打的时间（未接通时，与外呼开始时间的值相同）
     * 2. 已接通的通话：用户的接通时间
     */
    @JsonAlias("startTime")
    private String ringTime;

    /**
     * 最后通话时间
     */
    @JsonAlias("endTime")
    private String lastCallTime;

    /**
     * session id
     */
    @JsonAlias("callInstanceId")
    private String sid;

    /**
     * 京东言犀通话状态: 5:已接听,9:无人接听,8:占线,7:关机,11:停机,12:拒接,6:空号,4:无法接通,1012:主叫欠费,1013:外呼失败,1014:并发呼损,1018:防骚扰拦截,1015:黑名单,1011:呼出拦截,1016:无可用线路,1017:呼叫受限
     */
    private Integer callStatus;
    public Integer getCallStatus() {
        return this.finishStatus == null ? null : AiCallStatusEnum.getByByaiNumber(this.finishStatus).getJdyxNumber();
    }

    /**
     * 百应通话状态：0:已接听,1:拒接,2:无法接通,3:外呼失败,4:空号,5:关机,6:占线,7:停机,8:未接,9:主叫欠费,10:呼损,11:黑名单,12:天盾拦截,22:线路盲区,23:呼出拦截,25:无可用线路
     */
    private Integer finishStatus;

    /**
     * 对话轮次
     */
    @JsonAlias("chatRound")
    private Long dialogCount;

    /**
     * 话术模板ID
     */
//    @JsonAlias("robotDefId")
//    private Integer cid;

    /**
     * 意图标签列表,逗号分隔
     */
    private String intentLabels;

    /**
     * 百应companyId
     */
    private String companyId;

    /**
     * 客户属性
     */
    @JsonAlias("properties")
    private Map<String, Object> customerVariables;

    /**
     * 录音地址
     */
    @JsonAlias("luyinOssUrl")
    private String audioPath;

    /**
     * 录音文本
     */
    private String chatText;

    /**
     * 将回调参数转成实体
     *
     * @return com.niceloo.cmc.ex.entity.es.AICallJobCustomerInfo
     * <AUTHOR>
     * @Date 11:19 2022/8/25
     **/
    public AICallJobCustomerInfo convertToAIJobCustomerEntity() {
        if (this.customerVariables == null || !this.customerVariables.containsKey("uuid")) {
            return null;
        }
        AICallJobCustomerInfo jobCustomerInfo = BeanUtils.copyFromObjToClass(AICallJobCustomerInfo.class, this);

        // 获取 uuid 并处理，如果包含 @ 则只取 @ 前的部分
        String uuid = (String)this.customerVariables.get("uuid");
        if (uuid.contains("@")) {
            uuid = uuid.substring(0, uuid.indexOf("@"));
        }

        jobCustomerInfo.setId(uuid);
        jobCustomerInfo.setJobId(null);
        jobCustomerInfo.setJobType(null);
        jobCustomerInfo.setCallStatus(this.getCallStatus());
        jobCustomerInfo.setCallDuration(Optional.ofNullable(this.callDuration).orElse(0L));
        jobCustomerInfo.setDialogCount(Optional.ofNullable(this.dialogCount).orElse(0L));
        jobCustomerInfo.setLastCallTime(Optional.ofNullable(this.lastCallTime).orElse(""));
        jobCustomerInfo.setAnswerTime(Optional.ofNullable(this.getAnswerTime()).orElse(""));
        jobCustomerInfo.setRingTime(Optional.ofNullable(this.ringTime).orElse(""));
        jobCustomerInfo.setVendorJobId(this.jobId);
        jobCustomerInfo.setIntentLabels(Optional.ofNullable(this.intentLabels).orElse(""));
        jobCustomerInfo.setSessionId(this.sid);
        jobCustomerInfo.setSyncStatus("Y");
        jobCustomerInfo.setCallbackStatus("Y");
        jobCustomerInfo.setJobEnv(1);
        jobCustomerInfo.setCallbackTime(new Date());
        jobCustomerInfo.setModifiedTime(jobCustomerInfo.getCallbackTime());
        jobCustomerInfo.setRecordingUrl(Optional.ofNullable(this.audioPath).orElse(""));
        jobCustomerInfo.setHasRecordingDownLoad("N");
        jobCustomerInfo.setRecordingDownLoadTimes(0);
        jobCustomerInfo.setFileKey("");
        jobCustomerInfo.setServerFolder("");
        if (StringUtils.isNotEmpty(this.audioPath) && this.callDuration > 0) {
            // todo 需要添加根据标签来校验是否下载录音逻辑
            jobCustomerInfo.setHasRecordingDownLoad("Y");
        }
        return jobCustomerInfo;
    }

    /**
     * <a href="https://byaitech.feishu.cn/wiki/R760wAACSiYVWakOj3icxq9knUh">百应回调</a>.
     * @param taskResult 客户意向等级
     */
    public void populateIntentLabels(List<Map<String, Object>> taskResult) {
        taskResult.stream()
                .filter(map -> "客户意向等级".equals(map.get("resultName")))
                .map(map -> String.format("%s,%s", (String) map.get("resultValue"), (String) map.get("resultDesc")))
                .findFirst()
                .ifPresent(s -> this.intentLabels = s);
    }

}
