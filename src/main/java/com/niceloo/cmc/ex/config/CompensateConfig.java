package com.niceloo.cmc.ex.config;

import com.niceloo.framework.utils.RandomUtils;
import com.niceloo.segment.core.IdCompensation;

/**
 * @description: 发号服务id补偿实现
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-23 17:21
 */
public class CompensateConfig implements IdCompensation {

    @Override
    public String compensate(String prefix, String timePhase) {
        return prefix + timePhase + RandomUtils.randomNumberString(12);
    }
}
