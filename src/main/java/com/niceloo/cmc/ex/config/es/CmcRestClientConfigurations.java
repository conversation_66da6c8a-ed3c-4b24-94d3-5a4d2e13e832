package com.niceloo.cmc.ex.config.es;

import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.utils.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.elasticsearch.rest.RestClientBuilderCustomizer;
import org.springframework.boot.autoconfigure.elasticsearch.rest.RestClientProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.PropertyMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * CMC自定义的Elasticsearch REST客户端基础设施配置,用于覆盖spring-boot的默认配置
 * @see org.springframework.boot.autoconfigure.elasticsearch.rest.RestClientAutoConfiguration
 * <AUTHOR>
 * @date 2022/4/18 9:08
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(RestClient.class)
@EnableConfigurationProperties({RestClientProperties.class, CmcRestClientProperties.class})
public class CmcRestClientConfigurations {
    private static final Logger logger = LoggerFactory.getLogger(CmcRestClientConfigurations.class);
    @Configuration(proxyBeanMethods = false)
    static class RestClientBuilderConfiguration {

        @Bean
        RestClientBuilder elasticsearchRestClientBuilder(RestClientProperties properties,
                                                         CmcRestClientProperties cmcProperties,
                                                         ObjectProvider<RestClientBuilderCustomizer> builderCustomizers) {
            HttpHost[] hosts = properties.getUris().stream().map(HttpHost::create).toArray(HttpHost[]::new);
            RestClientBuilder builder = RestClient.builder(hosts);
            logger.info("ES启动,账号:{},密码:{}", properties.getUsername(), properties.getPassword());
            builder.setHttpClientConfigCallback((httpClientBuilder) -> {
                if (StringUtils.isNotEmpty(properties.getUsername())) {
                    Credentials credentials = new UsernamePasswordCredentials(properties.getUsername(), properties.getPassword());
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    credentialsProvider.setCredentials(AuthScope.ANY, credentials);
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                }

                // 设置可以安全保持连接空闲的持续时间,默认3分钟
                httpClientBuilder.setKeepAliveStrategy((response, context) -> {
                    long keepAliveDuration = DefaultConnectionKeepAliveStrategy.INSTANCE.getKeepAliveDuration(response, context);
                    return keepAliveDuration <= 0 ? cmcProperties.getConnectionKeepAlive().toMillis() : keepAliveDuration;
                });

                return httpClientBuilder;
           });

            PropertyMapper map = PropertyMapper.get();
            builder.setRequestConfigCallback((requestConfigBuilder) -> {
                map.from(properties::getConnectionTimeout).whenNonNull().asInt(Duration::toMillis)
                        .to(requestConfigBuilder::setConnectTimeout);
                map.from(properties::getReadTimeout).whenNonNull().asInt(Duration::toMillis)
                        .to(requestConfigBuilder::setSocketTimeout);

                return requestConfigBuilder;
            });

            builderCustomizers.orderedStream().forEach((customizer) -> customizer.customize(builder));
            return builder;
        }

    }

}
