package com.niceloo.cmc.ex.config;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;

/**
 * 自定义拦截器.新增注释,第一行头加批注
 *
 * <AUTHOR>
 */
public class CommentWriteHandler implements RowWriteHandler {

    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        if (BooleanUtils.isTrue(context.getHead())) {
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
            Row row = sheet.getRow(0);
            short lastCellNum = row.getLastCellNum();
            for (int i = 0; i < lastCellNum; i++) {
                Cell cell = row.getCell(i);
                // 输入批注信息,根据单元格名称中的`-`获取
                String stringCellValue = cell.getStringCellValue();
                String[] cellValueSplit = stringCellValue.split("-");
                cell.setCellValue(cellValueSplit[0]);
                if (cellValueSplit.length > 1) {
                    Comment comment =
                            drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex() + 1));
                    comment.setString(new XSSFRichTextString(cellValueSplit[1]));
                    // 将批注添加到单元格对象中
                    cell.setCellComment(comment);
                }
            }
        }
    }

}
