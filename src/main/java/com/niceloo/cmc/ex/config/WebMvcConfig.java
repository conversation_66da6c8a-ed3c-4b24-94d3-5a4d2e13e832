package com.niceloo.cmc.ex.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 请求路径添加统一前缀
 *
 * <AUTHOR>
 * @Date 2022-03-09 16:03
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void configurePathMatch(PathMatchConfigurer configure) {
        configure.addPathPrefix("/api/commu/", aClass -> {
            String packageName = aClass.getPackageName();
            if(packageName.contains("com.niceloo.cmc.ex.controller")){
                return !"com.niceloo.cmc.ex.controller.callback".equals(packageName);
            }
            return false;
        });
        // 添加回调的前缀
        configure.addPathPrefix("/commu/", aClass -> {
            String packageName = aClass.getPackageName();
            return "com.niceloo.cmc.ex.controller.callback".equals(packageName);
        });
    }
}