package com.niceloo.cmc.ex.config;

import lombok.CustomLog;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * OKHttp3配置
 **/
@Configuration
@CustomLog
public class OkHttpConfig {
    @Value("${ok-http.connectTimeout:15}")
    public Long connectTimeout;
    @Value("${ok-http.readTimeout:15}")
    public Long readTimeout;
    @Value("${ok-http.writeTimeout:15}")
    public Long writeTimeout;
    @Value("${ok-http.maxIdleConnections:10}")
    public Integer maxIdleConnections;

    /**
     * 证书信任管理器类 (信任所有证书)
     *
     * @return javax.net.ssl.X509TrustManager
     * <AUTHOR>
     * @Date 14:17 2022/2/14
     **/
    @Bean
    public X509TrustManager x509TrustManager() {
        return new X509TrustManager() {
            /*检查客户端的证书，若不信任该证书则抛出异常*/
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {
            }

            /*方法检查服务器的证书，若不信任该证书同样抛出异常,不做任何处理即信任任何证书*/
            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {
            }

            /*返回受信任的X509证书数组。*/
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
    }

    /**
     *  使用Java中的SSLContext类来创建SSL连接，并指定了一个信任管理器（TrustManager）来验证远程服务器的证书。
     * @return  SSLSocketFactory对象，用于创建SSL Socket连接
     */
    @Bean
    public SSLSocketFactory sslSocketFactory() {
        try {
            /*创建SSLContext对象，并使用我们指定的信任管理器初始化*/
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{this.x509TrustManager()}, new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 连接池
     */
    @Bean
    public ConnectionPool pool() {
        /*最多可容纳10个空闲连接，在5分钟不活动后将被逐出*/
        return new ConnectionPool(maxIdleConnections, 5, TimeUnit.MINUTES);
    }

    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                .sslSocketFactory(sslSocketFactory(), x509TrustManager())
                .protocols(Collections.singletonList(Protocol.HTTP_1_1))// 禁用HTTP2.0使用1.1
                .retryOnConnectionFailure(true)
                .connectionPool(pool())//连接池
                .connectTimeout(connectTimeout, TimeUnit.SECONDS)//连接超时时间
                .readTimeout(readTimeout, TimeUnit.SECONDS)//读取超时时间
                .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                .build();
    }
}