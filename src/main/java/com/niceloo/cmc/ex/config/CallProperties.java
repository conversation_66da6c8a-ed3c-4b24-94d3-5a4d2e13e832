package com.niceloo.cmc.ex.config;

import lombok.Data;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @desc: 外呼厂商的属性配置类
 * @author: song
 * @date: 2022/2/23
 */
@Component
public class CallProperties {

    //=======================一号互联=======================
    @Component
    public static class OIProperty{
        public static String account;
        public static String password;
        public static String type;
        public static String authUrl;
        public static String recordUrl;
        public static String callIn;
        public static String callOutNot;

        @Value("${call.yhhl.account}")
        public void setAccount(String account) {
            OIProperty.account = account;
        }
        @Value("${call.yhhl.password}")
        public void setPassword(String password) {
            OIProperty.password = password;
        }
        @Value("${call.yhhl.type}")
        public void setType(String type) {
            OIProperty.type = type;
        }
        @Value("${call.yhhl.authUrl}")
        public void setAuthUrl(String authUrl) {
            OIProperty.authUrl = authUrl;
        }
        @Value("${call.yhhl.recordUrl}")
        public void setRecordUrl(String recordUrl) {
            OIProperty.recordUrl = recordUrl;
        }
        @Value("${call.yhhl.callIn}")
        public void setCallIn(String callIn) {
            OIProperty.callIn = callIn;
        }
        @Value("${call.yhhl.callOutNot}")
        public void setCallOutNot(String callOutNot) {
            OIProperty.callOutNot = callOutNot;
        }
    }

    @Component
    public static class YKProperty {
        public static String url;
        public static String company;
        public static String sign;
        public static String partnerId;

        @Value("${yunke.url}")
        public void setUrl(String url) {
            YKProperty.url = url;
        }
        @Value("${yunke.company}")
        public void setCompany(String company) {
            YKProperty.company = company;
        }
        @Value("${yunke.sign}")
        public void setSign(String sign) {
            YKProperty.sign = sign;
        }
        @Value("${yunke.manager.partnerId}")
        public void setPartnerId(String partnerId) {
            YKProperty.partnerId = partnerId;
        }

    }


    @Component
    public static class YXProperty {
        public static String ZK_HOST;
        public static String YH_HOST;
        public static String YX_SIP_HOST;
        public static String GET_CDR_LIST_API;
        public static String ONE_RECORDS;
        public static String RECORD_DOWNLOAD_API;
        public static String RECORD_DOWNLOAD_API_CIPHERTEXT;
        public static Integer GET_CDR_LIST_SLEEP_MILLIS;
        public static Integer GET_CDR_LIST_BUSY_SLEEP_MILLIS;
        public static String BALANCE;

        @Value("${zhongke.host}")
        public void setZkHost(String zkHost) {
            YXProperty.ZK_HOST = zkHost;
        }
        @Value("${zhongke.host.huibo}")
        public void setYhHost(String yhHost) {
            YXProperty.YH_HOST = yhHost;
        }
        @Value("${zhongke.host.sip}")
        public void setYxSipHost(String yxSipHost) {
            YXProperty.YX_SIP_HOST = yxSipHost;
        }
        @Value("${zhongke.records}")
        public void setGetCdrListApi(String getCdrListApi) {
            YXProperty.GET_CDR_LIST_API = getCdrListApi;
        }
        @Value("${zhongke.oneRecords}")
        public void setOneRecords(String oneRecords) {
            YXProperty.ONE_RECORDS = oneRecords;
        }
        @Value("${zhongke.record.download}")
        public void setRecordDownloadApi(String recordDownloadApi) {
            YXProperty.RECORD_DOWNLOAD_API = recordDownloadApi;
        }
        @Value("${zhongke.record.download.ciphertext}")
        public void setRecordDownloadApiCiphertext(String recordDownloadApiCiphertext) {
            YXProperty.RECORD_DOWNLOAD_API_CIPHERTEXT = recordDownloadApiCiphertext;
        }
        @Value("${zhongke.records.sleep}")
        public void setGetCdrListSleepMillis(Integer getCdrListSleepMillis) {
            YXProperty.GET_CDR_LIST_SLEEP_MILLIS = getCdrListSleepMillis;
        }
        @Value("${zhongke.records.busy.sleep}")
        public void setGetCdrListBusySleepMillis(Integer getCdrListBusySleepMillis) {
            YXProperty.GET_CDR_LIST_BUSY_SLEEP_MILLIS = getCdrListBusySleepMillis;
        }
        @Value("${zhongke.balance}")
        public void setBalance(String balance) {
            YXProperty.BALANCE = balance;
        }
    }


    @Component
    public static class TQProperty {
        public static String url;
        // 暂时没有用到
        public static String verifyUrl;
        public static Integer validTime ;
        public static String appKey;
        public static String managerAccount;
        // 用来TQ下载录音更改路径 [mdb.tq.cn-]
        public static String oldHost;
        // 用来TQ下载录音更改路径 [mdbnl.tq.cn-]
        public static String newHost;

        @Value("${tq.url}")
        public void setUrl(String url) {
            TQProperty.url = url;
        }
        @Value("${tq.verify.url}")
        public void setVerifyUrl(String verifyUrl) {
            TQProperty.verifyUrl = verifyUrl;
        }
        @Value("${tq.token.time}")
        public void setValidTime(Integer validTime) {
            TQProperty.validTime = validTime;
        }
        @Value("${tq.sync.appkey}")
        public void setAppKey(String appKey) {
            TQProperty.appKey = appKey;
        }
        @Value("${youlu.manager.account}")
        public void setManagerAccount(String managerAccount) {
            TQProperty.managerAccount = managerAccount;
        }

        @Value("${tq.oldHost}")
        public void setOldHost(String oldHost) {
            TQProperty.oldHost = oldHost;
        }

        @Value("${tq.newHost}")
        public void setNewHost(String newHost) {
            TQProperty.newHost = newHost;
        }
    }


    @Component
    public static class JDYXProperty {

        // URL前缀
        public static String url_pre;
        // 主动调用京东言犀需要此TOKEN
        public static String token;
        // 调用时的开发者账号
        public static String caller;
        // 租户id (应用id)
        public static String tenantId;
        // 机器人id
        public static String bot_id;
        // 用户pin(默认用的是caller)
        public static String userPin;

        @Value("${jdyx.url}")
        public void setUrl_pre(String url_pre) {
            JDYXProperty.url_pre = url_pre;
        }

        @Value("${jdyx.token:6a48076a44a07755}")
        public void setToken(String token) {
            JDYXProperty.token = token;
        }

        @Value("${jdyx.caller:youlujiaoyu}")
        public void setCaller(String caller) {
            JDYXProperty.caller = caller;
        }

        @Value("${jdyx.tenant_id:10695}")
        public void setTenantId(String tenantId) {
            JDYXProperty.tenantId = tenantId;
        }

        @Value("${jdyx.bot_id:107857}")
        public void setBot_id(String bot_id) {
            JDYXProperty.bot_id = bot_id;
        }

        @Value("${jdyx.user_pin:youlujiaoyu}")
        public void setUserPin(String userPin) {
            JDYXProperty.userPin = userPin;
        }
    }

    /**
     * 言犀AI外呼配置参数
     * <p>
     * 2023-04-18
     * <AUTHOR>
     */
    @Component
    @ConfigurationProperties(prefix = "yanxi")
    @Data
    public static class YanxiProperty {

        /**
         * 租户信息集合
         */
        private List<Tenant> tenants;

        /**
         * 租户实体类
         */
        @Data
        public static class Tenant {
            /**
             * 调用京东言犀需要TOKEN，拼装Sign签名
             * 一般，同一公司多租户，token设置相同
             */
            private String token;

            /**
             * 调用时的开发者账号
             * 一般，同一公司多租户，caller设置相同
             */
            private String caller;

            /**
             * 租户ID=商户ID，是确定企业身份账号的唯一标识
             */
            private String tenantId;

            /**
             * 用户PIN，即登录言犀的账号
             */
            private String userPin;

            /**
             * 应用ID，是确认该使用租户的外呼应用唯一标识
             */
            private Integer botId;
        }
    }

    @Component
    public static class ZhzxProperty {

        // URL前缀
        public static String HOST;

        // 获取短信验证码
        public static String SEND_SMS;

        // 设置白名单
        public static String SETUP_WHITE_LIST;

        // 删除白名单
        public static String DELETE_WHITE_LIST;

        // 获取白名单
        public static String GET_WHITE_LIST;

        // 双呼请求
        public static String DOUBLE_CALL;

        // 获取账户余额
        public static String GET_BALANCE;

        // 短信vid超时时间（比验证码有效时间5分钟多5秒，便于判断验证码错误原因）
        public static String SMS_VID_TIMEOUT;

        // 获取短信验证码
        public static String GET_SMS_CODE;

        // 需查询余额的外呼通道
        public static String BALANCE_CHANNEL_TYPES;

        @Value("${zhzx.host}")
        public void setHost(String host) {
            ZhzxProperty.HOST = host;
        }

        @Value("${zhzx.sendSms}")
        public void setSendSms(String sendSms) {
            ZhzxProperty.SEND_SMS = sendSms;
        }

        @Value("${zhzx.setupWhiteList}")
        public void setSetupWhiteList(String setupWhiteList) {
            ZhzxProperty.SETUP_WHITE_LIST = setupWhiteList;
        }

        @Value("${zhzx.deleteWhiteList}")
        public void setDeleteWhiteList(String deleteWhiteList) {
            ZhzxProperty.DELETE_WHITE_LIST = deleteWhiteList;
        }

        @Value("${zhzx.getWhiteNumber}")
        public void setGetWhiteNumber(String getWhiteNumber) {
            ZhzxProperty.GET_WHITE_LIST = getWhiteNumber;
        }

        @Value("${zhzx.doubleCall}")
        public void setDoubleCall(String doubleCall) {
            ZhzxProperty.DOUBLE_CALL = doubleCall;
        }

        @Value("${zhzx.getBalance}")
        public void setGetBalance(String getBalance) {
            ZhzxProperty.GET_BALANCE = getBalance;
        }

        @Value("${zhzx.smsVidTimeout}")
        public void setSmsVidTimeout(String smsVidTimeout) {
            ZhzxProperty.SMS_VID_TIMEOUT = smsVidTimeout;
        }

        @Value("${zhzx.balanceChannelTypes}")
        public void setBalanceChannelTypes(String balanceChannelTypes) {
            ZhzxProperty.BALANCE_CHANNEL_TYPES = balanceChannelTypes;
        }
    }

    @Getter
    @Component
    public static class ByAiProperty {

        @Value("${byai.url}")
        private String url;

        @Value("${byai.oauth_token}")
        private String oauthToken;

        @Value("${byai.tiandun_list}")
        private String tiandunList;

        @Value("${byai.client_id}")
        private String clientId;

        @Value("${byai.client_secret}")
        private String clientSecret;

        @Value("${byai.company_id}")
        private Long companyId;
    }

}

