package com.niceloo.cmc.ex.config.es;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.convert.DurationUnit;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

/**
 * CMC自定义的Elasticsearch REST客户端的配置属性
 *
 * <AUTHOR>
 * @date 2022/4/18 10:37
 */
@Data
@ConfigurationProperties(prefix = "spring.elasticsearch.rest.cmc")
public class CmcRestClientProperties {

    /**
     * 可以安全保持连接空闲的持续时间,默认3分钟
     */
    @DurationUnit(ChronoUnit.MINUTES)
    private Duration connectionKeepAlive = Duration.ofMinutes(3);

}
