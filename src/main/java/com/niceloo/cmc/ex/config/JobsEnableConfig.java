package com.niceloo.cmc.ex.config;

import com.niceloo.framework.spring.SpringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.stereotype.Component;

/**
 * @description: 定时任务是否启用
 * @author: <PERSON><PERSON><PERSON>yu
 * @create: 2022-03-07 09:46
 */
@Component
public class JobsEnableConfig implements BeanDefinitionRegistryPostProcessor {

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        // 将创建的跟定时任务有关的bean定义信息删除
        if ("false".equals(SpringUtils.getProperty("jobs.enable"))) {
            if (registry.containsBeanDefinition("jobBeanPostProcessor")) {
                registry.removeBeanDefinition("jobBeanPostProcessor");
            }
            if (registry.containsBeanDefinition("jobExecutor")) {
                registry.removeBeanDefinition("jobExecutor");
            }
            if (registry.containsBeanDefinition("jobMvcConfig")) {
                registry.removeBeanDefinition("jobMvcConfig");
            }
        }
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
    }
}
