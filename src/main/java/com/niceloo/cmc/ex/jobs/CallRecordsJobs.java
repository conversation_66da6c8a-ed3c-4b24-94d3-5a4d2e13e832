package com.niceloo.cmc.ex.jobs;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.YKCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.dto.YXCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.dto.YXSipCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.request.PullFYRecordRequest;
import com.niceloo.cmc.ex.pojo.request.PullRecordRequest;
import com.niceloo.cmc.ex.pojo.request.PullTQRecordRequest;
import com.niceloo.cmc.ex.pojo.request.PullYKRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.FYCallRecordsVO;
import com.niceloo.cmc.ex.pojo.vo.TQCallRecordsVO;
import com.niceloo.cmc.ex.pojo.vo.YKCallRecordsPageVO;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.call.*;
import com.niceloo.cmc.ex.service.impl.CallRecordServiceImpl;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.cmc.ex.utils.RedisUtil;
import com.niceloo.framework.job.core.NicelooJob;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.mq.client.Client;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilterBuilder;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 重构通话记录定时任务
 *
 * <AUTHOR>
 * @date 2022/2/25
 */
@Component
public class CallRecordsJobs {

    private static final Logger LOGGER = LoggerFactory.getLogger(CallRecordsJobs.class);

    @Resource(name = "syncRecordRabbitMqClient")
    private Client client;
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Resource
    private CallRecordService callRecordService;
    @Resource
    private AICallJobCustomerInfoService aiCallJobCustomerInfoService;

    /**
     * 每隔一小时拉取一次人工外呼通话记录和录音（前一个小时产生的数据），存储到ES中，同时上传音频文件到OSS
     */
    @NicelooJob(cron = "0 0 0/1 * * ?", dbRecord = true, singleRun = true)
    public void saveOrUpdateRecordToES() {
        String startDate = DateUtils.getNowDString("yyyy-MM-dd HH") + ":00:00";
        String selfMark = startDate;
        String value = redisTemplate.opsForValue().getAndSet(RedisConst.CALL_RECORD_PULL_KEY, startDate);
        if (StringUtils.isNotEmpty(value)) {
            selfMark = value;
        }
        // 执行统计逻辑
        int hours = (int) (DateUtils.compare(startDate, selfMark) / (1000 * 60 * 60));
       /* for (int i = 1; i < hours; i++) {
            Date lastDate = DateUtils.addHour(DateUtils.toDate(selfMark), i - 2);
            Date nextDate = DateUtils.addHour(lastDate, 1);
            String startTime = DateUtils.toStr(lastDate);
            String endTime = DateUtils.toStr(nextDate);
            LOGGER.info("定时任务推送通话记录拉取消息" + startTime);
            publishCallRecordMsg(startTime, endTime);
        }*/
        Date date = DateUtils.addHour(DateUtils.toDate(startDate), -2);
        startDate = DateUtils.toStr(date);
        String endDate = DateUtils.toStr(DateUtils.addHour(date, 1));
        LOGGER.info("定时任务推送通话记录拉取消息" + startDate);
        publishCallRecordMsg(startDate, endDate);

    }

    /**
     * MQ消息推送
     * 1、生成一号互联通话记录
     * 2、风云和TQ通过定时任务来补充话单信息
     *
     * <AUTHOR>
     * @Date 2022/2/25
     */
    private void publishCallRecordMsg(String startDate, String endDate) {
        // message: FS~~~2022-07-08 14:00:00~~~2022-07-08 15:00:00~~~A~~~C  (厂商~~~开始时间~~~结束时间~~~是否是定时任务执行~~~添加通话记录还是更新通话记录)
        client.publish(MQConst.CALL_RECORD_TOPIC, UUID.randomUUID().toString(), CallChannelEnum.CALL_TYPE_OI.getType() + BizConst.LINK_SYMBOL + startDate + BizConst.LINK_SYMBOL + endDate + BizConst.LINK_SYMBOL + BizConst.AUTO + BizConst.LINK_SYMBOL + BizConst.CREATE);
        client.publish(MQConst.CALL_RECORD_TOPIC, UUID.randomUUID().toString(), CallChannelEnum.CALL_TYPE_FY.getType() + BizConst.LINK_SYMBOL + startDate + BizConst.LINK_SYMBOL + endDate + BizConst.LINK_SYMBOL + BizConst.AUTO + BizConst.LINK_SYMBOL + BizConst.UPDATE);

        String uuid = UUID.randomUUID().toString();
        if (startDate.contains(" CST ")) {
            LOGGER.warn(String.format("CallRecordsJobs#publishCallRecordMsg 的 startDate: %s, uuid: %s", startDate, uuid));
        }
        client.publish(MQConst.CALL_RECORD_TOPIC, uuid, CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType() + BizConst.LINK_SYMBOL + startDate + BizConst.LINK_SYMBOL + endDate + BizConst.LINK_SYMBOL + BizConst.AUTO + BizConst.LINK_SYMBOL + BizConst.UPDATE);
    }

    /**
     * @Description: 重试拉取通话记录失败的请求, 每天执行6*7=42次
     * 先从redis读,再推送MQ消息,再删除redis里的请求
     * 非原子操作的优点是可以避免服务重启造成的请求数据丢失,缺点是有可能重复推送请求
     * <AUTHOR>
     * @Date 2021/12/14 10:44
     */
    @NicelooJob(cron = "0 0/10 0-6 * * ?", dbRecord = true, singleRun = true)
    public void retryPullRecordRequest() {
        Long number = RedisUtil.redisTemplate.opsForSet().size(RedisConst.FAILED_PULL_RECORD_REQUEST);
        if (null == number || number <= 0) {
            return;
        }
        LOGGER.info("待重试的失败请求个数: " + number);
        List<Object> failedRequests = RedisUtil.redisTemplate.opsForSet().randomMembers(RedisConst.FAILED_PULL_RECORD_REQUEST, 1);
        if (CollectionUtils.isEmpty(failedRequests)) {
            return;
        }
        String failedRequestJson = failedRequests.get(0).toString();
        PullRecordRequest pullRecordRequest = JSONUtils.toObject(failedRequestJson, PullRecordRequest.class);
        // 上线兼容以前的数据
        if (StringUtils.isEmpty(pullRecordRequest.getActionType())) {
            pullRecordRequest.setActionType("C");
        }
        String failedRequest = pullRecordRequest.buildPullRecordRequest();
        if (!StringUtils.isEmpty(failedRequest)) {
            // 是否忽略该条通话记录
            if (this.verifyObsoleteCallRecord(pullRecordRequest)) {
                LOGGER.info("定时任务->重试拉取通话记录失败的请求忽略通话记录:{}", failedRequest);
                RedisUtil.redisTemplate.opsForSet().remove(RedisConst.FAILED_PULL_RECORD_REQUEST, failedRequestJson);
                return;
            }
            try {
                String uuid = UUID.randomUUID().toString();
                if (failedRequest.contains(" CST ")) {
                    LOGGER.warn(String.format("CallRecordsJobs#retryPullRecordRequest 的 failedRequest: %s, uuid: %s", failedRequest, uuid));
                }
                client.publish(MQConst.CALL_RECORD_TOPIC, uuid, failedRequest);
            } catch (Exception e) {
                LOGGER.info(e, "推送MQ消息失败: " + failedRequest);
                return;
            }
        }
        RedisUtil.redisTemplate.opsForSet().remove(RedisConst.FAILED_PULL_RECORD_REQUEST, failedRequestJson);
        LOGGER.info("移除请求: " + pullRecordRequest);
    }

    /**
     * 校验是否淘汰过滤通话记录,淘汰返回true (淘汰规则:如果厂商为亿迅且通话记录产生时间在三个月前则淘汰)
     *
     * @return boolean 淘汰返回true
     * @paramter pullRecordRequest
     * <AUTHOR>
     * @Date 9:39 2022/4/22
     **/
    public boolean verifyObsoleteCallRecord(PullRecordRequest pullRecordRequest) {
        String channelType = pullRecordRequest.getChannelType();
        if (StringUtils.isNotEmpty(channelType) && CallChannelEnum.CALL_TYPE_ZK.equals(CallChannelEnum.getCallChannel(channelType))) {
            String endTime = pullRecordRequest.getEndTime();
            if (StringUtils.isEmpty(endTime)) {
                return true;
            }
            Date date = DateUtils.toDate(endTime);
            Date verifyDate = DateUtils.addMonth(new Date(), -3);
            return date.compareTo(verifyDate) < 0;
        }
        return false;
    }

    /**
     * 每月27号晚上创建下个月的通话记录索引
     *
     * <AUTHOR>
     * @Date 9:30 2022/8/1
     **/
    @NicelooJob(cron = "0 0 23 27 * ?", dbRecord = true, singleRun = true)
    public void createCallRecordIndex() {
        LocalDate localDate = LocalDate.now().plusMonths(1L);
        String date = localDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String indexName = BizConst.CALL_RECORD_ES_PREFIX + date;
        callRecordService.createIndex(indexName);
        LOGGER.info("定时任务每个月27号创建下个月的索引,创建成功,索引名称:{}", indexName);
    }

    /**
     * 每月27号晚上创建下个月的AI外呼任务通话记录索引
     *
     * <AUTHOR>
     * @since  2024-04-03 18:04:16
     **/
    @NicelooJob(cron = "0 0 23 27 * ?", dbRecord = true, singleRun = true)
    public void createAiCallRecordIndex() {
        LocalDate localDate = LocalDate.now().plusMonths(1L);
        String date = localDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String indexName = BizConst.AI_JOB_CUSTOMER_ES_PREFIX + date;
        aiCallJobCustomerInfoService.createIndex(indexName);
        LOGGER.info("定时任务每个月27号创建下个月的AI外呼任务通话记录索引,创建成功,索引名称:{}", indexName);
    }

    /**
     * 每天晚上十点补充有通话记录但是通话记录里面缺少通话时长等信息
     *
     * <AUTHOR>
     * @Date 17:44 2022/7/5
     **/
    @NicelooJob(cron = "0 0 22 * * ?", dbRecord = true, singleRun = true)
    public void supplementaryCallRecord() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(4, 4, 30, TimeUnit.MINUTES, new LinkedBlockingQueue<>());
        threadPoolExecutor.execute(new SupplementaryCallRecord(CallChannelEnum.CALL_TYPE_YPHONE));
        threadPoolExecutor.execute(new SupplementaryCallRecord(CallChannelEnum.CALL_TYPE_FY));
        threadPoolExecutor.execute(new SupplementaryCallRecord(CallChannelEnum.CALL_TYPE_TQ_MOBILE));
        threadPoolExecutor.execute(new SupplementaryCallRecord(CallChannelEnum.CALL_TYPE_ZK));
        threadPoolExecutor.execute(new SupplementaryCallRecord(CallChannelEnum.CALL_TYPE_YX_SIP));
        threadPoolExecutor.shutdown();
    }

    class SupplementaryCallRecord implements Runnable {
        private final Logger LOGGER = LoggerFactory.getLogger(SupplementaryCallRecord.class);
        private final CallChannelEnum channelType;

        public SupplementaryCallRecord(CallChannelEnum channelType) {
            this.channelType = channelType;
        }

        @Override
        public void run() {
            Thread.currentThread().setName("supplementaryCallRecordThread_" + channelType.getType());
            LOGGER.info("定时任务->开始:使用多线程去补充当天通过智能运行平台拨打成功的通话里缺少通话时长属性的通话记录,外呼通道:{}", channelType.getType());
            CallRecordService callRecordService = SpringUtils.getBean(CallRecordServiceImpl.class);
            int numbers = 0;
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            TermQueryBuilder channelType = QueryBuilders.termQuery("channelType", this.channelType.getType());
            TermQueryBuilder duration = QueryBuilders.termQuery("duration", "-1");
            String startTime = DateUtils.getNowDString(DateUtil.YMD) + " 00:00:00";
            RangeQueryBuilder callTime = QueryBuilders.rangeQuery("callTime").gte(startTime);
            boolQueryBuilder.filter(channelType).filter(duration).filter(callTime);
            Pageable pageable = PageRequest.of(0, 1500, Sort.by(Sort.Direction.DESC, "callTime"));
            FetchSourceFilterBuilder filterBuilder = new FetchSourceFilterBuilder().withIncludes("callId", "field2", "field4", "reciverPhone", "callPhone", "callAccount", "channelType");
            NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withIndices(RecordUtil.getRecordIndexName(new Date()))
                    .withTypes("_doc")
                    .withFilter(boolQueryBuilder)
                    .withPageable(pageable)
                    .withSourceFilter(filterBuilder.build())
                    .build();
            List<CallRecord> recordList = callRecordService.scrollSelectList(searchQuery);
            if (CollectionUtils.isEmpty(recordList)) {
                LOGGER.info("定时任务->完成:使用多线程去补充当天通过智能运行平台拨打成功的通话里缺少通话时长属性的通话记录,外呼通道:{},补充数量:{}", this.channelType.getType(), numbers);
                return;
            }
            numbers = recordList.size();
            int successNum = 0;
            switch (this.channelType) {
                case CALL_TYPE_YPHONE: {
                    successNum = supplementaryYKCallRecord(recordList);
                    break;
                }
                case CALL_TYPE_FY: {
                    successNum = supplementaryFYCallRecord(recordList);
                    break;
                }
                case CALL_TYPE_TQ_MOBILE: {
                    successNum = supplementaryTQCallRecord(recordList);
                    break;
                }
                case CALL_TYPE_ZK: {
                    successNum = supplementaryYXCallRecord(recordList);
                    break;
                }
                case CALL_TYPE_YX_SIP: {
                    successNum = supplementaryYXSipCallRecord(recordList);
                    break;
                }
            }
            LOGGER.info("定时任务->完成:使用多线程去补充当天通过智能运行平台拨打成功的通话里缺少通话时长属性的通话记录,外呼通道:{},补充数量:{},补充成功数量:{}", this.channelType.getType(), numbers, successNum);
        }

        /**
         * 补充云客通话记录到ES
         *
         * @param recordList 需要补充的通话记录列表
         * <AUTHOR>
         * @Date 17:17 2022/7/6
         **/
        private int supplementaryYKCallRecord(List<CallRecord> recordList) {
            YKService ykService = SpringUtils.getBean(YKService.class);
            // 查询厂商获取通话记录信息
            String endTime = DateUtils.getNowDString();
            PullYKRecordRequest request = new PullYKRecordRequest(1, 10, DateUtils.getNowDString(DateUtil.YMD) + " 00:00:00", endTime);
            int updateSize = 0;
            int pageSize = 200;
            int total = recordList.size();
            int page = (total + pageSize - 1) / pageSize;
            List<CallRecord> subRecordList = new ArrayList<>(200);
            List<CallRecord> updateRecordList = new ArrayList<>(200);
            // 分段更新
            for (int i = 0; i < page; i++) {
                subRecordList.addAll(recordList.subList(pageSize * i, Math.min(pageSize * (i + 1), total)));
                for (CallRecord record : subRecordList) {
                    request.setUserId(record.getCallAccount());
                    request.setPhone(FieldCipherUtil.decrypt(record.getReciverPhone()));
                    String response = ykService.selectRecordsFromVendor(request);
                    // 解析云客返回的通话记录
                    YKCallRecordsPageVO ykCallRecordsPageVO = ykService.recordsResultConverter(response);
                    if (null == ykCallRecordsPageVO) {
                        continue;
                    }
                    List<YKCallRecordsDTO> ykCallRecordsDTOS = ykCallRecordsPageVO.getYkCallRecordsDTOS();
                    // 根据外呼成功时返回的callId进行判断是否是同一条通话记录
                    for (YKCallRecordsDTO ykCallRecordsDTO : ykCallRecordsDTOS) {
                        if (record.getField2().equals(ykCallRecordsDTO.getCallId())) {
                            CallRecord callRecord = ykCallRecordsDTO.callRecordsConverter();
                            callRecord.setCallId(record.getCallId());
                            updateRecordList.add(callRecord);
                            updateSize++;
                            break;
                        }
                    }
                }
                this.batchUpdateCallRecordAndSendRecordingMQ(updateRecordList);
                updateRecordList.clear();
                subRecordList.clear();
            }
            return updateSize;
        }


        /**
         * 补充风云通话记录到ES
         *
         * @param recordList 需要补充的通话记录列表
         * <AUTHOR>
         * @Date 17:17 2022/7/6
         **/
        private int supplementaryFYCallRecord(List<CallRecord> recordList) {
            FYService fyService = SpringUtils.getBean(FYService.class);
            List<CallRecord> updateRecordList = new ArrayList<>(recordList.size());
            // 只查询今天的通话记录
            PullFYRecordRequest fyRecordRequest = new PullFYRecordRequest(null, null, DateUtils.getNowDString(DateUtil.YMD) + " 00:00:00", DateUtils.getNowDString());
            fyRecordRequest.setPage(1);
            // 从json文件获取到风云的所有账号
            List<Map<String, String>> fyAccountInfoList = fyService.selectFYAccountInfoList();
            // 将账号List转为key为NameSuffix的map方便后面获取
            Map<String, Map<String, String>> fyAccountInfoMap = fyAccountInfoList.stream().collect(Collectors.toMap(m -> m.get("NameSuffix"), Function.identity(), (t1, t2) -> t1));
            for (CallRecord record : recordList) {
                String callAccount = record.getCallAccount();
                String nameSuffix = callAccount.split("@")[1];
                Map<String, String> fyAccountInfo = fyAccountInfoMap.get(nameSuffix);
                if (fyAccountInfo == null) {
                    LOGGER.error("使用多线程去补充当天[风云厂商]通过智能运行平台拨打成功的通话里缺少通话时长属性的通话记录时找不到风云账号,nameSuffix:{}", nameSuffix);
                    continue;
                }
                fyRecordRequest.setAccountId(fyAccountInfo.get("AccountId"));
                fyRecordRequest.setApiSecret(fyAccountInfo.get("ApiSecret"));
                fyRecordRequest.setCalledNo(FieldCipherUtil.decrypt(record.getReciverPhone()));
                List<FYCallRecordsVO> fyCallRecordsVOS = fyService.selectRecordsFromVendor(fyRecordRequest);
                if (CollectionUtils.isEmpty(fyCallRecordsVOS)) {
                    continue;
                }
                for (FYCallRecordsVO fyCallRecordsVO : fyCallRecordsVOS) {
                    if (record.getField2().equals(fyCallRecordsVO.getACTION_ID())) {
                        CallRecord callRecord = fyCallRecordsVO.callRecordsConverter();
                        callRecord.setCallId(record.getCallId());
                        updateRecordList.add(callRecord);
                        break;
                    }
                }
            }
            this.batchUpdateCallRecordAndSendRecordingMQ(updateRecordList);
            return updateRecordList.size();
        }

        /**
         * 补充TQ通话记录到ES
         *
         * @param recordList 需要补充的通话记录列表
         * <AUTHOR>
         * @Date 17:17 2022/7/6
         **/
        private int supplementaryTQCallRecord(List<CallRecord> recordList) {
            TQService tqService = SpringUtils.getBean(TQService.class);
            List<CallRecord> updateRecordList = new ArrayList<>(recordList.size());
            LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            long startSecond = startTime.toEpochSecond(ZoneOffset.of("+8"));
            long endSecond = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
            PullTQRecordRequest tqRecordRequest = new PullTQRecordRequest(1, String.valueOf(startSecond), String.valueOf(endSecond));
            for (CallRecord record : recordList) {
                tqRecordRequest.setClient_id(record.getField2());
                String response = tqService.selectRecordsFromVendor(tqRecordRequest);
                List<TQCallRecordsVO> tqCallRecordsVOS = tqService.recordsResultConverter(response);
                if (CollectionUtils.isEmpty(tqCallRecordsVOS)) {
                    continue;
                }
                // 根据client_id,所以最多查询出来一条
                TQCallRecordsVO tqCallRecordsVO = tqCallRecordsVOS.get(0);
                CallRecord callRecord = tqCallRecordsVO.callRecordsConverter();
                callRecord.setCallId(record.getCallId());
                updateRecordList.add(callRecord);
            }
            this.batchUpdateCallRecordAndSendRecordingMQ(updateRecordList);
            return updateRecordList.size();
        }

        /**
         * 补充亿迅通话记录到ES
         *
         * @param recordList 需要补充的通话记录列表
         * <AUTHOR>
         * @Date 17:17 2022/7/6
         **/
        private int supplementaryYXCallRecord(List<CallRecord> recordList) {
            YXService yxService = SpringUtils.getBean(YXService.class);
            List<CallRecord> updateRecordList = new ArrayList<>(recordList.size());
            LocalDateTime of = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            String startTime = of.format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_FORMAT));
            String endTime = DateUtils.getNowDString();
            for (CallRecord record : recordList) {
                // 调用亿迅接口获取通话记录
                Map<String, String> yxServiceOneFromVendor = yxService.getOneFromVendorByUserData(record.getChannelType(), record.getField4(), record.getField2(), startTime, endTime);
                YXCallRecordsDTO yxCallRecordsDTO = new YXCallRecordsDTO().oneCdrResultConverter(yxServiceOneFromVendor, record.getField2());
                if (yxCallRecordsDTO == null) {
                    continue;
                }
                CallRecord callRecord = yxCallRecordsDTO.callRecordsConverter();
                callRecord.setCallAccount(null);
                callRecord.setAgentId(null);
                callRecord.setCallId(record.getCallId());
                updateRecordList.add(callRecord);
            }
            this.batchUpdateCallRecordAndSendRecordingMQ(updateRecordList);
            return updateRecordList.size();
        }

        /**
         * 补充亿迅SIP通话记录到ES
         *
         * @param recordList 需要补充的通话记录列表
         * @return 补充后更新记录的个数
         */
        private int supplementaryYXSipCallRecord(List<CallRecord> recordList) {
            YXService yxService = SpringUtils.getBean(YXService.class);
            List<CallRecord> updateRecordList = new ArrayList<>(recordList.size());
            LocalDateTime startOfToday = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            String startTime = startOfToday.format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_FORMAT));
            String endTime = DateUtils.getNowDString();
            for (CallRecord record : recordList) {
                // 调用亿迅接口获取通话记录
                Map<String, String> yxServiceOneFromVendor = yxService.getOneFromVendorByUserData(record.getChannelType(), record.getField4(), record.getField2(), startTime, endTime);
                YXSipCallRecordsDTO yxSipCallRecordsDTO = new YXSipCallRecordsDTO().oneCdrResultConverter(yxServiceOneFromVendor, record.getField2());
                if (yxSipCallRecordsDTO == null) {
                    continue;
                }
                CallRecord callRecord = yxSipCallRecordsDTO.callRecordsConverter();
                callRecord.setCallAccount(null);
                callRecord.setAgentId(null);
                callRecord.setCallId(record.getCallId());
                updateRecordList.add(callRecord);
            }
            this.batchUpdateCallRecordAndSendRecordingMQ(updateRecordList);
            return updateRecordList.size();
        }

        /**
         * 批量更新通话记录并且发送下载录音的MQ
         *
         * @param updateRecordList 需要更新通话记录的列表
         * <AUTHOR>
         * @Date 16:57 2022/7/6
         **/
        private void batchUpdateCallRecordAndSendRecordingMQ(List<CallRecord> updateRecordList) {
            // 更新通话记录
            if (updateRecordList.size() > 0) {
                CallRecordsBaseService callRecordsBaseService = new CallRecordsBaseService();
                String indexName = RecordUtil.getCurrentMonthRecordIndexName();
                List<Map<String, Object>> recordingData = callRecordsBaseService.batchUpdateCallRecord(indexName, updateRecordList);
                // 发送下载录音MQ
                recordingData.forEach(data -> client.publish(MQConst.DOWNLOAD_RECORDING_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(data)));
            }
        }
    }
}
