package com.niceloo.cmc.ex.jobs;

import com.niceloo.cmc.ex.service.CallRecordingPrecheckService;
import com.niceloo.cmc.ex.service.CallRecordingUnaccessiblePrecheckService;
import com.niceloo.framework.job.core.NicelooJob;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/*
 * 处理录音文件预检
 * 1. 录音文件不可访问性预检
 * 2. 录音文件可访问性预检
 *
 * <AUTHOR>
 * @since 2024-10-25 17:55:10
 */
@CustomLog
@Component
public class RecordingAnalyzeJobs {

    @Resource
    CallRecordingPrecheckService callRecordingPrecheckService;

    @Resource
    CallRecordingUnaccessiblePrecheckService callRecordingUnaccessiblePrecheckService;

    @NicelooJob(cron = "0 0 0/2 * * ?", dbRecord = true, singleRun = true)
    public void handleUnaccessibleRecordings() {
        // 录音地址预检
        try {
            callRecordingPrecheckService.processUnaccessibleRecordings();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @NicelooJob(cron = "0 0 0/1 * * ?", dbRecord = true, singleRun = true)
    public void handleAccessibleRecordings() {
        // 录音地址预检
        try {
            callRecordingUnaccessiblePrecheckService.processAccessibleRecordings();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
