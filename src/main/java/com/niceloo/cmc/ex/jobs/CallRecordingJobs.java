package com.niceloo.cmc.ex.jobs;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.CustomThreadFactory;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.entity.es.CallRecording;
import com.niceloo.cmc.ex.pojo.dto.UpdateRecordingDTO;
import com.niceloo.cmc.ex.pojo.dto.YKCallRecordsDTO;
import com.niceloo.cmc.ex.pojo.request.PullYKRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.YKCallRecordsPageVO;
import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.service.CallRecordingService;
import com.niceloo.cmc.ex.service.call.CallRecordUtil;
import com.niceloo.cmc.ex.service.call.YKService;
import com.niceloo.cmc.ex.utils.FieldCipherUtil;
import com.niceloo.cmc.ex.utils.FileUtil;
import com.niceloo.cmc.ex.utils.HttpUtils;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.file.FileUploadResponse;
import com.niceloo.framework.job.core.NicelooJob;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通话录音定时任务执行类
 * <AUTHOR>
 * @since 2022-02-17 14:13
 */
@Component
public class CallRecordingJobs {
    private static final Logger logger = LoggerFactory.getLogger(CallRecordingJobs.class);

    private static final ExecutorService syncLast24hVoiceExec = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(), new CustomThreadFactory("sync_voice_24h"));
    private static final ExecutorService syncLess30dMore24hVoiceExec = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(), new CustomThreadFactory("sync_voice_24h-30d"));
    private static final ExecutorService syncLess100dMore30dVoiceExec = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2, new CustomThreadFactory("sync_voice_30-100d"));
    private static final ExecutorService syncLess200dMore100dVoiceExec = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2, new CustomThreadFactory("sync_voice_100-200d"));


    @Value("${yunke.ossUrl}")
    private String yunKeUrl;
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Resource
    private CallRecordingService callRecordingService;
    @Resource
    private CallRecordService callRecordService;
    @Resource
    private FileUtil fileUtil;
    @Resource
    private YKService ykService;

    /**
     * 24小时内的录音,每4小时下载一次
     *
     * <AUTHOR>
     * @since 2024-11-09 09:35:32
     */
    @NicelooJob(cron = "0 0 0/4 * * ?", dbRecord = true, singleRun = true)
    public void yxSyncLast24hoursVoice() {
        syncLast24hoursVoice(CallChannelEnum.CALL_TYPE_ZK.getType());
    }

    /**
     * 超过24小时但是在30天内的录音,每月1,8,16,23日各执行一次
     *
     * <AUTHOR>
     * @since 2024-11-09 09:35:32
     */
    @NicelooJob(cron = "0 0 0 1,8,16,23 * ?", dbRecord = true, singleRun = true)
    public void yxSyncLess30daysMore24hoursVoice() {
        syncLess30daysMore24hoursVoice(CallChannelEnum.CALL_TYPE_ZK.getType());
    }

    /**
     * 超过30天但是在100天内的录音,每月1,16日各执行一次
     *
     * <AUTHOR>
     * @since 2024-11-09 09:35:32
     */
    @NicelooJob(cron = "0 0 0 1,16 * ?", dbRecord = true, singleRun = true)
    public void yxSyncLess100daysMore30daysVoice() {
        syncLess100daysMore30daysVoice(CallChannelEnum.CALL_TYPE_ZK.getType());
    }

    /**
     * 超过100天但是在200天内的录音,每月1日执行1次
     *
     * <AUTHOR>
     * @since 2024-11-09 09:35:32
     */
    @NicelooJob(cron = "0 0 0 1 * ?", dbRecord = true, singleRun = true)
    public void yxSyncLess200daysMore100daysVoice() {
        syncLess200daysMore100daysVoice(CallChannelEnum.CALL_TYPE_ZK.getType());
    }

    /**
     * 24小时内的录音,每4小时下载一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0/4 * * ?", dbRecord = true, singleRun = true)
    public void tqMobileSyncLast24hoursVoice() {
        syncLast24hoursVoice(CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType());
    }

    /**
     * 超过24小时但是在30天内的录音,每月1,8,16,23日各执行一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1,8,16,23 * ?", dbRecord = true, singleRun = true)
    public void tqMobileSyncLess30daysMore24hoursVoice() {
        syncLess30daysMore24hoursVoice(CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType());
    }

    /**
     * 超过30天但是在100天内的录音,每月1,16日各执行一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1,16 * ?", dbRecord = true, singleRun = true)
    public void tqMobileSyncLess100daysMore30daysVoice() {
        syncLess100daysMore30daysVoice(CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType());
    }

    /**
     * 超过100天但是在200天内的录音,每月1日执行1次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1 * ?", dbRecord = true, singleRun = true)
    public void tqMobileSyncLess200daysMore100daysVoice() {
        syncLess200daysMore100daysVoice(CallChannelEnum.CALL_TYPE_TQ_MOBILE.getType());
    }

    /**
     * 24小时内的录音,每4小时下载一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0/4 * * ?", dbRecord = true, singleRun = true)
    public void tqFixSyncLast24hoursVoice() {
        syncLast24hoursVoice(CallChannelEnum.CALL_TYPE_TQ_FIX.getType());
    }

    /**
     * 超过24小时但是在30天内的录音,每月1,8,16,23日各执行一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1,8,16,23 * ?", dbRecord = true, singleRun = true)
    public void tqFixSyncLess30daysMore24hoursVoice() {
        syncLess30daysMore24hoursVoice(CallChannelEnum.CALL_TYPE_TQ_FIX.getType());
    }

    /**
     * 超过30天但是在100天内的录音,每月1,16日各执行一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1,16 * ?", dbRecord = true, singleRun = true)
    public void tqFixSyncLess100daysMore30daysVoice() {
        syncLess100daysMore30daysVoice(CallChannelEnum.CALL_TYPE_TQ_FIX.getType());
    }

    /**
     * 超过100天但是在200天内的录音,每月1日执行1次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1 * ?", dbRecord = true, singleRun = true)
    public void tqFixSyncLess200daysMore100daysVoice() {
        syncLess200daysMore100daysVoice(CallChannelEnum.CALL_TYPE_TQ_FIX.getType());
    }

    /**
     * 24小时内的录音,每4小时下载一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0/4 * * ?", dbRecord = true, singleRun = true)
    public void fySyncLast24hoursVoice() {
        syncLast24hoursVoice(CallChannelEnum.CALL_TYPE_FY.getType());
    }

    /**
     * 超过24小时但是在30天内的录音,每月1,8,16,23日各执行一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1,8,16,23 * ?", dbRecord = true, singleRun = true)
    public void fySyncLess30daysMore24hoursVoice() {
        syncLess30daysMore24hoursVoice(CallChannelEnum.CALL_TYPE_FY.getType());
    }

    /**
     * 超过30天但是在100天内的录音,每月1,16日各执行一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1,16 * ?", dbRecord = true, singleRun = true)
    public void fySyncLess100daysMore30daysVoice() {
        syncLess100daysMore30daysVoice(CallChannelEnum.CALL_TYPE_FY.getType());
    }

    /**
     * 超过100天但是在200天内的录音,每月1日执行1次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1 * ?", dbRecord = true, singleRun = true)
    public void fySyncLess200daysMore100daysVoice() {
        syncLess200daysMore100daysVoice(CallChannelEnum.CALL_TYPE_FY.getType());
    }

    /**
     * 24小时内的中弘智享录音,每1小时下载一次
     *
     * <AUTHOR>
     * @since  2023-08-23 12:53:39
     */
    @NicelooJob(cron = "0 0 0/1 * * ?", dbRecord = true, singleRun = true)
    public void zhzxSyncLast24hoursVoice() {
        syncLast24hoursVoice(CallChannelEnum.CALL_TYPE_ZHZX.getType());
    }

    /**
     * 超过24小时但是在30天内的录音,每月1,8,16,23日各执行一次
     *
     * <AUTHOR>
     * @since  2023-08-23 12:53:39
     */
    @NicelooJob(cron = "0 0 0 1,8,16,23 * ?", dbRecord = true, singleRun = true)
    public void zhzxSyncLess30daysMore24hoursVoice() {
        syncLess30daysMore24hoursVoice(CallChannelEnum.CALL_TYPE_ZHZX.getType());
    }

    /**
     * 超过30天但是在100天内的录音,每月1,16日各执行一次
     *
     * <AUTHOR>
     * @since  2023-08-23 12:53:39
     */
    @NicelooJob(cron = "0 0 0 1,16 * ?", dbRecord = true, singleRun = true)
    public void zhzxSyncLess100daysMore30daysVoice() {
        syncLess100daysMore30daysVoice(CallChannelEnum.CALL_TYPE_ZHZX.getType());
    }

    /**
     * 超过100天但是在200天内的录音,每月1日执行1次
     *
     * <AUTHOR>
     * @since  2023-08-23 12:53:39
     */
    @NicelooJob(cron = "0 0 0 1 * ?", dbRecord = true, singleRun = true)
    public void zhzxSyncLess200daysMore100daysVoice() {
        syncLess200daysMore100daysVoice(CallChannelEnum.CALL_TYPE_ZHZX.getType());
    }

    /**
     * 24小时内的录音,每4小时下载一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0/4 * * ?", dbRecord = true, singleRun = true)
    public void oiSyncLast24hoursVoice() {
        syncLast24hoursVoice(CallChannelEnum.CALL_TYPE_OI.getType());
    }

    /**
     * 超过24小时但是在30天内的录音,每月1,8,16,23日各执行一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1,8,16,23 * ?", dbRecord = true, singleRun = true)
    public void oiSyncLess30daysMore24hoursVoice() {
        syncLess30daysMore24hoursVoice(CallChannelEnum.CALL_TYPE_OI.getType());
    }

    /**
     * 超过30天但是在100天内的录音,每月1,16日各执行一次
     *
     * <AUTHOR>
     * @since 2022/2/17 15:53
     */
    @NicelooJob(cron = "0 0 0 1,16 * ?", dbRecord = true, singleRun = true)
    public void oiSyncLess100daysMore30daysVoice() {
        syncLess100daysMore30daysVoice(CallChannelEnum.CALL_TYPE_OI.getType());
    }

    /**
     * 同步24小时内的录音
     *
     * @param channelType
     * <AUTHOR>
     * @since 2022/2/17 16:25
     */
    public void syncLast24hoursVoice(String channelType) {
        Date nowDate = new Date();
        String now = DateUtils.toStr(nowDate);
        String _24hoursAgo = DateUtils.toStr(DateUtils.addDay(nowDate, -1));
        String indexes = RecordUtil.getIndexStr(_24hoursAgo, now);
        syncLast24hVoiceExec.submit(CallRecordUtil.syncVoice(indexes, _24hoursAgo, now,
                null, null, channelType));
    }

    /**
     * 同步超过24小时但是在30天内的录音
     *
     * @param channelType
     * <AUTHOR>
     * @since 2022/2/17 16:25
     */
    private void syncLess30daysMore24hoursVoice(String channelType) {
        Date nowDate = new Date();
        String _24hoursAgo = DateUtils.toStr(DateUtils.addDay(nowDate, -1));
        String _30daysAgo = DateUtils.toStr(DateUtils.addDay(nowDate, -30));
        String indexes = RecordUtil.getIndexStr(_30daysAgo, _24hoursAgo);
        syncLess30dMore24hVoiceExec.submit(CallRecordUtil.syncVoice(indexes, _30daysAgo, _24hoursAgo,
                null, null, channelType));
    }

    /**
     * 同步超过30天但是在100天内的录音
     *
     * @param channelType
     * <AUTHOR>
     * @since 2022/2/17 16:25
     */
    private void syncLess100daysMore30daysVoice(String channelType) {
        Date nowDate = new Date();
        String _30daysAgo = DateUtils.toStr(DateUtils.addDay(nowDate, -30));
        String _100daysAgo = DateUtils.toStr(DateUtils.addDay(nowDate, -100));
        String indexes = RecordUtil.getIndexStr(_100daysAgo, _30daysAgo);
        for (String index : indexes.split(",")) {
            syncLess100dMore30dVoiceExec.submit(CallRecordUtil.syncVoice(index, _100daysAgo, _30daysAgo,
                    null, null, channelType));
        }
    }

    /**
     * 同步超过100天但是在200天内的录音
     *
     * @param channelType
     * <AUTHOR>
     * @since 2022/2/17 16:25
     */
    private void syncLess200daysMore100daysVoice(String channelType) {
        Date nowDate = new Date();
        String _100daysAgo = DateUtils.toStr(DateUtils.addDay(nowDate, -100));
        String _200daysAgo = DateUtils.toStr(DateUtils.addDay(nowDate, -200));
        String indexes = RecordUtil.getIndexStr(_100daysAgo, _200daysAgo);
        for (String index : indexes.split(",")) {
            syncLess200dMore100dVoiceExec.submit(CallRecordUtil.syncVoice(index, _200daysAgo, _100daysAgo,
                    null, null, channelType));
        }
    }

    /**
     * 定时任务:补充通话录音到通话记录内(查询上传失败次数小于十次的通话录音)
     * 让两个节点都运行
     * 1、从Redis内取出value，
     * 2、value空或为1,查询修改时间升序后三千五
     * 3、value为2,查询修改时间升序后跳过三千五条数据后的数据[即第二页]
     *
     * @paramter
     * <AUTHOR>
     * @since 11:09 2021/12/24
     **/
    @NicelooJob(cron = "0 0/30 * * * ?", dbRecord = true, singleRun = true)
    public void supplementaryCallRecordingJobs() {
        int page = SpringUtils.getBean(CallRecordingJobs.class).getCallRecordingPage();
        logger.info("开始定时任务:补充通话录音到通话记录内,时间:" + DateUtils.getNowDString() + ",查询的当前页数:" + page);
        this.supplementaryCallRecording(0, page);
    }

    /**
     * 定时任务:每天尝试一次失败十次以上的补充通话录音到通话记录内(查询上传失败次数大于十次的通话录音)
     *
     * @param
     * <AUTHOR>
     * @since 17:25 2021/12/26
     **/
    @NicelooJob(cron = "0 0 0 * * ?", dbRecord = true, singleRun = true)
    public void tryUpDownloadRecordingJobs() {
        // 尝试上传下载失败次数大于24次的通话录音
        this.supplementaryCallRecording(1, 0);
        // 尝试修复有通话时长但通话录音地址为空的数据[暂时只提供云客]
        this.tryRepairRecordingNonExistentRecords();
    }

    /**
     * 补充通话录音到通话记录内
     *
     * @param operator 操作符:[0 小于等于,1 大于]
     * @param from : 当前页数
     * <AUTHOR>
     * @since 11:09 2021/12/24
     **/
    private void supplementaryCallRecording(int operator, int from) {
        // 根据修改时间升序查询ES,过滤查询 通话录音,一次查询三千五百条
        // [次数以48次为分界线，每半小时执行一次，48次刚好24小时，如果一天都拉取不到录音，再转入按天补充的流程]
        // 由尝试10次，调整为尝试48次，是因为某些通话，如中弘智享在每天18:00以后，由运营商集中生成录音，尝试次数太短，录音获取的周期就比较长
        // 因此，改为48次，如果48次都拉取不到录音，再转入按天补充的流程
        List<CallRecording> callRecordings = callRecordingService.selectCallRecording(operator, from);
        if (CollectionUtils.isEmpty(callRecordings)) {
            return;
        }
        logger.info("开始定时任务->补充通话录音到通话记录内[supplementaryCallRecording],补充数量:" + callRecordings.size());
        int errorNum = 0;
        // 定义一个数组，存储上传成功的通话录音信息,进行更新通话记录和删除录音记录
        List<UpdateRecordingDTO> updateRecordingDTOS = new LinkedList<>();
        int length = callRecordings.size();
        int size = 100;
        int page = (length + size - 1) / size;
        // 分段上传下载录音和更新通话记录
        List<CallRecording> subCallRecordingList = new ArrayList<>(size);
        for (int i = 0; i < page; i++) {
            updateRecordingDTOS.clear();
            subCallRecordingList.clear();
            subCallRecordingList.addAll(callRecordings.subList((size * i), Math.min(length, size * (i + 1))));
            // 循环列表，进行下载和上传
            Iterator<CallRecording> iterator = subCallRecordingList.iterator();
            try {
                while (iterator.hasNext()) {
                    CallRecording callRecording = iterator.next();
                    // 获取通话录音地址信息,包括云客的bucket拷贝和其他厂商的上传下载过程，[拷贝或下载上传失败返回null]
                    UpdateRecordingDTO callRecordingInfo = this.getCallRecordingInfo(callRecording);
                    if (null != callRecordingInfo) {
                        updateRecordingDTOS.add(callRecordingInfo);
                        iterator.remove();
                    }
                }
                errorNum += subCallRecordingList.size();
                // 上传下载录音信息后更新通话记录
                this.modifyRecordingInCallLog(updateRecordingDTOS, subCallRecordingList);
            } catch (Exception e) {
                logger.warn(e, "定时任务->录音上传下载执行出现异常,异常信息:{}", e.getMessage());
            }
            logger.info("正在上传下载通话录音信息,本次需上传:" + length + "条数据,已上传:" + Math.min((i + 1) * size, length) + "条数据");
        }
        logger.info("完成定时任务->补充通话录音到通话记录内[supplementaryCallRecording],补充数量:" + length + "失败数量:" + errorNum);
    }

    /**
     * 更新通话记录中的通话录音部分信息
     * 细节: 上传下载成功的录音批量更新call_record_202112[年月] 索引里的通话录音信息,将修改成功的数据从call_recording索引里删除
     * 上传下载失败的录音失败次数加 +1
     *
     * @paramter updateRecordingDTOS 上传录音成功的列表
     * @paramter callRecordingBos 上传录音失败的列表
     * <AUTHOR>
     * @since 11:31 2021/12/26
     **/
    private void modifyRecordingInCallLog(List<UpdateRecordingDTO> updateRecordingDTOS, List<CallRecording> callRecordingBos) {
        // 更新通话记录[call_record_xxxxx],删除存储的通话录音[call_recording]
        if (!CollectionUtils.isEmpty(updateRecordingDTOS)) {
            callRecordService.bulkUpdate(updateRecordingDTOS);
            // 根据ids批量删除通话录音
            List<String> ids = updateRecordingDTOS.stream().map(UpdateRecordingDTO::getCallId).collect(Collectors.toList());
            callRecordingService.batchDeleteByIds(ids);
        }
        // 更新上传下载失败的通话录音[call_recording]
        if (!CollectionUtils.isEmpty(callRecordingBos)) {
            callRecordingService.bulkUpdate(callRecordingBos);
        }
    }

    /**
     * 获取通话录音地址信息,包括云客的bucket拷贝和其他厂商的上传过程，[拷贝或上传失败返回null]
     *
     * @return com.niceloo.cmc.ex.pojo.dto.UpdateRecordingDTO
     * @paramter callRecordingBo
     * <AUTHOR>
     * @since 15:06 2021/12/24
     **/
    private UpdateRecordingDTO getCallRecordingInfo(CallRecording callRecording) {
        if (CallChannelEnum.CALL_TYPE_YPHONE.getType().equals(callRecording.getChannelType())) {
            return this.YKCallRecordingURLCopy(callRecording);
        }
        return this.otherCallRecodingUpDownload(callRecording);
    }

    /**
     * 云客的通话录音地址bucket copy
     *
     * @return com.niceloo.cmc.ex.pojo.dto.UpdateRecordingDTO
     * @paramter callRecordingBo
     * <AUTHOR>
     * @since 15:08 2021/12/24
     **/
    private UpdateRecordingDTO YKCallRecordingURLCopy(CallRecording callRecording) {
        String voiceSourceUrl = callRecording.getVoiceSourceUrl();
        String fileName = callRecording.getCallId() + ".mp3";
        try {
            // Bucket地址copy
            Map<String, String> ossCopyFile = fileUtil.ossCopy(voiceSourceUrl, fileName, yunKeUrl);
            if (MapUtils.isEmpty(ossCopyFile)) {
                return null;
            }
            String index = "call_record_" + callRecording.getDate().replace("-", "");
            return new UpdateRecordingDTO(index, callRecording.getCallId(), ossCopyFile.get("filePath"), ossCopyFile.get("fileKey"));
        } catch (Exception e) {
            logger.warn(e, "云客copyBucket通话录音callId为:" + callRecording.getCallId() + "出现异常");
            callRecording.setAttempts(callRecording.getAttempts() + 1);
            callRecording.setOperationSign(1);
            callRecording.setModifierTime(new Date());
            return null;
        }
    }

    /**
     * 除云客外的其他厂商通话录音上传下载[亿迅、TQ、风云、一号互联、 中弘智享]
     *
     * @return com.niceloo.cmc.ex.pojo.dto.UpdateRecordingDTO
     * @paramter callRecordingBo
     * <AUTHOR>
     * @since 15:08 2021/12/24
     **/
    private UpdateRecordingDTO otherCallRecodingUpDownload(CallRecording callRecording) {
        try {
            String fileName = callRecording.getCallId() + ".mp3";
            // URL网络地址录音上传到阿里云服务器
            logger.info("开始上传非云客厂家录音:{}", fileName);
            Map<String, Object> fileValue = HttpUtils.uploadVoiceToOSS(callRecording.getVoiceSourceUrl(), fileName);
            if (fileValue.isEmpty()) {
                return null;
            }
            FileUploadResponse fileUploadResponse = MapUtils.map2Bean(FileUploadResponse.class, fileValue);
            String index = "call_record_" + callRecording.getDate().replace("-", "");
            return new UpdateRecordingDTO(index, callRecording.getCallId(), fileUploadResponse.getFilePath(), fileUploadResponse.getFileKey());
        } catch (Exception e) {
            logger.info(e, "URL网络地址录音文件上传失败");
            callRecording.setAttempts(callRecording.getAttempts() + 1);
            callRecording.setOperationSign(1);
            callRecording.setModifierTime(new Date());
            return null;
        }
    }

    /**
     * 尝试修复通话录音地址为空,通话时长大于二十秒的通话记录,只支持云客
     *
     * @paramter
     * <AUTHOR>
     * @since 10:33 2021/12/27
     **/
    public void tryRepairRecordingNonExistentRecords() {
        logger.info("开始定时任务:尝试修复云客通话录音地址为空,通话时长大于二十秒的通话记录");
        // 查询通话录音地址为空,重试次数小于3次,升序查询出来，一次两千个
        List<CallRecording> callRecordingList = callRecordingService.selectNonRecordingList();
        // 循环列表
        if (CollectionUtils.isEmpty(callRecordingList)) {
            logger.info("完成定时任务:尝试修复云客通话录音地址为空通话时长大于二十秒的通话记录,未查询到数据");
            return;
        }
        int count = 0;
        // 索引分组
        Map<String, List<CallRecording>> map = callRecordingList.stream().collect(Collectors.groupingBy(dto -> dto.getDate().replace("-", "")));
        for (Map.Entry<String, List<CallRecording>> entry : map.entrySet()) {
            String index = RecordUtil.getRecordIndexName(entry.getKey(), "yyyyMM");
            List<CallRecording> subCallRecordingList = entry.getValue();
            List<String> ids = subCallRecordingList.stream().map(CallRecording::getCallId).collect(Collectors.toList());
            // 从通话记录里获取被叫手机号和员工的userID信息列表
            List<CallRecord> callRecordList = callRecordService.getPhoneAndUserIdList(index, ids);
            Map<String, CallRecord> callRecordMap = callRecordList.stream().collect(Collectors.toMap(CallRecord::getCallId, Function.identity(), (t1, t2) -> t2));
            for (CallRecording callRecording : subCallRecordingList) {
                CallRecord callRecord = callRecordMap.get(callRecording.getCallId());
                // 从厂商查询通话录音
                this.selectCallRecordsFromCompany(callRecording, callRecord);
            }
            // 更新通话记录和通话录音
            callRecordList.removeIf(callRecord -> StringUtils.isEmpty(callRecord.getVoiceSourceUrl()));
            callRecordService.bulkUpdate(index, callRecordList);
            callRecordingService.bulkUpdate(subCallRecordingList);
            count += callRecordList.size();
        }
        logger.info("完成定时任务:尝试修复云客通话录音地址为空,通话时长大于二十秒的通话记录,修复成功数量:" + count);
    }

    /**
     * 从厂商查询通话录音
     *
     * @paramter callRecording 通话录音信息
     * @paramter callRecord 该条通话记录的厂商id、主被叫手机号、用户id信息
     * <AUTHOR>
     * @since 15:13 2021/12/27
     **/
    private void selectCallRecordsFromCompany(CallRecording callRecording, CallRecord callRecord) {
        CallChannelEnum callChannel = CallChannelEnum.getCallChannel(callRecording.getChannelType());
        if (null == callChannel) {
            return;
        }
        switch (callChannel) {
            case CALL_TYPE_YPHONE: {
                this.selectYKCallRecords(callRecording, callRecord);
                break;
            }
            case CALL_TYPE_YH:
            case CALL_TYPE_ZK: {
                break;
            }
            default:
                logger.warn("未知的通话类型:{}", callChannel);
        }
    }

    /**
     * 查询云客系统获取录音地址
     *
     * @paramter callRecording
     * @paramter callRecord
     * <AUTHOR>
     * @since 15:04 2021/12/27
     **/
    private void selectYKCallRecords(CallRecording callRecording, CallRecord callRecord) {
        // 查询云客系统
        List<YKCallRecordsDTO> callRecordsDTOS = this.selectRecordsFromVendor(callRecording.getCreateTime(), callRecord);
        for (YKCallRecordsDTO callRecordsDTO : callRecordsDTOS) {
            if (callRecord.getVoiceSourceId().equals(callRecordsDTO.getId())) {
                if (!StringUtils.isEmpty(callRecordsDTO.getRecordUrl())) {
                    callRecord.setVoiceSourceUrl(callRecordsDTO.getRecordUrl());
                    callRecord.setVoiceSyncStatus(1);
                    callRecord.setModifiedTime(new Date());
                    callRecording.setVoiceSourceUrl(callRecordsDTO.getRecordUrl());
                    callRecording.setAttempts(0);
                    callRecording.setOperationSign(0);
                    callRecording.setModifierTime(new Date());
                    break;
                }
            }
        }
        callRecording.setAttempts(callRecording.getAttempts() + 1);
        callRecording.setOperationSign(-1);
        callRecording.setModifierTime(new Date());
    }

    /**
     * 根据时间段、员工ID、客户手机号查询云客系统获取通话记录
     *
     * @return java.util.List<com.niceloo.cmc.ex.pojo.dto.YKCallRecordsDTO>
     * @paramter date
     * @paramter callRecord
     * <AUTHOR>
     * @since 15:55 2021/12/31
     **/
    private List<YKCallRecordsDTO> selectRecordsFromVendor(Date date, CallRecord callRecord) {
        String phone = FieldCipherUtil.decrypt(callRecord.getReciverPhone());
        String beginTime = DateUtils.toStr(DateUtils.addHour(date, -2));
        String endTime = DateUtils.toStr(DateUtils.addHour(date, 2));
        PullYKRecordRequest request = new PullYKRecordRequest(1, BizConst.PAGE_SIZE, beginTime, endTime);
        request.setUserId(callRecord.getCallerUserId());
        request.setPhone(phone);
        // 查询云客系统
        String response = ykService.selectRecordsFromVendor(request);
        YKCallRecordsPageVO callRecordsPage = ykService.recordsResultConverter(response);
        if(null == callRecordsPage){
            return new ArrayList<>();
        }
        return callRecordsPage.getYkCallRecordsDTOS();
    }

    /**
     * 获取定时任务查询通话录音数据的第几页
     *
     * @return int
     * @paramter
     * <AUTHOR>
     * @since 17:10 2022/1/6
     **/
    @Klock(name = "CALL_RECORDING_REPLENISH_LOCK", waitTime = 30, leaseTime = 5, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public int getCallRecordingPage() {
        String key = RedisConst.CACHE_NAMESPACE + "CALL_RECORDING_REPLENISH";
        String page = redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(page)) {
            redisTemplate.opsForValue().set(key, "1", Duration.ofMinutes(5));
            page = "0";
        } else {
            redisTemplate.opsForValue().set(key, String.valueOf(Integer.parseInt(page) + 1), Duration.ofMinutes(5));
        }
        return Integer.parseInt(page);
    }

    /**
     * 删除昨天之前的call_recording中operationSign为1的记录
     * 每天的早上8点执行一次
     *
     * @dbRecord 是否记录任务执行日志，true表示记录
     */
    @NicelooJob(cron = "0 0 8 * * ?", dbRecord = true, singleRun = true)
    public void deleteDataBeforeYesterday() {
        callRecordingService.deleteDataBeforeYesterday();
    }
}
