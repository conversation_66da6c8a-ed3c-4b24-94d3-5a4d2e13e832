package com.niceloo.cmc.ex.jobs;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.service.TrafficStatisticService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.job.core.NicelooJob;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.CollectionUtils;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 话务统计定时任务
 *
 * <AUTHOR>
 * @since 2022-02-23 17:21
 */
@Component
public class TrafficStatisticJobs {
    private static final Logger LOGGER = LoggerFactory.getLogger(TrafficStatisticJobs.class);
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Resource
    private TrafficStatisticService trafficStatisticService;

    /**
     * 每天凌晨2:30 生成云外呼通道账号余额
     * modified by Liuqi，2023-11 定时任务停止，不再查询余额
     */
    // @NicelooJob(cron = "0 30 2 * * ?", dbRecord = true, singleRun = true)
    public void createSchoolBalance() {

        trafficStatisticService.createSchoolBalance();
    }

    /**
     * 实时统计呼叫数据，存储到 redis 中（每五分钟）
     */
    @NicelooJob(cron = "0 0/5 * * * ?", dbRecord = true, singleRun = true)
    public void saveRealtimeCount() {
        long l = System.currentTimeMillis();
        LOGGER.info("定时任务:开始执行-->实时统计呼叫数据,存储到redis中");
        Map<String, Object> map = new HashMap<>();
        try {
            String nowDString = DateUtils.getNowDString(DateUtil.YMD);
            String startTime = nowDString + " 00:00:00";
            String endTime = nowDString + " 23:59:59";
            Map<String, String> result = trafficStatisticService.getCountDataBySchool(startTime, endTime);
            if (result.size() > 0) {
                map.putAll(result);
                redisTemplate.opsForHash().putAll(RedisConst.REAL_TIME_KEY, map);
                //设置过期时间为今天最后一秒
                redisTemplate.expireAt(RedisConst.REAL_TIME_KEY, DateUtils.toDate(endTime));
            }
        } catch (Exception e) {
            LOGGER.error(e, "实时统计呼叫数据，存储到redis中出现异常,异常信息:{}", e.getMessage());
        }
        long l2 = System.currentTimeMillis();
        LOGGER.info("定时任务:执行完成-->实时统计呼叫数据,存储到redis中,耗时:" + (l2 - l) / 1000 + "秒");
    }

    /**
     * 每五分钟执行一次，统计员工通话记录数据汇总。查询ES,汇总到MySQL日统计表内<br/>
     *  2022年10月11日13:40:16->改为一天执行一次,因为后面统计直接在ES内统计不再使用MySQL进行统计
     *  2022年11月14日17:32:48->应产品老师要求,统计再次放开
     * <AUTHOR>
     * @since 15:21 2021/12/6
     **/
    @NicelooJob(cron = "0 2/5 6-23 * * ?", dbRecord = true, singleRun = true)
//    @NicelooJob(cron = "0 30 23 * * ?", dbRecord = true, singleRun = true)
    public void saveDayCountToDB() {
        long l = System.currentTimeMillis();
        String newDate = DateUtils.getNowDString(DateUtil.YMD_HM) + ":00";
        //获取上次执行的时间,并将该次执行时间放置到Redis内
        String oldDate = redisTemplate.opsForValue().getAndSet(RedisConst.TRAFFIC_STATISTIC_DAY_KEY, newDate);
        //如果不是第一次执行,并且间隔上次执行小五分钟直接return
        if (!StringUtils.isEmpty(oldDate) && DateUtils.compare(newDate, oldDate) < (1000 * 60 * 5)) {
            return;
        }
        LOGGER.info("定时任务:进行日话务统计,统计开始时间:" + newDate);
        //开始进行日话务统计
        this.saveDayRecordCount(newDate);
        long l2 = System.currentTimeMillis();
        LOGGER.info("定时任务:日话务统计完成,完成时间:" + DateUtils.getNowDString() + ",耗时:" + (l2 - l) / 1000 + "秒");
        //补偿是否有历史通话记录统计出现异常数据进行补偿
        this.compensationStatisticallyFailedTraffic("day");

        //补偿是否有历史通话记录未统计进行补偿
        if (StringUtils.isEmpty(oldDate)) {
            return;
        }
        //获取需要补偿的天数
        Long distanceDays = DateUtil.getDistanceDays(newDate, oldDate, DateUtil.YMD);
        for (int i = 0; i < distanceDays; i++) {
            String date = DateUtils.toStr(DateUtils.addDay(DateUtils.toDate(oldDate), i));
            LOGGER.info("定时任务:日话务统计补偿,进行补偿时间为(" + date + ")的通话记录,执行开始,开始时间:" + DateUtils.getNowDString());
            this.saveDayRecordCount(date);
            LOGGER.info("定时任务:日话务统计补偿,进行补偿时间为(" + date + ")的通话记录,执行完成,完成时间:" + DateUtils.getNowDString());
        }
    }

    /**
     * 一天执行一次，统计员工通话记录数据汇总。查询ES,汇总到MySQL月统计表内
     *
     * <AUTHOR>
     * @since 15:21 2021/12/6
     **/
    @NicelooJob(cron = "0 30 3 * * ?", dbRecord = true, singleRun = true)
    public void saveMonthCountToDB() {
        long l = System.currentTimeMillis();
        String newDate = DateUtils.getNowDString(DateUtil.YMD_HM) + ":00";
        //获取上次执行的时间,并将该次执行时间放置到Redis内
        String oldDate = redisTemplate.opsForValue().getAndSet(RedisConst.TRAFFIC_STATISTIC_MONTH_KEY, newDate);
        //如果不是第一次执行,并且间隔上次执行小于一天直接return
        if (!StringUtils.isEmpty(oldDate) && DateUtils.compare(newDate, oldDate) < (RedisConst.ONE_DAY_MILLIS)) {
            return;
        }
        LOGGER.info("定时任务:进行月话务统计,统计开始时间:" + newDate);
        //开始进行月话务统计[统计上一天所属月份数据]
        this.saveMonthRecordCount(DateUtils.toStr(DateUtils.addDay(DateUtils.toDate(newDate), -1)));
        long l2 = System.currentTimeMillis();
        LOGGER.info("定时任务:月话务统计完成,完成时间:" + DateUtils.getNowDString() + ",耗时:" + (l2 - l) / 1000 + "秒");

        //补偿是否有历史通话记录统计出现异常数据进行补偿
        this.compensationStatisticallyFailedTraffic("month");

        //补偿是否有历史通话记录未统计进行补偿
        if (StringUtils.isEmpty(oldDate)) {
            return;
        }
        //获取需要补偿的月数
        int distanceMonths = DateUtil.getDistanceMonths(newDate, oldDate);
        //如果今天是当月的一号,不需要补偿上个月的统计数据,因为上面已经进行了统计
        if (DateUtils.getDay(DateUtils.toDate(newDate)) == 1) {
            distanceMonths -= 1;
        }
        for (int i = 0; i < distanceMonths; i++) {
            String date = DateUtils.toStr(DateUtils.addDay(DateUtils.toDate(oldDate), i));
            LOGGER.info("定时任务:月话务统计补偿,进行补偿时间为(" + date + ")的通话记录,执行开始,开始时间:" + DateUtils.getNowDString());
            this.saveMonthRecordCount(date);
            LOGGER.info("定时任务:月话务统计补偿,进行补偿时间为(" + date + ")的通话记录,执行完成,完成时间:" + DateUtils.getNowDString());
        }
    }

    /**
     * 进行日话务统计
     *
     * @param newDate
     * <AUTHOR>
     * @since 11:22 2021/12/8
     **/
    private void saveDayRecordCount(String newDate) {
        String dayDate = DateUtil.format(newDate, DateUtil.YMD);
        CallChannelEnum[] callChannelEnums = CallChannelEnum.values();
        for (CallChannelEnum callChannelEnum : callChannelEnums) {
            // 京东言犀不需要进行话务统计
            if (CallChannelEnum.isNotTrafficStatistic(callChannelEnum)) {
                continue;
            }
            this.addDayStatisticToDB(dayDate, callChannelEnum.getType());
        }
    }

    /**
     * 添加日统计
     *
     * @param dayDate
     * @param channelType
     * <AUTHOR>
     * @since 11:51 2021/12/13
     **/
    private void addDayStatisticToDB(String dayDate, String channelType) {
        try {
            trafficStatisticService.addDayStatisticToDB(dayDate, channelType);
        } catch (Exception e) {
            //失败后,放入Redis内后面进行补偿
            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("date", dayDate);
            errorMap.put("channelType", channelType);
            redisTemplate.opsForSet().add(RedisConst.TRAFFIC_STATISTIC_ERROR_DAY_KEY, JSONUtils.toJSONString(errorMap));
            LOGGER.error(e, "定时任务:话务统计->日统计表出现异常,统计时间:{}外呼类型:{},异常信息:{}", dayDate, channelType, e.getMessage());
        }
    }

    /**
     * 进行月话务统计
     *
     * @param newDate
     * <AUTHOR>
     * @since 11:22 2021/12/8
     **/
    private void saveMonthRecordCount(String newDate) {
        String monthDate = DateUtil.format(newDate, DateUtil.YM);
        CallChannelEnum[] callChannelEnums = CallChannelEnum.values();
        for (CallChannelEnum callChannelEnum : callChannelEnums) {
            // 京东言犀不需要进行话务统计
            if (CallChannelEnum.isNotTrafficStatistic(callChannelEnum)) {
                continue;
            }
            this.addMonthStatisticToDB(monthDate, callChannelEnum.getType());
        }
    }

    /**
     * 添加月统计
     *
     * @param dayDate
     * @param channelType
     * <AUTHOR>
     * @since 11:51 2021/12/13
     **/
    private void addMonthStatisticToDB(String dayDate, String channelType) {
        try {
            trafficStatisticService.addMonthStatisticToDB(dayDate, channelType);
        } catch (Exception e) {
            //失败后,放入Redis内后面进行补偿
            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("date", dayDate);
            errorMap.put("channelType", channelType);
            redisTemplate.opsForSet().add(RedisConst.TRAFFIC_STATISTIC_ERROR_MONTH_KEY, JSONUtils.toJSONString(errorMap));
            LOGGER.error(e, "定时任务:话务统计->月统计表出现异常,统计时间:{}外呼类型:{},异常信息:{}", dayDate, channelType, e.getMessage());
        }
    }

    /**
     * 补偿话务统计失败的话务
     *
     * @param type 统计类型 [day:日,month:月]
     * <AUTHOR>
     * @since 11:40 2021/12/13
     **/
    public void compensationStatisticallyFailedTraffic(String type) {
        //获取全部失败的话务统计信息
        switch (type) {
            case "day": {
                Set<String> members = redisTemplate.opsForSet().members(RedisConst.TRAFFIC_STATISTIC_ERROR_DAY_KEY);
                redisTemplate.delete(RedisConst.TRAFFIC_STATISTIC_ERROR_DAY_KEY);
                if (CollectionUtils.isEmpty(members)) {
                    return;
                }
                for (String member : members) {
                    Map<String, Object> map = JSONUtils.toMap(member);
                    this.addDayStatisticToDB(map.get("date").toString(), map.get("channelType").toString());
                }
                return;
            }
            case "month": {
                Set<String> members = redisTemplate.opsForSet().members(RedisConst.TRAFFIC_STATISTIC_ERROR_MONTH_KEY);
                redisTemplate.delete(RedisConst.TRAFFIC_STATISTIC_ERROR_MONTH_KEY);
                if (CollectionUtils.isEmpty(members)) {
                    return;
                }
                for (String member : members) {
                    Map<String, Object> map = JSONUtils.toMap(member);
                    this.addMonthStatisticToDB(map.get("date").toString(), map.get("channelType").toString());
                }
                return;
            }
            default:
        }
    }
}
