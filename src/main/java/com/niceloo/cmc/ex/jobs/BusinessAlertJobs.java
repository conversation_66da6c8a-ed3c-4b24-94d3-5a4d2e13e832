package com.niceloo.cmc.ex.jobs;

import com.niceloo.cmc.ex.service.BusinessMetricsService;
import com.niceloo.cmc.ex.service.DataCollectionService;
import com.niceloo.cmc.ex.service.impl.WechatGroupRobotServiceImpl;
import com.niceloo.framework.job.core.NicelooJob;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@CustomLog
@Component
public class BusinessAlertJobs {

    @Resource
    private BusinessMetricsService businessMetricsService;

    @Resource
    private DataCollectionService dataCollectionService;

    @Resource
    private WechatGroupRobotServiceImpl wechatGroupRobotService;

    /**
     * 每天执行两次，执行业务告警
     *
     * @NicelooJob cron表达式设置为"0 0 10,16 * * ?"，表示每天的10点和16点执行
     * @return 无返回值
     */
    @NicelooJob(cron = "0 0 10,16 * * ?", dbRecord = true, singleRun = true)
    public void executeBusinessAlert() {
        // 1. 监控关键业务指标，并发送企业微信群告警
        LOGGER.debug("1. 监控关键业务指标，并发送企业微信群告警 START");
        monitorBusinessMetrics();
        LOGGER.debug("1. 监控关键业务指标，并发送企业微信群告警 END");

        // 2. 实时数据采集和分析，并发送企业微信群告警
        LOGGER.debug("2. 实时数据采集和分析，并发送企业微信群告警 START");
        collectAndAnalyzeData();
        LOGGER.debug("2. 实时数据采集和分析，并发送企业微信群告警 END");
    }

    private void monitorBusinessMetrics() {
        // 调用 BusinessMetricsService 的方法,监控关键业务指标
        businessMetricsService.monitorBusinessMetrics();
    }

    private void collectAndAnalyzeData() {
        // 调用 DataCollectionService 的方法,实时采集和分析数据
        dataCollectionService.collectAndAnalyzeData();
    }
}
