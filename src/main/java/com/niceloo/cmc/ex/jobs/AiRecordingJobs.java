package com.niceloo.cmc.ex.jobs;

import com.niceloo.cmc.ex.pojo.param.SyncAiVoiceParam;
import com.niceloo.cmc.ex.service.AICallJobCustomerInfoService;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.job.core.NicelooJob;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * AI通话录音定时任务执行类
 * <AUTHOR>
 * @since 2024-12-03 15:51:40
 */
@CustomLog
@Component
public class AiRecordingJobs {

    @Resource
    private AICallJobCustomerInfoService aICallJobCustomerInfoService;

    /**
     * 每4小时执行一次，下载最近24小时内的录音文件。
     *
     * 使用@NicelooJob注解配置定时任务，cron表达式"0 0 0/4 * * ?"指定了任务每4小时执行一次。
     *
     * 方法内部首先通过RecordUtil工具类获取当前月份的Elasticsearch索引。
     * 然后，使用RecordUtil的buildSyncAiVoiceParam方法构建SyncAiVoiceParam参数对象，
     * 该参数对象指定了录音文件的查询条件，即时间范围为最近24小时内（从0小时前到24小时前）。
     * 最后，调用aICallJobCustomerInfoService的downloadAndUploadRecordings方法，
     * 传入构建好的参数对象，下载并上传符合条件的录音文件。
     */
    @NicelooJob(cron = "0 0 0/4 * * ?", dbRecord = true, singleRun = true)
    public void downloadRecentRecordings() {
        String index = RecordUtil.getCurrentMonthIndex(); // 获取当前月份的索引
        SyncAiVoiceParam param = RecordUtil.buildSyncAiVoiceParam(index, 0, 24);
        aICallJobCustomerInfoService.downloadAndUploadRecordings(param);
    }

    /**
     * 每月1日、8日、16日、23日下载存储时间超过24小时但在30天内的录音文件。
     *
     * <p>该方法使用@NicelooJob注解配置了定时任务，cron表达式"0 0 0 1,8,16,23 * ?"指定了任务在每月的1日、8日、16日、23日的0点0分0秒执行。
     *
     * <p>方法内部首先通过RecordUtil工具类的getIndexesForRange方法获取上一个月的Elasticsearch索引列表。
     * 然后，遍历索引列表，为每个索引构建一个SyncAiVoiceParam参数对象，指定时间范围为从24小时前到30天*24小时前。
     * 最后，对于每个参数对象，调用aICallJobCustomerInfoService的downloadAndUploadRecordings方法，
     * 根据构建的参数下载并上传符合条件的录音文件。
     */
    // 2. 每月 1, 8, 16, 23 日下载超过 24 小时但在 30 天内的录音
    @NicelooJob(cron = "0 0 0 1,8,16,23 * ?", dbRecord = true, singleRun = true)
    public void downloadRecordingsWithin30Days() {
        List<String> indexes = RecordUtil.getIndexesForRange(1, 1); // 获取上一个月的索引
        for (String index : indexes) {
            SyncAiVoiceParam param = RecordUtil.buildSyncAiVoiceParam(index, 24, 30 * 24);
            aICallJobCustomerInfoService.downloadAndUploadRecordings(param);
        }
    }

    /**
     * 每月1日和16日下载存储时间超过30天但在100天内的录音文件。
     *
     * 使用@NicelooJob注解配置定时任务，cron表达式"0 0 0 1,16 * ?"指定了任务在每月的1日和16日的0点0分0秒执行。
     *
     * 方法功能描述：
     * 1. 调用RecordUtil工具类的getIndexesForRange方法获取过去4个月的Elasticsearch索引列表。
     * 2. 遍历索引列表，为每个索引构建SyncAiVoiceParam参数对象。
     *    - 参数对象的索引名设置为当前遍历到的索引。
     *    - 时间范围设置为从30天*24小时前到100天*24小时前，即下载超过30天但在100天内的录音。
     * 3. 调用aICallJobCustomerInfoService的downloadAndUploadRecordings方法，
     *    根据构建的SyncAiVoiceParam参数对象下载并上传符合条件的录音文件。
     *
     * <AUTHOR>
     * @since [创建日期]
     */
    // 3. 每月 1, 16 日下载超过 30 天但在 100 天内的录音
    @NicelooJob(cron = "0 0 0 1,16 * ?", dbRecord = true, singleRun = true)
    public void downloadRecordingsWithin100Days() {
        List<String> indexes = RecordUtil.getIndexesForRange(4, 1); // 获取过去4个月的索引
        for (String index : indexes) {
            SyncAiVoiceParam param = RecordUtil.buildSyncAiVoiceParam(index, 30 * 24, 100 * 24);
            aICallJobCustomerInfoService.downloadAndUploadRecordings(param);
        }
    }

    /**
     * 每月1日下载存储时间超过100天但在200天内的录音文件。
     *
     * 使用@NicelooJob注解配置定时任务，cron表达式"0 0 0 1 * ?"指定了任务在每月1日的0点0分0秒执行。
     *
     * 方法内部首先通过RecordUtil工具类的getIndexesForRange方法获取过去7个月的Elasticsearch索引列表。
     * 然后，遍历索引列表，为每个索引构建一个SyncAiVoiceParam参数对象，指定时间范围为从100天*24小时前到200天*24小时前。
     * 最后，对于每个参数对象，调用aICallJobCustomerInfoService的downloadAndUploadRecordings方法，
     * 下载并上传符合条件的录音文件。
     */
    // 4. 每月 1 日下载超过 100 天但在 200 天内的录音
    @NicelooJob(cron = "0 0 0 1 * ?", dbRecord = true, singleRun = true)
    public void downloadRecordingsWithin200Days() {
        List<String> indexes = RecordUtil.getIndexesForRange(7, 1); // 获取过去7个月的索引
        for (String index : indexes) {
            SyncAiVoiceParam param = RecordUtil.buildSyncAiVoiceParam(index, 100 * 24, 200 * 24);
            aICallJobCustomerInfoService.downloadAndUploadRecordings(param);
        }
    }
}
