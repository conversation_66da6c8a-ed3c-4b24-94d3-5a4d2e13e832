package com.niceloo.cmc.ex.mq;

import com.niceloo.cmc.ex.controller.mq.AddCustomerInfoToJobMQConsumer;
import com.niceloo.framework.json.JSONUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 追加客户MQ测试类
 *
 * <AUTHOR>
 * @Date 2022-09-30 17:14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AddCustomerInfoToJobMQConsumerTest {

    @Resource
    private AddCustomerInfoToJobMQConsumer addCustomerInfoToJobMQConsumer;

    @Test
    public void addTest() {
        Map<String, String> mqRequest = new HashMap<>();
        mqRequest.put("indexName", "ai_job_customer_202210");
        mqRequest.put("lotNo", "2ec065c3d7994aad94dd49eb09f7f5c7");
        mqRequest.put("jobId", "JOBID20221006000000106502");
        mqRequest.put("finishFlag", "Y");
        addCustomerInfoToJobMQConsumer.subscribe(JSONUtils.toJSONString(mqRequest));
    }
}
