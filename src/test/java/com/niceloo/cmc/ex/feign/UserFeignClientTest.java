package com.niceloo.cmc.ex.feign;

import com.niceloo.framework.json.JSONUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-29 17:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class UserFeignClientTest {

    @Autowired
    private UserFeignClient userFeignClient;

    @Test
    public void getUserInfoByUserIdTest() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId", "USER2020040901000004232");
        Map<String, Object> userInfo = userFeignClient.getUserInfoByUserId(JSONUtils.toJSONString(map));
        String ucSchoolId = userInfo.get("schoolId").toString();
        System.out.println(ucSchoolId);
    }
}

