package com.niceloo.cmc.ex.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * 根据excel文件中存储的es记录，校验通讯厂家下载路径的有效性
 * <AUTHOR>
 * @since 2023/08/08
 */
public class ExcelFileChecker {
    private static final int NUM_THREADS = 10; // 并行处理的线程数

    public static void main(String[] args) {
        String fileName = "export202107";
        String filePath = "D:\\git\\niceloo-center\\communicationcenter\\xlsx\\" + fileName + ".xlsx";
        String sheetName = fileName;
        int voiceSourceUrlColumnIndex = 29;
        int field6ColumnIndex = 47;
        AtomicInteger accessibleCount = new AtomicInteger();
        AtomicInteger notAccessibleCount = new AtomicInteger();

        try {
            FileInputStream input = new FileInputStream(filePath);
            Workbook workbook = new XSSFWorkbook(input);
            Sheet sheet = workbook.getSheet(fileName);
            int rowCount = sheet.getLastRowNum() - sheet.getFirstRowNum();

            ExecutorService executor = Executors.newFixedThreadPool(NUM_THREADS); // 创建线程池

            for (int i = 1; i <= rowCount; i++) { // 从第二行开始循环，跳过表头
                final int rowIndex = i;
                executor.execute(() -> {
                    System.out.println("开始处理第" + rowIndex + "行， 共" + rowCount + "行");
                    Row row = sheet.getRow(rowIndex);
                    String voiceSourceUrl = row.getCell(voiceSourceUrlColumnIndex).getStringCellValue();
                    boolean isUrlAccessible;
                    try {
                        isUrlAccessible = isUrlAccessible(voiceSourceUrl);
                        Cell field6Cell = row.getCell(field6ColumnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        if (isUrlAccessible) {
                            accessibleCount.getAndIncrement();
                            field6Cell.setCellValue("Url Accessible");
                        } else {
                            notAccessibleCount.getAndIncrement();
                            field6Cell.setCellValue("Url Not Accessible");
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            }

            executor.shutdown();
            executor.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS);

            FileOutputStream output = new FileOutputStream(filePath);
            workbook.write(output);
            output.close();
            workbook.close();
            System.out.printf("%s, Field6 updated successfully. accessibleCount:%s, notAccessibleCount:%s",fileName, accessibleCount, notAccessibleCount);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    private static boolean isUrlAccessible(String urlString) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String response = reader.readLine();
            reader.close();

            // 根据返回的内容判断路径是否有效
            return response == null || !response.contains("[Warn] The record file does not exist");
        }

        return false;
    }
}