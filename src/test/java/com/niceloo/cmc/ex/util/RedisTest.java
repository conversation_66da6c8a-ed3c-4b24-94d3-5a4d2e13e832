package com.niceloo.cmc.ex.util;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/3/2
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisTest {

//    @Resource
//    private RedisUtil redisUtil;

    @Test
    public void test(){

        String key = "NICELOO_CMC:" + "DUPLICATE_RECORD_CHECK:" + "AAAAAA";

//        RedisUtil.delKey(key);



//        boolean flag = RedisUtil.setNx(key, "redis锁值", 10, TimeUnit.HOURS);
//        System.out.println(flag);
//        RedisUtil.setEx(key, "redis锁值的替换");


//        RedisUtil.set(key, "字符串测试值");
//        long incr = RedisUtil.incr(key);
//        System.out.println("============================" + incr);
//        long incr2 = RedisUtil.incr(key, 10);
//        System.out.println("============================" + incr2);
//        long decr = RedisUtil.decr(key);
//        System.out.println("============================" + decr);
//        long decre = RedisUtil.decr(key, 5);
//        System.out.println("============================" + decre);


        //key模糊查询
//        RedisUtil.setExpire(key, 10, TimeUnit.MINUTES);

//        long expire = RedisUtil.getExpire(key);
//        System.out.println("========================" + expire);
//        RedisUtil.persistKey(key);
//        long expire2 = RedisUtil.getExpire(key, TimeUnit.SECONDS);
//        System.out.println("========================" + expire2);
//        RedisUtil.delKey(key);

//        //移除KEY
//        boolean delKey = RedisUtil.delKey(key);
//        System.out.println("======================" + delKey);
//        //移除KEY
//        long delKeys = RedisUtil.delKey(Collections.singleton(key));
//        System.out.println("======================" + delKeys);


    }
//    {
//        String key = "NICELOO_CMC:" + "DUPLICATE_RECORD_CHECK:" + "AAAAAA";
//        //设置字符串KEY
//        RedisUtil.set(key, "AAAAABBBBBCCCCC");
//        //是否存在Key
//        boolean hasKey = RedisUtil.hasKey(key);
//        System.out.println("======================" + hasKey);
//        //获取存储类型
//        DataType dataType = RedisUtil.getKeyType(key);
//        System.out.println("======================" + dataType.name());
//        //设置过期时间
//        Set<String> keys = RedisUtil.getKeys(key);
//        System.out.println("======================" + JSONUtils.toJSONString(keys));
//    }

}
