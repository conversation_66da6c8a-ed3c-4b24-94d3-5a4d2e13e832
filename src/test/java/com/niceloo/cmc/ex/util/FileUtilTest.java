package com.niceloo.cmc.ex.util;

import com.niceloo.cmc.ex.utils.FileUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 文件服务测试类
 *
 * <AUTHOR>
 * @Date 2022-08-08 10:50
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class FileUtilTest {
    @Resource
    private FileUtil fileUtil;
    
    
    @Test
    public void yunkeUrlTest(){
        Map<String, String> stringStringMap = fileUtil.ossCopy("http://hqyl-yunke.oss-cn-hangzhou.aliyuncs.com/cellPhone-record-13269493488_20220805161610_16000.mp3", "20220808.mp3", "hqyl-yunke.oss-cn-hangzhou.aliyuncs.com");
        System.out.println(stringStringMap.toString());
    }
    
}
