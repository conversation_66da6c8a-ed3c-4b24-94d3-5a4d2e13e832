package com.niceloo.cmc.ex.jobs;

import com.niceloo.cmc.ex.service.BusinessMetricsService;
import com.niceloo.cmc.ex.service.DataCollectionService;
import com.niceloo.cmc.ex.service.impl.WechatGroupRobotServiceImpl;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BusinessAlertJobsTest {

    @Mock private WechatGroupRobotServiceImpl wechatGroupRobotService;

    @Mock private DataCollectionService dataCollectionService;

    @Mock private BusinessMetricsService businessMetricsService;

    @InjectMocks private BusinessAlertJobs businessAlertJobs;

    @Before
    public void setUp() {
        // 初始化Mockito
    }

    @Test
    public void testExecuteBusinessAlert() {
        // 调用被测方法
        businessAlertJobs.executeBusinessAlert();

        // 验证 businessMetricsService.monitorBusinessMetrics() 被调用了一次
        Mockito.verify(businessMetricsService, Mockito.times(1)).monitorBusinessMetrics();

        // 验证 dataCollectionService.collectAndAnalyzeData() 被调用了一次
        Mockito.verify(dataCollectionService, Mockito.times(1)).collectAndAnalyzeData();
    }
}
