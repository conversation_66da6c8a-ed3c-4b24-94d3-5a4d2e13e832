package com.niceloo.cmc.ex.jobs;

import com.niceloo.cmc.ex.service.CallRecordService;
import com.niceloo.cmc.ex.utils.RecordUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/3/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CallRecordsTest {

    @Autowired
    private CallRecordsJobs callRecordsJobs;

    @Autowired
    private CallRecordService callRecordService;


    @Test
    public void retryPullRecordRequestTest() {
        for (int i = 0; i < 100; i++) {
            callRecordsJobs.retryPullRecordRequest();
        }
    }


    @Test
    public void delRecordRequestTest() {
        LocalDate now = LocalDate.now();
        List<String> indexNames = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            LocalDate localDate = now.plusMonths(i);
            Instant instant = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
            Date from = Date.from(instant);
            String indexName = RecordUtil.getRecordIndexName(from);
            indexNames.add(indexName);
        }
        callRecordService.batchDel(indexNames);
    }
    
    @Test
    public void createRecordRequestTest() {
        LocalDate now = LocalDate.now();
        for (int i = 0; i < 12; i++) {
            LocalDate localDate = now.plusMonths(i);
            Instant instant = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
            Date from = Date.from(instant);
            String indexName = RecordUtil.getRecordIndexName(from);
            callRecordService.createIndex(indexName);
        }
    }
}
