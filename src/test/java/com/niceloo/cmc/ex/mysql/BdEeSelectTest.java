package com.niceloo.cmc.ex.mysql;

import com.niceloo.cmc.ex.mapper.BdEeMapper;
import com.niceloo.cmc.ex.pojo.dto.BdEeDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @description: 员工表查询测试
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-03-02 15:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BdEeSelectTest {

    @Autowired
    private BdEeMapper bdEeMapper;


    @Test
    public void sqlSelectTest(){
        String sql = "SELECT * FROM `BdEe` where eeId = 'EE20180101010000000001';";
        List<BdEeDTO> bdEeDTOList = bdEeMapper.selectBdEeInfoUseSQLOrder(sql);
        System.out.println(bdEeDTOList.toString());
    }
}
