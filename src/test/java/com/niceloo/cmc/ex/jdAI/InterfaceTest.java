package com.niceloo.cmc.ex.jdAI;

import com.niceloo.cmc.ex.pojo.dto.jdyx.JDContextListDTO;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDJobInfoDTO;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDJobReportDTO;
import com.niceloo.cmc.ex.pojo.dto.jdyx.JDLineListDTO;
import com.niceloo.cmc.ex.pojo.param.JDCallCustomerParam;
import com.niceloo.cmc.ex.pojo.param.JDCallInterceptsParam;
import com.niceloo.cmc.ex.pojo.param.JDCallPeriodParam;
import com.niceloo.cmc.ex.pojo.param.JDCreateJobParam;
import com.niceloo.cmc.ex.service.ai.JDYXService;
import com.niceloo.cmc.ex.utils.JDHmacMD5Util;
import com.niceloo.cmc.ex.utils.OkHttpUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.utils.RandomUtils;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.*;

/**
 * 言犀接口测试类
 *
 * <AUTHOR>
 * @Date 2022-08-02 09:10
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class InterfaceTest {

    @Autowired
    private OkHttpUtil okHttpUtil;


    @Autowired
    private JDYXService jdyxService;

    private final static String URL_PRE = "https://extpre-yanxi-api.jd.com";
    private final static String TOKEN = "6a48076a44a07755";
    private final static String CALLER = "youlujiaoyu";
    private final static String TENANT_ID = "10695";
    private final static String USER_PIN = "<EMAIL>";
    private final static String BOT_ID = "107857";
    String url = null;
    private static final Map<String, Object> param = new HashMap<>();
    static final Map<String, Object> businessParam = new ConcurrentHashMap<>();




    static {
        param.put("caller", CALLER);
        param.put("tenantId", TENANT_ID);
        param.put("params", businessParam);
    }

    /**
     * 1. 基础信息查询
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void queryBotInfoTest() {
        url = URL_PRE + "/api/common_setting/queryBotInfo";
        businessParam.put("tenantId", TENANT_ID);
        param.put("sign", JDHmacMD5Util.genSign(businessParam, TOKEN));
        okHttpUtil.post(url, JSONUtils.toJSONString(param));
    }

    /**
     * 2.查询话术模板列表
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void context_getContextListTest() {
        url = URL_PRE + "/api/voice_call/context_getContextList";
        businessParam.put("userPin", USER_PIN);
        businessParam.put("botId", BOT_ID);
        this.sendRequest();
    }

    /**
     * 3.查询电话线路列表
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void tel_getLineListTest() {
        url = URL_PRE + "/api/voice_call/tel_getLineList";
        businessParam.put("userPin", USER_PIN);
        businessParam.put("botId", BOT_ID);
        this.sendRequest();
    }

    /**
     * 4.查询指定机器人任务列表
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void job_getListTest() {
        url = URL_PRE + "/api/voice_call/job_getList";
        businessParam.put("pageSize", "20");
        businessParam.put("currentPage", "1");
        businessParam.put("botId", BOT_ID);
        this.sendRequest();
    }

    /**
     * 5.查询指定任务ID执行状态
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void job_getJobInfoTest() {
        url = URL_PRE + "/api/voice_call/job_getJobInfo";
        businessParam.put("userPin", "youlujiaoyu");
        businessParam.put("jobId", "1525638");
        businessParam.put("botId", BOT_ID);
        this.sendRequest();
    }

    /**
     * 6.批量任务结果查询接口
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void job_getReportByIdsTest() {
        url = URL_PRE + "/api/voice_call/job_getReportByIds";
        businessParam.put("userPin", "youlujiaoyu");
        businessParam.put("botId", BOT_ID);
        businessParam.put("jobIds", List.of("1525266"));
        this.sendRequest();
    }


    /**
     * 7.创建外呼任务
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void job_createJobTest() {
        url = URL_PRE + "/api/voice_call/job_createJob";
        /*拨打时间段*/
        JDCallPeriodParam JDCallPeriodParam = new JDCallPeriodParam("TUE", false, "10:35:00", "16:21:01");
        JDCallPeriodParam JDCallPeriodParam1 = new JDCallPeriodParam("FRI", false, "09:30:00", "16:30:00");
        /*外显手机号*/
        Map<String, Object> jobPhoneCallMap1 = new HashMap<>();
        jobPhoneCallMap1.put("innerPhoneNumber", "***********");
        Map<String, Object> map = new LinkedHashMap();
        map.put("custId", "USERID999");

        JDCallCustomerParam JDCallCustomerParam = new JDCallCustomerParam("王一一", "***********", map);
        JDCallCustomerParam JDCallCustomerParam1 = new JDCallCustomerParam("余一一", "***********", map);
        JDCallCustomerParam JDCallCustomerParam2 = new JDCallCustomerParam("刘一一", "***********", map);

        JDCallInterceptsParam JDCallInterceptsParam = new JDCallInterceptsParam("call", true, 2, 3);

        businessParam.put("userPin", "wangzhen");
        businessParam.put("botId", 107857);
        businessParam.put("name", "接口_09_不同账号测试");
        businessParam.put("description", "又双叒叕次使用接口创建外呼任务");
        businessParam.put("startupMode", "Timing");
        businessParam.put("startTime", "2022-08-09 10:35:00");
        businessParam.put("dialTimeStyle", "Workday");
        businessParam.put("jobOfflineTimes", List.of(JDCallPeriodParam, JDCallPeriodParam1));
        businessParam.put("jobPhoneCalls", List.of(jobPhoneCallMap1));
        businessParam.put("lineId", 89);
        businessParam.put("concurrentNumber", 3);
        businessParam.put("contextId", 281423);
        businessParam.put("customers", List.of(JDCallCustomerParam, JDCallCustomerParam1, JDCallCustomerParam2));
        businessParam.put("isRedial", true);
        businessParam.put("redialTimes", 3);
        businessParam.put("redialInterval", 5);
        businessParam.put("redialReason", "Closed|Busy|OutboundFail");
        businessParam.put("isCallIntercept", true);
        businessParam.put("jobCallIntercepts", List.of(JDCallInterceptsParam));
        businessParam.put("hasBlacklist", false);
        this.sendRequest();
    }

    /**
     * 8.任务追加客户名单（名单列表形式）
     *
     * <AUTHOR>
     * @Date 17:57 2022/8/2
     **/
    @Test
    public void customer_appendCustomerTest() {
        url = URL_PRE + "/api/voice_call/customer_appendCustomer";
        businessParam.put("userPin", "youlujiaoyu");
        businessParam.put("jobId", "1524223");
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 10, 30, TimeUnit.MINUTES, new LinkedBlockingQueue<>());
        for (int k = 0; k < 10; k++) {
            int finalK = k;
            threadPoolExecutor.execute(() -> {
                final Map<String, Object> param = new HashMap<>();
                final Map<String, Object> businessParam = new ConcurrentHashMap<>();
                param.put("caller", CALLER);
                param.put("tenantId", TENANT_ID);
                param.put("params", businessParam);
                List<JDCallCustomerParam> JDCallCustomerParams = new ArrayList<>();
                for (int i = 0; i < 100; i++) {
                    JDCallCustomerParams.clear();
                    for (int j = 0; j < 200; j++) {
                        String phone = "17632" + finalK + (i < 10 ? "0" + i : i + "");
                        String suffix = j < 10 ? "00" + j : j < 100 ? "0" + j : String.valueOf(j);

                        Map<String, Object> map = new LinkedHashMap();
                        map.put("custId", "USERID999");

                        JDCallCustomerParam JDCallCustomerParam = new JDCallCustomerParam("接口批量追加客户", phone + suffix, map);
                        JDCallCustomerParams.add(JDCallCustomerParam);
                    }
                    businessParam.put("userPin", "youlujiaoyu");
                    businessParam.put("jobId", "1524221");
                    businessParam.put("customers", JDCallCustomerParams);
                    param.put("sign", JDHmacMD5Util.genSign(businessParam, TOKEN));
                    okHttpUtil.post(url, JSONUtils.toJSONString(param));
                    System.out.println("正在执行线程:" + Thread.currentThread().getName() + ",k=" + finalK + ",i=" + i);
                }
            });
        }
        while (true) ;
    }


    /**
     * 8.任务追加客户名单（名单列表形式）
     *
     * <AUTHOR>
     * @Date 17:57 2022/8/2
     **/
    @Test
    public void customer_appendCustomer2Test() {
        url = URL_PRE + "/api/voice_call/customer_appendCustomer";
        Map map1 = new LinkedHashMap<String,Object>();
        map1.put("custId", "USERID666999");
        Map map2 = new LinkedHashMap<String,Object>();
        map2.put("custId", "USERID666888");
        JDCallCustomerParam JDCallCustomerParam = new JDCallCustomerParam("接口不加密追加客户3", "***********", map1);
        JDCallCustomerParam JDCallCustomerParam1 = new JDCallCustomerParam("接口不加密追加客户2", "***********", map2);
        businessParam.put("userPin", "youlujiaoyu");
        businessParam.put("jobId", "1524836");
        businessParam.put("customers", List.of(JDCallCustomerParam, JDCallCustomerParam1));
        this.sendRequest();
    }


    /**
     * 9.继续外呼任务
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void job_continueJobTest() {
        url = URL_PRE + "/api/voice_call/job_continueJob";
        businessParam.put("userPin", CALLER);
        businessParam.put("jobId", "1524836");
        this.sendRequest();
    }


    /**
     * 10.启动外呼任务
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void job_startJobTest() {
        url = URL_PRE + "/api/voice_call/job_startJob";
        businessParam.put("userPin", CALLER);
        businessParam.put("jobId", "1524836");
        this.sendRequest();
    }


    /**
     * 11.暂停外呼任务
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void job_pauseJobTest() {
        url = URL_PRE + "/api/voice_call/job_pauseJob";
        businessParam.put("userPin", CALLER);
        businessParam.put("jobId", "1524836");
        this.sendRequest();
    }

    /**
     * 12.删除外呼任务
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    @Test
    public void job_deleteJobTest() {
        url = URL_PRE + "/api/voice_call/job_deleteJob";
        businessParam.put("userPin", CALLER);
        businessParam.put("jobId", "1524836");
        this.sendRequest();
    }

    /**
     * 调用接口发送请求
     *
     * <AUTHOR>
     * @Date 9:26 2022/8/2
     **/
    private void sendRequest() {
        param.put("sign", JDHmacMD5Util.genSign(businessParam, TOKEN));
        okHttpUtil.post(url, JSONUtils.toJSONString(param));
    }

    @Test
    public void getContextListTest() {
        NgExpand ngExpand = new NgExpand();
        String userId = "USER20210524240000000128";
        UcUser ucUser = new UcUser();
        ucUser.setUserId(userId);
        ngExpand.setUcUser(ucUser);
        List<JDContextListDTO> contextList = jdyxService.getContextList("youlujiaoyu", ngExpand);
        System.out.println(contextList);
    }


    @Test
    public void getLineListTest() {
        NgExpand ngExpand = new NgExpand();
        String userId = "USER20210524240000000128";
        UcUser ucUser = new UcUser();
        ucUser.setUserId(userId);
        ngExpand.setUcUser(ucUser);
        List<JDLineListDTO> lineList = jdyxService.getLineList(ngExpand);
        System.out.println(lineList);
    }

    @Test
    public void getJobInfoTest() {
        NgExpand ngExpand = new NgExpand();
        String userId = "USER20210524240000000128";
        UcUser ucUser = new UcUser();
        ucUser.setUserId(userId);
        ngExpand.setUcUser(ucUser);
        JDJobInfoDTO jobInfo = jdyxService.getJobInfo(1524831, "youlujiaoyu", ngExpand);
        System.out.println(JSONUtils.toJSONString(jobInfo));
    }

    @Test
    public void getJobReportListTest() {
        NgExpand ngExpand = new NgExpand();
        String userId = "USER20210524240000000128";
        UcUser ucUser = new UcUser();
        ucUser.setUserId(userId);
        ngExpand.setUcUser(ucUser);
        List<JDJobReportDTO> jobReportList = jdyxService.getJobReportList(List.of(1525425), ngExpand);
        System.out.println(JSONUtils.toJSONString(jobReportList));
    }


    @Test
    public void createJobTest() {
        NgExpand ngExpand = new NgExpand();
        String userId = "USER20210524240000000128";
        UcUser ucUser = new UcUser();
        ucUser.setUserId(userId);
        ngExpand.setUcUser(ucUser);

        JDCreateJobParam jdCreateJobParam = JDCreateJobParam.defaultValue();
        jdCreateJobParam.setUserPin("youlujiaoyu");
        jdCreateJobParam.setName("第一次创建哦");
        jdCreateJobParam.setDialTimeStyle("Workday");
        JDCallPeriodParam JDCallPeriodParam = new JDCallPeriodParam("THU", false, "10:35:00", "16:21:01");
        JDCallPeriodParam JDCallPeriodParam1 = new JDCallPeriodParam("FRI", false, "09:30:00", "16:30:00");
        jdCreateJobParam.setJobOfflineTimes(List.of(JDCallPeriodParam, JDCallPeriodParam1));
        Map<String, String> jobPhoneCallMap = new HashMap<>();
        jobPhoneCallMap.put("innerPhoneNumber", "***********");
        jdCreateJobParam.setJobPhoneCalls(List.of(jobPhoneCallMap));
        jdCreateJobParam.setLineId(89L);
        jdCreateJobParam.setConcurrentNumber(3);
        jdCreateJobParam.setContextId(281423);
        Integer job = jdyxService.createJob(jdCreateJobParam, ngExpand);
        System.out.println(job);
    }

    @Test
    public void operatingJobTest() {
        NgExpand ngExpand = new NgExpand();
        String userId = "USER20210524240000000128";
        UcUser ucUser = new UcUser();
        ucUser.setUserId(userId);
        ngExpand.setUcUser(ucUser);
        jdyxService.operatingJob(1524831, 4, "youlujiaoyu", ngExpand);
    }

}
