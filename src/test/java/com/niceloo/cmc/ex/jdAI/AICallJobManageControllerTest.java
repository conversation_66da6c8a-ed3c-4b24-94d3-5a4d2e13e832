package com.niceloo.cmc.ex.jdAI;

import com.niceloo.cmc.ex.controller.web.AICallJobManageController;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerAddRequest;
import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.pojo.request.JDCreateJobRequest;
import com.niceloo.framework.utils.RandomUtils;
import com.niceloo.framework.web.NgExpand;
import com.niceloo.framework.web.model.UcUser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.niceloo.cmc.ex.common.BizConst.AI_ALL_TASK;
import static com.niceloo.cmc.ex.common.BizConst.AI_SCHOOL_TASK;

/**
 * <AUTHOR>
 * @Date 2022-09-30 09:11
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AICallJobManageControllerTest {

    @Resource
    private AICallJobManageController aiCallJobManageController;

    @Test
    public void createJobTest() {
        JDCreateJobRequest jdCreateJobRequest = new JDCreateJobRequest();
        jdCreateJobRequest.setName("测试-2024-07-02-002");
        jdCreateJobRequest.setDescription("描述描述描述描述描述");
        jdCreateJobRequest.setJobOfflineWeek("1,2,3,4,5,6,7");
        jdCreateJobRequest.setTimeBegin("09:30:00");
        jdCreateJobRequest.setTimeEnd("19:00:00");
        jdCreateJobRequest.setLineId(537L);
        jdCreateJobRequest.setConcurrentNumber(2);
        jdCreateJobRequest.setContextId(730917);
        jdCreateJobRequest.setContextName("测试1");
        jdCreateJobRequest.setIsRedial(1);
        jdCreateJobRequest.setRedialTimes(1);
        jdCreateJobRequest.setRedialInterval(5);
        jdCreateJobRequest.setRedialReason("NeedRedial|Busy");
        jdCreateJobRequest.setCallProject("一级建造师");
        jdCreateJobRequest.setExploitProject("一级建造师");
        jdCreateJobRequest.setCustomerNum(500);
        jdCreateJobRequest.setCallTaskType(AI_ALL_TASK);

        NgExpand ngExpand = new NgExpand();
        UcUser ucUser = new UcUser();
        ucUser.setUserId("USER20210830240000000047");
        ucUser.setUserName("王晨雨");
        ucUser.setDptId("DPT20211106770000001761");
        ucUser.setSchoolId("SCHOOL20190411010000000019");
        ucUser.setSchoolName("环球优路");
        ngExpand.setUcUser(ucUser);
        String job = aiCallJobManageController.createJob(jdCreateJobRequest, ngExpand);
        System.out.println(job);
    }

    /**
     * 添加客户任务的测试方法_分校任务
     *
     * @since 版本号（根据需要填写）
     */
    @Test
    public void addCustomerSchoolTaskTest() {
        int total = 50000;
        int pageSize = 500;
        int page = (total + pageSize - 1) / pageSize;
        long totalTime = 0;
        for (int i = 0; i < page; i++) {
            List<JDCallCustomerRequest> jdCallCustomerRequestList = new ArrayList<>();
            JDCallCustomerAddRequest jdCreateJobRequest = new JDCallCustomerAddRequest();
            jdCreateJobRequest.setJobId("JOBID20240702000000012501");
            jdCreateJobRequest.setFinishFlag("N");
            if (i == page - 1) {
                jdCreateJobRequest.setFinishFlag("Y");
            }
            for (int j = 0; j < pageSize; j++) {
                JDCallCustomerRequest customerRequest = new JDCallCustomerRequest();
                String k = (i < 10 ? "000" + i : i < 100 ? "00" + i : i < 1000 ? "0" + i : String.valueOf(i));
                String q = k + (j < 10 ? "000" + j : j < 100 ? "00" + j : "0" + j);
                customerRequest.setName("王晨雨" + q);
                customerRequest.setPhone("176339" + q);
                int i1 = RandomUtils.randomInt(8);
                customerRequest.setSchoolId("SCHOOL20190411010000000019" + i1);
                customerRequest.setSchoolName("环球优路" + i1);
                customerRequest.setCustId("CUST7209754010184" + q);
                jdCallCustomerRequestList.add(customerRequest);
            }
            jdCreateJobRequest.setJdCallCustomerRequestList(jdCallCustomerRequestList);
            long startTime = System.currentTimeMillis();
            UcUser ucUser = new UcUser();
            ucUser.setUserId("USER20210830240000000047");
            ucUser.setUserName("王晨雨");
            NgExpand ngExpand = new NgExpand();
            ngExpand.setUcUser(ucUser);
            aiCallJobManageController.createSubJobAndAddCustomer(jdCreateJobRequest, ngExpand);
            long endTime = System.currentTimeMillis();
            totalTime += (endTime - startTime);
            System.out.println("子接口耗时:->" + (endTime - startTime) + "毫秒");
        }
        System.out.println("接口总耗时:->" + (totalTime / 1000) + "秒");
    }

    /**
     * 添加客户任务的测试方法_全国任务
     *
     * @since 版本号（根据需要填写）
     */
    @Test
    public void addCustomerAllTaskTest() {
        int total = 50000;
        int pageSize = 500;
        int page = (total + pageSize - 1) / pageSize;
        long totalTime = 0;
        for (int i = 0; i < page; i++) {
            List<JDCallCustomerRequest> jdCallCustomerRequestList = new ArrayList<>();
            JDCallCustomerAddRequest jdCreateJobRequest = new JDCallCustomerAddRequest();
            jdCreateJobRequest.setJobId("JOBID20240702000000012501");
            jdCreateJobRequest.setFinishFlag("N");
            if (i == page - 1) {
                jdCreateJobRequest.setFinishFlag("Y");
            }
            for (int j = 0; j < pageSize; j++) {
                JDCallCustomerRequest customerRequest = new JDCallCustomerRequest();
                String k = (i < 10 ? "000" + i : i < 100 ? "00" + i : i < 1000 ? "0" + i : String.valueOf(i));
                String q = k + (j < 10 ? "000" + j : j < 100 ? "00" + j : "0" + j);
                customerRequest.setName("王晨雨" + q);
                customerRequest.setPhone("176339" + q);
                int i1 = RandomUtils.randomInt(8);
                customerRequest.setSchoolId("SCHOOL20190411010000000019");
                customerRequest.setSchoolName("环球优路");
                customerRequest.setCustId("CUST7209754010184" + q);
                jdCallCustomerRequestList.add(customerRequest);
            }
            jdCreateJobRequest.setJdCallCustomerRequestList(jdCallCustomerRequestList);
            long startTime = System.currentTimeMillis();
            UcUser ucUser = new UcUser();
            ucUser.setUserId("USER20210830240000000047");
            ucUser.setUserName("王晨雨");
            NgExpand ngExpand = new NgExpand();
            ngExpand.setUcUser(ucUser);
            aiCallJobManageController.createSubJobAndAddCustomer(jdCreateJobRequest, ngExpand);
            long endTime = System.currentTimeMillis();
            totalTime += (endTime - startTime);
            System.out.println("子接口耗时:->" + (endTime - startTime) + "毫秒");
        }
        System.out.println("接口总耗时:->" + (totalTime / 1000) + "秒");
    }
}
