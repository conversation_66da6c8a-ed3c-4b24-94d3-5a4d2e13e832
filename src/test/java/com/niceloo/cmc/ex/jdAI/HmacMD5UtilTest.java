package com.niceloo.cmc.ex.jdAI;

import com.niceloo.cmc.ex.pojo.param.JDCallCustomerParam;
import com.niceloo.cmc.ex.utils.JDHmacMD5Util;
import com.niceloo.framework.json.JSONUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * HMAC加密工具类(京东言犀提供);
 *
 * @date 2022-04-16;
 */
public class HmacMD5UtilTest {

    /**
     * 测试函数;
     *
     * @param args args
     */
    public static void main(String[] args) {
        String key = "6a48076a44a07755";
        Map map1 = new LinkedHashMap();
        map1.put("custId", "USERID987456");
        Map map2 = new LinkedHashMap();
        map2.put("custId", "USERID986666");
        JDCallCustomerParam JDCallCustomerParam = new JDCallCustomerParam("张翼飞", "17588965841", map1);
        JDCallCustomerParam JDCallCustomerParam1 = new JDCallCustomerParam("嘛赤兔", "17588965891", map2);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userPin", "youlujiaoyu");
        paramMap.put("jobId", 1524271);
        paramMap.put("customers", List.of(JDCallCustomerParam, JDCallCustomerParam1));
        System.out.println(JDHmacMD5Util.genSign(paramMap, key));
        System.out.println(JSONUtils.toJSONString(paramMap));
    }
}


 


    