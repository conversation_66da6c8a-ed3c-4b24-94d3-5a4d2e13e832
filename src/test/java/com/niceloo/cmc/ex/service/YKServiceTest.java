package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.pojo.request.PullYKRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.YKCallRecordsPageVO;
import com.niceloo.cmc.ex.service.call.YKService;
import com.niceloo.framework.json.JSONUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * @description:
 * @author: Wang<PERSON>henyu
 * @create: 2022-03-18 15:06
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class YKServiceTest {


    @Test
    public void getHeader(){
        YKService ykService = new YKService(BizConst.CREATE);
        Map<String, String> header = ykService.getHeader();
        System.out.println(JSONUtils.toJSONString(header));
    }

    /**
     * 厂商拉取通话记录
     * @paramter
     * <AUTHOR>
     * @Date 11:51 2022/3/21
     **/
    @Test
    public void selectRecordsFromVendor(){
        YKService ykService = new YKService(BizConst.CREATE);
        PullYKRecordRequest pullYKRecordRequest = new PullYKRecordRequest(1,5,"2022-03-21 11:00:00", "2022-03-21 11:10:00");
        String response = ykService.selectRecordsFromVendor(pullYKRecordRequest);
        YKCallRecordsPageVO ykCallRecordsPageVO = ykService.recordsResultConverter(response);
        System.out.println(ykCallRecordsPageVO.getYkCallRecordsDTOS());
    }

}
