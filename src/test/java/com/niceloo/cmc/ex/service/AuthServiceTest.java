package com.niceloo.cmc.ex.service;

import com.niceloo.auth.service.AuthService;
import com.niceloo.cmc.ex.feign.AuthFeignClient;
import com.niceloo.framework.json.JSONUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: Wang<PERSON><PERSON>yu
 * @create: 2022-05-21 09:11
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AuthServiceTest {

    @Autowired
    private AuthService authService;

    @Autowired
    private AuthFeignClient authFeignClient;


    @Test
    public void test(){
        List<String> datapolicy = authService.getDatapolicy("ic/JLFY/config-add", "USER20210830240000000047");
        System.out.println(datapolicy);
        Map<String,String> param = new HashMap<>(2);
        param.put("code", "ic/JLFY/config-add");
        param.put("userId", "USER20210830240000000047");
        authFeignClient.checkAuth(JSONUtils.toJSONString(param));
    }
}
