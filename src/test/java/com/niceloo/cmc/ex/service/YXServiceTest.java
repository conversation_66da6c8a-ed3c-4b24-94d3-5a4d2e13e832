package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.config.CallProperties;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.AccountDTO;
import com.niceloo.cmc.ex.service.call.YXService;
import com.niceloo.cmc.ex.utils.OkHttpUtil;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.spring.SpringUtils;
import com.niceloo.framework.utils.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 亿迅服务测试类
 * @author: Wang<PERSON>henyu
 * @create: 2022-03-21 14:08
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class YXServiceTest {
    
    @Resource
    private OkHttpUtil okHttpUtil;
     @Resource
    private BdCallaccountinfoService bdCallaccountinfoService ;

    @Test
    public void getRequestBodyTest(){
        YXService yxService = new YXService();
        String requestBody = yxService.getRequestBody("2022-03-21 14:00:00", "2022-03-21 14:10:00", null, "C144", "43755B5C36164676444DD5D518833D5E91B5D8F8");
        System.out.println(requestBody);
    }

    @Test
    public void getRecordsFromVendor(){
        CallRecord oneCdrFromVendorByUserData = getOneCdrFromVendorByUserData("C146", "EEFCD6BB89B2464FAE1A69BB3FF6DA2E");
        /*YXService yxService = new YXService();
        List<Map<String, Object>> c144 = yxService.getRecordsFromVendor(
                "2022-03-21 14:00:00",
                "2022-03-21 14:10:00",
                null,
                "C144",
                "43755B5C36164676444DD5D518833D5E91B5D8F8");
        System.out.println(c144);*/
    }
    
        private CallRecord getOneCdrFromVendorByUserData(String accountId, String userData) {
        // 查询亿迅账号秘钥
        AccountDTO account = bdCallaccountinfoService.selectAccount(accountId, CallChannelEnum.CALL_TYPE_ZK.getType());
        if (null == account || StringUtils.isEmpty(account.getApiSecret())) {
            return null;
        }
        Map<String, String> request = new HashMap<>(2);
        request.put("userData", userData);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("authentication", YXService.getAuthentication(accountId, account.getApiSecret()));
        requestBody.put("request", request);
        String url = CallProperties.YXProperty.ZK_HOST + CallProperties.YXProperty.ONE_RECORDS;
        String response = okHttpUtil.post(url, JSONUtils.toJSONString(requestBody));
        if (StringUtils.isEmpty(response)) {
            return null;
        }
        List<Map<String, String>> cdr = (List<Map<String, String>>) ((Map<String, Object>) ((Map<String, Object>)
                JSONUtils.toMap(response).get("data")).get("response")).get("cdr");
        for (Map<String, String> map : cdr) {
            String voiceSign = map.get("voiceSign");
            if (voiceSign.contains("other")) {
                CallRecord callRecord = new CallRecord();
                callRecord.setCallPhone(map.get("caller"));
                callRecord.setField2(userData);
                callRecord.setDuration(Integer.parseInt(map.get("timeLen")));
                
            }
        }
        return null;
    }
}
