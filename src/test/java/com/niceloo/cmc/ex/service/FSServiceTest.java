package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.pojo.request.PullFYRecordRequest;
import com.niceloo.cmc.ex.pojo.vo.FYCallRecordsVO;
import com.niceloo.cmc.ex.service.call.FYService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * @description: 风云服务测试类
 * @author: <PERSON><PERSON><PERSON>yu
 * @create: 2022-03-21 14:08
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class FSServiceTest {

    /**
     * 获取所有的风云账号
     * @paramter
     * <AUTHOR>
     * @Date 15:43 2022/3/21
     **/
    @Test
    public void selectFYAccountInfoListTest(){
        FYService fyService = new FYService(BizConst.CREATE);
        List<Map<String, String>> mapList = fyService.selectFYAccountInfoList();
        System.out.println(mapList);
    }

    @Test
    public void getRecordsFromVendor(){
        FYService fyService = new FYService(BizConst.CREATE);
        PullFYRecordRequest request = new PullFYRecordRequest(
                "N00000035875",
                "3ceabab0-bfdb-11e9-89f3-e12c88bc57b8",
                "2022-03-21 15:00:00",
                "2022-03-21 16:00:00");
        request.setPage(0);
        request.setCalledNo("***********");
        List<FYCallRecordsVO> fyCallRecordsVOList = fyService.selectRecordsFromVendor(request);
        System.out.println(fyCallRecordsVOList);
    }


    @Test
    public void getRecordsFromVendor1(){
        FYService fyService = new FYService(BizConst.CREATE);
        PullFYRecordRequest request = new PullFYRecordRequest(
                "N00000035875",
                "3ceabab0-bfdb-11e9-89f3-e12c88bc57b8",
                "2022-03-21 15:00:00",
                "2022-03-21 16:00:00");
        request.setPage(1);
        request.setPageSize(2);
        List<FYCallRecordsVO> fyCallRecordsVOList = fyService.selectRecordsFromVendor(request);
        System.out.println(fyCallRecordsVOList);
    }



    @Test
    public void getRecordsFromVendor2(){
        FYService fyService = new FYService(BizConst.CREATE);
        PullFYRecordRequest request = new PullFYRecordRequest(
                "N00000045814",
                "716123e0-bfdd-11e9-89b9-7d527464c48f",
                "2022-03-21 15:00:00",
                "2022-03-21 16:00:00");
        request.setPage(1);
        request.setPageSize(2);
        List<FYCallRecordsVO> fyCallRecordsVOList = fyService.selectRecordsFromVendor(request);
        System.out.println(fyCallRecordsVOList);
    }
}
