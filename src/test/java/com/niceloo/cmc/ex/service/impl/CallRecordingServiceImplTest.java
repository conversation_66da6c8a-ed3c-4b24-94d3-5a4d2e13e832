package com.niceloo.cmc.ex.service.impl;

import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.framework.utils.DateUtils;
import lombok.SneakyThrows;
import org.junit.Test;
import org.mockito.Mockito;
import static org.mockito.Mockito.*;
import org.junit.Before;
import org.junit.runner.RunWith;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Date;
import com.niceloo.cmc.ex.service.impl.CallRecordingServiceImpl;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

@RunWith(MockitoJUnitRunner.class)
public class CallRecordingServiceImplTest{

    @Mock
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @InjectMocks
    private CallRecordingServiceImpl callRecordingService;

    @SneakyThrows
    @Test
    public void testDeleteDataBeforeYesterday() {
        // Arrange
        Date today = new Date();
        Date yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        String yesterdayEndTime = DateUtils.toStr(yesterday, DateUtil.YMD) + " 23:59:59";
    
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("operationSign", "1"))
                .must(QueryBuilders.termQuery("channelType", CallChannelEnum.CALL_TYPE_YPHONE.getType()))
                .must(QueryBuilders.rangeQuery("createTime").lte(yesterdayEndTime));
    
        SearchRequest searchRequest = new SearchRequest("call_recording").source(new SearchSourceBuilder().query(queryBuilder).size(1000));
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHit[] hits = new SearchHit[]{mock(SearchHit.class), mock(SearchHit.class)};
    
        when(elasticsearchTemplate.getClient().search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        when(searchResponse.getHits().getHits()).thenReturn(hits);
    
        // Act
        callRecordingService.deleteDataBeforeYesterday();
    
        // Assert
        verify(elasticsearchTemplate.getClient(), times(1)).search(any(SearchRequest.class), any(RequestOptions.class));
        verify(searchResponse, times(1)).getHits().getHits();
        verify(elasticsearchTemplate.getClient(), times(1)).bulk(any(BulkRequest.class), any(RequestOptions.class));
    }

}