package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.pojo.request.PullTQRecordRequest;
import com.niceloo.cmc.ex.service.call.TQService;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.utils.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * @description: TQ服务测试类
 * @author: Wang<PERSON><PERSON>yu
 * @create: 2022-03-21 14:08
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class TQServiceTest {

    /**
     * 获取Token信息
     */
    @Test
    public void getTQTokenInfo() {
        TQService tqService = new TQService(BizConst.CREATE);
        String tqTokenInfo = tqService.getTQTokenInfo();
        System.out.println(tqTokenInfo);
    }

    /**
     * 查询通话记录
     * @paramter
     * <AUTHOR>
     * @Date 15:39 2022/3/21
     **/
    @Test
    public void getRecordsFromVendor() {
        TQService tqService = new TQService(BizConst.CREATE);
        String startTime = "2022-03-21 11:00:00";
        String endTime = "2022-03-21 12:00:00";
        PullTQRecordRequest request = new PullTQRecordRequest(0, DateUtils.toDate(startTime).getTime() / 1000 + "", DateUtils.toDate(endTime).getTime() / 1000 + "");
        String response = tqService.selectRecordsFromVendor(request);
        Map<String, Object> map = JSONUtils.toMap(response);
        System.out.println(map);
    }
}
