package com.niceloo.cmc.ex.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@RunWith(MockitoJUnitRunner.class)
public class WechatGroupRobotServiceImplTest {

    @InjectMocks private WechatGroupRobotServiceImpl wechatGroupRobotService;

    @Mock private RestTemplate restTemplate;

    @Test
    public void testSendMessageWechatDisabled() {
        wechatGroupRobotService.wechatEnabled = false;
        wechatGroupRobotService.sendMessage("Hello, <PERSON>!");

        Mockito.verify(restTemplate, Mockito.times(0))
                .postForEntity(any(String.class), any(HttpEntity.class), any(Class.class));
    }

    @Before
    public void setUp() {
        wechatGroupRobotService.mentionedMobileList = "18903831292,@all";
        wechatGroupRobotService.webHook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=262051f1-157e-43ca-9d92-7c4aa70cce55";
        wechatGroupRobotService.wechatEnabled = true;
    }

    @Test
    public void testSendMessageSuccess() {
        String message = "Hello, World!";
        String jsonResponse = "{\"errcode\":0,\"errmsg\":\"ok\"}";
        ResponseEntity<String> responseEntity = ResponseEntity.ok(jsonResponse);

        when(restTemplate.postForEntity(any(String.class), any(HttpEntity.class), any(Class.class)))
                .thenReturn(responseEntity);

        wechatGroupRobotService.sendMessage(message);

        Mockito.verify(restTemplate, Mockito.times(1))
                .postForEntity(
                        wechatGroupRobotService.webHook, any(HttpEntity.class), any(Class.class));
    }

    @Test
    public void testSendMessageFailure() {
        String message = "Hello, World!";
        String jsonResponse = "{\"errcode\":1,\"errmsg\":\"error\"}";
        ResponseEntity<String> responseEntity = ResponseEntity.ok(jsonResponse);

        when(restTemplate.postForEntity(any(String.class), any(HttpEntity.class), any(Class.class)))
                .thenReturn(responseEntity);

        wechatGroupRobotService.sendMessage(message);

        Mockito.verify(restTemplate, Mockito.times(1))
                .postForEntity(
                        wechatGroupRobotService.webHook, any(HttpEntity.class), any(Class.class));
    }

    @Test
    public void testSendMessageHttpFailure() {
        String message = "Hello, World!";
        ResponseEntity<String> responseEntity =
                ResponseEntity.status(500).body("Internal Server Error");

        when(restTemplate.postForEntity(any(String.class), any(HttpEntity.class), any(Class.class)))
                .thenReturn(responseEntity);

        wechatGroupRobotService.sendMessage(message);

        Mockito.verify(restTemplate, Mockito.times(1))
                .postForEntity(
                        wechatGroupRobotService.webHook, any(HttpEntity.class), any(Class.class));
    }
}
