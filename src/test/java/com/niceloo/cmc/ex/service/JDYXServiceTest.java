package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.pojo.request.JDCallCustomerRequest;
import com.niceloo.cmc.ex.pojo.request.JDCreateJobRequest;
import com.niceloo.cmc.ex.service.ai.JDYXService;
import com.niceloo.framework.utils.RandomUtils;
import com.niceloo.framework.web.model.UcUser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 京东言犀服务测试类
 *
 * <AUTHOR>
 * @Date 2022-09-03 10:18
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class JDYXServiceTest {

    @Resource
    private JDYXService jdyxService;


    @Test
    public void createJob() {
        long l = System.currentTimeMillis();
        UcUser ucUser = new UcUser();
        ucUser.setUserName("王晨雨");
        ucUser.setUserId("USER20210830240000000047");
        ucUser.setSchoolId("SCHOOL20190411010000000019");
        ucUser.setSchoolName("环球优路");
        ucUser.setDptId("DPT20201201220000000046");
        //jdyxService.(buildRequest(), ucUser);
        long l1 = System.currentTimeMillis();
        System.out.println("创建任务执行耗时->" + (l1 - l));
    }


    private JDCreateJobRequest buildRequest() {
        JDCreateJobRequest request = new JDCreateJobRequest();
        request.setName("9.03-9.9秒杀课-大量客户测试");
        request.setDescription("随便写点描述");
        request.setJobOfflineWeek("4,3,5,6,7,1,2");
        request.setTimeBegin("09:00:00");
        request.setTimeEnd("20:00:00");
        request.setLineId(89L);
        request.setConcurrentNumber(3);
        request.setContextId(281423);
        request.setContextName("测试_话术模板1");
        request.setIsRedial(1);
        request.setRedialTimes(1);
        request.setRedialInterval(5);
        request.setRedialReason("NeedRedial|Busy");
        request.setCallProject("水利水电造价师");
        request.setExploitProject("水利水电造价师");
        request.setTimestamp(1661247148222L);
     /*   List<JDCallCustomerRequest> customerList = new ArrayList<>();
        for (int i = 0; i < 200000; i++) {
            JDCallCustomerRequest customerRequest = new JDCallCustomerRequest();
            customerRequest.setName("李大强" + i);
            int i1 = RandomUtils.randomInt(5);
            customerRequest.setSchoolName("B85星云" + i1);
            customerRequest.setSchoolId("SCHOOL2019041101000000011" + i1);
            customerRequest.setPhone("174886" + i);
            customerRequest.setCustId("USER20200409010000" + i);
            customerList.add(customerRequest);
        }*/
        return request;
    }
}
