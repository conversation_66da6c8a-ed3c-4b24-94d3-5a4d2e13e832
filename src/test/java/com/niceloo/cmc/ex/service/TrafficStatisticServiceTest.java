package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-03-18 16:27
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class TrafficStatisticServiceTest {

    @Autowired
    private TrafficStatisticService trafficStatisticService;

    /**
     * 添加到日统计表
     * @paramter
     * <AUTHOR>
     * @Date 9:41 2022/3/21
     **/
    @Test
    public void addDayStatisticToDBTest1(){
        trafficStatisticService.addDayStatisticToDB("2022-03-16", CallChannelEnum.CALL_TYPE_YPHONE.getType());
    }

    @Test
    public void addDayStatisticToDBTest2(){
        trafficStatisticService.addDayStatisticToDB("2022-03-16", CallChannelEnum.CALL_TYPE_ZK.getType());
    }

    /**
     * 添加到月统计表
     * @paramter
     * <AUTHOR>
     * @Date 9:41 2022/3/21
     **/
    @Test
    public void addMonthStatisticToDBTest1(){
        trafficStatisticService.addMonthStatisticToDB("2022-03", CallChannelEnum.CALL_TYPE_ZK.getType());
    }

    @Test
    public void addMonthStatisticToDBTest2(){
        trafficStatisticService.addMonthStatisticToDB("2022-03", CallChannelEnum.CALL_TYPE_YPHONE.getType());
    }


    @Test
    public void getCountDataBySchoolTest(){
        Map<String, String> countDataBySchool = trafficStatisticService.getCountDataBySchool("2022-03-16 00:00:00", "2022-03-16 23:59:59");
        System.out.println(countDataBySchool.toString());
    }
}
