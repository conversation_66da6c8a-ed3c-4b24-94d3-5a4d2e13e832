package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.service.call.CallRecordsBaseService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-08 09:49
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class CallRecordBaseServiceTest {

    @Test
    public void syncTest() {
        CallRecordsBaseService callRecordsBaseService = new CallRecordsBaseService();
        callRecordsBaseService.sync("TQ", "2022-04-08 07:00:00", "2022-04-08 08:00:00");
    }

    @Test
    public void test2() {
        CallRecordsBaseService callRecordsBaseService = new CallRecordsBaseService();

        for (int i = 0; i < 20; i++) {
            int finalI = i;
            new Thread(()->{
                //callRecordsBaseService.checkRequestTime("ZK_ACCOUNT", 30000);
                System.out.println(finalI);
            }).start();
        }
        while (true);
    }
}
