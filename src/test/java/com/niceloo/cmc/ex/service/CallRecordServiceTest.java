package com.niceloo.cmc.ex.service;

import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.framework.utils.StringUtils;
import com.niceloo.plugin.sdk.lang.Pair;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilterBuilder;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: WangChenyu
 * @create: 2022-04-01 10:02
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class CallRecordServiceTest {

    @Autowired
    private CallRecordService callRecordService;


    @Test
    public void createIndex(){
        callRecordService.createIndex("call_record_202204");
    }



    @Test
    public void startScroll(){
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermQueryBuilder channelType = QueryBuilders.termQuery("channelType", "YP");
        TermQueryBuilder duration = QueryBuilders.termQuery("duration", "-1");
        String startTime = DateUtils.getNowDString(DateUtil.YMD) + " 00:00:00";
        RangeQueryBuilder callTime = QueryBuilders.rangeQuery("callTime").gte(startTime);
        boolQueryBuilder.filter(channelType).filter(duration).filter(callTime);
        Pageable pageable = PageRequest.of(0, 1500, Sort.by(Sort.Direction.DESC, "callTime"));
        FetchSourceFilterBuilder filterBuilder = new FetchSourceFilterBuilder().withIncludes("callId", "field2", "field4", "reciverPhone", "callPhone", "callAccount");
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(RecordUtil.getRecordIndexName(new Date()))
                .withTypes("_doc")
                .withFilter(boolQueryBuilder)
                .withPageable(pageable)
                .withSourceFilter(filterBuilder.build())
                .build();
        Pair<String, List<CallRecord>> stringListPair = callRecordService.startScroll(searchQuery);
        String key = stringListPair.getKey();
        while (StringUtils.isNotEmpty(key)){
            List<CallRecord> value = stringListPair.getValue();
            System.out.println(key  + "----------------->"+ value.size());
            stringListPair =  callRecordService.continueScroll(key);
            key =  stringListPair.getKey();
            if (StringUtils.isEmpty(key)){
                System.out.println("任务结束");
                return;
            }
        }
    }
}
