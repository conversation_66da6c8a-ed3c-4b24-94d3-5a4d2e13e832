package com.niceloo.cmc.ex.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description: 通话录音服务测试类
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-03-17 10:01
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class CallRecordingServiceTest {

    @Autowired
    private CallRecordingService callRecordingService;


    @Test
    public void createIndexTest(){
        callRecordingService.createIndex();
    }
}
