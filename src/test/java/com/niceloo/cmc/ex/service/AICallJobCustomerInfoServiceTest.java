package com.niceloo.cmc.ex.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * AI外呼任务下客户信息service测试类
 * <AUTHOR>
 * @Date 2022-08-23 09:23
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AICallJobCustomerInfoServiceTest {
    
    @Autowired
    private AICallJobCustomerInfoService aiCallJobCustomerInfoService;
    
    @Test
    public void createIndexTest(){
        boolean flag = aiCallJobCustomerInfoService.createIndex("ai_job_customer_202209");
        System.out.println(flag);
    }
}
