package com.niceloo.cmc.ex.controller;

import com.niceloo.cmc.ex.common.BizConst;
import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.common.MQConst;
import com.niceloo.cmc.ex.common.RedisConst;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.pojo.dto.RecordQueryDTO;
import com.niceloo.cmc.ex.pojo.param.AddRecordParam;
import com.niceloo.cmc.ex.pojo.param.ManualSyncParam;
import com.niceloo.cmc.ex.service.CtCustService;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.cmc.ex.utils.RedisUtil;
import com.niceloo.framework.exception.ApplicationException;
import com.niceloo.framework.json.JSONUtils;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import com.niceloo.mq.client.Client;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/3/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ManualSyncTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManualSyncTest.class);

    @Resource
    private Client defaultRabbitMqClient;
    @Resource
    @Qualifier("other")
    private Client syncRecordRabbitMqClient;
    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;
    @Resource
    private CtCustService ctCustService;

    @Test
    public void addEETest() {
        AddRecordParam param = new AddRecordParam();
        param.setIndexes("call_record_202202");
        param.setChannelType("OI");
        param.setCallTimeStart("2022-02-01 00:00:00");
        param.setCallTimeEnd("2022-02-12 00:00:00");


        List<String> indexNames;
        List<String> allIndexNames = RecordUtil.getAllIndexNames();
        if (BizConst.CALL_RECORD_ES_ALL_PREFIXS.equals(param.getIndexes())) {
            indexNames = allIndexNames;
        } else {
            indexNames = Arrays.asList(param.getIndexes().split(","));
            if (!allIndexNames.containsAll(indexNames)) {
                throw new ApplicationException("1003", param.getIndexes() + ": 全部或某个索引不存在");
            }
        }
        List<String> failedIndexNames = new ArrayList<>();
        RecordQueryDTO recordQueryDTO = new RecordQueryDTO();
        BeanCopier copier = BeanCopier.create(AddRecordParam.class, RecordQueryDTO.class, false);
        copier.copy(param, recordQueryDTO, null);
        for (String indexName : indexNames) {
            try {
                recordQueryDTO.setIndexes(indexName);
                defaultRabbitMqClient.publish(MQConst.ADD_EE_INFO_TOPIC, UUID.randomUUID().toString(), JSONUtils.toJSONString(recordQueryDTO), 1000);
            } catch (Exception e) {
                LOGGER.error(e, e.toString());
                failedIndexNames.add(indexName);
            }
            System.out.println("需要传递的参数=====================" + JSONUtils.toJSONString(recordQueryDTO));
        }
        if (failedIndexNames.size() == indexNames.size()) {
            System.out.println(String.format("补充员工信息(%s--%s--%s--%s):任务提交失败", param.getIndexes(), param.getCallTimeStart(), param.getCallTimeEnd(), param.getChannelType()));
        }
        if (failedIndexNames.size() > 0) {
            System.out.println(String.format("补充员工信息:(%s--%s--%s--%s)任务提交失败,其他任务提交成功", failedIndexNames, param.getCallTimeStart(), param.getCallTimeEnd(), param.getChannelType()));
        }
        System.out.println(String.format("补充员工信息(%s--%s--%s--%s):任务已提交", param.getIndexes(), param.getCallTimeStart(), param.getCallTimeEnd(), param.getChannelType()));;

    }

    @Test
    public void recordSync_Test() {
        ManualSyncParam param = new ManualSyncParam();
        param.setChannelType("OI");
        param.setReplenish("Y");
        param.setStartDate("2022-02-01 00:00:00");
        param.setEndDate("2022-02-23 00:00:00");

        String setData = param.getStartDate() + "_" + param.getEndDate();
        if("Y".equals(param.getReplenish())){
            // 存储同步周期，防止重复提交，过期时间为一天
            String key = RedisConst.RECORD_SYNC_KEY + "_" + param.getChannelType() + "_" + setData;
            Object value = RedisUtil.redisTemplate.opsForHash().get(key, setData);
            System.out.println("key: " + key);
            System.out.println("field: " + setData);
            if(!ObjectUtils.isEmpty(value)){
                throw new ApplicationException("1003", "此周期内的通话记录正在同步，时间段为 " + param.getStartDate() + " " + value);
            }
            RedisUtil.redisTemplate.opsForHash().put(key, setData, setData);
            RedisUtil.redisTemplate.expire(key, 1, TimeUnit.DAYS);

            // 如果是同步两个月之前的数据，则拒绝
            String[] nows = DateUtils.getNowDString().split("-");
            Date firstDayOfMonth = DateUtils.getFirstDayOfMonth(Integer.parseInt(nows[0]), Integer.parseInt(nows[1]));
            Date twoMonthAgo = DateUtils.addMonth(firstDayOfMonth, -2);
            Date start = DateUtils.toDate(param.getStartDate());
            if(DateUtils.compare(twoMonthAgo, start) >= 0){
                throw new ApplicationException("1003", "不可以补充两个月之前的数据");
            }
        }else {
            // 先检查 ES 是否已经同步过，是则不允许同步
            // bug: 以startDate确定索引, 当同步时间段跨月时, 此限制条件只在首月生效
            String[] splits = param.getStartDate().split("-");
            String indexName = BizConst.CALL_RECORD_ES_PREFIX + splits[0] + splits[1];
            NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
            builder.withIndices(indexName)
                    .withTypes("_doc")
                    .withQuery(QueryBuilders.boolQuery().must((QueryBuilders.rangeQuery("callTime").gte(param.getStartDate()).lte(param.getEndDate())))
                            .must(QueryBuilders.queryStringQuery("ERROR").field("message")));
            if (CallChannelEnum.CALL_TYPE_YH.getType().equals(param.getChannelType())) {
                builder.withQuery(QueryBuilders.matchQuery("channelType", param.getChannelType()));
                builder.withQuery(QueryBuilders.matchQuery("field3",  CallRecord.CALL_TYPE_YH_FIELD3));
            } else {
                builder.withQuery(QueryBuilders.matchQuery("channelType", param.getChannelType()));
            }
            builder.withPageable(PageRequest.of(0, 1));
            NativeSearchQuery searchQuery = builder.build();
            List<Map> callRecords = elasticsearchTemplate.queryForList(searchQuery, Map.class);

            // 存储同步周期，防止重复提交，过期时间为整体时间加一周
            String key = RedisConst.RECORD_SYNC_KEY + "_" + param.getChannelType() + "_" + setData;
            System.out.println("key: " + key);
            System.out.println("field: " + setData);
            Object value = RedisUtil.redisTemplate.opsForHash().get(key, setData);
            if(null != value){
                throw new ApplicationException("1003", "此周期内的通话记录正在同步", null, null);
            }
            RedisUtil.redisTemplate.opsForHash().put(key, setData, setData);
            RedisUtil.redisTemplate.expire(key, DateUtils.compare(param.getEndDate(), param.getStartDate()), TimeUnit.DAYS);
        }

//        syncRecordRabbitMqClient.publish(MQConst.CALL_RECORD_TOPIC, UUID.randomUUID().toString(), param.getChannelType() + BizConst.LINK_SYMBOL + param.getStartDate() + BizConst.LINK_SYMBOL + param.getEndDate() + BizConst.LINK_SYMBOL + BizConst.MANUAL);
        System.out.println("同步开始==========================");
    }

}
