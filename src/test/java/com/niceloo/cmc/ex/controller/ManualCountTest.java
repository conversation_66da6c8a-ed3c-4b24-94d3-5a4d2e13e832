package com.niceloo.cmc.ex.controller;

import com.niceloo.cmc.ex.common.CallChannelEnum;
import com.niceloo.cmc.ex.entity.es.CallRecord;
import com.niceloo.cmc.ex.service.TrafficStatisticService;
import com.niceloo.cmc.ex.utils.DateUtil;
import com.niceloo.cmc.ex.utils.RecordUtil;
import com.niceloo.framework.log.Logger;
import com.niceloo.framework.log.LoggerFactory;
import com.niceloo.framework.utils.DateUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SourceFilter;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/3/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ManualCountTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManualCountTest.class);

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Test
    public void saveAB_Test() {
        String timeStart = "2022-01-10 00:00:00";
        String timeEnd = "2022-02-10 00:00:00";

        long totalCount = 0;
        BigDecimal totalDura = new BigDecimal("0");

        String result;
        String indexs = RecordUtil.getIndexStr(timeStart, timeEnd);
        System.out.println("隔断============================" + indexs);

        //TODO: 方法调整，待验证
        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("_doc");
        SourceFilter sourceFilter = new FetchSourceFilter(new String[]{"channelType", "duration"}, null);
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder()
                .withTypes("_doc")
                .withQuery(QueryBuilders.rangeQuery("duration").gte(0))
                .withQuery(QueryBuilders.matchQuery("channelType", "TQ"))
                .withSourceFilter(sourceFilter)
                .withSort(sortBuilder)
                .withPageable(PageRequest.of(0, 1000));//从0页开始查，每页1000个结果

        ScrolledPage<CallRecord> scroll = null;
        for(String index : indexs.split(",")){
            builder.withIndices(index);
            NativeSearchQuery searchQuery = builder.build();
            scroll = elasticsearchRestTemplate.startScroll(60 * 1000,searchQuery, CallRecord.class);
            while (scroll.hasContent()) {
                List<CallRecord> content = scroll.getContent();
                BigDecimal currentDura = content.stream().map(item -> {return new BigDecimal(item.getDuration().toString());}).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalDura = totalDura.add(currentDura);
                totalCount = totalCount + content.size();

                scroll = elasticsearchRestTemplate.continueScroll(scroll.getScrollId(),60 * 1000, CallRecord.class);
            }
        }
        assert scroll != null;
        elasticsearchRestTemplate.clearScroll(scroll.getScrollId());
        System.out.println("总个数============================" + totalCount);
        System.out.println("总时长============================" + totalDura.toString());
    }

    @Autowired
    private TrafficStatisticService trafficStatisticService;

    @Test
    public void saveAllCountToDB_Test() {
        String timeStart = "2022-01-10 00:00:00";
        String timeEnd = "2022-02-10 00:00:00";

        Set<String> dayDateList = new HashSet<>();
        Set<String> monthDateList = new HashSet<>();
        //获取需要日统计数据和月统计数据的时间列表
        while (DateUtils.compare(DateUtils.toDate(timeStart), DateUtils.toDate(timeEnd)) <= 0){
            dayDateList.add(DateUtil.format(timeStart, DateUtil.YMD));
            timeStart =DateUtils.toStr(DateUtils.addDay(DateUtils.toDate(timeStart),1));
        }
        dayDateList.forEach(date -> monthDateList.add(date.substring(0,7)));
        //进行日统计
        LOGGER.info("手动统计:进行日话务统计,统计开始时间:" + DateUtils.getNowDString());
        long l = System.currentTimeMillis();
        for (String dayDate : dayDateList) {
            for (CallChannelEnum callChannelEnum : CallChannelEnum.values()) {
                try {
                    trafficStatisticService.addDayStatisticToDB(dayDate, callChannelEnum.getType());
                } catch (Exception e) {
                    LOGGER.error(e, "进行手动话务统计->日统计表出现异常,统计时间:" + dayDate + "外呼类型:" + callChannelEnum.getType());
                }
            }
        }
        long l2 = System.currentTimeMillis();
        LOGGER.info("手动统计:日话务统计完成,完成时间:" + DateUtils.getNowDString() + ",耗时:" + (l2-l)/1000 + "秒");
        //进行月统计
        LOGGER.info("手动统计:进行月话务统计,统计开始时间:" + DateUtils.getNowDString());
        for (String monthDate : monthDateList) {
            for (CallChannelEnum callChannelEnum : CallChannelEnum.values()) {
                try {
                    trafficStatisticService.addMonthStatisticToDB(monthDate, callChannelEnum.getType());
                } catch (Exception e) {
                    LOGGER.error(e, "进行手动话务统计->月统计表出现异常,统计时间:" + monthDate + "外呼类型:" + callChannelEnum.getType());
                }
            }
        }
        long l3 = System.currentTimeMillis();
        LOGGER.info("手动统计:月话务统计完成,完成时间:" + DateUtils.getNowDString() + ",耗时:" + (l3-l2)/1000 + "秒");

    }

}
