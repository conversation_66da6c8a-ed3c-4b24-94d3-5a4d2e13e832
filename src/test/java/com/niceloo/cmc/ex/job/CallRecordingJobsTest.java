package com.niceloo.cmc.ex.job;

import com.niceloo.cmc.ex.jobs.CallRecordingJobs;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * @description: 定时任务拉取通话录音
 * @author: <PERSON><PERSON><PERSON>yu
 * @create: 2022-03-14 15:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CallRecordingJobsTest {

    @Resource
    private CallRecordingJobs callRecordingJobs;

    /**
     * 录音下载
     */
    @Test
    public void test1() {
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(), new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                ThreadFactory threadFactory = Executors.defaultThreadFactory();
                Thread thread = threadFactory.newThread(r);
                String name = thread.getName();
                return new Thread(r, "wcy_" + name);
            }
        });
        for (int i = 0; i < 3; i++) {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    callRecordingJobs.supplementaryCallRecordingJobs();
                }
            });
        }
        while (true){}
    }


    @Test
    public void supplementaryCallRecordingTest() {
        callRecordingJobs.supplementaryCallRecordingJobs();
    }


    @Test
    public void test2() {
        callRecordingJobs.tryUpDownloadRecordingJobs();
    }


    /**
     * 尝试修复通话录音地址为空,通话时长大于二十秒的通话记录,只支持云客
     *
     * @return void
     * @paramter
     * <AUTHOR>
     * @Date 17:16 2022/3/17
     **/
    @Test
    public void test3() {
        callRecordingJobs.tryRepairRecordingNonExistentRecords();
    }
}
