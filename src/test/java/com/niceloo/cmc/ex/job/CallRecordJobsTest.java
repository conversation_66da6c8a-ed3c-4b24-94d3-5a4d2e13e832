package com.niceloo.cmc.ex.job;

import com.niceloo.cmc.ex.controller.mq.CallRecordPullConsumer;
import com.niceloo.cmc.ex.jobs.CallRecordsJobs;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @description: 定时任务通话记录拉取测试
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-28 11:54
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CallRecordJobsTest {

    @Resource
    private CallRecordPullConsumer callRecordPullConsumer;

    @Resource
    private CallRecordsJobs callRecordsJobs;



    @Test
    public void callRecordConsumerTest() {
        callRecordPullConsumer.subscribe("YP~~~2022-03-25 07:00:00~~~2022-03-25 08:00:00~~~A");
    }

    @Test
    public void callRecordConsumerTest2() {
        callRecordPullConsumer.subscribe("FS~~~2022-03-22 07:00:00~~~2022-03-22 08:00:00~~~A");
    }

    @Test
    public void callRecordConsumerTest3() {
        callRecordPullConsumer.subscribe("TQ~~~2022-03-24 07:00:00~~~2022-03-24 08:00:00~~~A");
    }

    @Test
    public void callRecordConsumerTest5() {
        callRecordPullConsumer.subscribe("OI~~~2022-03-24 09:00:00~~~2022-03-24 10:00:00~~~A");
    }

    @Test
    public void callRecordConsumerTest4() {
        try {
            callRecordsJobs.saveOrUpdateRecordToES();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void supplementaryCallRecordTest() {
        callRecordsJobs.supplementaryCallRecord();
        while (true);
    }

}
