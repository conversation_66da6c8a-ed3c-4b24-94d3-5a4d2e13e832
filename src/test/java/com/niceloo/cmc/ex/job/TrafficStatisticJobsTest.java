package com.niceloo.cmc.ex.job;

import com.niceloo.cmc.ex.entity.CcSchoolBalance;
import com.niceloo.cmc.ex.jobs.TrafficStatisticJobs;
import com.niceloo.cmc.ex.mapper.CcSchoolBalanceMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 话务统计测试类
 * <AUTHOR>
 * @since 2022-03-01 16:37
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TrafficStatisticJobsTest {
    @Resource
    private TrafficStatisticJobs trafficStatisticJobs;

    @Resource
    private CcSchoolBalanceMapper ccSchoolBalanceMapper;
    @Test
    public void createSchoolBalance() {
        trafficStatisticJobs.createSchoolBalance();

        // 查询数据库，验证是否已经成功生成了账号余额信息
        List<CcSchoolBalance> balanceList = ccSchoolBalanceMapper.selectList(null);
        assertNotNull(balanceList);
        assertFalse(balanceList.isEmpty());
    }

    @Test
    public void saveDayCountToDBTest() {
        trafficStatisticJobs.saveDayCountToDB();
    }

    @Test
    public void saveMonthCountToDBTest() {
        trafficStatisticJobs.saveMonthCountToDB();
    }

    @Test
    public void saveRealtimeCountTest() {
        trafficStatisticJobs.saveRealtimeCount();
    }

    /**
     * 失败的重试
     */
    @Test
    public void compensationStatisticallyFailedTraffic() {
        trafficStatisticJobs.compensationStatisticallyFailedTraffic("day");
    }

    /*@Test
    public void saveDayCountToDBTest2() {
        trafficStatisticJobs.saveDayRecordCount("2022-04-04");
    }*/
}
