FROM nexus.niceloo.com:8083/baseimage/agent-server:3.0.0-java11
MAINTAINER MaNing <<EMAIL>>

COPY service.app /root/app/config/service.app
ARG DOCKER_BUILD_APP_ID
ARG DOCKER_BUILD_BASE_DIR
ARG DOCKER_BUILD_DEPLOYMENT_MODE
ARG DOCKER_BUILD_APP_RESOURCE
ARG HTTP_PROBE_PATH
RUN sed -i "s@\${DOCKER_BUILD_APP_ID}@${DOCKER_BUILD_APP_ID}@g" /root/app/config/service.app \
    && sed -i "s@\${DOCKER_BUILD_BASE_DIR}@${DOCKER_BUILD_BASE_DIR}@g" /root/app/config/service.app \
    && sed -i "s@\${DOCKER_BUILD_DEPLOYMENT_MODE}@${DOCKER_BUILD_DEPLOYMENT_MODE}@g" /root/app/config/service.app \
    && sed -i "s@\${DOCKER_BUILD_APP_RESOURCE}@${DOCKER_BUILD_APP_RESOURCE}@g" /root/app/config/service.app \
    && sed -i "s@\${HTTP_PROBE_PATH}@${HTTP_PROBE_PATH}@g" /root/app/config/service.app

ARG BIN_FILE_PATH
ARG BIN_FILE_NAME
COPY ${BIN_FILE_PATH}/${BIN_FILE_NAME} ${DOCKER_BUILD_APP_RESOURCE}