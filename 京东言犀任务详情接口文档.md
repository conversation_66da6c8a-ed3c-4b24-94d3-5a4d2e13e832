# 京东言犀AI外呼任务详情查看接口文档

## 接口概述

**接口名称**: 京东言犀AI外呼任务详情查看  
**接口路径**: `/api/commu/aiCallJob/jdyxJobDetail`  
**请求方式**: POST  
**Content-Type**: application/x-www-form-urlencoded  

## 功能描述

根据CcAiCallJob表的jobId查询京东言犀AI外呼任务的详细信息，包括：
- 已选的外呼线路
- AI坐席数量
- 外呼时段
- 外呼时间
- 自动重播开启情况
- 重播原因勾选情况
- 重播次数
- 间隔时间

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| jobId | String | 是 | AI外呼任务主键ID |

## 请求示例

```bash
curl -X POST "http://localhost:8080/api/commu/aiCallJob/jdyxJobDetail" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "jobId=AiCallJob202412190001"
```

## 响应参数

### 主要字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| jobId | String | 任务ID |
| jobName | String | 任务名称 |
| status | String | 任务状态 |
| statusDesc | String | 任务状态描述 |
| lineInfo | Object | 外呼线路信息 |
| concurrency | Integer | AI坐席数量 |
| callPeriods | Array | 外呼时段列表 |
| callTime | Object | 外呼时间信息 |
| redialConfig | Object | 自动重播配置 |
| redialReasons | Array | 重播原因列表 |
| redialTimes | Integer | 重播次数 |
| redialInterval | Integer | 间隔时间（分钟） |
| taskProgress | Object | 任务进度信息 |

### 外呼线路信息 (lineInfo)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| lineId | Long | 线路ID |
| lineName | String | 线路名称 |
| displayNumber | String | 外显号码 |

### 外呼时段 (callPeriods)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| weekDay | String | 星期几（1-7） |
| weekDayDesc | String | 星期几描述 |
| timeBegin | String | 开始时间 |
| timeEnd | String | 结束时间 |
| enabled | Boolean | 是否启用 |

### 外呼时间 (callTime)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| startupMode | String | 启动模式 |
| startupModeDesc | String | 启动模式描述 |
| startTime | String | 任务启动时间 |
| createTime | String | 任务创建时间 |
| actualStartTime | String | 任务开始时间 |
| endTime | String | 任务结束时间 |

### 重播配置 (redialConfig)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| enabled | Boolean | 是否开启重播 |
| enabledDesc | String | 重播开启状态描述 |

### 任务进度 (taskProgress)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalCalledNum | Integer | 拨打号码总数 |
| calledTaskNum | Integer | 已拨打号码数 |
| connectedTaskNum | Integer | 接通号码数 |
| pendingTaskNum | Integer | 未拨打号码数 |
| outboundProcess | Double | 外呼进度百分比 |
| connectedRatio | Double | 接通率 |

## 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "jobId": "AiCallJob202412190001",
    "jobName": "测试外呼任务",
    "status": "running",
    "statusDesc": "执行中",
    "lineInfo": {
      "lineId": 12345,
      "lineName": "北京线路01",
      "displayNumber": "010-12345678"
    },
    "concurrency": 10,
    "callPeriods": [
      {
        "weekDay": "1",
        "weekDayDesc": "星期一",
        "timeBegin": "09:00:00",
        "timeEnd": "18:00:00",
        "enabled": true
      },
      {
        "weekDay": "2",
        "weekDayDesc": "星期二",
        "timeBegin": "09:00:00",
        "timeEnd": "18:00:00",
        "enabled": true
      }
    ],
    "callTime": {
      "startupMode": "Manual",
      "startupModeDesc": "手动",
      "startTime": "2024-12-19 10:00:00",
      "createTime": "2024-12-19 09:30:00",
      "actualStartTime": "2024-12-19 10:05:00",
      "endTime": null
    },
    "redialConfig": {
      "enabled": true,
      "enabledDesc": "已开启"
    },
    "redialReasons": [
      "无人接听",
      "占线",
      "关机"
    ],
    "redialTimes": 3,
    "redialInterval": 30,
    "taskProgress": {
      "totalCalledNum": 1000,
      "calledTaskNum": 500,
      "connectedTaskNum": 200,
      "pendingTaskNum": 500,
      "outboundProcess": 50.0,
      "connectedRatio": 40.0
    }
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误 |
| 401 | 未登录 |
| 404 | 任务不存在 |
| 500 | 服务器内部错误 |

### 常见错误响应

```json
{
  "code": 400,
  "message": "非法参数!!!根据任务ID未找到任务!!",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "该任务不是京东言犀任务!!",
  "data": null
}
```

```json
{
  "code": 500,
  "message": "未能从京东言犀获取任务详情信息!!",
  "data": null
}
```

## 注意事项

1. **权限验证**: 接口需要用户登录，会验证NgExpand参数
2. **任务类型**: 只支持京东言犀类型的任务（aiJobType = "JDYX"）
3. **数据来源**: 
   - 基本信息来自本地数据库CcAiCallJob表
   - 实时状态和进度信息来自京东言犀JOB_INFO接口
4. **接通率计算**: 接通率 = (接通号码数 / 已拨打号码数) × 100%
5. **重播原因**: 多个原因用竖线(|)分隔存储，接口返回时解析为数组

## 技术实现

### 核心流程

1. 验证用户登录状态
2. 根据jobId查询本地任务信息
3. 验证任务类型是否为京东言犀
4. 调用京东言犀JOB_INFO接口获取实时信息
5. 合并本地和远程数据构建响应结果

### 依赖接口

- **京东言犀JOB_INFO接口**: `/api/voice_call/job_getJobInfo`
- **枚举类**: `JDInterfaceTypeEnum.JOB_INFO`

### 关键方法

- `JDYXService.getJobInfo()`: 调用京东言犀接口
- `buildJDYXJobDetailVO()`: 构建响应数据
- `getStatusDescription()`: 状态描述转换
- `buildCallPeriods()`: 外呼时段解析
- `parseRedialReasons()`: 重播原因解析

## 版本信息

- **创建时间**: 2024-12-19
- **版本**: v1.0
- **分支**: feature/jdyx-job-detail
