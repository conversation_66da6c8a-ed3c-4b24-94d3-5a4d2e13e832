<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.niceloo.framework</groupId>
        <artifactId>common-parent</artifactId>
        <version>2.0.22-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <artifactId>niceloo-communicationcenter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <druid.version>1.1.10</druid.version>
        <dynamic.datasource.boot.version>2.5.4</dynamic.datasource.boot.version>
        <start-class>com.niceloo.cmc.ex.CommuCenterApplication</start-class>
    </properties>

    <dependencies>
        <!--apollo 配置中心-->
        <dependency>
            <groupId>com.niceloo.framework</groupId>
            <artifactId>starter-apollo</artifactId>
        </dependency>
        <!--web-->
        <dependency>
            <groupId>com.niceloo.framework</groupId>
            <artifactId>starter-web</artifactId>
        </dependency>
        <!--数据库-->
        <dependency>
            <groupId>com.niceloo.framework</groupId>
            <artifactId>starter-db-dynamic</artifactId>
        </dependency>
        <!--feign RPC远程服务调用-->
        <dependency>
            <groupId>com.niceloo.framework</groupId>
            <artifactId>starter-openfeign</artifactId>
        </dependency>
        <!--权限校验-->
        <dependency>
            <groupId>com.niceloo.framework</groupId>
            <artifactId>starter-auth</artifactId>
        </dependency>
        <!--mq client-->
        <dependency>
            <groupId>com.niceloo</groupId>
            <artifactId>mq-client</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!-- redis 缓存-->
        <dependency>
            <groupId>com.niceloo.framework</groupId>
            <artifactId>starter-redis</artifactId>
        </dependency>
        <!--分布式锁 redis-->
        <dependency>
            <groupId>com.niceloo.framework</groupId>
            <artifactId>starter-lock</artifactId>
        </dependency>
        <!--id发号服务-->
        <dependency>
            <groupId>com.niceloo</groupId>
            <artifactId>id-client-starter</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!--SpringBoot-ES-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore-nio</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.12</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore-nio</artifactId>
            <version>4.4.13</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.13</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.12</version>
        </dependency>
        <!--okhttp工具包-->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.6.0</version>
        </dependency>
        <!-- 音频文件转换 -->
        <dependency>
            <groupId>com.github.dadiyang</groupId>
            <artifactId>jave</artifactId>
            <version>1.0.5</version>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 代码生成器 -->
        <dependency>
            <groupId>com.niceloo.framework</groupId>
            <artifactId>starter-code-gen</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- EXCEL读写工具 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>
        <!-- encrypt-starter -->
        <dependency>
            <groupId>com.niceloo</groupId>
            <artifactId>encrypt-starter</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.niceloo</groupId>
            <artifactId>encrypt-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.byai</groupId>
            <artifactId>open-sdk-java</artifactId>
            <version>1.1.19</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!--覆盖父类的格式化插件-->
            <plugin>
                <groupId>net.revelc.code.formatter</groupId>
                <artifactId>formatter-maven-plugin</artifactId>
                <version>2.23.0</version>
                <configuration>
                    <!--跳过编译阶段的代码格式化-->
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>youlu-nexus</id>
            <url>http://nexus.niceloo.com:11081/repository/maven-public/</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>youlu</id>
            <name>优路maven仓库私服</name>
            <url>${maven.repo}</url>
        </repository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <maven.repo>http://nexus.niceloo.com:11081/repository/maven-releases/</maven.repo>
                <docker.repo>nexus.niceloo.com:8083</docker.repo>
            </properties>
        </profile>

        <profile>
            <id>prod</id>
            <properties>
                <maven.repo>http://nexus.niceloo.com:11081/repository/maven-releases/</maven.repo>
                <docker.repo>nexus.niceloo.com:8083</docker.repo>
            </properties>
        </profile>

        <profile>
            <id>docker</id>
            <properties>
                <!-- docker镜像前缀 -->
                <docker.image.prefix>youlu</docker.image.prefix>
                <!-- tag标签 -->
                <docker.tag>${project.version}</docker.tag>
                <!-- 应用打包后的jar或war包所在目录，相对位置（相对于dockerfile） -->
                <bin.file.path>target/</bin.file.path>
                <!-- 应用打包后jar包或war包的名字 -->
                <bin.file.name>niceloo-communicationcenter.jar</bin.file.name>
                <!-- 应用ID，注意，要和Apollo中一致 -->
                <app.id>communicationcenter</app.id>
                <!-- 应用部署方式，枚举类型，TOMCAT和STANDARD，如果是在tomcat中部署启动的就选tomcat，如果是独立启动的就选STANDARD -->
                <app.deploymentmode>STANDARD</app.deploymentmode>
                <!-- 应用工作目录，没有特殊需求无需更改 -->
                <app.basedir>/root/app/work</app.basedir>
                <!-- 应用资源，没有特殊需求无需更改 -->
                <app.resource>/root/app/resource/${bin.file.name}</app.resource>
                <!-- HTTP方式健康检查URL，注意修改为自己系统实际的健康检查URL -->
                <app.probe.http.path>http://127.0.0.1:8590/communicationcenter/api/core/test/heartbeat
                </app.probe.http.path>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <version>1.4.13</version>
                        <executions>
                            <execution>
                                <id>default</id>
                                <goals>
                                    <goal>build</goal>
                                    <goal>push</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <!-- 仓库 -->
                            <repository>${docker.repo}/${docker.image.prefix}/${project.artifactId}</repository>
                            <!-- docker image的tag，相当于版本号 -->
                            <tag>${docker.tag}</tag>
                            <!-- 使用maven全局配置中的用户名密码 -->
                            <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                            <buildArgs>
                                <BIN_FILE_PATH>${bin.file.path}</BIN_FILE_PATH>
                                <BIN_FILE_NAME>${bin.file.name}</BIN_FILE_NAME>
                                <DOCKER_BUILD_APP_ID>${app.id}</DOCKER_BUILD_APP_ID>
                                <DOCKER_BUILD_DEPLOYMENT_MODE>${app.deploymentmode}</DOCKER_BUILD_DEPLOYMENT_MODE>
                                <DOCKER_BUILD_BASE_DIR>${app.basedir}</DOCKER_BUILD_BASE_DIR>
                                <DOCKER_BUILD_APP_RESOURCE>${app.resource}</DOCKER_BUILD_APP_RESOURCE>
                                <HTTP_PROBE_PATH>${app.probe.http.path}</HTTP_PROBE_PATH>
                            </buildArgs>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>