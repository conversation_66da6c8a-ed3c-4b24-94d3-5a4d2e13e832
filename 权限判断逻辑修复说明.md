# 权限判断逻辑修复说明

## 问题描述

原有的权限判断逻辑存在问题：当用户传入多个部门ID（用逗号分割）时，如果其中任何一个部门没有权限，就会直接抛出"您暂无所选部门权限"异常，这种处理方式过于严格。

## 修复方案

### 修复前的逻辑
```java
// 校验是否选择了无权限的部门
if (StringUtils.isNotEmpty(dptId)) {
    String[] dptIdArray = dptId.split(",");
    Set<String> dptIdSet = new HashSet<>(dptIds);
    for (String dptIdParam : dptIdArray) {
        if (!dptIdSet.contains(dptIdParam)) {
            throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选部门权限");
        }
    }
}
```

**问题**：只要有一个部门没有权限就直接抛异常，不允许用户访问任何数据。

### 修复后的逻辑
```java
// 从传参的部门中过滤掉无权限的部门
if (StringUtils.isNotEmpty(dptId)) {
    String[] dptIdArray = dptId.split(",");
    Set<String> dptIdSet = new HashSet<>(dptIds);
    List<String> validDptIds = new ArrayList<>();
    for (String dptIdParam : dptIdArray) {
        if (dptIdSet.contains(dptIdParam)) {
            validDptIds.add(dptIdParam);
        }
    }
    // 过滤后如果没有有效的部门，则抛出权限异常
    if (validDptIds.isEmpty()) {
        throw new ApplicationException(ApiErrorCodes.execute_failed, "您暂无所选部门权限");
    }
    // 更新请求中的部门ID为过滤后的有效部门
    request.setDptId(String.join(",", validDptIds));
}
```

**优化**：
1. 先过滤掉没有权限的部门
2. 只有当过滤后没有任何有效部门时才抛出权限异常
3. 将过滤后的有效部门ID更新到请求参数中

## 修改的文件和方法

### 文件：`src/main/java/com/niceloo/cmc/ex/utils/InterfaceRequestAuthUtils.java`

### 修改的方法：`interfaceAuthOfSelectType`

#### 涉及的权限策略：
1. **myDept权限**（第103-127行）
2. **otherDept权限**（第129-165行）

## 修改详情

### 1. myDept权限处理
- **位置**：第103-127行
- **修改内容**：将原来的直接抛异常改为过滤无权限部门
- **保留逻辑**：`if (StringUtils.isEmpty(dptId))` 的处理逻辑继续保留

### 2. otherDept权限处理
- **位置**：第129-165行
- **修改内容**：新增了对otherDept权限的处理，逻辑与myDept相同
- **额外功能**：包含了对话务统计接口的员工权限检查

### 3. 导入依赖
- 添加了 `BdService` 和 `MapUtils` 的导入
- 用于支持员工权限检查功能

## 业务逻辑说明

### 处理流程
1. **获取用户权限部门列表**：通过 `authService.getDptIds()` 获取用户有权限的部门列表
2. **解析请求参数**：将请求中的部门ID字符串按逗号分割成数组
3. **过滤有效部门**：遍历请求的部门ID，只保留用户有权限的部门
4. **权限验证**：如果过滤后没有有效部门，抛出权限异常
5. **更新请求参数**：将过滤后的有效部门ID重新设置到请求对象中
6. **默认部门设置**：如果请求中没有指定部门，则设置为用户所有有权限的部门

### 员工权限检查（仅限话务统计接口）
- 当菜单代码为 `CALL_RECORD_COUNT_EE` 且请求中包含员工ID时
- 通过 `BdService` 查询员工信息，获取员工所属部门
- 验证用户是否有该员工所属部门的权限

## 优势

### 1. 用户体验改善
- 用户不会因为选择了一个无权限的部门就被完全拒绝访问
- 系统会自动过滤掉无权限的部门，返回用户有权限的数据

### 2. 安全性保障
- 仍然确保用户只能访问有权限的部门数据
- 无权限的部门会被自动过滤掉，不会泄露数据

### 3. 灵活性提升
- 支持用户同时选择多个部门，系统智能处理权限过滤
- 保持了原有的默认行为（未指定部门时返回所有有权限的部门）

## 测试建议

### 测试场景
1. **用户选择的所有部门都有权限**：应该正常返回数据
2. **用户选择的部门部分有权限**：应该返回有权限部门的数据
3. **用户选择的所有部门都没有权限**：应该抛出"您暂无所选部门权限"异常
4. **用户未选择部门**：应该返回用户所有有权限部门的数据
5. **话务统计接口的员工权限检查**：验证员工权限验证逻辑

### 测试数据准备
- 准备具有不同部门权限的测试用户
- 准备包含多个部门的测试数据
- 测试各种权限组合场景

## 注意事项

1. **向后兼容性**：修改保持了原有API的兼容性，不会影响现有调用方
2. **日志记录**：建议在实际使用中添加日志记录，记录权限过滤的详细信息
3. **性能考虑**：权限检查涉及多次数据库查询，在高并发场景下需要考虑缓存优化
4. **错误处理**：确保所有异常情况都有适当的错误信息返回给用户

## 总结

这次修复解决了权限判断过于严格的问题，提升了用户体验，同时保持了数据安全性。修改后的逻辑更加灵活和智能，能够更好地处理复杂的权限场景。
