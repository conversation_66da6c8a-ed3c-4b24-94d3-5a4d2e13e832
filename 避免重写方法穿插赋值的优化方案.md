# 避免重写方法穿插赋值的优化方案

## 问题分析

原有代码中存在的问题：
1. **CountDayRequest** 和 **CountEeRequest** 类继承了 `AuthBaseRequest`，并重写了 `getEeUserId()`、`setEeUserId()`、`getDptId()`、`setDptId()` 等方法
2. 这些重写方法通过穿插赋值的方式将具体字段（如 `callerUserId`、`dptIds`）映射到父类的抽象字段
3. 这种设计导致代码维护困难，字段映射关系不清晰，容易出错

## 优化方案

### 方案一：参数传递方式（推荐）

#### 1. 移除重写方法
从 `CountDayRequest` 和 `CountEeRequest` 类中移除所有重写的方法：

```java
// 移除前
@Override
public String getEeUserId() {
    return callerUserId;
}

@Override
public void setEeUserId(String eeUserId) {
    this.callerUserId = eeUserId;
}

// 移除后 - 保持类的简洁性
// 不再重写父类方法
```

#### 2. 新增参数传递的权限验证方法
在 `InterfaceRequestAuthUtils` 中新增 `validatePermissionWithParams` 方法：

```java
public static AuthValidationResult validatePermissionWithParams(
    String eeUserId, 
    String dptIds, 
    String schoolIds, 
    NgExpand ngExpand, 
    String menuCode) {
    // 直接使用传入的参数进行权限验证
    // 返回验证结果而不是修改对象
}
```

#### 3. 修改Controller方法
在Controller中使用新的权限验证方式：

```java
// 优化前
InterfaceRequestAuthUtils.interfaceAuthOfSelectTypeV2(request, ngExpand, menuCode);
// 直接使用被修改的request对象

// 优化后
AuthValidationResult authResult = InterfaceRequestAuthUtils.validatePermissionWithParams(
    request.getCallerUserId(), 
    request.getDptIds(), 
    request.getSchoolId(), 
    ngExpand, 
    menuCode);
ValidatedQueryParams validatedParams = ValidatedQueryParams.fromCountDayRequest(request, authResult);
// 使用不可变的validatedParams对象
```

### 方案二：适配器模式（备选）

#### 创建适配器类
```java
public class AuthRequestAdapter extends AuthBaseRequest {
    private final Object originalRequest;
    
    public static AuthRequestAdapter from(CountDayRequest request) {
        AuthRequestAdapter adapter = new AuthRequestAdapter(request);
        adapter.setEeUserId(request.getCallerUserId());
        adapter.setDptId(request.getDptIds());
        adapter.setSchoolId(request.getSchoolId());
        return adapter;
    }
}
```

## 实施步骤

### 1. 创建新的DTO类
- `AuthValidationResult`：封装权限验证结果
- `ValidatedQueryParams`：不可变的查询参数类

### 2. 新增权限验证方法
- `validatePermissionWithParams`：使用参数传递的权限验证方法

### 3. 修改请求类
- 移除 `CountDayRequest` 和 `CountEeRequest` 中的重写方法
- 保持类的简洁性和单一职责

### 4. 更新Controller方法
- 修改 `countDayV2` 和 `countEeV2` 方法
- 使用新的权限验证方式
- 添加 `manualPageV2` 方法支持新的参数类型

## 优化效果

### 1. 代码清晰度提升
- **消除穿插赋值**：不再通过重写方法进行字段映射
- **职责明确**：每个类都有明确的职责，不再承担额外的映射功能
- **易于理解**：代码逻辑更加直观，便于新人理解

### 2. 维护性改善
- **减少耦合**：请求类与权限验证逻辑解耦
- **易于修改**：字段变更时不需要修改多个重写方法
- **错误减少**：避免了字段映射错误的可能性

### 3. 扩展性增强
- **灵活配置**：可以轻松添加新的权限验证场景
- **参数复用**：权限验证方法可以被其他场景复用
- **向后兼容**：保留原有方法，确保现有代码不受影响

## 使用示例

### 优化前的代码
```java
// 请求类中的重写方法
@Override
public String getEeUserId() {
    return callerUserId;  // 穿插赋值
}

// Controller中的使用
InterfaceRequestAuthUtils.interfaceAuthOfSelectTypeV2(request, ngExpand, menuCode);
// request对象被修改，副作用不明显
```

### 优化后的代码
```java
// 请求类保持简洁
public class CountDayRequest extends AuthBaseRequest {
    private String callerUserId;
    private String dptIds;
    // 不再重写父类方法
}

// Controller中的使用
AuthValidationResult authResult = InterfaceRequestAuthUtils.validatePermissionWithParams(
    request.getCallerUserId(), 
    request.getDptIds(), 
    request.getSchoolId(), 
    ngExpand, 
    menuCode);
ValidatedQueryParams validatedParams = ValidatedQueryParams.fromCountDayRequest(request, authResult);
// 明确的数据流，无副作用
```

## 最佳实践

### 1. 避免重写方法进行字段映射
- 重写方法应该用于真正的行为重定义，而不是字段映射
- 字段映射应该通过明确的转换方法或适配器实现

### 2. 使用不可变对象
- 权限验证后的参数应该封装在不可变对象中
- 避免在业务流程中意外修改参数

### 3. 明确的数据流
- 权限验证 → 验证结果 → 不可变参数 → 业务逻辑
- 每个步骤都有明确的输入和输出

### 4. 职责分离
- 请求类只负责参数接收和验证
- 权限验证类只负责权限逻辑
- 业务逻辑类只负责核心业务

## 总结

通过移除重写方法的穿插赋值，我们实现了：

1. **代码简化**：请求类更加简洁，职责单一
2. **逻辑清晰**：权限验证逻辑独立，易于理解和维护
3. **扩展性强**：新的权限验证方式可以轻松应用到其他场景
4. **向后兼容**：保留原有方法，确保平滑过渡

这种优化方案不仅解决了当前的维护性问题，还为未来的功能扩展提供了良好的基础。建议在项目中逐步推广这种模式，提升整体代码质量。
