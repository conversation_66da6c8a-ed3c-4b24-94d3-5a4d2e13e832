# DTO类使用说明

## 概述

为了解决权限验证代码中直接修改入参对象的维护性问题，我们创建了两个新的DTO类：

1. `AuthValidationResult` - 权限验证结果类
2. `ValidatedQueryParams` - 经过权限验证的查询参数类

## 类详细说明

### 1. AuthValidationResult

**位置：** `src/main/java/com/niceloo/cmc/ex/pojo/dto/AuthValidationResult.java`

**作用：** 封装权限验证的结果，避免直接修改入参对象

**主要字段：**
- `validatedEeUserId` - 验证后的员工用户ID
- `validatedDptIds` - 验证后的部门ID列表（逗号分割）
- `validatedSchoolIds` - 验证后的分校ID列表（逗号分割）
- `passed` - 是否通过权限验证
- `failureReason` - 权限验证失败的原因

**主要方法：**
```java
// 创建验证通过的结果
AuthValidationResult.success(String eeUserId, String dptIds, String schoolIds)

// 创建验证失败的结果
AuthValidationResult.failure(String reason)

// 检查是否验证通过
boolean isValid()
```

**使用示例：**
```java
// 创建成功结果
AuthValidationResult result = AuthValidationResult.success("user123", "dept1,dept2", "school1");

// 创建失败结果
AuthValidationResult result = AuthValidationResult.failure("您暂无所选部门权限");

// 检查验证结果
if (result.isValid()) {
    // 验证通过，继续业务逻辑
    String validatedDptIds = result.getValidatedDptIds();
}
```

### 2. ValidatedQueryParams

**位置：** `src/main/java/com/niceloo/cmc/ex/pojo/dto/ValidatedQueryParams.java`

**作用：** 封装经过权限验证后的查询参数，保持不可变性

**主要字段：**
- `eeUserId` - 验证后的员工用户ID
- `dptIds` - 验证后的部门ID列表
- `schoolIds` - 验证后的分校ID列表
- `channelType` - 呼叫通道类型
- `createdTimeStart` - 查询开始时间
- `createdTimeEnd` - 查询结束时间
- `countType` - 查询统计类型
- `pageIndex` - 分页起始位置
- `pageSize` - 分页大小

**特点：**
- 所有字段都是 `final` 的，确保不可变性
- 提供静态工厂方法从原始请求对象创建

**主要方法：**
```java
// 从CountEeRequest创建
ValidatedQueryParams.fromCountEeRequest(CountEeRequest request, AuthValidationResult authResult)

// 从CountDayRequest创建
ValidatedQueryParams.fromCountDayRequest(CountDayRequest request, AuthValidationResult authResult)
```

**使用示例：**
```java
// 权限验证
AuthValidationResult authResult = InterfaceRequestAuthUtils.validatePermissionWithParams(
    request.getCallerUserId(), 
    request.getDptIds(), 
    request.getSchoolId(), 
    ngExpand, 
    menuCode);

// 创建不可变的查询参数对象
ValidatedQueryParams validatedParams = ValidatedQueryParams.fromCountEeRequest(request, authResult);

// 使用验证后的参数进行业务逻辑
List<RecordEeStatisticsVO> result = service.query(
    validatedParams.getEeUserId(),
    validatedParams.getDptIds(),
    validatedParams.getChannelType()
);
```

## 在Controller中的使用模式

### 优化前的代码
```java
@PostMapping("/count/ee/v2")
public BasePageVO<RecordEeStatisticsVO> countEeV2(CountEeRequest request, NgExpand ngExpand) {
    // 直接修改request对象
    InterfaceRequestAuthUtils.interfaceAuthOfSelectTypeV2(request, ngExpand, menuCode);
    
    // 使用被修改的request对象
    List<RecordEeStatisticsVO> result = service.query(request.getCallerUserId(), request.getDptIds());
    return this.manualPage(request, result);
}
```

### 优化后的代码
```java
@PostMapping("/count/ee/v2")
public BasePageVO<RecordEeStatisticsVO> countEeV2(CountEeRequest request, NgExpand ngExpand) {
    // 权限验证 - 使用参数传递方式，避免修改入参对象
    String menuCode = MenuCodeEnum.OtherMenuEnum.CALL_RECORD_COUNT_EE.getCode();
    AuthValidationResult authResult = InterfaceRequestAuthUtils.validatePermissionWithParams(
        request.getCallerUserId(), 
        request.getDptIds(), 
        request.getSchoolId(), 
        ngExpand, 
        menuCode);
    
    // 创建经过权限验证的查询参数对象
    ValidatedQueryParams validatedParams = ValidatedQueryParams.fromCountEeRequest(request, authResult);
    
    // 使用验证后的参数进行查询
    List<RecordEeStatisticsVO> result = service.query(
        validatedParams.getEeUserId(), 
        validatedParams.getDptIds(), 
        validatedParams.getChannelType());
    return this.manualPageV2(validatedParams, result);
}
```

## 优势

1. **不可变性**：`ValidatedQueryParams` 的所有字段都是 `final` 的，确保数据不被意外修改
2. **明确的数据流**：权限验证 → 验证结果 → 不可变查询参数 → 业务逻辑
3. **易于测试**：可以轻松创建测试数据和模拟权限验证结果
4. **类型安全**：明确的类型定义，避免类型错误
5. **职责分离**：权限验证逻辑与业务逻辑完全分离

## 注意事项

1. **导入路径**：确保正确导入DTO类的包路径
2. **Lombok依赖**：这两个类使用了Lombok的 `@Data` 注解，确保项目中有Lombok依赖
3. **向后兼容**：原有的权限验证方法仍然保留，确保现有代码不受影响
4. **渐进式迁移**：可以逐步将其他方法迁移到新的模式

## 扩展建议

1. 可以为其他类型的请求创建类似的工厂方法
2. 可以添加更多的验证逻辑到 `AuthValidationResult` 中
3. 可以考虑添加Builder模式来创建复杂的查询参数
4. 可以添加更多的静态工厂方法来支持不同的使用场景
