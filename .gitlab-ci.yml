sonarqube-check:
  image: maven:3.6.3-jdk-11
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script: 
    - mvn verify sonar:sonar -Dsonar.qualitygate.wait=true -Dsonar.projectName=技术架构研发部-communicationcenter -Dsonar.projectDescription=通话和录音的记录和统计
  allow_failure: true
  only:
    - master # or the name of your main branch
  tags:
    - runner-shared-sonar