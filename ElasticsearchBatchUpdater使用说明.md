# Elasticsearch批量更新工具使用说明

## 概述
这个工具用于批量更新Elasticsearch中的文档，根据项目根目录下的field6.txt文件中的值查询匹配的文档，并更新其dataCompleteStatus和serverFolder字段。

## 文件位置
- 主程序：`src/main/java/com/niceloo/cmc/ex/utils/ElasticsearchBatchUpdater.java`
- 配置文件：`src/main/java/com/niceloo/cmc/ex/utils/BatchUpdateConfig.java`
- 数据文件：`field6.txt`（位于项目根目录下）

## 功能特性
- 从field6.txt文件中读取field6值（每行一个）
- 分批处理，避免对服务器造成过大压力
- 可配置的批次大小和间隔时间
- 自动重试机制
- 详细的日志输出
- 优雅的错误处理

## 配置参数说明

在 `BatchUpdateConfig.java` 中可以调整以下参数：

### Elasticsearch连接配置
```java
ES_HOST = "es-cn-8ex3wgtqb000755wp.public.elasticsearch.aliyuncs.com"
ES_PORT = 9200
ES_USERNAME = "elastic"
ES_PASSWORD = "W86mXT4x1t2T"
```

### 查询配置
```java
INDEX_NAME = "call_record_202505"  // 索引名称
CHANNEL_TYPE = "ZK"                // 通道类型过滤条件
FIELD6_FILE_PATH = "field6.txt"    // field6文件路径（项目根目录）
```

### 批处理配置
```java
BATCH_SIZE = 100        // 每批处理的field6数量
INTERVAL_MS = 100       // 批次间隔时间（毫秒）
SEARCH_SIZE = 1000      // 每次搜索返回的最大文档数
MAX_RETRIES = 3         // 最大重试次数
```

### 更新字段配置
```java
DATA_COMPLETE_STATUS_VALUE = "N"  // dataCompleteStatus字段的新值
SERVER_FOLDER_VALUE = ""          // serverFolder字段的新值（空字符串）
```

## 使用方法

### 1. 准备field6.txt文件
在项目根目录下创建field6.txt文件，每行一个field6值：
```
1747122834-2912-447575
1747122927-3171-447834
1747122925-3163-447826
...
```

### 2. 运行程序
可以通过以下几种方式运行：

#### 方式1：直接运行main方法
在IDE中直接运行 `ElasticsearchBatchUpdater.main()` 方法

#### 方式2：命令行运行
```bash
cd 项目根目录
java -cp "target/classes:依赖jar包路径" com.niceloo.cmc.ex.utils.ElasticsearchBatchUpdater
```

#### 方式3：在项目中调用
```java
ElasticsearchBatchUpdater updater = new ElasticsearchBatchUpdater();
updater.executeBatchUpdate();
```

## 执行流程

1. 程序启动，建立Elasticsearch连接
2. 读取项目根目录下的field6.txt文件中的所有field6值
3. 将field6值分批处理（每批100个，可配置）
4. 对每批field6值：
   - 构建查询条件：`channelType="ZK" AND field6 IN (批次中的值)`
   - 执行搜索，获取匹配的文档
   - 批量更新文档的`dataCompleteStatus="N"`和`serverFolder=""`
   - 等待配置的间隔时间（默认100毫秒）
5. 完成所有批次后，关闭连接并输出统计信息

## 日志输出示例

```
[2024-01-15 10:30:00] 开始执行批量更新任务...
[2024-01-15 10:30:00] 从文件中读取到 40000 条field6记录
[2024-01-15 10:30:00] 总共分为 400 个批次处理，每批次间隔 100 毫秒
[2024-01-15 10:30:00] 正在处理第 1/400 批次...
[2024-01-15 10:30:01] 查询到 85 条匹配的文档
[2024-01-15 10:30:01] 成功更新 85 条文档
[2024-01-15 10:30:01] 正在处理第 2/400 批次...
...
[2024-01-15 10:35:00] 批量更新任务完成！
[2024-01-15 10:35:00] 总耗时: 300.5 秒
[2024-01-15 10:35:00] 处理批次数: 400
[2024-01-15 10:35:00] Elasticsearch客户端连接已关闭
```

## 错误处理

- **网络连接错误**：自动重试最多3次，每次重试间隔递增
- **文件读取错误**：程序终止并输出错误信息
- **Elasticsearch错误**：记录错误日志并继续处理下一批次
- **批量更新失败**：自动重试，超过最大重试次数后跳过该批次

## 注意事项

1. **确保field6.txt文件存在**：文件应位于项目根目录下
2. **确保Elasticsearch服务器可访问**：检查网络连接和认证信息
3. **建议在非高峰期执行**：批量更新可能对服务器造成一定压力
4. **可以根据服务器性能调整参数**：
   - 如果服务器性能较好，可以增加`BATCH_SIZE`和减少`INTERVAL_MS`
   - 如果服务器压力较大，可以减少`BATCH_SIZE`和增加`INTERVAL_MS`
5. **程序支持中断恢复**：可以随时停止和重新启动
6. **检查索引名称**：确保`INDEX_NAME`配置正确

## 性能调优建议

- **批次大小**：根据服务器性能调整，建议50-200之间
- **间隔时间**：根据服务器负载调整，建议50-500毫秒之间
- **搜索大小**：建议保持1000，除非有特殊需求
- **重试次数**：建议保持3次，避免过多重试影响性能

## 依赖要求

确保项目中包含以下Elasticsearch客户端依赖：
```xml
<dependency>
    <groupId>org.elasticsearch.client</groupId>
    <artifactId>elasticsearch-rest-high-level-client</artifactId>
    <version>7.x.x</version>
</dependency>
```
