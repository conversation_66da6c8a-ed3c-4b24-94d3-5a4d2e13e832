# 通讯中心项目架构分析

## 项目概述

**项目名称**: Communication Center (通讯中心)
**技术栈**: Spring Boot + MyBatis Plus + Elasticsearch + Redis + RabbitMQ
**主要功能**: 多渠道外呼管理、通话记录管理、AI外呼任务管理、话务统计分析

## 架构图

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                   外部系统层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  客户营销系统  │  用户中心  │  文件服务  │  第三方适配器  │  权限服务  │  加密服务      │
│   (Marketing)  │   (UC)    │   (FS)    │     (TA)      │  (Auth)   │  (Encrypt)    │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                   ┌────▼────┐
                                   │  网关层  │
                                   └────┬────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  应用服务层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                              Controller 层                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  CallRecord     │  │   AICallJob     │  │   BYAICallJob   │  │   AuthInterface │ │
│  │  ManageController│  │ ManageController │  │ ManageController │  │ ForwardController│ │
│  │                 │  │                 │  │                 │  │                 │ │
│  │ • 通话记录查询   │  │ • AI外呼任务管理 │  │ • 百应AI外呼    │  │ • 权限接口转发   │ │
│  │ • 话务统计      │  │ • 任务创建/操作  │  │ • 任务管理      │  │ • 部门树查询     │ │
│  │ • 实时统计      │  │ • 客户信息管理   │  │ • 录音下载      │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                              Service 层                                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ CallRecordService│  │AICallJobCustomer │  │TrafficStatistic │  │DataCollection   │ │
│  │                 │  │ InfoService     │  │   Service       │  │   Service       │ │
│  │ • 通话记录处理   │  │ • 客户信息管理   │  │ • 话务统计分析   │  │ • 数据收集分析   │ │
│  │ • 录音下载      │  │ • ES数据操作    │  │ • 报表生成      │  │ • 告警监控      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   AI服务层       │  │   外呼服务层     │  │   工具服务层     │  │   权限服务层     │ │
│  │                 │  │                 │  │                 │  │                 │ │
│  │ • JDYXService   │  │ • ZHZXService   │  │ • FileUtil      │  │ • AuthService   │ │
│  │ • BYAIService   │  │ • JLFYService   │  │ • RedisUtil     │  │ • 权限验证工具   │ │
│  │ • 话术管理      │  │ • YKService     │  │ • RecordUtil    │  │ • 数据权限控制   │ │
│  │ • 任务调度      │  │ • YXService     │  │ • DateUtil      │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  消息队列层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                              MQ Consumer 层                                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │CallRecordsCall  │  │DownloadRecording │  │AddCustomerInfo  │  │SyncCallRecord   │ │
│  │BackMQConsumer   │  │   MQConsumer    │  │ToJobMQConsumer  │  │   MQConsumer    │ │
│  │                 │  │                 │  │                 │  │                 │ │
│  │ • 通话记录回调   │  │ • 录音下载处理   │  │ • 客户信息追加   │  │ • 通话记录同步   │ │
│  │ • 多渠道适配    │  │ • 文件上传      │  │ • 批量处理      │  │ • 数据清洗      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  数据访问层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                              Mapper 层                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │CcAiCallJobMapper│  │CcCallAccountConfig│ │BdCallaccountinfo│  │   其他Mapper    │ │
│  │                 │  │    Mapper       │  │    Service      │  │                 │ │
│  │ • AI任务数据    │  │ • 外呼账号配置   │  │ • 账号信息管理   │  │ • 基础数据操作   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  基础设施层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │     MySQL       │  │  Elasticsearch  │  │     Redis       │  │   RabbitMQ      │ │
│  │                 │  │                 │  │                 │  │                 │ │
│  │ • 业务数据存储   │  │ • 通话记录索引   │  │ • 缓存          │  │ • 消息队列      │ │
│  │ • 任务配置      │  │ • 客户信息索引   │  │ • 分布式锁      │  │ • 异步处理      │ │
│  │ • 账号管理      │  │ • 全文检索      │  │ • 会话存储      │  │ • 事件驱动      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  外部服务层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   京东言犀AI     │  │    百应AI       │  │   中弘智享      │  │    巨量飞鱼      │ │
│  │    (JDYX)       │  │   (BYAI)        │  │   (ZHZX)        │  │    (JLFY)       │ │
│  │                 │  │                 │  │                 │  │                 │ │
│  │ • AI外呼服务    │  │ • AI外呼服务    │  │ • 外呼服务      │  │ • 外呼服务      │ │
│  │ • 话术管理      │  │ • 录音服务      │  │ • 通话记录      │  │ • 通话记录      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │     云客        │  │     亿讯        │  │     天权        │  │     飞鱼        │ │
│  │     (YK)        │  │     (YX)        │  │     (TQ)        │  │     (FY)        │ │
│  │                 │  │                 │  │                 │  │                 │ │
│  │ • 外呼服务      │  │ • 外呼服务      │  │ • 外呼服务      │  │ • 外呼服务      │ │
│  │ • 通话记录      │  │ • 通话记录      │  │ • 通话记录      │  │ • 通话记录      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 核心组件说明

### 1. 应用服务层
- **CallRecordManageController**: 通话记录管理，话务统计，实时统计
- **AICallJobManageController**: AI外呼任务管理，任务创建和操作
- **BYAICallJobManageController**: 百应AI外呼任务专用管理
- **AuthInterfaceForwardController**: 权限接口转发，部门树查询

### 2. 业务服务层
- **AI服务**: JDYXService(京东言犀), BYAIService(百应AI)
- **外呼服务**: 多渠道外呼服务适配(ZHZX, JLFY, YK, YX等)
- **数据服务**: 通话记录处理，话务统计，数据收集分析
- **权限服务**: 权限验证，数据权限控制

### 3. 消息队列层
- **通话记录回调处理**: 多渠道通话记录回调统一处理
- **录音下载处理**: 异步录音文件下载和上传
- **客户信息追加**: AI外呼任务客户信息批量处理
- **数据同步**: 通话记录数据同步和清洗

### 4. 数据存储层
- **MySQL**: 业务数据，任务配置，账号管理
- **Elasticsearch**: 通话记录索引，客户信息索引，全文检索
- **Redis**: 缓存，分布式锁，会话存储
- **RabbitMQ**: 消息队列，异步处理，事件驱动

### 5. 外部集成
- **多渠道外呼服务**: 京东言犀、百应AI、中弘智享、巨量飞鱼等
- **内部服务**: 用户中心、文件服务、权限服务、客户营销系统

## 数据流向图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   前端应用   │───▶│   API网关    │───▶│ 通讯中心服务 │
└─────────────┘    └─────────────┘    └─────┬───────┘
                                            │
                   ┌────────────────────────┼────────────────────────┐
                   │                        │                        │
                   ▼                        ▼                        ▼
            ┌─────────────┐         ┌─────────────┐         ┌─────────────┐
            │  权限验证    │         │  业务处理    │         │  数据查询    │
            └─────┬───────┘         └─────┬───────┘         └─────┬───────┘
                  │                       │                       │
                  ▼                       ▼                       ▼
            ┌─────────────┐         ┌─────────────┐         ┌─────────────┐
            │  权限服务    │         │  消息队列    │         │ Elasticsearch│
            └─────────────┘         └─────┬───────┘         └─────────────┘
                                          │
                                          ▼
                                   ┌─────────────┐
                                   │  外部服务    │
                                   │ (AI外呼等)   │
                                   └─────────────┘
```

## 核心业务流程

### 1. AI外呼任务流程
```
客户营销系统 ──┐
              │
              ▼
         ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
         │  创建任务    │───▶│  分校分组    │───▶│  创建子任务  │
         └─────────────┘    └─────────────┘    └─────────────┘
                                                       │
                                                       ▼
         ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
         │  启动外呼    │◀───│  追加客户    │◀───│ 客户入ES    │
         └─────────────┘    └─────────────┘    └─────────────┘
                │
                ▼
         ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
         │  通话记录    │───▶│  录音下载    │───▶│  数据统计    │
         └─────────────┘    └─────────────┘    └─────────────┘
```

### 2. 通话记录处理流程
```
外呼服务商 ──┐
            │
            ▼
     ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
     │  回调接收    │───▶│  数据清洗    │───▶│  入库ES     │
     └─────────────┘    └─────────────┘    └─────────────┘
                                                   │
                                                   ▼
     ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
     │  话务统计    │◀───│  实时统计    │◀───│  数据查询    │
     └─────────────┘    └─────────────┘    └─────────────┘
```

## 技术特点

1. **微服务架构**: 基于Spring Boot的微服务设计
2. **多渠道适配**: 统一接入多个外呼服务提供商
3. **异步处理**: 基于RabbitMQ的消息驱动架构
4. **大数据处理**: Elasticsearch支持海量通话记录检索
5. **权限控制**: 细粒度的数据权限控制
6. **配置中心**: Apollo配置中心统一配置管理
7. **容器化部署**: Docker容器化部署支持

## 关键技术栈

### 后端框架
- **Spring Boot 2.x**: 主框架
- **MyBatis Plus**: ORM框架
- **Spring Cloud OpenFeign**: 服务间调用
- **Spring Security**: 安全框架

### 数据存储
- **MySQL**: 关系型数据库
- **Elasticsearch 6.8.1**: 搜索引擎
- **Redis**: 缓存和分布式锁

### 消息队列
- **RabbitMQ**: 消息中间件
- **自定义MQ Client**: 消息处理封装

### 外部集成
- **Apollo**: 配置中心
- **多渠道外呼API**: 京东言犀、百应AI等
- **OSS**: 对象存储服务

### 监控运维
- **Docker**: 容器化
- **企业微信机器人**: 告警通知
- **定时任务**: 数据收集和分析
